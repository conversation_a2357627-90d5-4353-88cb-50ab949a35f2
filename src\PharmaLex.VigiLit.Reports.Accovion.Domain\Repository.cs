using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain;

internal class Repository : GenericRepository<Result>, IRepository
{
    public Repository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<List<Result>> GetStoredProcResults(int year, int month)
    {
        var startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Utc);
        var endDate = startDate.AddMonths(1);

        var results = await context.Set<Result>()
            .FromSqlRaw("exec spCaseFileReport @CompanyName, @StartDate, @EndDate",
                new SqlParameter("@CompanyName", "Accovion GmbH"),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate))
            .ToListAsync();

        return results;
    }
}
