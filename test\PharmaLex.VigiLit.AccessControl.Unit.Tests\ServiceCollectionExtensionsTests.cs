﻿using Microsoft.Extensions.DependencyInjection;

namespace PharmaLex.VigiLit.AccessControl.Unit.Tests;

public class ServiceCollectionExtensionsTests
{

    [Fact]
    public void AddAccessControl_when_called_services_are_registered()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddAccessControl();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        Assert.NotNull(serviceProvider.GetService<IAccessControlService>());

        var countOfRegisteredPharmaLexServices = CountOfRegisteredPharmaLexServices(services);
        Assert.Equal(1, countOfRegisteredPharmaLexServices);
    }

    private static int CountOfRegisteredPharmaLexServices(ServiceCollection services)
    {
        int count = services.Where(x => x.ServiceType.FullName?.StartsWith("PharmaLex.VigiLit.AccessControl") == true).Select(x => x).Count();
        return count;
    }
}