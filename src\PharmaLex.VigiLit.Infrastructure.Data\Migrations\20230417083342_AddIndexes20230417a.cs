﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230417a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId_ReferenceClassificationId",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "ImportId", "ReferenceClassificationId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId_ReferenceClassificationId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId",
                table: "ImportContractReferenceClassifications",
                column: "ImportId");
        }
    }
}
