﻿using PharmaLex.VigiLit.Reporting.Contracts.ClassMap;

namespace PharmaLex.VigiLit.Reports.Merz.Domain;

public sealed class ResultClassMap : CamelCaseClassMap<MerzClientReportResult>
{
    public ResultClassMap()
    {
        Map(m => m.PlxId).Name("PlxId");
        Map(m => m.Substance).Name("Substance");
        Map(m => m.Authors).Name("Authors");
        Map(m => m.Title).Name("Title");
        Map(m => m.Citation).Name("Citation");
        Map(m => m.ImportDate).Name("ImportDate");
        Map(m => m.Source).Name("Source");
        Map(m => m.ContractVersion).Name("ContractVersion");

        Map(m => m.CreatedDate).Ignore();
        Map(m => m.CreatedBy).Ignore();
        Map(m => m.LastUpdatedDate).Ignore();
        Map(m => m.LastUpdatedBy).Ignore();
    }
}