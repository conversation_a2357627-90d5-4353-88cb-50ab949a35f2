/*!*******************************************!*\
  !*** ./src/js/utilities/download-file.js ***!
  \*******************************************/
DownloadFile={fromUrl:async(e,n=null,o=null,t=null,l=null)=>{var a="GET";if(e||plx.toast.show("Invalid URL given for download.",2,"failed",null,2500),!i)var i=document.getElementsByName("__RequestVerificationToken")[0]?.value;n&&(a="POST"),await fetch(e,{method:a,credentials:"same-origin",body:n,headers:{RequestVerificationToken:i,"Content-Type":"application/json"}}).then((async e=>{if(!e)throw"Did not get a response from the server.";var n=e.headers.get("Content-Disposition").split("filename=")[1].split(";")[0].replaceAll('"',""),t=await e.blob(),l=window.URL.createObjectURL(t),a=document.createElement("a");a.href=l,a.download=n,a.click(),a.remove(),o&&o()})).catch((e=>{console.log(e),t&&t()})).then((()=>{l&&l()}))}};
//# sourceMappingURL=downloadFile.js.map