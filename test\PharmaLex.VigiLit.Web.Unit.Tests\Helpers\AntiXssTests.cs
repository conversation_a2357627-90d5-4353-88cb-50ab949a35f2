using PharmaLex.Core.Web.Helpers;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.Web.Unit.Tests.Helpers;

[SuppressMessage("Major Code Smell", "S4144:Methods should not have identical implementations", Justification = "Inputs are grouped logically, don't want one giant test.")]
public class AntiXssTests
{
    [Theory]
    [InlineData("", "")]
    public void SanitizeJsonTagSoup_AlwaysPass(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Theory]
    [InlineData("[{\"key\":\"value\"}]", "[{\"key\":\"value\"}]")]
    public void SanitizeJsonTagSoup_ProducesValidJson(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Theory]
    [InlineData("[{\"key\":\"<script>alert('xss!')</script>\"}]", "[{\"key\":\"\"}]")]
    public void SanitizeJsonTagSoup_RemovesAttacks(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Theory]
    [InlineData("[{\"key\":\"<b>bold</b>\"}]", "[{\"key\":\"<b>bold</b>\"}]")]
    [InlineData("[{\"key\":\"<i>italic</i>\"}]", "[{\"key\":\"<i>italic</i>\"}]")]
    [InlineData("[{\"key\":\"<ul>underline</ul>\"}]", "[{\"key\":\"<ul>underline</ul>\"}]")]
    [InlineData("[{\"key\":\"<u>unarticulated</u>\"}]", "[{\"key\":\"<u>unarticulated</u>\"}]")]
    [InlineData("[{\"key\":\"<sup>super</sup>\"}]", "[{\"key\":\"<sup>super</sup>\"}]")]
    [InlineData("[{\"key\":\"<sub>sub</sub>\"}]", "[{\"key\":\"<sub>sub</sub>\"}]")]
    [InlineData("[{\"key\":\"line<br>break\"}]", "[{\"key\":\"line<br>break\"}]")]
    [InlineData("[{\"key\":\"line<br />break\"}]", "[{\"key\":\"line<br>break\"}]")]
    public void SanitizeJsonTagSoup_AllowsSafeHtml(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Theory]
    [InlineData("[{\"key\":\"carriage&#xD;return\"}]", "[{\"key\":\"carriage\\rreturn\"}]")]
    [InlineData("[{\"key\":\"carriage&#13;return\"}]", "[{\"key\":\"carriage\\rreturn\"}]")]
    [InlineData("[{\"key\":\"carriage&#xDreturn\"}]", "[{\"key\":\"carriage\\rreturn\"}]")]
    [InlineData("[{\"key\":\"line&#xA;feed\"}]", "[{\"key\":\"line\\nfeed\"}]")]
    [InlineData("[{\"key\":\"line&#10;feed\"}]", "[{\"key\":\"line\\nfeed\"}]")]
    [InlineData("[{\"key\":\"double&quot;quote\"}]", "[{\"key\":\"double\\\"quote\"}]")]
    [InlineData("[{\"key\":\"double&#34;quote\"}]", "[{\"key\":\"double\\\"quote\"}]")]
    [InlineData("[{\"key\":\"double&#x22;quote\"}]", "[{\"key\":\"double\\\"quote\"}]")]
    public void SanitizeJsonTagSoup_RemovesBreakages(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Theory]
    [InlineData("[{\"key\":\"less <than\"}]", "[{\"key\":\"less &lt;than\"}]")]
    [InlineData("[{\"key\":\"greater >than\"}]", "[{\"key\":\"greater &gt;than\"}]")]
    [InlineData("[{\"key\":\"published in <PEC, 116 (2023) 107964>, <https://doi.org/10.1016/j.pec.2023.107964>. The\"}]", "[{\"key\":\"published in &lt;PEC, 116 (2023) 107964&gt;, &lt;https://doi.org/10.1016/j.pec.2023.107964&gt;. The\"}]")]
    public void SanitizeJsonTagSoup_HandlesNonTags(string input, string expected)
    {
        string actual = AntiXss.SanitizeJsonTagSoup(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_HandlesNull()
    {
        object? input = null;
        string expected = "null";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_ProducesValidJson()
    {
        object? input = new[] { new { key = "value" } };
        string expected = "[{\"key\":\"value\"}]";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_RemovesAttacks()
    {
        object? input = new[] { new { key = "<script>alert('xss!')</script>" } };
        string expected = "[{\"key\":\"\"}]";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_AllowsSafeHtml()
    {
        object? input = new[] { new { key = "<b>bold</b>" } };
        string expected = "[{\"key\":\"<b>bold</b>\"}]";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_RemovesBreakages()
    {
        object? input = new[] { new { key = "carriage&#xD;return--line&#xA;feed" } };
        string expected = "[{\"key\":\"carriage\\rreturn--line\\nfeed\"}]";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void ToJson_HandlesNonTags()
    {
        object? input = new[] { new { key = "published in <PEC, 116 (2023) 107964>, <https://doi.org/10.1016/j.pec.2023.107964>. The" } };
        string expected = "[{\"key\":\"published in &lt;PEC, 116 (2023) 107964&gt;, &lt;https://doi.org/10.1016/j.pec.2023.107964&gt;. The\"}]";

        string actual = AntiXss.ToJson(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void SanitizeHtml_AllowsSafeHtml()
    {
        string input = "<h1>Hello World</h1>";
        string expected = "<h1>Hello World</h1>";

        string actual = AntiXss.SanitizeHtml(input);

        Assert.Equal(expected, actual);
    }

    [Fact]
    public void SanitizeHtml_HandlesNonTags()
    {
        string input = "published in <PEC, 116 (2023) 107964>, <https://doi.org/10.1016/j.pec.2023.107964>. The";
        string expected = "published in &lt;PEC, 116 (2023) 107964&gt;, &lt;https://doi.org/10.1016/j.pec.2023.107964&gt;. The";

        string actual = AntiXss.SanitizeHtml(input);

        Assert.Equal(expected, actual);
    }
}