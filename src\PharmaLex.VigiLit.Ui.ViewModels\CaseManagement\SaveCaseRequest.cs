﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

public record SaveCaseRequest(int? CaseId, int? PlxId, IReadOnlyCollection<CompanyModel> Companies, PSURRelevanceAbstract PSUR, 
    CaseMLMDuplicate MLM, string PvSafetyDatabaseId, string Comment, string UploadId, IReadOnlyCollection<string> UploadedFileNames, IReadOnlyCollection<int> DeletedFileIds);