﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Dotnet.Adapters;
using PharmaLex.Dotnet.Adapters.Reflection;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Reporting.Access;
using PharmaLex.VigiLit.Reporting.Contracts;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;
using PharmaLex.VigiLit.Reporting.Repositories;
using PharmaLex.VigiLit.Reporting.Services;

namespace PharmaLex.VigiLit.Reporting;

public static class ConfigureServices
{
    private static readonly TypeAdapter StartupType = new(typeof(IConfigureServices));

    public static void RegisterReporting(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IReportRepository, ReportRepository>();
        services.AddScoped<IReportService, ReportService>();
        services.AddScoped<IExportService, ExportService>();

        services.AddScoped<IPermissionEvaluator, ViewReportEvaluator>();
        services.AddScoped<IPermissionEvaluator<ViewReportPermissionContext>, ViewReportEvaluator>();
        services.AddScoped<IPermissionEvaluator, CreateReportEvaluator>();
        services.AddScoped<IPermissionEvaluator<CreateReportPermissionContext>, CreateReportEvaluator>();

        RegisterReportAssemblies(services, configuration);
    }

    static void RegisterReportAssemblies(IServiceCollection services, IConfiguration configuration)
    {
        var assemblies = GetAssemblies();
        var reportAssemblies = GetReportingAssemblies(assemblies);

        foreach (var assembly in reportAssemblies)
        {
            var startupTypes = GetAssemblyStartupTypes(assembly);
            RegisterReportTypes(services, configuration, startupTypes);
        }
    }

    private static void RegisterReportTypes(IServiceCollection services, IConfiguration configuration, IEnumerable<IType> startupTypes)
    {
        foreach (var startupType in startupTypes)
        {
            var instance = (IConfigureServices)(Activator.CreateInstance(startupType.Assembly.FullName, startupType.FullName)?.Unwrap());
            instance?.RegisterServices(services, configuration);
        }
    }

    private static IEnumerable<IType> GetAssemblyStartupTypes(IAssembly assembly)
    {
        var types = assembly.GetTypes();
        var startupTypes = types.Where(IsStartupType());
        return startupTypes;
    }

    private static Func<IType, bool> IsStartupType()
    {
        return type => !type.IsAbstract && !type.IsInterface && StartupType.IsAssignableFrom(type);
    }

    private static IEnumerable<IAssembly> GetReportingAssemblies(IAssembly[] assemblies)
    {
        return assemblies
            .Where(x =>
            {
                var name = x.FullName.ToLowerInvariant().Substring(0, x.FullName.IndexOf(','));
                return IsReportAssemblyName(name);
            });
    }

    private static bool IsReportAssemblyName(string name)
    {
        return name.StartsWith("pharmalex.vigilit.reports.");
    }

    private static IAssembly[] GetAssemblies()
    {

        var filenames = Directory.EnumerateFiles(@AppDomain.CurrentDomain.BaseDirectory, "PharmaLex.VigiLit.Reports.*.dll").Where(f => f.Contains("PharmaLex.VigiLit.Reports.", StringComparison.OrdinalIgnoreCase));

        foreach (var filename in filenames)
        {
            System.Runtime.Loader.AssemblyLoadContext.Default.LoadFromAssemblyPath(filename);
        }

        var currentDomain = new AppDomainAdapter(AppDomain.CurrentDomain);
        return currentDomain.GetAssemblies();
    }
}
