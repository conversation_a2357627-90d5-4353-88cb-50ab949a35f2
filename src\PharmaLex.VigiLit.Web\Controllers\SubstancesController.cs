using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class SubstancesController : BaseController
{
    private readonly ISubstanceService _substanceService;

    public SubstancesController(ISubstanceService substanceService,
                                IUserSessionService userSessionService,
                                IConfiguration configuration) : base(userSessionService, configuration)
    {
        _substanceService = substanceService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var substances = await _substanceService.GetAllAsync();
        return View(substances);
    }

    [HttpGet("[action]")]
    public IActionResult Create()
    {
        return View("Edit", new SubstanceModel());
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Create(SubstanceModel model)
    {
        if (ModelState.IsValid)
        {
            try
            {
                await _substanceService.AddAsync(model);
                AddNotification($"Substance {model.Name} was created successfully", UserNotificationType.Confirm);
            }
            catch (Exception ex)
            {

                AddNotification($"An issue occurred while creating {ex.Message}", UserNotificationType.Failed);

            }
            return RedirectToAction("Index");
        }
        return View("Edit", model);
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        var substance = await _substanceService.GetByIdAsync(id);

        return View(substance);
    }

    [HttpPost("[action]/{id}")]
    public async Task<IActionResult> Edit(SubstanceModel model)
    {
        if (ModelState.IsValid)
        {
            try
            {
                await _substanceService.UpdateAsync(model);
                AddNotification($"Substance {model.Name} was edited successfully", UserNotificationType.Confirm);

            }
            catch (Exception ex)
            {
                AddNotification($"An issue occurred while editing {ex.Message}", UserNotificationType.Failed, 3500);

            }
            return RedirectToAction("Edit");
        }

        AddNotification($"An issue occurred while editing the substance", UserNotificationType.Failed, 3500);
        return View(model);
    }

    [HttpDelete("[action]/{id}")]
    public async Task<IActionResult> DeleteSynonym(int id)
    {
        await _substanceService.DeleteSynonymAsync(id);
        AddNotification($"Synonym was deleted successfully", UserNotificationType.Confirm);
        return Ok();
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> ValidateSubstanceName(string name, int id)
    {
        var isNameValid = await _substanceService.ValidateNameAsync(name, id);
        return Json(isNameValid);
    }
}

