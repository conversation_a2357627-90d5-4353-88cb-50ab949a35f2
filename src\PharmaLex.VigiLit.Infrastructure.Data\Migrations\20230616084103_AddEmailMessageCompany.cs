﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddEmailMessageCompany : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CompanyId",
                table: "EmailMessages",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_EmailMessages_CompanyId",
                table: "EmailMessages",
                column: "CompanyId");

            migrationBuilder.AddForeignKey(
                name: "FK_EmailMessages_Companies_CompanyId",
                table: "EmailMessages",
                column: "CompanyId",
                principalTable: "Companies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EmailMessages_Companies_CompanyId",
                table: "EmailMessages");

            migrationBuilder.DropIndex(
                name: "IX_EmailMessages_CompanyId",
                table: "EmailMessages");

            migrationBuilder.DropColumn(
                name: "CompanyId",
                table: "EmailMessages");
        }
    }
}
