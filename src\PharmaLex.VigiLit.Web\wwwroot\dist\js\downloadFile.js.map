{"version": 3, "file": "js/downloadFile.js", "mappings": ";;;AAAAA,aAAe,CACXC,QAASC,MAAOC,EAAKC,EAAO,KAAMC,EAAY,KAAMC,EAAY,KAAMC,EAAa,QAC/E,IAAIC,EAAc,MAKlB,GAHKL,GACDM,IAAIC,MAAMC,KAAK,kCAAmC,EAAG,SAAU,KAAM,OAEpEC,EACD,IAAIA,EAAQC,SAASC,kBAAkB,8BAA8B,IAAIC,MAEzEX,IACAI,EAAc,cAGZQ,MAAMb,EAAK,CACbc,OAAQT,EACRU,YAAa,cACbC,KAAMf,EACNgB,QAAS,CACL,yBAA4BR,EAC5B,eAAgB,sBAErBS,MAAKnB,MAAOoB,IACX,IAAKA,EACD,KAAM,0CAEV,IACIC,EADaD,EAASF,QAAQI,IAAI,uBACZC,MAAM,aAAa,GAAGA,MAAM,KAAK,GAAGC,WAAW,IAAK,IAC1EC,QAAaL,EAASK,OAEtBC,EAAUC,OAAOC,IAAIC,gBAAgBJ,GACrCK,EAAInB,SAASoB,cAAc,KAC/BD,EAAEE,KAAON,EACTI,EAAEG,SAAWZ,EACbS,EAAEI,QACFJ,EAAEK,SAEEhC,GACAA,GACJ,IACDiC,OAAMC,IACLC,QAAQC,IAAIF,GACRjC,GACAA,GACJ,IACDe,MAAK,KACAd,GACAA,GACJ,GACF", "sources": ["webpack://PharmaLex.VigiLit.v2/./src/js/utilities/download-file.js"], "sourcesContent": ["DownloadFile = {\r\n    fromUrl: async (url, data = null, onSuccess = null, onFailure = null, onComplete = null) => {\r\n        var requestType = \"GET\";\r\n\r\n        if (!url) {\r\n            plx.toast.show('Invalid URL given for download.', 2, 'failed', null, 2500);\r\n        }\r\n        if (!token) {\r\n            var token = document.getElementsByName(\"__RequestVerificationToken\")[0]?.value;\r\n        }\r\n        if (data) {\r\n            requestType = \"POST\";\r\n        }\r\n        \r\n        await fetch(url, {\r\n            method: requestType,\r\n            credentials: 'same-origin',\r\n            body: data,\r\n            headers: {\r\n                \"RequestVerificationToken\": token,\r\n                \"Content-Type\": \"application/json\"\r\n            }\r\n        }).then(async (response) => {\r\n            if (!response) {\r\n                throw 'Did not get a response from the server.';\r\n            }\r\n            var headerInfo = response.headers.get('Content-Disposition');\r\n            var fileName = headerInfo.split('filename=')[1].split(';')[0].replaceAll('\"', '');\r\n            var blob = await response.blob();\r\n\r\n            var blobUrl = window.URL.createObjectURL(blob);\r\n            var a = document.createElement(\"a\");\r\n            a.href = blobUrl;\r\n            a.download = fileName;\r\n            a.click();\r\n            a.remove();\r\n            \r\n            if (onSuccess) {\r\n                onSuccess();\r\n            }\r\n        }).catch(error => {\r\n            console.log(error);\r\n            if (onFailure) {\r\n                onFailure();\r\n            }\r\n        }).then(() => {\r\n            if (onComplete) {\r\n                onComplete();\r\n            }\r\n        });\r\n    }\r\n}"], "names": ["DownloadFile", "fromUrl", "async", "url", "data", "onSuccess", "onFailure", "onComplete", "requestType", "plx", "toast", "show", "token", "document", "getElementsByName", "value", "fetch", "method", "credentials", "body", "headers", "then", "response", "fileName", "get", "split", "replaceAll", "blob", "blobUrl", "window", "URL", "createObjectURL", "a", "createElement", "href", "download", "click", "remove", "catch", "error", "console", "log"], "sourceRoot": ""}