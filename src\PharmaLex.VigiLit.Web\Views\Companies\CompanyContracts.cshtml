@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;
@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.CompanyWithContractsModel

@{
    ViewData["Title"] = "Company Contracts";
    Layout = "CompanyLayout";
}

@section Buttons {
    <a href="@($"/Companies/{Model.Id}/Contract/Create")" class="button btn-default">Create Company Contract</a>
}

<div id="contract" v-cloak>
    <section>
        <filtered-table :items="contract" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section SubPageScripts {
<script type="text/javascript">

        let editUrl = "@($"/Companies/{@Model.Id}/Contract/")"

        var pageConfig = {
            appElement: "#contract",
            data: function () {
                return {
                    link: editUrl,
                    contract: @Html.Raw(AntiXss.ToJson(Model.Contracts)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'projectName',
                                sortKey: 'projectName',
                                header: 'Project',
                                edit: {},
                                type: 'text',
                                style: 'width: 14%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                edit: {},
                                type: 'text',
                                style: 'width: 14%;'
                            },
                            {
                                dataKey: 'contractType',
                                sortKey: 'contractType',
                                header: 'Type',
                                edit: {},
                                type: 'text',
                                style: 'width: 14%;'
                            },
                            {
                                dataKey: 'contractWeekday',
                                sortKey: 'contractWeekday',
                                header: 'Weekday',
                                edit: {},
                                type: 'text',
                                style: 'width: 14%;'
                            },
                            {
                                dataKey: 'searchPeriod',
                                sortKey: 'searchPeriod',
                                header: 'Search Period',
                                edit: {},
                                type: 'text',
                                style: 'width: 14%;'
                            },
                            {
                                dataKey: 'contractStartDate',
                                sortKey: 'contractStartDate',
                                header: 'Contract Start',
                                edit: {
                                    convert: e => {
                                        const date = moment(e);
                                        if (date.year() < 1901)
                                        {
                                            return 'Not Started';
                                        }
                                        return date.format('DD MMM YYYY')
                                    }
                                },
                                type: 'date',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'isActive',
                                sortKey: 'isActive',
                                header: 'Active',
                                type: 'bool',
                                style: 'width: 14%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'projectName',
                            options: [],
                            type: 'search',
                            header: 'Search Project',
                            fn: v => p => p.projectName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'substanceName',
                            options: [],
                            type: 'search',
                            header: 'Search Substance',
                            fn: v => p => p.substanceName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'contractType',
                            options: [],
                            type: 'search',
                            header: 'Search Type',
                            fn: v => p => p.contractType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'contractWeekday',
                            options: [],
                            type: 'search',
                            header: 'Search Weekday',
                            fn: v => p => p.contractWeekday.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'searchPeriod',
                            options: [],
                            type: 'search',
                            header: 'Search Search Period',
                            fn: v => p => p.searchPeriod.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
</script>
}
