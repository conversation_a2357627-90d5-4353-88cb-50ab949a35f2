@using PharmaLex.VigiLit.Web.Controllers
@using PharmaLex.VigiLit.Web.Helpers
@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.CompanyModel

@{
    Layout = "_Layout";
}

<form method="post">
    <div class="sub-header">
        @if (Model.Id == default)
        {
            <h2>Create Company</h2>
        }
        else
        {
            <h2>Company | @Model.Name</h2>
        }

        <div class="controls">
            @await RenderSectionAsync("Buttons")
        </div>
    </div>

    <section>
        @if (Model.Id != default)
        {
            <ul class="sub-nav">
                <li>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="@(ViewContext.IsActionActive(nameof(CompaniesController.Edit)) ? "active": "")">Details</a>
                </li>
                <li>
                    <a asp-action="CompanyContracts" asp-route-companyId="@Model.Id" class="@(ViewContext.IsActionActive(nameof(CompaniesController.CompanyContracts)) ? "active": "")">Contracts</a>
                </li>
                <li>
                    <a asp-action="CompanyProjects" asp-route-companyId="@Model.Id" class="@(ViewContext.IsActionActive(nameof(CompaniesController.CompanyProjects)) ? "active": "")">Projects</a>
                </li>
                <li>
                    <a asp-action="CompanyUsers" asp-route-companyId="@Model.Id" class="@(ViewContext.IsActionActive(nameof(CompaniesController.CompanyUsers)) ? "active": "")">Users</a>
                </li>
            </ul>
            <hr />
        }

        @RenderBody()

    </section>
</form>


@section Scripts {
    @await RenderSectionAsync("SubPageScripts", required: false)
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
<partial name="Components/ModalDialog" />
<partial name="Components/ImageCell" />
}