﻿using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceHistoryActionModel
{
    public int Id { get; set; }
    public int ReferenceClassificationId { get; set; }
    public ReferenceHistoryActionType ReferenceHistoryActionType { get; set; }
    public string ReferenceHistoryActionTypeText { get; set; }
    public int UserId { get; set; }
    public string UserName { get; set; }
    public DateTime TimeStamp { get; set; }
}