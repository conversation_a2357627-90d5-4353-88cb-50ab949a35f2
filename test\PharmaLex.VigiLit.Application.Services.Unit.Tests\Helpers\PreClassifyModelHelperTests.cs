﻿using Microsoft.FeatureManagement;
using Moq;
using PharmaLex.VigiLit.Application.Services.Helpers;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Services.Unit.Tests.Helpers;

public class PreClassifyModelHelperTests
{
    private readonly Mock<IClassificationCategoryService> _mockClassificationCategoryService = new();
    private readonly Mock<ICountryService> _mockCountryService = new();
    private readonly Mock<IClassificationService> _mockClassificationService = new();
    private readonly Mock<IFeatureManager> _mockFeatureManager = new();

    private readonly IPreClassifyModelHelper _preClassifyModelHelper;

    public PreClassifyModelHelperTests()
    {
        _preClassifyModelHelper = new PreClassifyModelHelper(
            _mockClassificationCategoryService.Object,
            _mockCountryService.Object,
            _mockClassificationService.Object,
            _mockFeatureManager.Object
            );
    }

    [Fact]
    public async Task GetPreClassifyModel()
    {
        // Arrange
        const int importId = 42;
        const int todoCount = 43;
        const int preClassifiedCount = 44;

        IEnumerable<ClassificationCategoryModel> classificationCategoryModelCollection = new List<ClassificationCategoryModel>();
        IEnumerable<string> countries = new List<string>();

        _mockClassificationCategoryService.Setup(x => x.GetAllAsync())
            .Returns(Task.FromResult(classificationCategoryModelCollection));
        _mockCountryService.Setup(x => x.GetAllAsync())
            .Returns(Task.FromResult(countries));
        _mockClassificationService.Setup(x => x.GetToDoCount(importId))
            .Returns(Task.FromResult(todoCount));
        _mockClassificationService.Setup(x => x.GetPreclassifiedCount(importId))
            .Returns(Task.FromResult(preClassifiedCount));
        _mockFeatureManager.Setup(x => x.IsEnabledAsync("DisplayAiSuggestions"))
            .Returns(Task.FromResult(true));

        var importSelectionModel = new ImportSelectionModel()
        {
            ImportId = importId,
        };

        IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels = new List<PreclassifyReferenceModel>();

        // Act
        var result = await _preClassifyModelHelper.GetPreClassifyModel(importSelectionModel, preClassifyReferenceModels);

        // Assert
        Assert.Equal(importSelectionModel, result.SelectedImport);
        Assert.Equal(todoCount, result.TodoCount);
        Assert.Equal(preClassifiedCount, result.CompletedCount);
        Assert.Equal(preClassifyReferenceModels, result.PreclassifyReferenceModels);
        Assert.Equal(classificationCategoryModelCollection, result.ClassificationCategories);
        Assert.Equal(countries, result.Countries);
        Assert.True(result.DisplayAiSuggestions);
    }
}
