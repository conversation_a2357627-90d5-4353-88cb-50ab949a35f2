﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.ContractManagement.Ui.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.Countries;

namespace PharmaLex.VigiLit.ContractManagement.Ui;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class JournalsController(IJournalService journalService, ICountryService countryService)
    : Controller
{

    [HttpGet]
    public async Task<IActionResult> GetAllJournals()
    {
        var journals = await journalService.GetAll();
        return Ok(journals);
    }

    [HttpGet("[action]/{countryId}")]
    public async Task<IEnumerable<JournalViewModel>> JournalsForCountry(int countryId)
    {
        return await journalService.GetJournalsForCountry(countryId);
    }

    [HttpGet("[action]")]
    public async Task<IEnumerable<CountryModel>> GetCountryList()
    {
        return await countryService.GetAllWithDetailsAsync();
    }

    [HttpGet("[action]")]
    public async Task<IEnumerable<CountryModel>> GetSubscribedCountries()
    {
        return await journalService.GetSubscribedCountries();
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetJournal(int id)
    {
        var journal = await journalService.GetJournal(id);

        if (journal == null)
        {
            return NotFound();
        }
        return Ok(journal);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Save([FromBody] JournalModel model)
    {
        var errors = ValidateJournalModel(model);
        if (errors != null)
        {
            return BadRequest(new { success = false, errors });
        }
        await journalService.AddAsync(model);
        return Ok();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Update([FromBody] JournalModel model)
    {
        var errors = ValidateJournalModel(model);
        if (errors != null)
        {
            return BadRequest(new { success = false, errors });
        }
        await journalService.UpdateAsync(model);
        return Ok();
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetEnabledJournalContractCount(int id)
    {
        var count = await journalService.CountContractsWithEnabledJournal(id);
        return Ok(count);
    }

    private static readonly string[] EmptyModelError = ["Request body is empty."];

    private Dictionary<string, string[]>? ValidateJournalModel(JournalModel? model)
    {
        if (model == null)
            return new Dictionary<string, string[]> { { "model", EmptyModelError } };

        if (!string.IsNullOrEmpty(model.Url) && !Uri.TryCreate(model.Url, UriKind.Absolute, out _))
        {
            ModelState.AddModelError("url", "Invalid URL format.");
        }

        if (model.CountryId <= 0)
        {
            ModelState.AddModelError("countryId", "Country is required.");
        }

        if (!string.IsNullOrEmpty(model.Issn) && !IssnValidator.IsValidIssn(model.Issn))
        {
            ModelState.AddModelError("issn", "ISSN is invalid.");
        }

        if (!ModelState.IsValid)
        {
            return ModelState
                .Where(x => x.Value != null && x.Value.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value!.Errors
                                .Where(e => e != null && !string.IsNullOrWhiteSpace(e.ErrorMessage))
                                .Select(e => e.ErrorMessage)
                                .ToArray()
                );
        }

        return null;
    }
}
