@model List<PharmaLex.VigiLit.Ui.ViewModels.EmailService.EmailMessageModel>
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Email Messages";
}

<div id="emailMessages">
    <div class="sub-header">
        <h2>Email Messages</h2>
        <div class="controls">
            <a class="button btn-default" href="../Emails">Back to Email Log</a>
        </div>
    </div>
    <section>
        <filtered-table :items="emailMessages" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

<script type="text/javascript">

        var pageConfig = {
            appElement: "#emailMessages",
            data: function () {
                return {
                    link: '/Emails/EmailMessageDetails/',
                    emailMessages: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'companyName',
                                sortKey: 'companyName',
                                header: 'Company',
                                type: 'text',
                                style: 'width: 16%;'
                            },
                            {
                                dataKey: 'recipientName',
                                sortKey: 'recipientName',
                                header: 'Recipient',
                                type: 'text',
                                style: 'width: 18%;'
                            },
                            {
                                dataKey: 'recipientEmailAddress',
                                sortKey: 'recipientEmailAddress',
                                header: 'Email Address',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'subject',
                                sortKey: 'subject',
                                header: 'Subject',
                                type: 'text',
                                style: 'width: 19%;'
                            },
                            {
                                dataKey: 'status',
                                sortKey: 'status',
                                header: 'Status',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'timestamp',
                                sortKey: 'timestamp',
                                header: 'Timestamp',
                                type: 'text',
                                style: 'width: 14%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'companyName',
                            options: [],
                            type: 'search',
                            header: 'Search Company',
                            fn: v => p => p.companyName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'recipientName',
                            options: [],
                            type: 'search',
                            header: 'Search Recipient',
                            fn: v => p => p.recipientName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'recipientEmailAddress',
                            options: [],
                            type: 'search',
                            header: 'Search Email Address',
                            fn: v => p => p.recipientEmailAddress.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'subject',
                            options: [],
                            type: 'search',
                            header: 'Search Subject',
                            fn: v => p => p.subject.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'status',
                            options: [],
                            type: 'search',
                            header: 'Search Status',
                            fn: v => p => p.status.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}