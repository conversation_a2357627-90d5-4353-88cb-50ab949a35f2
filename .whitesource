{"scanSettings": {"configMode": "LOCAL", "configExternalURL": "", "projectToken": "", "baseBranches": []}, "checkRunSettings": {"vulnerableCheckRunConclusionLevel": "failure", "displayMode": "diff"}, "pullRequestStatusSettings": {"displayMode": "diff", "vulnerablePullRequestStatus": "failed", "useMendsStatusNames": true}, "issueSettings": {"minSeverityLevel": "LOW", "issueType": "DEPENDENCY"}, "remediateSettings": {"workflowRules": {"enabled": true}, "enableRenovate": true, "extends": ["config:base"]}}