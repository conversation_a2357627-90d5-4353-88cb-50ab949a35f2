﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class DenormalizeImportId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.AddColumn<int>(
                name: "ImportId",
                table: "ImportContractReferenceClassifications",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId")
                .Annotation("SqlServer:Include", new[] { "ReferenceClassificationId", "ICRCType", "EmailQueued", "EmailSent", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "EmailReason", "ImportId" });

            migrationBuilder.Sql("exec('UPDATE ImportContractReferenceClassifications SET ImportId = (SELECT ImportId FROM ImportContracts WHERE Id = ImportContractId)')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "ImportId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId")
                .Annotation("SqlServer:Include", new[] { "ReferenceClassificationId", "ICRCType", "EmailQueued", "EmailSent", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "EmailReason" });
        }
    }
}
