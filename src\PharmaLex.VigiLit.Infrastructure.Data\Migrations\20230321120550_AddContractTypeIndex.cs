﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddContractTypeIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive",
                table: "Contracts");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive_ContractType",
                table: "Contracts",
                columns: new[] { "IsActive", "ContractType" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive_ContractType",
                table: "Contracts");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive",
                table: "Contracts",
                column: "IsActive");
        }
    }
}
