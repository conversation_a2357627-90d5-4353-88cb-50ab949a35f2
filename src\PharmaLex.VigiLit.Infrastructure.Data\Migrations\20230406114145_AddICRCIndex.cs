﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddICRCIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId_ImportContractId",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "ReferenceClassificationId", "ImportContractId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId",
                table: "ImportContractReferenceClassifications",
                column: "ReferenceClassificationId");
        }
    }
}
