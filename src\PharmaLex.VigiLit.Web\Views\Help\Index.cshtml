@using PharmaLex.Core.Configuration
@using AppSettingsHelper = PharmaLex.Core.Configuration.AppSettingsHelper

@inject AppSettingsHelper AppSettings

@{
    ViewData["Title"] = "Help";
}

<div id="help" v-cloak>
    
    <div class="sub-header">
        <h2>Help</h2>
        <div class="controls">
        </div>
    </div>

    <section>
        <div class="files">
            <div class="file">
                <h2>User Manual</h2>
                <p><span>File Name:</span> VigiLit User Manual V1.pdf</p>
                <p><span>File Type:</span> PDF</p>
                <p><span>File Size:</span> 1.3 MB</p>
                <div class="file-buttons">
                    <a class="button" href="/Help/ViewUserManual" target="_blank">View in New Tab</a>
                    <a class="button" href="/Help/DownloadUserManual" download>Download File</a>
                </div>
            </div>
        </div>
    </section>

    <br />

    <section>
        <div id="build-info">
            <h2>About</h2>
            <div class="content">
                <p><b>Version:</b> @AppSettings.Version</p>
                <p><b>Build:</b> @AppSettings.BuildInfo</p>
            </div>
        </div>
    </section>

</div>

@section Scripts {
<script type="text/javascript">
        var pageConfig = {
            appElement: "#help",
            data: function () {
                return {
                };
            }
        };
</script>
}
