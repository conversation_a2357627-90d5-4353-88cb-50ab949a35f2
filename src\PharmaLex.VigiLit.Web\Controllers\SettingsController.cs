﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.Configuration.Services;
using PharmaLex.FeatureManagement.Entities;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;
using System;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policy = Policies.SuperAdmin)]
public class SettingsController : BaseController
{
    private readonly IFeatureFlagServices _featureFlagServices;
    private const string DisplayAiSuggestions = "DisplayAiSuggestions";

    public SettingsController(IUserSessionService userSessionService,
        IConfiguration configuration,
        IFeatureFlagServices featureFlagServices
        ) : base(userSessionService, configuration)
    {
        _featureFlagServices = featureFlagServices;

    }
    public async Task<IActionResult> Index()
    {
        var aiSuggestionFlag = await _featureFlagServices.GetFeatureFlag(DisplayAiSuggestions);
        return View(aiSuggestionFlag);
    }


    [HttpPost("[action]")]
    public async Task<IActionResult> Edit([FromBody] FeatureFlag model)
    {
        if (ModelState.IsValid)
        {
            try
            {
                await _featureFlagServices.UpdateAsync(model);
                AddNotification($"Ai Feature was successfully changed", UserNotificationType.Confirm);
            }
            catch (Exception ex)
            {

                AddNotification($"An issue occurred {ex.Message}", UserNotificationType.Failed);

            }
            return RedirectToAction("Index");
        }
        return View("Index", model);
    }

}
