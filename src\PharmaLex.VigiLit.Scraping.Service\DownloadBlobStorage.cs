﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service;

public class DownloadBlobStorage : IDownloadBlobStorage
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportScrapeDocumentOptions _importScrapeDocumentOptions;
    private string _folderName;

    public DownloadBlobStorage(IDocumentService documentService, IOptions<AzureStorageImportScrapeDocumentOptions> importScrapeDocumentOptions)
    {
        _documentService = documentService;
        _folderName = string.Empty;
        _importScrapeDocumentOptions = importScrapeDocumentOptions.Value;
    }

    public async Task WriteDataItemAsync(string fileName, Stream fileStream, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(fileName);
        await _documentService.Create(documentDescriptor, fileStream, cancellationToken);
    }
    public void SetBlobFolderName(string folderName)
    {
        _folderName = folderName;
    }

    private DocumentDescriptor GetDocumentDescriptor(string fileName)
    {
        var blobName = $"scraping/{_folderName}/{fileName}";
        return new DocumentDescriptor(_importScrapeDocumentOptions.AccountName, _importScrapeDocumentOptions.ContainerName, blobName);
    }
}
