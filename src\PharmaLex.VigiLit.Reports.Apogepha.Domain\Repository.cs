using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

public class Repository : GenericRepository<ApogephaClientReportResult>, IRepository
{
    public Repository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<List<ApogephaClientReportResult>> GetStoredProcResults(int year, int month)
    {
        var startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Utc);
        var endDate = startDate.AddMonths(1);

        var results = await context.Set<ApogephaClientReportResult>()
            .FromSqlRaw("exec spApogephaClassificationReport @CompanyName, @StartDate, @EndDate",
                new SqlParameter("@CompanyName", "APOGEPHA Arzneimittel GmbH"),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate))
            .ToListAsync();

        return results;
    }
}