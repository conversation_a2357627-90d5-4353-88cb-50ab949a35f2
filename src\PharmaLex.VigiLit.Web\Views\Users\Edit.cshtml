﻿@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using PharmaLex.Authentication.B2C
@using PharmaLex.Core.UserManagement.Claims
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Application.UserManagement.Users
@using PharmaLex.VigiLit.Domain.UserManagement
@using PharmaLex.VigiLit.Web.Helpers
@model PharmaLex.VigiLit.Ui.ViewModels.UserManagement.UserFullModel
@inject IUserService userService
@inject IHttpContextAccessor httpContextAccessor
@{
    ViewData["Title"] = "Edit User";
    List<ClaimModel> _claims = await userService.GetClaimsAsync(httpContextAccessor.HttpContext.User);
    var pharamalexClientResearcherClaim = await userService.GetClaimByNameAsync(Claims.PharmaLexClientResearcher);
    var userId = User.GetClaimValue<int>("plx:userid");
}

<div id="user" v-cloak>
    <form method="post">
        <div class="sub-header">
            @if (Model.Id == default)
            {
                <h2 class="brand-color">Create User</h2>
            }
            else
            {
                <h2>@Model.DisplayNameAndEmail</h2>
            }
            <div class="controls">
                @if (Model.Id == default)
                {
                    <button asp-action="Create" type="submit" class="btn-default" :disabled="!isModelSet || selfEdit">Save</button>
                }
                else
                {
                    <button v-on:click.stop.prevent="saveUser()" class="btn-default" :disabled="!isModelSet || selfEdit">Save</button>
                }
                <a class="button secondary btn-default" href="/users/index">Cancel</a>
            </div>
        </div>
        <section class="flex flex-wrap flex-gap card-container">
            <div id="details-column" v-if="user" v-bind:class="['section-card', {'hidden': !user}]">
                <h2>Details</h2>
                <div class="form-group">
                    <label>First Name</label>
                    <input name="GivenName" v-model="user.givenName" type="text" readonly="readonly" required :disabled="selfEdit" />
                </div>
                <div class="form-group">
                    <label>Last Name</label>
                    <input name="FamilyName" v-model="user.familyName" type="text" readonly="readonly" required :disabled="selfEdit" />
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input name="Email" v-model="user.email" type="text" readonly="readonly" :disabled="selfEdit" />
                    <span asp-validation-for="Email" class="error-color"></span>
                </div>
            </div>
            <div id="search-column" v-if="!user" class="section-card">
                <h2>User Details</h2>
                <div class="form-group">
                    <label>Start typing the user's name or email address</label>
                    <autocomplete :config="config" v-on:selected="selectedItem"></autocomplete>
                </div>
            </div>
            <div id="claims-column" v-bind:class="['section-card', {'form-col-disabled': !user}]">
                <h2>Manage roles</h2>
                <div class="form-group">
                    <div v-for="claim in claims" :key="claim.id" class="checkbox-list-item">
                        <div class="switch-wrapper" v-bind:class="{'disabled' : isDisabled(claim.id)}">
                            <span :id="'claim' + claim.id + '-label'">{{ claim.displayName }}</span>
                            <label class="switch-container" >
                                <input v-if="user" aria-labelled-by="claim.id + '-label'" class="switch" name="claimids" :id="claim.id" :value="claim.id" v-model="user.claimIds" type="checkbox" :checked="isChecked(claim.id)" :disabled="isDisabled(claim.id) || selfEdit" />
                                <label :for="claim.id" class="switch" :title="getReasonForNonEdit(claim.id)"></label>
                            </label>
 
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        var model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var pageConfig = {
            appElement: '#user',
            data: function () {
                return {
                    config: {
                        canAddNew: false,
                        searchUrl: '/users/find?term={term}'
                    },
                    user: model.initialised ? model : null,
                    isModelSet: model.initialised,
                    claims: @Html.Raw(AntiXss.ToJson(_claims)),
                    selfEdit: model.id === @userId,
                    reasonForNotEditable: ''
                                         };
            },
            methods: {
                selectedItem(selectedUser) {
                    this.user = selectedUser;
                    this.user.claimIds = [];
                    this.isModelSet = true;
                    /* Id has a value 0, when the selected user does not exist
                     * in the db of the application, but comes from the active directory only*/
                    if (this.user.id > 0) {
                        document.location.href = '/users/edit/' + this.user.id;
                    }
                },
                isChecked(claimId) {
                    var checked = 'checked'
                    if (this.user && this.user.claimIds && this.user.claimIds.includes(claimId)) {
                        return checked;
                    }
                },
                isDisabled(claimId) {
                    return claimId === @pharamalexClientResearcherClaim.Id ? true : false;
                },
                getReasonForNonEdit(claimId) {
                    if (claimId === @pharamalexClientResearcherClaim.Id ) {
                        return this.reasonForNotEditable = "This role is set via companies screen";
                    }
                }, saveUser() {
                    fetch(`/Users/<USER>/` + this.user.id, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(this.user),

                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                    }).then(result => {
                        if (result.ok) {
                            location.href = '/users/index';
                        }
                        else {
                            plx.toast.show('There was a problem saving the user. Please try again.', 2, 'failed', null, 5000)
                            console.log(error);
                        }

                    })
                },

            },
            created() {
                if (@Model.Id !== 0) {
                    this.user = @Html.Raw(AntiXss.ToJson(Model));
                    this.isModelSet = true;
                }
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/AutoCompleteList" />
 }