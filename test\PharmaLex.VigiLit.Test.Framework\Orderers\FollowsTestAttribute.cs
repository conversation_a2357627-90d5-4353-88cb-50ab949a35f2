﻿namespace PharmaLex.VigiLit.Test.Framework.Orderers;

/// <summary>
/// Attribute for specifying the test method that should be executed before this test.
/// </summary>
/// <remarks>
/// To run chained tests apply this attribute to the test method specifying the name of the test that should be run before it.
/// <code>
/// [TestCaseOrderer(
///     ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.FollowsTestOrderer",
///     ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
/// public class MyTests 
/// {
///    public UserRepositoryTests(DatabaseFixture&lt;MyDbContext&gt; fixture)
///    {
///        _context = fixture.Context;
///        _context.Database.BeginTransaction();
///    }
///
///    [Fact, FollowsTest("")]
///    public async Task AddUser_Creates_user_in_database_without_error()
///    {
///    }
///
///    [Fact, FollowsTest(nameof(AddUser_Creates_user_in_database_without_error))]
///    public async Task UpdateUser_updates_user_without_error()
///    {
///    }
///
///    [Fact, FollowsTest(nameof(UpdateUser_updates_user_without_error))]
///    public async Task ReadUser_reads_updated_user()
///    {
///    }
/// }
/// </code>
/// This is similar to <seealso cref="PriorityOrderer" /> except that it is easier to maintain if addition steps are required in between methods and
/// the use of <see langword="nameof" /> shows more clearly which test will be executed prior to the test.
/// </remarks>
/// <seealso cref="System.Attribute" />
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class FollowsTestAttribute : Attribute
{
    /// <summary>
    /// Gets the previous test method.
    /// </summary>
    /// <value>
    /// The previous test method.
    /// </value>
    public string PreviousTestMethod { get; }

    public FollowsTestAttribute(string previousTestMethod)
    {
        PreviousTestMethod = previousTestMethod;
    }
}