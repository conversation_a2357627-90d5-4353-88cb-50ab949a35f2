﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230411b : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceState_Id_ClassificationCategoryId_SubstanceId_ReferenceId_ClassifierId",
                table: "ReferenceClassifications",
                columns: new[] { "ReferenceState", "Id", "ClassificationCategoryId", "SubstanceId", "ReferenceId", "ClassifierId" });

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId_Id_ClassificationCategoryId_SubstanceId_ReferenceId_ClassifierId",
                table: "ReferenceClassifications",
                columns: new[] { "ReferenceState", "PreAssessorId", "Id", "ClassificationCategoryId", "SubstanceId", "ReferenceId", "ClassifierId" });

            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferenceClassificationsHistory_ReferenceId_SubstanceId_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] ([ReferenceId],[SubstanceId],[PeriodEnd],[PeriodStart])");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceState_Id_ClassificationCategoryId_SubstanceId_ReferenceId_ClassifierId",
                table: "ReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId_Id_ClassificationCategoryId_SubstanceId_ReferenceId_ClassifierId",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications",
                columns: new[] { "ReferenceState", "PreAssessorId" });

            migrationBuilder.Sql("DROP INDEX [IX_ReferenceClassificationsHistory_ReferenceId_SubstanceId_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] WITH ( ONLINE = OFF )");
        }
    }
}
