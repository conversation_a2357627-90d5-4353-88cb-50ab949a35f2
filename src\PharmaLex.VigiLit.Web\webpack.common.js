const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const RemoveEmptyScriptsPlugin = require('webpack-remove-empty-scripts');
const ESLintPlugin = require('eslint-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
    entry: {
        vigiLitCss: '/src/scss/index.scss',
        modal: '/src/js/modal/modal.js',
        downloadFile: '/src/js/utilities/download-file.js',
        localStorageValidator: '/src/js/utilities/local-storage-validator.js',
        filteredTable: '/src/js/vue/v3/filtered-table-v3.js'
    },
    output: {
        path: path.resolve(__dirname, 'wwwroot/dist'),
        publicPath: '/dist/',
        assetModuleFilename: 'assets/[name][ext][query]',
        filename: 'js/[name].js',
    },
    plugins: [
        new CleanWebpackPlugin(),
        new RemoveEmptyScriptsPlugin(),
        new MiniCssExtractPlugin({
            filename: "css/[name].css",
        }),
        new VueLoaderPlugin(),
        new ESLintPlugin(),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: path.resolve(__dirname, 'node_modules/diff/dist/diff.js'),
                    to: path.resolve(__dirname, 'wwwroot/lib/jsdiff/diff.js') 
                },
                {
                    from: path.resolve(__dirname, 'node_modules/diff/dist/diff.min.js'),
                    to: path.resolve(__dirname, 'wwwroot/lib/jsdiff/diff.min.js') 
                }
            ],
        }),
    ],
    module: {
        rules: [
            {
                test: /\.js$/, exclude: /(node_modules)/
            },
            {
                test: /\.vue$/,
                use: ['vue-loader']
            },
            {
                test: /\.(?:ico|gif|png|jpg|jpeg)$/i,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 5 * 1024
                    }
                }
            },
            {
                test: /\.(woff(2)?|eot|ttf|otf|svg|)$/,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 5 * 1024
                    }
                }
            },
            {
                test: /\.(scss|css)$/,
                use: [
                    "vue-style-loader",
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true,
                        },
                    },
                    {
                        loader: "postcss-loader",
                        options: {
                            postcssOptions: {
                                config: path.resolve(__dirname, 'postcss.config.js'),
                            },
                        },
                    },
                    'sass-loader',
                ],
            },
        ],
    },
    optimization: {
        minimize: true,
        minimizer: [new TerserPlugin({ extractComments: false })],
        splitChunks: {
            chunks: 'all',
            name:'vendors' 
        },
    },
    stats: 'minimal'
};
