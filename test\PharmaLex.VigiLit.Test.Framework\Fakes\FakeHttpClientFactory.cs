﻿namespace PharmaLex.VigiLit.Test.Framework.Fakes;

using System.Net.Http;

/// <summary>
/// A fake implementation of the <see cref="IHttpClientFactory"/> interface for testing purposes.
/// </summary>
public class FakeHttpClientFactory : IHttpClientFactory
{
    private readonly HttpMessageHandler _httpMessageHandler;

    /// <summary>
    /// Initializes a new instance of the <see cref="FakeHttpClientFactory"/> class.
    /// </summary>
    /// <param name="httpMessageHandler">The HTTP message handler to be used by the created <see cref="HttpClient"/> instances.</param>
    public FakeHttpClientFactory(HttpMessageHandler httpMessageHandler)
    {
        _httpMessageHandler = httpMessageHandler;
    }

    /// <summary>
    /// Creates a new <see cref="HttpClient"/> instance using the provided message handler.
    /// </summary>
    /// <param name="name">The logical name of the client. This parameter is not used in this implementation.</param>
    /// <returns>A new <see cref="HttpClient"/> instance.</returns>
    public HttpClient CreateClient(string name)
    {
        return new HttpClient(_httpMessageHandler);
    }

    /// <summary>
    /// Creates a new <see cref="HttpClient"/> instance using the provided message handler.
    /// </summary>
    /// <returns>A new <see cref="HttpClient"/> instance.</returns>
    public HttpClient Create()
    {
        return new HttpClient(_httpMessageHandler);
    }
}