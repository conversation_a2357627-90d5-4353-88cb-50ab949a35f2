﻿using Microsoft.Extensions.Configuration;
using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using Microsoft.CodeAnalysis.Scripting.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class ReferenceMatcherTests
{
    private readonly IReferenceMatcher _referenceMatcher;
    private readonly Mock<ICSharpScriptFactory> _mockCSharpScriptFactory = new();
    private readonly Mock<IAbstractTextSearcher> _abstractTextSearcher = new();
    private readonly Mock<IExpressionBuilder> _expressionBuilder = new();
    private readonly Mock<ILogger<ReferenceMatcher>> _mockLogger = new();
    private readonly Mock<IConfiguration> _mockConfiguration = new();
    private readonly Reference _testReference;
    private readonly IReadOnlyCollection<string> _noJournalTitles = new List<string>();


    public ReferenceMatcherTests()
    {

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpJournalMatchThreshold", "90" }, })
            .Build();

        _referenceMatcher = new ReferenceMatcher(_abstractTextSearcher.Object, _mockCSharpScriptFactory.Object, _expressionBuilder.Object, _mockLogger.Object, configuration);
        _testReference = new Reference
        {
            SourceId = "123",
            Abstract =
                "Oxytocin is a hormone,and Piperacillin Oxytocin is an antibiotic. Amoxicillin is also mentioned.",
            JournalTitle = "Eye of Newt Weekly",
        };
    }

    [Theory]
    [InlineData("Amoxicillin", "IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData("Amox*", "IsWordInAbstract(\"Amox*\")")]
    [InlineData(@"""Amoxicillin""", @"IsWordInAbstract(""Amoxicillin"")")]
    [InlineData(@"""Amox*""", "IsWordInAbstract(\"Amox*\")")]
    [InlineData("Piperacillin Oxytocin", "IsWordInAbstract(\"Piperacillin Oxytocin\")")]
    [InlineData("Piperacillin Oxyt*", "IsWordInAbstract(\"Piperacillin Oxyt*\")")]
    [InlineData(@"""Piperacillin Oxytocin""", "IsWordInAbstract(\"Piperacillin Oxytocin\")")]
    [InlineData(@"""Piperacillin Oxyt*""", "IsWordInAbstract(\"Piperacillin Oxyt*\")")]
    [InlineData("Piperacillin AND Amoxicillin", "IsWordInAbstract(\"Piperacillin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData("Piperacillin OR Amoxicillin", "IsWordInAbstract(\"Piperacillin\")||IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData(@"""Piperacillin"" AND Amoxicillin", "IsWordInAbstract(\"Piperacillin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData(@"""Piperacillin"" OR Amoxicillin", "IsWordInAbstract(\"Piperacillin\")||IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData(@"""Piperacillin Oxytocin"" AND Amoxicillin", "IsWordInAbstract(\"Piperacillin Oxytocin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData(@"Piperacillin Oxytocin AND Amoxicillin", "IsWordInAbstract(\"Piperacillin Oxytocin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    [InlineData(@"(Piperacillin)", "(IsWordInAbstract(\"Piperacillin\"))")]
    [InlineData(@"(Piperacillin) AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin""))&&IsWordInAbstract(""Amoxicillin"")")]
    [InlineData(@"(Piperacillin AND Amoxicillin)", @"(IsWordInAbstract(""Piperacillin"")&&IsWordInAbstract(""Amoxicillin""))")]
    [InlineData(@"(Piperacillin Oxytocin) AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin Oxytocin""))&&IsWordInAbstract(""Amoxicillin"")")]
    [InlineData(@"(""Piperacillin Oxytocin"") AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin Oxytocin""))&&IsWordInAbstract(""Amoxicillin"")")]
    public async Task ReferenceMatcher_MatchFound_ReturnsTrue(string searchTerm, string code)
    {
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);
        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            code,
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, _noJournalTitles);

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData("Amoxicilline", "IsWordInAbstract(\"Amoxicilline\")")]
    [InlineData("Amx*", "IsWordInAbstract(\"Amx*\")")]
    [InlineData(@"""Amxicillin""", "IsWordInAbstract(\"Amxicillin\")")]
    [InlineData(@"""Ax*""", "IsWordInAbstract(\"Ax*\")")]
    public async Task ReferenceMatcher_MatchNotFound_ReturnsFalse(string searchTerm, string code)
    {
        //_abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(false);

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            code,
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);


        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, _noJournalTitles);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFoundWithNoJournal_ReturnsTrue()
    {
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, _noJournalTitles);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFoundWithJournal_ReturnsTrue()
    {
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "Eye of Newt Weekly",
        };


        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFoundWithDifferentJournal_ReturnsFalse()
    {
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "Toe of frog Magazine",
        };


        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.False(result);
    }


    [Fact]
    public async Task ReferenceMatcher_MatchingJournalWithDifferentCase_ReturnsTrue()
    {
        // Arrange
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "eYe Of NeWt WEeKlY",
        };

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchingJournalWithDifferentCaseAndExtraCharacters_ReturnsTrue()
    {
        // Arrange
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "eYe Of NeWt - WEeKlY",
        };

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_NonMatchingJournal_ReturnsFalse()
    {
        // Arrange
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "No match whatsoever",
        };

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ReferenceMatcher_PartiallyMatchingJournal_ReturnsFalse()
    {
        // Arrange
        _abstractTextSearcher.Setup(x => x.IsWordInAbstract(It.IsAny<string>())).Returns(true);

        var searchTerm = "(Piperacillin)";

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);
        var script = CSharpScript.Create<bool>(
            "(IsWordInAbstract(\"Piperacillin\"))",
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _mockCSharpScriptFactory.Setup(x => x.Create(It.IsAny<string>(), It.IsAny<ScriptOptions?>(), It.IsAny<Type?>(), It.IsAny<InteractiveAssemblyLoader?>()))
            .Returns(script);

        var journals = new List<string>()
        {
            "eye of newt",
        };

        // Act
        var result = await _referenceMatcher.Matches(_testReference, searchTerm, journals);

        // Assert
        Assert.False(result);
    }
}