﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Moq;
using PharmaLex.VigiLit.Web.Controllers.CustomTypeFilters;
using System.Security.Claims;

namespace PharmaLex.VigiLit.Web.Unit.Tests.Controllers.CustomTypeFilters;

public class AuthorizeOnAnyOnePolicyFilterTests
{
    [Fact]
    public async Task OnAuthorizationAsync_UserBelongsToOnePolicy_ReturnsWithoutSettingResult()
    {
        // Arrange
        var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[] { new Claim(ClaimTypes.Name, "testuser") }));
        var authorizationServiceMock = new Mock<IAuthorizationService>();

        // Mocking the extension method AuthorizeAsync
        authorizationServiceMock.Setup(s => s.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), It.IsAny<object>(), "Policy1"))
            .ReturnsAsync(AuthorizationResult.Success());

        var httpContext = new DefaultHttpContext { User = user };
        var actionContext = new ActionContext(httpContext, new RouteData(), new Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor());
        var context = new AuthorizationFilterContext(actionContext, Array.Empty<IFilterMetadata>());

        var filter = new AuthorizeOnAnyOnePolicyFilter("Policy1,Policy2", authorizationServiceMock.Object);

        // Act
        await filter.OnAuthorizationAsync(context);

        // Assert
        Assert.Null(context.Result);
    }

    [Fact]
    public async Task OnAuthorizationAsync_UserDoesNotBelongToAnyPolicy_SetsForbidResult()
    {
        // Arrange
        var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[] { new Claim(ClaimTypes.Name, "testuser") }));
        var authorizationServiceMock = new Mock<IAuthorizationService>();
        authorizationServiceMock
            .Setup(s => s.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), It.IsAny<object>(), "Policy1"))
            .ReturnsAsync(AuthorizationResult.Failed());
        authorizationServiceMock
            .Setup(s => s.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), It.IsAny<object>(), "Policy2"))
            .ReturnsAsync(AuthorizationResult.Failed());

        var httpContext = new DefaultHttpContext { User = user };
        var actionContext = new ActionContext(httpContext, new RouteData(), new Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor());
        var context = new AuthorizationFilterContext(actionContext, Array.Empty<IFilterMetadata>());

        var filter = new AuthorizeOnAnyOnePolicyFilter("Policy1,Policy2", authorizationServiceMock.Object);

        // Act
        await filter.OnAuthorizationAsync(context);

        // Assert
        Assert.IsType<ForbidResult>(context.Result);
    }
}
