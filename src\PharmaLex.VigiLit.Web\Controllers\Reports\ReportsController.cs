﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers.Reports;

[Authorize]
[Route("[controller]")]
public class ReportsController : BaseController
{
    private readonly IUserRepository<User> _userRepository;
    private readonly IReportService _reportService;

    public ReportsController(
        IUserRepository<User> userRepository,
        IReportService reportService,
        IUserSessionService userSessionService,
        IConfiguration configuration) : base(userSessionService, configuration)

    {
        _userRepository = userRepository;
        _reportService = reportService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var model = await _reportService.GetReportsPageModel(await _userRepository.GetForSecurity(CurrentUserId));

        return View(model);
    }

    [HttpGet("{reportId}")]
    public async Task<IActionResult> ViewReport(int reportId)
    {
        var report = await _reportService.GetReport(reportId);

        return RedirectToAction("Index", report.ControllerName);
    }
}