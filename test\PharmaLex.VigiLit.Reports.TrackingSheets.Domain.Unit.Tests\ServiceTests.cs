﻿using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.Models.Document.TrackingSheet;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System.Text;
using Xunit;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests;

public class ServiceTests
{
    private readonly IService _service;

    private readonly Mock<IReportingCompanyRepository> _companyRepository = new();
    private readonly Mock<ITrackingSheetPdfGenerator> _trackingSheetPdfGenerator = new();
    private readonly Mock<IRepository> _trackingSheetRepository = new();
    private readonly Mock<ITrackingSheetDocumentService> _trackingSheetDocumentService = new();
    private readonly Mock<IUserRepository<User>> _userRepository = new();

    public ServiceTests()
    {
        _service = new Service(
            new NullLoggerFactory(),
            _companyRepository.Object,
            _trackingSheetPdfGenerator.Object,
            _trackingSheetRepository.Object,
            _trackingSheetDocumentService.Object,
            _userRepository.Object);
    }

    [Fact]
    public async Task GetTrackingSheetsPageModel_Returns_Model_ForInternalUser()
    {
        // Arrange
        var user = GetInternalUser();

        var companyItems = new List<CompanyItemModel>
        {
            new() {Id=1, Name="Company1"},
            new() {Id=2, Name="Company2"},
        };

        _companyRepository
            .Setup(x => x.GetCompanyItems(user))
            .ReturnsAsync(companyItems);

        // Act
        var model = await _service.GetTrackingSheetsPageModel(user);

        // Assert
        Assert.NotNull(model);
        Assert.Equal(2, model.Companies.Count());
        Assert.NotEmpty(model.Years);
        Assert.Equal(model.Years.Max(), model.SelectedYear);
        Assert.Empty(model.Cards);
    }

    [Fact]
    public async Task GetTrackingSheetsPageModel_Returns_Model_ForExternalUser()
    {
        // Arrange
        var user = GetExternalUser();

        var companyItems = new List<CompanyItemModel>();

        _companyRepository
            .Setup(x => x.GetCompanyItems(user))
            .ReturnsAsync(companyItems);

        // Act
        var model = await _service.GetTrackingSheetsPageModel(user);

        // Assert
        Assert.NotNull(model);
        Assert.Empty(model.Companies);
        Assert.NotEmpty(model.Years);
        Assert.Equal(model.Years.Max(), model.SelectedYear);
        Assert.InRange(model.Cards.Count(), 52, 53);
    }

    [Fact]
    public async Task GetCards_InvalidYear_Throws_ArgumentException()
    {
        // Arrange
        var user = GetInternalUser();

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _service.GetCards(user, 0, 2023));
    }

    [Fact]
    public async Task GetCards_Returns_NoCards_ForInternalUser_WhenNoCompanyIsSelected()
    {
        // Arrange
        var user = GetInternalUser();

        // Act
        var cards = await _service.GetCards(user, 0, 2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Empty(cards);
    }

    [Fact]
    public async Task GetCards_Returns_Cards_ForInternalUser_WhenCompanyIsSelected()
    {
        // Arrange
        var user = GetInternalUser();

        // Act
        var cards = await _service.GetCards(user, 1, 2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Equal(52, cards.Count());

        var lastCard = cards.Last();

        Assert.NotNull(lastCard);
        Assert.Equal(1, lastCard.WeekNumber);
        Assert.NotNull(lastCard.WeekStart);
        Assert.NotEqual("", lastCard.WeekStart);
        Assert.NotNull(lastCard.WeekEnd);
        Assert.NotEqual("", lastCard.WeekEnd);
    }

    [Fact]
    public async Task GetCards_Returns_Cards_ForExternalUser_WithActiveCompany()
    {
        // Arrange
        var user = GetExternalUser();

        // Act
        var cards = await _service.GetCards(user, 0, 2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Equal(52, cards.Count());

        var lastCard = cards.Last();

        Assert.NotNull(lastCard);
        Assert.Equal(1, lastCard.WeekNumber);
        Assert.NotNull(lastCard.WeekStart);
        Assert.NotEqual("", lastCard.WeekStart);
        Assert.NotNull(lastCard.WeekEnd);
        Assert.NotEqual("", lastCard.WeekEnd);
    }

    [Fact]
    public async Task GetCards_Returns_NoCards_ForExternalUser_WithNoActiveCompany()
    {
        // Arrange
        var user = GetExternalUser();
        user.CompanyUser.Active = false;

        // Act
        var cards = await _service.GetCards(user, 0, 2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Empty(cards);
    }

    [Theory]
    [InlineData(2024, 52)]
    public async Task GetCards_Returns_CorrectNumberOfCards_ForYear(int year, int expectedWeeks)
    {
        // Arrange
        var user = GetInternalUser();

        // Act
        var cards = await _service.GetCards(user, 1, year);

        // Assert
        Assert.NotNull(cards);
        Assert.Equal(expectedWeeks, cards.Count());
    }

    [Fact]
    public async Task Download_Returns_DownloadFile()
    {
        // Arrange
        TrackingSheet trackingSheet = new()
        {
            CompanyId = 2,
            Year = 2024,
            Week = 3,
            FileName = "MyFile.pdf"
        };

        byte[] bytes = Encoding.ASCII.GetBytes("my stream content");
        using var stream = new MemoryStream(bytes);

        _trackingSheetRepository
            .Setup(x => x.GetById(1))
            .ReturnsAsync(trackingSheet);

        _trackingSheetDocumentService
           .Setup(x => x.OpenRead(It.IsAny<TrackingSheetDocumentDescriptor>(), default))
           .ReturnsAsync(stream);

        // Act
        var downloadFile = await _service.Download(1);

        // Assert
        Assert.NotNull(downloadFile);
        Assert.Equal("MyFile.pdf", downloadFile.FileName);
        Assert.Equal("application/pdf", downloadFile.ContentType);
        Assert.True(bytes.SequenceEqual(downloadFile.Bytes));
    }

    [Fact]
    public async Task CreateTrackingSheetsForLastWeek_CreatesTrackingSheet_ForEachCompany()
    {
        // Arrange
        var company1 = new FakeCompany(1);
        var company2 = new FakeCompany(2);
        var company3 = new FakeCompany(3);

        var companies = new List<Company>
        {
            company1,
            company2,
            company3
        };

        TrackingSheetPdfModel trackingSheetPdfModel = new();

        byte[] bytes = Array.Empty<byte>();

        _companyRepository
            .Setup(x => x.GetActiveCompanies())
            .ReturnsAsync(companies);

        _companyRepository.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(company1);
        _companyRepository.Setup(x => x.GetByIdAsync(2)).ReturnsAsync(company2);
        _companyRepository.Setup(x => x.GetByIdAsync(3)).ReturnsAsync(company3);

        _trackingSheetRepository.Setup(x => x.GetTrackingSheetPdfModel(company1, 2024, 3)).ReturnsAsync(trackingSheetPdfModel);
        _trackingSheetRepository.Setup(x => x.GetTrackingSheetPdfModel(company2, 2024, 3)).ReturnsAsync(trackingSheetPdfModel);
        _trackingSheetRepository.Setup(x => x.GetTrackingSheetPdfModel(company3, 2024, 3)).ReturnsAsync(trackingSheetPdfModel);

        _trackingSheetPdfGenerator
            .Setup(x => x.GetPdfBytes(It.IsAny<TrackingSheetPdfModel>()))
            .Returns(bytes);

        _trackingSheetDocumentService
           .Setup(x => x.Exists(It.IsAny<TrackingSheetDocumentDescriptor>(), default))
           .ReturnsAsync(false);

        _trackingSheetDocumentService
           .Setup(x => x.Create(It.IsAny<TrackingSheetDocumentDescriptor>(), It.IsAny<Stream>(), default));

        _trackingSheetRepository.Setup(x => x.Add(It.IsAny<TrackingSheet>()));
        _trackingSheetRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _service.CreateTrackingSheetsForLastWeek();

        // Assert
        _companyRepository.Verify(x => x.GetActiveCompanies(), Times.Once());

        _trackingSheetRepository.Verify(x => x.Add(It.Is<TrackingSheet>(x => x.CompanyId == 1)), Times.Once());
        _trackingSheetRepository.Verify(x => x.Add(It.Is<TrackingSheet>(x => x.CompanyId == 2)), Times.Once());
        _trackingSheetRepository.Verify(x => x.Add(It.Is<TrackingSheet>(x => x.CompanyId == 3)), Times.Once());

        _trackingSheetRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(3));
    }

    [Fact]
    public async Task Create_CreatesTrackingSheet()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 3,
            Year = 2024
        };

        Company company = new FakeCompany(2);

        TrackingSheetPdfModel trackingSheetPdfModel = new();

        byte[] bytes = Array.Empty<byte>();

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))
            .ReturnsAsync(company);

        _trackingSheetRepository
            .Setup(x => x.GetTrackingSheetPdfModel(company, 2024, 3))
            .ReturnsAsync(trackingSheetPdfModel);

        _trackingSheetPdfGenerator
            .Setup(x => x.GetPdfBytes(trackingSheetPdfModel))
            .Returns(bytes);

        _trackingSheetDocumentService
           .Setup(x => x.Exists(It.IsAny<TrackingSheetDocumentDescriptor>(), default))
           .ReturnsAsync(false);

        _trackingSheetDocumentService
           .Setup(x => x.Create(It.IsAny<TrackingSheetDocumentDescriptor>(), It.IsAny<Stream>(), default));

        _trackingSheetRepository.Setup(x => x.Add(It.IsAny<TrackingSheet>()));
        _trackingSheetRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _service.Create(request);

        // Assert
        _companyRepository.Verify(x => x.GetByIdAsync(2), Times.Once());
        _trackingSheetRepository.Verify(x => x.Exists(2, 2024, 3), Times.Once());
        _trackingSheetRepository.Verify(x => x.GetTrackingSheetPdfModel(company, 2024, 3), Times.Once());
        _trackingSheetPdfGenerator.Verify(x => x.GetPdfBytes(trackingSheetPdfModel), Times.Once());

        _trackingSheetDocumentService.Verify(x => x.Exists(It.IsAny<TrackingSheetDocumentDescriptor>(), default), Times.Once());
        _trackingSheetDocumentService.Verify(x => x.Create(It.IsAny<TrackingSheetDocumentDescriptor>(), It.IsAny<Stream>(), default), Times.Once());

        _trackingSheetRepository.Verify(x => x.Add(It.Is<TrackingSheet>(x =>
            x.CompanyId == 2
            && x.Year == 2024
            && x.Week == 3
            && x.FileName == "TrackingSheet_2024_3.pdf"
            && x.FileSize == 0
        )), Times.Once());

        _trackingSheetRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task Create_DeletesDocument_IfExists_Then_CreatesTrackingSheet()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 3,
            Year = 2024
        };

        Company company = new FakeCompany(2);

        TrackingSheetPdfModel trackingSheetPdfModel = new();

        byte[] bytes = Array.Empty<byte>();

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))
            .ReturnsAsync(company);

        _trackingSheetRepository
            .Setup(x => x.GetTrackingSheetPdfModel(company, 2024, 3))
            .ReturnsAsync(trackingSheetPdfModel);

        _trackingSheetPdfGenerator
            .Setup(x => x.GetPdfBytes(trackingSheetPdfModel))
            .Returns(bytes);

        _trackingSheetDocumentService
           .Setup(x => x.Exists(It.IsAny<TrackingSheetDocumentDescriptor>(), default))
           .ReturnsAsync(true);

        _trackingSheetDocumentService
           .Setup(x => x.Delete(It.IsAny<TrackingSheetDocumentDescriptor>(), default));

        _trackingSheetDocumentService
           .Setup(x => x.Create(It.IsAny<TrackingSheetDocumentDescriptor>(), It.IsAny<Stream>(), default));

        _trackingSheetRepository.Setup(x => x.Add(It.IsAny<TrackingSheet>()));
        _trackingSheetRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _service.Create(request);

        // Assert
        _companyRepository.Verify(x => x.GetByIdAsync(2), Times.Once());
        _trackingSheetRepository.Verify(x => x.Exists(2, 2024, 3), Times.Once());
        _trackingSheetRepository.Verify(x => x.GetTrackingSheetPdfModel(company, 2024, 3), Times.Once());
        _trackingSheetPdfGenerator.Verify(x => x.GetPdfBytes(trackingSheetPdfModel), Times.Once());

        _trackingSheetDocumentService.Verify(x => x.Exists(It.IsAny<TrackingSheetDocumentDescriptor>(), default), Times.Once());
        _trackingSheetDocumentService.Verify(x => x.Delete(It.IsAny<TrackingSheetDocumentDescriptor>(), default), Times.Once());
        _trackingSheetDocumentService.Verify(x => x.Create(It.IsAny<TrackingSheetDocumentDescriptor>(), It.IsAny<Stream>(), default), Times.Once());

        _trackingSheetRepository.Verify(x => x.Add(It.Is<TrackingSheet>(x =>
            x.CompanyId == 2
            && x.Year == 2024
            && x.Week == 3
            && x.FileName == "TrackingSheet_2024_3.pdf"
            && x.FileSize == 0
        )), Times.Once());

        _trackingSheetRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task Create_InvalidYear_Throws_ArgumentException()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 3,
            Year = 2023
        };

        Company company = new FakeCompany(2);

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))
            .ReturnsAsync(company);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _service.Create(request));
    }

    [Fact]
    public async Task Create_InvalidWeek_Throws_ArgumentException()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 54,
            Year = 2024
        };

        Company company = new FakeCompany(2);

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))
            .ReturnsAsync(company);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _service.Create(request));
    }

    [Fact]
    public async Task Create_Throws_ArgumentException_If_Company_Is_Null()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 3,
            Year = 2024
        };

        Company? company = null;

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))!
            .ReturnsAsync(company);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _service.Create(request));
    }

    [Fact]
    public async Task Create_Returns_If_TrackingSheet_AlreadyExists()
    {
        // Arrange
        CreateRequest request = new()
        {
            CompanyId = 2,
            Week = 3,
            Year = 2024
        };

        Company company = new FakeCompany(2);

        _companyRepository
            .Setup(x => x.GetByIdAsync(2))
            .ReturnsAsync(company);

        _trackingSheetRepository
            .Setup(x => x.Exists(2, 2024, 3))
            .ReturnsAsync(true);

        // Act
        await _service.Create(request);

        // Assert
        _companyRepository.Verify(x => x.GetByIdAsync(2), Times.Once());
        _trackingSheetRepository.Verify(x => x.Exists(2, 2024, 3), Times.Once());
        _trackingSheetRepository.Verify(x => x.Add(It.IsAny<TrackingSheet>()), Times.Never());
        _trackingSheetRepository.Verify(x => x.SaveChangesAsync(), Times.Never());
    }

    private static User GetInternalUser()
    {
        return new User("FirstName", "LastName", "EmailAddress");
    }

    private static User GetExternalUser()
    {
        var user = new User("FirstName", "LastName", "EmailAddress");
        user.AddClaim(new FakeClaim(6, "ClientResearcher"));
        user.CompanyUser = new CompanyUser() { Active = true, CompanyId = 1 };
        return user;
    }
}
