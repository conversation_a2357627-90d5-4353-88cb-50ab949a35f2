﻿@using AppSettingsHelper = PharmaLex.Core.Configuration.AppSettingsHelper
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Configuration
@using PharmaLex.Core.Configuration
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.UserManagement
@using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard
@using PharmaLex.VigiLit.Web.Helpers;

@inject IConfiguration Configuration
@inject IAuthorizationService AuthorizationService
@inject AppSettingsHelper AppSettings

@model DashboardModel

@{
    ViewData["Title"] = "Home";
}

<div id="dashboard" v-cloak>
    @Html.AntiForgeryToken()

    <div id="import-dashboard-container" v-if="data.displayImportsDashboard" :class="[{'wide':!data.displayEmailDashboard, 'narrow':data.displayEmailDashboard, 'empty':data.imports.length === 0}]">
        <div id="import-dashboard">
            <h2 class="dashboard">Imports</h2>
            <div class="content">
                <div class="cards" v-if="data.imports.length > 0">
                    <div v-for="i in data.imports" :class="['card', {'selected':data.selectedImportId === i.id}]">
                        <div class="info">
                            <div class="text">
                                <div :class="['import-type', {'scheduled':i.importType==='Scheduled', 'adhoc':i.importType==='Ad-Hoc'}]">{{i.importType}}</div>
                                <div class="import-date">{{i.importDate}}</div>
                                <div class="import-status">{{i.importDashboardStatusType}}</div>
                            </div>
                            <div class="number" v-if="!(i.importDashboardStatusType === 'Signed')">
                                <div class="todo-count-label">To-Do</div>
                                <div class="todo-count-number">{{ formatNumber(i.todoCount)}}</div>
                            </div>
                        </div>
                        <div class="buttons">
                            <div class="left">
                                <button v-if="data.selectedImportId !== i.id" @@click.stop.prevent="select(i)" :disabled="inProgress">Select</button>
                                <button v-else @@click.stop.prevent="deselect(i)" :disabled="inProgress" class="positive">Deselect</button>
                            </div>
                            <div class="right">
                                <a class="button secondary margin-right-xs" :href="`/ImportDashboardDetails/${i.id}?page=1`">Details</a>
                                <button :disabled="inProgress || !(i.importDashboardStatusType === 'Signed')" @@click.stop.prevent="archive(i)" class="secondary">Archive</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="empty" v-else>
                    <p>There are no imports to display.</p>
                </div>
            </div>
        </div>
    </div>

    <div id="email-dashboard-container" v-if="data.displayEmailDashboard">
        <div id="email-dashboard">
            <h2 class="dashboard">Email Suppressions</h2>
            <div class="content">
                <div class="cards">
                    <div id="blocked" class="card">
			            <div class="text">BLOCKED</div>
                        <div class="value" :class="{'email-block-foreground': data.total.blockCount>0}">{{data.total.blockCount}}</div>
                    </div>
                    <div id="blocked" class="card">
			            <div class="text">BOUNCED</div>
                        <div class="value" :class="{'email-bounce-foreground': data.total.bounceCount>0}">{{data.total.bounceCount}}</div>
		            </div>
                    <div id="blocked" class="card">
			            <div class="text">SPAM</div>
                        <div class="value" :class="{'email-spam-foreground': data.total.spamCount>0}">{{data.total.spamCount}}</div>
		            </div>
                </div>
                <h3 class="text-color">Suppressions By Company</h3>
	            <div id="table-container">
                    <filtered-table :items="companySummary" :columns="columns" :link="link"></filtered-table>
                </div>
            </div>
        </div>

        @if ((await AuthorizationService.AuthorizeAsync(User, Policies.SuperAdmin)).Succeeded)
        {
            <div id="build-info">
                <h2 class="dashboard">Build Info</h2>
                <div class="content">
                    <p><b>Version:</b> @AppSettings.Version</p>
                    <p><b>Build:</b> @AppSettings.BuildInfo</p>
                </div>
            </div>
        }

    </div>

</div>

@section Scripts {
	<script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var companyModel = @Html.Raw(AntiXss.ToJson(Model.CompanySummary));

        if (companyModel) {
            companyModel.forEach(x => {
                x.blockCount = x.suppressionSummary.blockCount.toString();
                x.bounceCount = x.suppressionSummary.bounceCount.toString();
                x.spamCount = x.suppressionSummary.spamCount.toString();
            });
        }

		var pageConfig = {
            appElement: "#dashboard",
			data: function () {
				return {
                    link: '/Companies/edit/',
                    data: @Html.Raw(AntiXss.ToJson(Model)),
					companySummary: companyModel,
                    inProgress: false,
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'companyName',
                                sortKey: 'companyName',
                                header: 'Company',
                                edit: {},
                                type: 'text',
                                style: 'width: 42%;'
                            },
                            {
                                dataKey: 'blockCount',
                                sortKey: 'blockCount',
                                header: 'Blocked',
                                edit: {},
                                type: 'text',
                                style: 'text-align: center;'
                            },                            
                            {
                                dataKey: 'bounceCount',
                                sortKey: 'bounceCount',
                                header: 'Bounced',
                                edit: {},
                                type: 'text',
                                style: 'text-align: center;'
                            },                            
                            {
                                dataKey: 'spamCount',
                                sortKey: 'spamCount',
                                header: 'Spam',
                                edit: {},
                                type: 'text',
                                style: 'text-align: center;'
                            }
                        ]
                    },
				}
			},
            methods: {
                archive: function (i) {
                    this.inProgress = true;
                    fetch(`/Archive/${i.id}`, {
                        method: 'GET',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            this.data.imports.splice(this.data.imports.indexOf(i), 1);
                            if (this.data.selectedImportId === i.id) {
                                this.data.selectedImportId = 0;
                            }
                            plx.toast.show('Archived.', 2, 'confirm', null, 2500);
                        }
                        else {
                            plx.toast.show('Failed.', 2, 'failed', null, 5000);
                        }
                        this.inProgress = false;
                    });
                },
                select: function (i) {
                    this.inProgress = true;
                    fetch(`/Select/${i.id}`, {
                        method: 'GET',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            this.data.selectedImportId = i.id;
                        }
                        else {
                            plx.toast.show('Failed.', 2, 'failed', null, 5000);
                        }
                        this.inProgress = false;
                    });
                },
                deselect: function (i) {
                    this.inProgress = true;
                    fetch(`/Deselect/${i.id}`, {
                        method: 'GET',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            this.data.selectedImportId = 0;
                        }
                        else {
                            plx.toast.show('Failed.', 2, 'failed', null, 5000);
                        }
                        this.inProgress = false;
                    });
                },
                formatNumber(number) {
                    return new Intl.NumberFormat({
                        maximumFractionDigits: 0,
                    }).format(number);

                }
            },
            created() {
                //console.log(this.data);
            }
		}
	</script>
}

@section VueComponentScripts{
	<partial name="Components/Vue3/FilteredTable" />
}
