﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class UsersController : BaseController
{
    private readonly IUserRepository _userRepository;
    private readonly IUserService _userService;
    private readonly IUserSearchService<User> _userSearchService;
    private readonly IMapper _mapper;
    private readonly ClaimsPrincipal _claimsPrincipal;

    public UsersController(
        IUserRepository userRepository,
        IUserService userService,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor,
        IUserSessionService userSessionService,
        IConfiguration configuration, 
        IUserSearchService<User> userSearchService) : base(userSessionService, configuration)
    {
        _userRepository = userRepository;
        _userService = userService;
        _mapper = mapper;
        _userSearchService = userSearchService;
        _claimsPrincipal = httpContextAccessor.HttpContext.User;
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> Index()
    {
        var model = new ManageUsersViewModel
        {
            Users = await _userService.GetAllAsync(_claimsPrincipal),
            Claims = await _userService.GetClaimsAsync(_claimsPrincipal),
        };
        return View(model);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> Find(string term)
    {
        var users = await _userSearchService.Find(term);
        return this.Json(users);
    }

    [HttpGet("create")]
    public IActionResult Create()
    {
        return View("Edit", new UserFullModel());
    }

    [HttpPost("create")]
    public async Task<IActionResult> Create(CreateUserModel userModel)
    {
        if (ModelState.IsValid)
        {
            await _userService.Create(userModel.GivenName, userModel.FamilyName, userModel.Email, userModel.ClaimIds);
            return RedirectToAction("Index");
        }

        userModel.Initialised = true;
        return View("Edit", userModel);
    }

    [HttpGet("edit/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        var user = await _userRepository.GetById(id);
        var userModel = _mapper.Map<UserFullModel>(user);

        return View(userModel);
    }

    [HttpPost("edit/{id}")]
    public async Task<IActionResult> Edit([FromBody] UserFullModel userModel)
    {
        if (ModelState.IsValid)
        {
            await _userService.Update(_claimsPrincipal, userModel.Id, userModel.GivenName, userModel.FamilyName, userModel.Email, userModel.ClaimIds);
            return RedirectToAction("Index");
        }

        return View(userModel);
    }
}