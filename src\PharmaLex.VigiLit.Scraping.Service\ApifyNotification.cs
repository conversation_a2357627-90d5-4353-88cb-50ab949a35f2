using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service;

public class ApifyNotification : IApifyNotification
{
    private readonly IApifyClient apifyClient;
    private readonly IDownloadBlobStorage downloadStorage;
    private readonly ILogger<ApifyNotification> logger;

    public ApifyNotification(IApifyClient apifyClient, IDownloadBlobStorage downloadStorage, ILogger<ApifyNotification> logger)
    {
        this.apifyClient = apifyClient;
        this.downloadStorage = downloadStorage;
        this.logger = logger;
    }

    public async Task RunSucceeded(ApifyWebhookPayload runData)
    {
        if (runData == null || runData.resource == null || 
            string.IsNullOrEmpty(runData.resource.defaultKeyValueStoreId) || 
            string.IsNullOrEmpty(runData.resource.defaultDatasetId))
        {
            logger.LogError("ApifyNotification: Insufficient run data {RunData}", runData);
            return;
        }

        try
        {
            logger.LogInformation("ApifyNotification: TransferFilesAsync started for taskId: {TaskId},DataSetId: {DataSetId},KeyValueStoreId: {KeyValueStoreId}", runData.resource.actorTaskId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);

            downloadStorage.SetBlobFolderName(runData.resource.actorTaskId ?? string.Empty);

            await apifyClient.TransferFilesAsync(runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId, downloadStorage);
           
            logger.LogInformation("ApifyNotification: TransferFilesAsync ended for taskId: {TaskId},DataSetId: {DataSetId},KeyValueStoreId: {KeyValueStoreId}", runData.resource.actorTaskId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: An error occured: {ErrorMessage}", ex.Message);
        }
    }

    public async Task RunFailed(ApifyWebhookPayload runData)
    {
        throw new NotImplementedException();
    }
}