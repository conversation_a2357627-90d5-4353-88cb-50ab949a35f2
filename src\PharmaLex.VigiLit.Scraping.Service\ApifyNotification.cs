using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Contracts.Services;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service;

public class ApifyNotification : IApifyNotification
{
    private readonly IApifyClient _apifyClient;
    private readonly IDownloadBlobStorage _downloadStorage;
    private readonly IApifyRunCompletionService _apifyRunCompletionService;
    private readonly ILogger<ApifyNotification> _logger;

    public ApifyNotification(
        IApifyClient apifyClient,
        IDownloadBlobStorage downloadStorage,
        IApifyRunCompletionService apifyRunCompletionService,
        ILogger<ApifyNotification> logger)
    {
        _apifyClient = apifyClient;
        _downloadStorage = downloadStorage;
        _apifyRunCompletionService = apifyRunCompletionService;
        _logger = logger;
    }

    public async Task RunSucceeded(ApifyWebhookPayload runData)
    {
        if (runData == null || runData.resource == null ||
            string.IsNullOrEmpty(runData.resource.defaultKeyValueStoreId) ||
            string.IsNullOrEmpty(runData.resource.defaultDatasetId))
        {
            _logger.LogError("ApifyNotification: Insufficient run data {RunData}", LogSanitizer.Sanitize(runData));
            return;
        }

        var runId = runData.resource.id ?? runData.resource.actorTaskId ?? "unknown";
        var actorName = runData.resource.actorId ?? "unknown";

        try
        {
            _logger.LogInformation("ApifyNotification: Processing successful run {RunId} for actor {ActorName}",
                LogSanitizer.Sanitize(runId), LogSanitizer.Sanitize(actorName));

            // Step 1: Register the run in our tracking system
            var apifyRun = await _apifyRunCompletionService.RegisterRunAsync(runId, actorName);

            // Step 2: Transfer files to Azure Blob Storage using Phlex.Core.Apify
            _logger.LogInformation("ApifyNotification: Starting file transfer for run {RunId}", LogSanitizer.Sanitize(runId));

            _downloadStorage.SetBlobFolderName(runData.resource.actorTaskId ?? string.Empty);
            await _apifyClient.TransferFilesAsync(runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId, _downloadStorage);

            _logger.LogInformation("ApifyNotification: File transfer completed for run {RunId}", LogSanitizer.Sanitize(runId));

            // Step 3: Mark files as transferred and trigger metadata extraction
            await _apifyRunCompletionService.MarkFilesTransferredAsync(runId);
            await _apifyRunCompletionService.ProcessRunAsync(runId);

            _logger.LogInformation("ApifyNotification: Successfully processed webhook for run {RunId}", LogSanitizer.Sanitize(runId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ApifyNotification: Error processing webhook for run {RunId}: {ErrorMessage}",
                LogSanitizer.Sanitize(runId), ex.Message);
            throw; // Re-throw to ensure webhook failure is reported back to Apify
        }
    }

    public async Task RunFailed(ApifyWebhookPayload runData)
    {
        if (runData == null || runData.resource == null)
        {
            _logger.LogError("ApifyNotification: Insufficient run data for failed run {RunData}", LogSanitizer.Sanitize(runData));
            return;
        }

        var runId = runData.resource.id ?? runData.resource.actorTaskId ?? "unknown";
        var actorName = runData.resource.actorId ?? "unknown";

        try
        {
            _logger.LogWarning("ApifyNotification: Processing failed run {RunId} for actor {ActorName}",
                LogSanitizer.Sanitize(runId), LogSanitizer.Sanitize(actorName));

            // Register the run as failed in our tracking system
            var apifyRun = await _apifyRunCompletionService.RegisterRunAsync(runId, actorName);
            await _apifyRunCompletionService.MarkRunFailedAsync(runId);

            _logger.LogInformation("ApifyNotification: Successfully processed failed webhook for run {RunId}", LogSanitizer.Sanitize(runId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ApifyNotification: Error processing failed webhook for run {RunId}: {ErrorMessage}",
                LogSanitizer.Sanitize(runId), ex.Message);
            throw; // Re-throw to ensure webhook failure is reported back to Apify
        }
    }
}