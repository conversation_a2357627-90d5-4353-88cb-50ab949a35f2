﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.Fakes.Entities;
public class FakeSubstanceSynonym : SubstanceSynonym
{
    public FakeSubstanceSynonym(int id, string name)
    {
        Id = id;
        Name = name;
    }
    public FakeSubstanceSynonym(int id, string name, int substanceId, FakeSubstance substance) 
    {
        Id = id;
        Name = name;
        SubstanceId = substanceId;
        Substance = substance;
    }
}
