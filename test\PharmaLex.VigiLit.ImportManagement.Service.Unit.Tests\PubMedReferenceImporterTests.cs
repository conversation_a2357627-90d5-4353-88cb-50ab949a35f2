﻿using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Moq;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Core.Aggregator.Models;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Contracts;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Service.Services;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class PubMedReferenceImporterTests
{
    private readonly IImportProcessingService _importProcessingService;
    private readonly IOptions<ImportOptions> _options = Options.Create(new ImportOptions() { TimeoutMinutes = 60 });
    private readonly Mock<IImportingImportRepository> _importRepository = new();
    private readonly Mock<IImportingImportContractRepository> _importContractRepository = new();
    private readonly Mock<IImportingReferenceRepository> _referenceRepository = new();
    private readonly Mock<IImportingReferenceClassificationRepository> _referenceClassificationRepository = new();
    private readonly Mock<IImportingReferenceUpdateRepository> _referenceUpdateRepository = new();
    private readonly Mock<IReferenceResolver> _importProcessingHelperService = new();
    private readonly Mock<IFeatureManager> _featureManager = new();
    private readonly Mock<IReferenceManager> _referenceManager = new();
    private readonly Mock<IAiReferencePublisher> _aiReferencePublisher = new();

    public PubMedReferenceImporterTests()
    {
        _importProcessingService = new PubMedReferenceImporter(
            _options,
            new NullLoggerFactory(),
            _importRepository.Object,
            _importContractRepository.Object,
            _referenceRepository.Object,
            _referenceClassificationRepository.Object,
            _referenceUpdateRepository.Object,
            _featureManager.Object,
            _importProcessingHelperService.Object,
            _referenceManager.Object,
            _aiReferencePublisher.Object
            );
    }

    [Fact]
    public async Task ImportProcessingActivity_HasNoWorkToDo_ThrowsArgumentException()
    {
        // Arrange
        ImportProcessingParams input = new(0, 0);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _importProcessingService.ImportProcessingActivity(input));
    }

    [Fact]
    public async Task ImportProcessingActivity_HandlesImportWithNoContracts()
    {
        // Arrange
        ImportProcessingParams input = new(1, 0);

        Import import = new();

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ImportProcessingActivity_HandlesException()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .Throws<Exception>();

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Failed, importContract.ImportContractStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ImportProcessingActivity_HandlesTimeout()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .Throws<TimeoutException>();

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Timeout, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.CompletedWithFailedContracts, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessResults_SearchStringMissing()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            SearchStringMissing = true
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.InvalidSearchString, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.CompletedWithFailedContracts, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessResults_SearchFailed()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            SearchFailed = true
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.SearchFailed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.CompletedWithFailedContracts, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessResults_FetchFailed()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            FetchFailed = true
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Failed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.CompletedWithFailedContracts, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessResults_ResultsCapped()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            ResultsCapped = true
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.CompletedCapped, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessResults_Completed()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new();

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessReferences_New_SavesReference_and_Does_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123"
        };

        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        var substance = new FakeSubstance(4, "Substance 4", "type")
        {
            SubstanceSynonyms = new List<SubstanceSynonym>()
        };

        _importProcessingHelperService.Setup(x => x.GetSubstance(It.IsAny<int>())).ReturnsAsync(substance);
        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers? identifiers = null;
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))!
            .ReturnsAsync(identifiers);

        _referenceManager
            .Setup(x => x.UpdateImportContractWithReference(It.IsAny<ImportContract>(), It.IsAny<Reference>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Once);
    }

    [Fact]
    public async Task ProcessReferences_Sends_Correct_Parameters_To_Ai()
    {
        // Arrange

        const string pubmedId = "123";
        const string referenceTitle = "Reference Title";
        const string referenceAbstract = "Reference abstract";

        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = pubmedId,
            Title = referenceTitle,
            Abstract = referenceAbstract
        };

        ReferenceResults results = new()
        {
            References = [testReference]
        };

        var synonyms = new List<SubstanceSynonym>
        {
            new FakeSubstanceSynonym(1, "Synonym 1"),
            new FakeSubstanceSynonym(2, "Synonym 2"),
        };

        //var substance = new SubstanceModel { Id = 4, Name = "Substance 4", SubstanceSynonyms = synonyms };

        var substance = new FakeSubstance(4, "Substance 4", "type")
        {
            SubstanceSynonyms = synonyms
        };

        _importProcessingHelperService.Setup(x => x.GetSubstance(It.IsAny<int>())).ReturnsAsync(substance);
        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers? identifiers = null;
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers(pubmedId))!
            .ReturnsAsync(identifiers);

        _referenceManager
            .Setup(x => x.UpdateImportContractWithReference(It.IsAny<ImportContract>(), It.IsAny<Reference>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);


        var synonymNames = synonyms.Select(y => y.Name).ToList();

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);

        _aiReferencePublisher.Verify(x => x.Send(It.Is<PreClassifyReferenceCommand>(
                obj => obj.Abstract == referenceAbstract &&
                       obj.Title == referenceTitle &&
                       obj.Substance == substance.Name &&
                       obj.RecordIdentifier == pubmedId.ToString() &&
                       obj.SourceSystem == ((int)SourceSystem.PubMed).ToString() &&
                       obj.Synonyms.SequenceEqual(synonymNames)
                )), Times.Once);
    }


    [Fact]
    public async Task ProcessReferences_EnsureAllocation_NewClassification()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            References = new()
            {
                new()
                {
                    SourceId ="123"
                }
            }
        };

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Single(importContract.ImportContractReferenceClassifications.First().ReferenceClassification.ReferenceHistoryActions);
        Assert.Equal(ReferenceHistoryActionType.New, importContract.ImportContractReferenceClassifications.First().ReferenceClassification.ReferenceHistoryActions.First().ReferenceHistoryActionType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessReferences_EnsureAllocation_ExistingClassification()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new()
        {
            References = new()
            {
                new()
                {
                    SourceId = "123"
                }
            }
        };

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(6, 4);
        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Single(importContract.ImportContractReferenceClassifications);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessCurrentReference_SavesReference_and_DoesNot_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123"
        };

        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(testReference, 4);

        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Equal(0, importContract.UpdatesCount);
        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.Current, importContract.ImportContractReferenceClassifications.First().ICRCType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Never);
    }

    [Fact]
    public async Task ProcessReferences_SilentUpdate_SavesReference_and_DoesNot_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123",
            DateRevised = new DateTime(2023, 12, 13, 0, 0, 0, DateTimeKind.Utc)
        };

        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(testReference, 4);
        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        Reference reference = new();
        _referenceRepository
            .Setup(x => x.GetByIdForImport(6))
            .ReturnsAsync(reference);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Equal(1, importContract.SilentUpdatesCount);
        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.SilentUpdate, importContract.ImportContractReferenceClassifications.First().ICRCType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Never);
    }

    [Fact]
    public async Task ProcessReferences_NewUpdate_SavesReference_and_Does_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123",
            DateRevised = new DateTime(2023, 12, 13, 0, 0, 0, DateTimeKind.Utc)
        };

        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        var substance = new FakeSubstance(4, "Substance 4", "type")
        {
            SubstanceSynonyms = new List<SubstanceSynonym>()
        };

        _importProcessingHelperService.Setup(x => x.GetSubstance(It.IsAny<int>())).ReturnsAsync(substance);
        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(testReference, 4);
        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        Reference reference = new() { Title = "title changed" };
        _referenceRepository
            .Setup(x => x.GetByIdForImport(6))
            .ReturnsAsync(reference);

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.NewUpdate, importContract.ImportContractReferenceClassifications.First().ICRCType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Once);
    }

    [Fact]
    public async Task ProcessReferences_UpdatedUpdate_SavesReference_and_Does_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };

        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123",
            DateRevised = new DateTime(2023, 12, 13, 0, 0, 0, DateTimeKind.Utc)
        };

        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(testReference, 4);
        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        List<ReferenceUpdate> updates = new()
        {
            new ReferenceUpdate()
            {
                SubstanceId = 4,
                DateRevised = new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc)
            }
        };

        var substance = new FakeSubstance(4, "Substance 4", "type")
        {
            SubstanceSynonyms = new List<SubstanceSynonym>()
        };

        _referenceUpdateRepository
            .Setup(x => x.GetUpdatesByReferenceId(6))
            .ReturnsAsync(updates);

        _importProcessingHelperService.Setup(x => x.GetSubstance(It.IsAny<int>())).ReturnsAsync(substance);
        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Equal(0, importContract.UpdatesCount);
        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.UpdatedUpdate, importContract.ImportContractReferenceClassifications.First().ICRCType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Once);
    }

    [Fact]
    public async Task ProcessReferences_CurrentUpdate_SavesReference_and_DoesNot_Send_To_Ai()
    {
        // Arrange
        _featureManager.Setup(x => x.IsEnabledAsync("AiMessageBusSend"))
            .ReturnsAsync(true);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>())).Returns(Task.CompletedTask);

        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        var testReference = new Reference
        {
            SourceId = "123",
            DateRevised = new DateTime(2023, 12, 13, 0, 0, 0, DateTimeKind.Utc)
        };


        ReferenceResults results = new()
        {
            References = new()
            {
                testReference
            }
        };

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers identifiers = new(6, "123", new DateTime(2023, 12, 12, 0, 0, 0, DateTimeKind.Utc));
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))
            .ReturnsAsync(identifiers);

        ReferenceClassification classification = new(testReference, 4);

        _referenceClassificationRepository
            .Setup(x => x.GetClassificationForImport(6, 4))
            .ReturnsAsync(classification);

        List<ReferenceUpdate> updates = new()
        {
            new ReferenceUpdate()
            {
                SubstanceId = 4,
                DateRevised = new DateTime(2023, 12, 13, 0, 0, 0, DateTimeKind.Utc)
            }
        };
        _referenceUpdateRepository
            .Setup(x => x.GetUpdatesByReferenceId(6))
            .ReturnsAsync(updates);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        Assert.Equal(0, importContract.UpdatesCount);
        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.CurrentUpdate, importContract.ImportContractReferenceClassifications.First().ICRCType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _aiReferencePublisher.Verify(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()), Times.Never);
    }

    [Fact]
    public async Task GetNextImportProcessingParams_NullImport()
    {
        // Arrange
        Import? import = null;

        _importRepository
            .Setup(x => x.GetPriorityImportForProcessing())!
            .ReturnsAsync(import);

        // Act
        var nextInput = await _importProcessingService.GetNextImportProcessingParams();

        // Assert
        Assert.Equal(0, nextInput.ImportId);
        Assert.Equal(0, nextInput.ImportContractId);
    }

    [Fact]
    public async Task GetNextImportProcessingParams_ImportWithNoImportContracts()
    {
        // Arrange
        Import import = new FakeImport(1);

        _importRepository
            .Setup(x => x.GetPriorityImportForProcessing())
            .ReturnsAsync(import);

        // Act
        var nextInput = await _importProcessingService.GetNextImportProcessingParams();

        // Assert
        Assert.Equal(1, nextInput.ImportId);
        Assert.Equal(0, nextInput.ImportContractId);
    }

    [Fact]
    public async Task GetNextImportProcessingParams_ImportWithoutQueuedImportContracts()
    {
        // Arrange
        Import import = new FakeImport(1)
        {
            ImportContracts = new List<ImportContract>()
            {
                new FakeImportContract(1){ ImportContractStatusType = ImportContractStatusType.Completed },
            }
        };

        _importRepository
            .Setup(x => x.GetPriorityImportForProcessing())
            .ReturnsAsync(import);

        // Act
        var nextInput = await _importProcessingService.GetNextImportProcessingParams();

        // Assert
        Assert.Equal(1, nextInput.ImportId);
        Assert.Equal(0, nextInput.ImportContractId);
    }

    [Fact]
    public async Task GetNextImportProcessingParams_ReturnsFirstQueuedImportContract()
    {
        // Arrange
        Import import = new FakeImport(1)
        {
            ImportContracts = new List<ImportContract>()
            {
                new FakeImportContract(1){ ImportContractStatusType = ImportContractStatusType.Completed },
                new FakeImportContract(2){ ImportContractStatusType = ImportContractStatusType.Queued },
                new FakeImportContract(3){ ImportContractStatusType = ImportContractStatusType.Queued },
                new FakeImportContract(4){ ImportContractStatusType = ImportContractStatusType.Queued },
            }
        };

        _importRepository
            .Setup(x => x.GetPriorityImportForProcessing())
            .ReturnsAsync(import);

        // Act
        var nextInput = await _importProcessingService.GetNextImportProcessingParams();

        // Assert
        Assert.Equal(1, nextInput.ImportId);
        Assert.Equal(2, nextInput.ImportContractId);
    }

    [Fact]
    public async Task ProcessReferences_BatchedSaves()
    {
        // Arrange
        ImportProcessingParams input = new(1, 2);

        Import import = new();
        ImportContract importContract = new()
        {
            Import = import,
            ContractVersionId = 0,
            Contract = new(4, 5)
            {
                ContractVersionsInternal = new List<ContractVersion>()
                {
                    new(new(4, 5), "my search string")
                }
            }
        };
        import.ImportContracts.Add(importContract);

        ReferenceResults results = new();

        int referencesCount = 300;
        for (int i = 0; i < referencesCount; i++)
        {
            results.References.Add(new()
            {
                SourceId = "123"
            });
        }

        List<SubstanceModel> substances = new() {
            new SubstanceModel{ Id = 4, Name = "Substance 4", SubstanceSynonyms = new List<SubstanceSynonymModel>() }
        };

        _importRepository
            .Setup(x => x.GetByIdForImport(1))
            .ReturnsAsync(import);

        _importContractRepository
            .Setup(x => x.GetByIdForImport(2))
            .ReturnsAsync(importContract);

        _importProcessingHelperService
            .Setup(x => x.GetReferences(It.IsAny<ESearchCriteria>(), It.IsAny<DateTime>()))
            .ReturnsAsync(results);

        ReferenceIdentifiers? identifiers = null;
        _referenceRepository
            .Setup(x => x.GetReferenceIdentifiers("123"))!
            .ReturnsAsync(identifiers);

        _aiReferencePublisher.Setup(x => x.Send(It.IsAny<PreClassifyReferenceCommand>()));

        _referenceManager
            .Setup(x => x.UpdateImportContractWithReference(It.IsAny<ImportContract>(), It.IsAny<Reference>()));

        // Act
        await _importProcessingService.ImportProcessingActivity(input);

        // Assert
        Assert.Equal(ImportContractStatusType.Completed, importContract.ImportContractStatusType);
        Assert.Equal(ImportStatusType.Completed, import.ImportStatusType);

        _referenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));
        _referenceRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));
        _importContractRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));
    }
}