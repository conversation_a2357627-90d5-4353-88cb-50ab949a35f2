using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public partial class AuthorList
{
    [XmlElement("Author")]
    public List<Author> Author { get; set; } = new List<Author>();

    [XmlAttribute()]
    public EnumYN CompleteYN { get; set; } = EnumYN.Y;

    [XmlAttribute()]
    public AuthorListType Type { get; set; }

    [XmlIgnore()]
    public bool TypeSpecified { get; set; }
}