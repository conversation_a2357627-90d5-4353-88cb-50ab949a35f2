﻿using Moq;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;
using PharmaLex.VigiLit.ReferenceManagement.Service.Access;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests.Access;
public class ViewReferenceClassificationEvaluatorTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new();
    private readonly Mock<ICompanyInterestRepository> _mockCompanyInterestRepository = new();

    private const int UserId = 42;
    private const int ReferenceClassificationId = 43;


    [Fact]
    public async Task ViewReferenceClassification_NullUser_Returns_False()
    {
        // Arrange
        FakeUser? user = null;

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewReferenceClassificationEvaluator = new ViewReferenceClassificationEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferenceClassificationPermissionContext = new ViewReferenceClassificationPermissionContext(UserId, ReferenceClassificationId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceClassificationEvaluator.HasPermissions(viewReferenceClassificationPermissionContext);
        });
    }

    [Fact]
    public async Task ViewReferenceClassification_InternalUser_Returns_True()
    {
        // Arrange
        var user = GetInternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewReferenceClassificationEvaluator = new ViewReferenceClassificationEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferenceClassificationPermissionContext = new ViewReferenceClassificationPermissionContext(UserId, ReferenceClassificationId);


        // Act
        var result = await viewReferenceClassificationEvaluator.HasPermissions(viewReferenceClassificationPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewReferenceClassification_ExternalUser_Returns_False_If_User_HasNoActiveCompany()
    {
        // Arrange
        var user = GetExternalUser();
        user.CompanyUser.Active = false;

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewReferenceClassificationEvaluator = new ViewReferenceClassificationEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferenceClassificationPermissionContext = new ViewReferenceClassificationPermissionContext(UserId, ReferenceClassificationId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceClassificationEvaluator.HasPermissions(viewReferenceClassificationPermissionContext);
        });
    }

    [Fact]
    public async Task ViewReferenceClassification_ExternalUser_Returns_True_If_User_CompanyHasInterestInClassification()
    {
        // Arrange
        var user = GetExternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        _mockCompanyInterestRepository
            .Setup(x => x.CompanyHasInterestInClassification(43, 43))
            .ReturnsAsync(true);

        var viewReferenceClassificationEvaluator = new ViewReferenceClassificationEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferenceClassificationPermissionContext = new ViewReferenceClassificationPermissionContext(UserId, ReferenceClassificationId);

        // Act
        var result = await viewReferenceClassificationEvaluator.HasPermissions(viewReferenceClassificationPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewReferenceClassification_ExternalUser_Returns_False_If_User_CompanyHasNoInterestInClassification()
    {
        // Arrange
        var user = GetExternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        _mockCompanyInterestRepository
            .Setup(x => x.CompanyHasInterestInClassification(43, 43))
            .ReturnsAsync(false);

        var viewReferenceClassificationEvaluator = new ViewReferenceClassificationEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferenceClassificationPermissionContext = new ViewReferenceClassificationPermissionContext(UserId, ReferenceClassificationId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceClassificationEvaluator.HasPermissions(viewReferenceClassificationPermissionContext);
        });
    }

    private static FakeUser GetInternalUser()
    {
        return new FakeUser("FirstName", "LastName", "EmailAddress", 42, DateTime.Now, DateTime.Now);
    }

    private static FakeUser GetExternalUser()
    {
        var user = new FakeUser("FirstName", "LastName", "EmailAddress", 42, DateTime.Now, DateTime.Now);
        user.AddClaim(new FakeClaim(6, "ClientResearcher"));
        user.CompanyUser = new CompanyUser() { Active = true, CompanyId = 43 };
        return user;
    }

}
