﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Ui;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Web.Controllers.CustomTypeFilters;
using PharmaLex.VigiLit.Web.ViewModels;
using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[SuppressMessage("Maintainability", "S1075: Refactor to not use hardcoded URI", Justification = "Card exists to revisit suppressions.")]
public class HomeController : BaseController
{
    const string LogoutUrlFormat = "https://login.microsoftonline.com/smartphlexb2c/oauth2/v2.0/logout?post_logout_redirect_uri=https://{0}/?client_id={1}";

    private readonly IUserService _userService;
    private readonly IUserSessionService _userSessionService;
    private readonly ICompanyUserService _companyUserService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IDashboardService _dashboardService;
    private readonly IConfiguration _configuration;
    private readonly IExportService _exportService;

    public HomeController(
        IUserService userService,
        IUserSessionService userSessionService,
        ICompanyUserService companyUserService,
        IAuthorizationService authorizationService,
        IDashboardService dashboardService,
        IConfiguration configuration,
        IExportService exportService)
                    : base(userSessionService, configuration)
    {
        _userService = userService;
        _userSessionService = userSessionService;
        _companyUserService = companyUserService;
        _authorizationService = authorizationService;
        _dashboardService = dashboardService;
        _configuration = configuration;
        _exportService = exportService;
    }

    private void ClearSessionAndCookies()
    {
        HttpContext.Session.Clear();

        if (HttpContext.Request.Cookies.Count > 0)
        {
            var test = HttpContext.Request.Cookies.ToList();
            var siteCookies = HttpContext.Request.Cookies.Where(c => c.Key.Contains(".AspNetCore.") || c.Key.Contains("Microsoft.Authentication"));

            foreach (var cookie in test)
            {
                Response.Cookies.Delete(cookie.Key);
            }
        }
    }

    private AuthenticationProperties GetLogoutAuthenticationProperties()
    {
        var clientId = _configuration.GetValue<string>("AzureAdB2C:ClientId");
        var host = HttpContext.Request.Host;
        var redirectUrl = string.Format(LogoutUrlFormat, host, clientId);
        return new AuthenticationProperties { RedirectUri = redirectUrl }; 
    }

    [Route("/logoutex")]
    public async Task<IActionResult> LogoutEx()
    {
        await ClearStoredUserSession();
        ClearSessionAndCookies();
        var properties = GetLogoutAuthenticationProperties();
        return SignOut(properties, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
    }

    private async Task ClearStoredUserSession()
    {
        var userId = HttpContext.User.GetClaimValue("plx:userid");
        if (!string.IsNullOrEmpty(userId))
        {
            await _userSessionService.DeleteUserSessionAsync(int.Parse(userId));
        }
    }

    [HttpGet("/unauthorised")]
    [AllowAnonymous]
    public IActionResult Anonymous()
    {
        ViewData["unauthorised"] = true;
        ViewBag.ShowBackground = true;
        return View();
    }

    [HttpGet("/newlogin")]
    [AllowAnonymous]
    public IActionResult NewLogin()
    {
        return SignOut(new AuthenticationProperties { RedirectUri = "/login" }, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var companyUser = await _companyUserService.GetCompanyUserByEmail(HttpContext.User.GetEmail());
        var pharmalexClientResearcherClaim = await _userService.GetClaimByNameAsync(Claims.PharmaLexClientResearcher);

        if (companyUser != null)
        {
            // If company user belongs to an inactive company then signout
            if (companyUser.CompanyUserType == CompanyUserType.External && !companyUser.IsCompanyActive)
            {
                return SignOut(new AuthenticationProperties { RedirectUri = "/unauthorised" }, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
            }

            // If internal company user with only Plx Client Researcher claim belongs to inactive company or is inactive user then signout
            if (companyUser.CompanyUserType == CompanyUserType.Internal
                && companyUser.ClaimIds.Count == 1
                && pharmalexClientResearcherClaim != null
                && companyUser.ClaimIds.Contains(pharmalexClientResearcherClaim.Id)
                && (!companyUser.IsCompanyActive || !companyUser.IsActive))
            {
                return SignOut(new AuthenticationProperties { RedirectUri = "/unauthorised" }, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
            }
        }

        // TODO: Move this, the last login date should be recorded on login, not on home page load.
        //       Move to AddClaimsCallback.OnTicketReceived?
        await _userService.RecordLogin(HttpContext.User.GetEmail());

        // Load the dashboard
        var dashboard = new DashboardModel
        {
            CompanySummary = [],
            Total = new CompanyEmailStats(),
        };

        // Load the import queues into the dashboard
        if ((await _authorizationService.AuthorizeAsync(User, Policies.Admin)).Succeeded || (await _authorizationService.AuthorizeAsync(User, Policies.Assessor)).Succeeded)
        {
            var selectedImport = await _dashboardService.GetSelectedImport();

            dashboard.Imports = await _dashboardService.GetImportsForDashboard();
            dashboard.DisplayImportsDashboard = true;
            dashboard.SelectedImportId = selectedImport != null ? selectedImport.ImportId : 0;
        }

        // Load the email support stats into the dashboard
        if ((await _authorizationService.AuthorizeAsync(User, Policies.InternalSupport)).Succeeded)
        {
            var emailDashboard = await _dashboardService.GetCompanyDashboardStats();

            dashboard.Total = emailDashboard.Total;
            dashboard.CompanySummary = emailDashboard.CompanySummary;
            dashboard.DisplayEmailDashboard = true;
        }

        return View(dashboard);
    }

    [HttpGet("[action]/{importId}")]
    [AuthorizeOnAnyOnePolicy("Admin,Assessor")]
    public async Task<IActionResult> ImportDashboardDetails(int importId, int page = 1, int pageSize = 25)
    {
        try
        {
            var model = await _dashboardService.GetImportDashboardDetails(importId, page, pageSize);
            return View(model);
        }
        catch (ArgumentOutOfRangeException e)
        {
            var errorMessage = e.Message;
            return BadRequest(errorMessage);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid();
        }
    }

    [HttpGet("[action]/{importId}")]
    [ValidateAntiForgeryToken]
    [AuthorizeOnAnyOnePolicy("Admin,Assessor")]
    public async Task<IActionResult> ImportDashboardDetailsExport(int importId)
    {
        try
        {
            var data = await _dashboardService.GetImportDashboardDetailsExport(importId);
            var export = _exportService.Export<DashboardDetailsRowModel, ImportDashboardDetailsRowModelClassMap>("ImportDetails", data);
            return File(export.Data, export.ContentType, export.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid();
        }
    }

    [HttpGet("[action]/{importId}")]
    [ValidateAntiForgeryToken]
    [AuthorizeOnAnyOnePolicy("Admin,Assessor")]
    public async Task<IActionResult> Archive(int importId)
    {
        await _dashboardService.Archive(importId);
        return Ok();
    }

    [HttpGet("[action]/{importId}")]
    [ValidateAntiForgeryToken]
    [AuthorizeOnAnyOnePolicy("Admin,Assessor")]
    public async Task<IActionResult> Select(int importId)
    {
        await _dashboardService.Select(importId);
        return Ok();
    }

    [HttpGet("[action]/{importId}")]
    [ValidateAntiForgeryToken]
    [AuthorizeOnAnyOnePolicy("Admin,Assessor")]
    public async Task<IActionResult> Deselect(int importId)
    {
        await _dashboardService.Deselect(importId);
        return Ok();
    }

    [HttpGet("/boom"), Authorize(Policy = "SuperAdmin")]
    public IActionResult Boom()
    {
#pragma warning disable S112 // General or reserved exceptions should never be thrown
        throw new Exception("Boom! Test exception thrown in VigiLit.");
#pragma warning restore S112 // General or reserved exceptions should never be thrown
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel
        {
            RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier,
            NotifyEmail = _configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail"),
            AppName = _configuration.GetValue<string>("AppSettings:AppName")
        });
    }
}