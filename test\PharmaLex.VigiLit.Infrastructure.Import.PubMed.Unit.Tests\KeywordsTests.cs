﻿using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests;

public class KeywordsTests
{
    private readonly IReferenceBuilder _referenceBuilder;

    public KeywordsTests()
    {
        _referenceBuilder = new ReferenceBuilder();

        _keywordList1.Keyword.Add(Keyword1);
        _keywordList1.Keyword.Add(Keyword2);
        _keywordList1.Keyword.Add(Keyword3);
        _keywordList1.Keyword.Add(Keyword4);
        _keywordList2.Keyword.Add(Keyword5);
        _keywordList2.Keyword.Add(Keyword6);
    }

    private readonly KeywordList _keywordList1 = new();
    private readonly KeywordList _keywordList2 = new();

    private static bool ArrayIsInAlphabeticalOrder(string?[] array) => array.Zip(array.Skip(1), (a, b) => string.Compare(a?.Trim(), b?.Trim())).All(x => x == -1 || x == 0);

    private static bool NextCharacterIs(string searchString, string searchCharacter, string nextcharacter)
    {
        var finished = false;
        var index = 0;

        while (!finished)
        {
            index = searchString.IndexOf(searchCharacter, index) + 1;

            if (index == 0 || index >= searchString.Length - 1)
            {
                finished = true;
            }
            else if (!string.Equals(nextcharacter, searchString.Substring(index, 1)))
            {
                return false;
            }
        }

        return true;
    }

    private static bool KeywordInAlphabeticalOrder(string keywords)
    {
        if (keywords != null)
        {
            var keywordArray = keywords.Split("; ");

            if (!ArrayIsInAlphabeticalOrder(keywordArray))
            {
                return false;
            }
        }

        return true;
    }

    private static PubmedArticle EmptyArticle => new()
    {
        MedlineCitation = new MedlineCitation
        {
            DateRevised = new DateRevised { Day = 1, Month = "1", Year = 2000 },
            Article = new Article { },
            PMID = new PMID { Value = 999 },
            KeywordList = new List<KeywordList>()
        }
    };

    private static Keyword Keyword1 => new() { Text = "XXXX" };
    private static Keyword Keyword2 => new() { Text = "ZZ" };
    private static Keyword Keyword3 => new() { Text = "AAA" };
    private static Keyword Keyword4 => new() { Text = "BBB" };
    private static Keyword Keyword5 => new() { Text = "DD" };
    private static Keyword Keyword6 => new() { Text = "JJ" };
    private static Keyword Keyword7MajorTopic => new() { Text = "Major topic", MajorTopicYN = EnumYN.Y };
    private static Keyword Keyword7NonMajorTopic => new() { Text = "Non major topic", MajorTopicYN = EnumYN.N };

    [Fact]
    public void NoKeywords_ReturnsNull()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Null(reference.Keywords);
    }

    [Fact]
    public void NoSemiColon_AtEnd()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList1);
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList2);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.NotEqual(';', reference.Keywords.Last());
    }

    [Fact]
    public void DescriptorAndQualifier_OrderderedAlphabetically()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList1);
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList2);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.True(KeywordInAlphabeticalOrder(reference.Keywords));
    }

    [Fact]
    public void MajorTopic_HasAsteriskAtEndOfKeyword()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;

        KeywordList Keywords = new();
        Keywords.Keyword.Add(Keyword7MajorTopic);
        Keywords.Keyword.Add(Keyword7NonMajorTopic);
        pubmedArticle.MedlineCitation.KeywordList.Add(Keywords);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Contains("Major topic*", reference.Keywords);
    }

    [Fact]
    public void NonMajorTopic_DoesNotHaveAsteriskAtEndOfKeyword()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;

        KeywordList Keywords = new();
        Keywords.Keyword.Add(Keyword7MajorTopic);
        Keywords.Keyword.Add(Keyword7NonMajorTopic);
        pubmedArticle.MedlineCitation.KeywordList.Add(Keywords);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.DoesNotContain("Non major topic*", reference.Keywords);
    }

    [Fact]
    public void SemiColons_HaveSpaceAfter()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList1);
        pubmedArticle.MedlineCitation.KeywordList.Add(_keywordList2);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.True(NextCharacterIs(reference.Keywords, ";", " "));
    }
}
