﻿using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.ESearch;

public class WarningList
{
    public WarningList()
    {
        OutputMessage = new List<string>();
        QuotedPhraseNotFound = new List<string>();
        PhraseIgnored = new List<string>();
    }

    [XmlElement("PhraseIgnored")]
    public List<string> PhraseIgnored { get; set; }

    [XmlElement("QuotedPhraseNotFound")]
    public List<string> QuotedPhraseNotFound { get; set; }

    [XmlElement("OutputMessage")]
    public List<string> OutputMessage { get; set; }
}