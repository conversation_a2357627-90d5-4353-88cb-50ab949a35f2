﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;
public class SplitReferenceServiceTests
{
    private readonly ISplitReferenceService _splitReferenceService;

    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<ICompanyInterestRepository> _mockCompanyInterestRepository = new();
    private readonly Mock<IClassificationService> _mockClassificationService = new();

    public SplitReferenceServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ReferenceClassificationMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();

        _mockReferenceClassificationRepository.Setup(x => x.Add(It.IsAny<ReferenceClassification>()));
        _mockReferenceClassificationRepository.Setup(x => x.SaveChangesAsync());

        _mockCompanyInterestRepository.Setup(x => x.Add(It.IsAny<CompanyInterest>()));
        _mockCompanyInterestRepository.Setup(x => x.SaveChangesAsync());

        _mockClassificationService.Setup(x => x.AddReferenceHistoryAction(0, ReferenceHistoryActionType.SplitReference)).Verifiable();

        _splitReferenceService = new SplitReferenceService(_mockReferenceClassificationRepository.Object, _mockCompanyInterestRepository.Object, _mockClassificationService.Object, mapper);
    }

    [Fact]
    public async Task CreateClassification_Creates_Classification()
    {
        // Arrange
        var referenceSplitModel = GetSplitReferenceModelData();

        // Act
        await _splitReferenceService.CreateClassification(referenceSplitModel);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.Add(It.Is<ReferenceClassification>(x =>
                                                                    x.ReferenceId == 1
                                                                    && x.ClassificationCategoryId == 1
                                                                )), Times.Once());

        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task CreateClassification_Creates_CompanyInterest()
    {
        // Arrange
        var referenceSplitModel = GetSplitReferenceModelData();

        // Act
        await _splitReferenceService.CreateClassification(referenceSplitModel);

        // Assert
        _mockCompanyInterestRepository.Verify(x => x.Add(It.Is<CompanyInterest>(x =>
                                                                    x.ReferenceId == 1
                                                                    && x.CompanyId == 1
                                                                    && x.EmailRelevantEvents.Count == 1
                                                                )), Times.Once());

        _mockCompanyInterestRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }


    [Fact]
    public async Task CreateClassification_Creates_ReferenceHistoryAction()
    {
        // Arrange
        var referenceSplitModel = GetSplitReferenceModelData();

        // Act
        await _splitReferenceService.CreateClassification(referenceSplitModel);

        // Assert
        _mockClassificationService.Verify(x => x.AddReferenceHistoryAction(It.IsAny<int>(), It.Is<ReferenceHistoryActionType>(x =>
                                                    x == ReferenceHistoryActionType.SplitReference)), Times.Once());

        _mockClassificationService.Verify(x => x.SaveReferenceHistoryChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task CreateClassification_Creates_Classification_ReferenceState_Split()
    {
        // Arrange
        var referenceSplitModel = GetSplitReferenceModelData();

        // Act
        await _splitReferenceService.CreateClassification(referenceSplitModel);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.Add(It.Is<ReferenceClassification>(x =>
                                                                    x.ReferenceState == ReferenceState.Split
                                                                )), Times.Once());

        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }
    private static ReferenceSplitModel GetSplitReferenceModelData()
    {
        return new ReferenceSplitModel()
        {
            SubstanceId = 1,
            ReferenceId = 1,
            SelectedCompanyIds = new List<int>() { 1 },
            ReferenceClassification = new ReferenceClassificationModel()
            { ClassificationCategoryId = 1 }
        };
    }

}
