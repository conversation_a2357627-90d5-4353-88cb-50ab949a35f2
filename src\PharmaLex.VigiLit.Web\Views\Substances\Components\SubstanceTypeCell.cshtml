﻿<script type="text/x-template" id="substance-type-cell-template">
    <td>{{type}}</td>
</script>

<script type="text/javascript">
        vueApp.component('substance-type-cell', {
        template: '#substance-type-cell-template',
        props: {
            val: String
        },
        computed: {
            type(){
                return this.val === 'ch' ? 'Chemical' : 'Phyto (Herbal)';
            }
        }
    });
</script>