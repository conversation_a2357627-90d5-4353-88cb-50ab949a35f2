using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class Book
{
    public Publisher Publisher { get; set; }

    public BookTitle BookTitle { get; set; }

    public PubDate PubDate { get; set; }

    public BeginningDate BeginningDate { get; set; }

    public EndingDate EndingDate { get; set; }

    [XmlElement("AuthorList")]
    public List<AuthorList> AuthorList { get; set; } = new List<AuthorList>();

    [XmlArrayItem("Investigator", IsNullable = false)]
    public List<Investigator> InvestigatorList { get; set; } = new List<Investigator>();

    public string Volume { get; set; }

    public VolumeTitle VolumeTitle { get; set; }

    public string Edition { get; set; }

    public CollectionTitle CollectionTitle { get; set; }

    [XmlElement("Isbn")]
    public List<string> Isbn { get; set; } = new List<string>();

    [XmlElement("ELocationID")]
    public List<ELocationID> ELocationID { get; set; } = new List<ELocationID>();

    public string Medium { get; set; }

    public string ReportNumber { get; set; }
}