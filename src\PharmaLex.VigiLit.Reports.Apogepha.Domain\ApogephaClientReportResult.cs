﻿using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

public class ApogephaClientReportResult : EntityBase
{
    public required string DateOfQuery { get; set; }
    public required string Substance { get; set; }
    public required string YesId { get; set; }
    public required string EntryDate { get; set; }
    public required string Title { get; set; }
    public required string Authors { get; set; }
    public required string Abstract { get; set; }
    public required string Source { get; set; }
    public required string Category { get; set; }
}