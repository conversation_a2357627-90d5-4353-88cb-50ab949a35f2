using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class SubstanceModel
{
    public int Id { get; set; }
    [Required]
    [MaxLength(250)]
    [RegularExpression("^\\S[\\S\\s]*$", ErrorMessage = "Invalid Name")]
    [Remote("ValidateSubstanceName", "Substances", AdditionalFields = "Id", ErrorMessage = "Substance name already exists")]
    public string Name { get; set; }
    [Required]
    public string Type { get; set; }

    public List<SubstanceSynonymModel> SubstanceSynonyms { get; set; }
}
