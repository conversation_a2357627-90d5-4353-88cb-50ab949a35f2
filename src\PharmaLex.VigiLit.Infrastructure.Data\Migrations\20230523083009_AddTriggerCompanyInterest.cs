﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddTriggerCompanyInterest : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // create trigger is not compatible with idempotent migrations.sql
            // https://stackoverflow.com/questions/55336035/is-it-possible-and-how-create-a-trigger-with-entity-framework-core
            // https://learn.microsoft.com/en-us/ef/core/managing-schemas/migrations/applying?tabs=dotnet-core-cli#idempotent-sql-scripts
            // because create trigger must be first in a batch
            // https://learn.microsoft.com/en-us/sql/t-sql/statements/create-trigger-transact-sql?view=sql-server-ver16#remarks-for-dml-triggers
            // the workaround is to use exec and bypass the transaction
            // https://stackoverflow.com/questions/68045105/ef-core-idempotent-migration-script-fails-despite-check-on-applied-migration
            // Use the EXEC function when a statement must be the first or only one in a SQL batch.
            // https://learn.microsoft.com/en-us/ef/core/managing-schemas/migrations/operations#using-migrationbuildersql

            migrationBuilder.Sql(@"exec('CREATE TRIGGER [dbo].[TR_CompanyInterest] ON [dbo].[ImportContractReferenceClassifications]
    AFTER INSERT
AS
	BEGIN
		INSERT	INTO [CompanyInterests]
			( 
				[CompanyId],
				[ReferenceId],
				[ReferenceClassificationId],
				[CreatedDate],
				[CreatedBy],
				[LastUpdatedDate],
				[LastUpdatedBy]
			)
			SELECT	(
						SELECT [p].[CompanyId] 
						FROM [Projects] [p], [Contracts] [c], [ImportContracts] [ic]
						WHERE [p].[Id] = [c].[ProjectId]
						AND [c].[Id] = [ic].[ContractId] 
						AND [ic].[Id] = [I].[ImportContractId]
					),
					(
						SELECT [ReferenceId] 
						FROM [ReferenceClassifications] 
						WHERE [Id] = [I].[ReferenceClassificationId]
					),
					[I].[ReferenceClassificationId],
					GETUTCDATE(),
					''TR_CompanyInterest'',
					GETUTCDATE(),
					''TR_CompanyInterest''
			FROM	[Inserted] [I]
			WHERE	NOT EXISTS 
			(
				SELECT	[Id]
				FROM	[CompanyInterests] 
				WHERE 
						[CompanyId] = (
										SELECT [p].[CompanyId] 
										FROM [Projects] [p], [Contracts] [c], [ImportContracts] [ic]
										WHERE [p].[Id] = [c].[ProjectId]
										AND [c].[Id] = [ic].[ContractId] 
										AND [ic].[Id] = [I].[ImportContractId]
									)
						AND [ReferenceClassificationId] = [I].[ReferenceClassificationId]
			)
	END;')", true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS [dbo].[TR_CompanyInterest];");
        }
    }
}
