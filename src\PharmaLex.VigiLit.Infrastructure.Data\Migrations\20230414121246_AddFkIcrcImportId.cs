﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddFkIcrcImportId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId",
                table: "ImportContractReferenceClassifications",
                column: "ImportId");

            migrationBuilder.AddForeignKey(
                name: "FK_ImportContractReferenceClassifications_Imports_ImportId",
                table: "ImportContractReferenceClassifications",
                column: "ImportId",
                principalTable: "Imports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ImportContractReferenceClassifications_Imports_ImportId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportId",
                table: "ImportContractReferenceClassifications");
        }
    }
}
