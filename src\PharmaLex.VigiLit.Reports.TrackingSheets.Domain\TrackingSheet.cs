﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class TrackingSheet : VigiLitEntityBase
{
    [ForeignKey("Companies")]
    public int CompanyId { get; set; }

#pragma warning disable CS8618
    public Company Company { get; set; }

    [Required]
    public int Year { get; set; }

    [Required]
    public int Week { get; set; }

    [Required, MaxLength(256)]
    public string FileName { get; set; }
#pragma warning restore CS8618

    [Required]
    public int FileSize { get; set; }
}
