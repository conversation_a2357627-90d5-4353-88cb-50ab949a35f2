﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Caching;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Web.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.SuperAdmin)]
[Route("[controller]")]
public partial class CacheController : BaseController
{
    private readonly IDistributedCacheService cache;
    public CacheController(
        IDistributedCacheService cache,
        IUserSessionService userSessionService,
        IConfiguration configuration)
        : base(userSessionService, configuration)
    {
        this.cache = cache;
    }

    [HttpGet]
    public IActionResult Cache()
    {
        return View(GetCacheModel());
    }

    [HttpGet("[action]/{key}")]
    public async Task<IActionResult> Edit(string key)
    {
        string val = await cache.GetStringAsync(key);
        return View(new CacheViewModel { Key = key, Value = val });
    }

    [HttpPost("[action]/{key}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string key, CacheViewModel model)
    {
        Regex regex = EditRegex();
        string value = regex.Replace(model.Value, string.Empty);
        await cache.SetStringAsync(key, value.Replace("&#39;", "'"));
        return View("Cache", GetCacheModel());
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Delete(string key)
    {
        await cache.RemoveAsync(key);

        return View("Cache", GetCacheModel());
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Flush()
    {
        await cache.RemoveAllAsync();

        return RedirectToAction("Cache");
    }

    private List<CacheViewModel> GetCacheModel()
    {
        List<CacheViewModel> models = [];

        var allCache = cache.GetAll();

        foreach (string dep in allCache.Keys.OrderBy(x => x))
        {
            foreach (string key in allCache[dep])
            {
                models.Add(new CacheViewModel
                {
                    Key = dep,
                    Value = key
                });
            }
        }

        return models;
    }

    [GeneratedRegex("\r\n[ ]*", RegexOptions.None)]
    private static partial Regex EditRegex();
}