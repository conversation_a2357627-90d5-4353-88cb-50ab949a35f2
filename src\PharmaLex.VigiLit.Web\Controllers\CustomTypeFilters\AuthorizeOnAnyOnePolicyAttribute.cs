﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Web.Controllers.CustomTypeFilters;

/// <summary>
/// Specifies that the class or method that this attribute is applied to requires 
/// authorization based on user passing any one policy in the provided list of policies.
/// </summary>
/// <remarks>https://stackoverflow.com/questions/51443605/how-to-include-multiple-policies/</remarks>
public class AuthorizeOnAnyOnePolicyAttribute : TypeFilterAttribute
{
    /// <summary>
    /// Initializes a new instance of the AuthorizeOnAnyOnePolicyAttribute class.
    /// </summary>
    /// <param name="policies">A comma delimited list of policies that are allowed to access the resource.</param>
    public AuthorizeOnAnyOnePolicyAttribute(string policies) : base(typeof(AuthorizeOnAnyOnePolicyFilter))
    {
#pragma warning disable SYSLIB1045
        var commaDelimitedWhitespaceCleanup = new Regex("\\s+,\\s+|\\s+,|,\\s+",
#pragma warning restore SYSLIB1045
            RegexOptions.IgnoreCase | RegexOptions.CultureInvariant | RegexOptions.IgnorePatternWhitespace | RegexOptions.Compiled, TimeSpan.FromSeconds(30));

        Arguments = [commaDelimitedWhitespaceCleanup.Replace(policies, ",")];
    }
}