﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class RemoveEmailCols : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_EmailSent_EmailReason_EmailQueued",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "EmailQueued",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "EmailReason",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "EmailSent",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId")
                .Annotation("SqlServer:Include", new[] { "ReferenceClassificationId", "ICRCType", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "ImportId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.AddColumn<DateTime>(
                name: "EmailQueued",
                table: "ImportContractReferenceClassifications",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EmailReason",
                table: "ImportContractReferenceClassifications",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "EmailSent",
                table: "ImportContractReferenceClassifications",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_EmailSent_EmailReason_EmailQueued",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "EmailSent", "EmailReason", "EmailQueued" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId")
                .Annotation("SqlServer:Include", new[] { "ReferenceClassificationId", "ICRCType", "EmailQueued", "EmailSent", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "EmailReason", "ImportId" });
        }
    }
}
