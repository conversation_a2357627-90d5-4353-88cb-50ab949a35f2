using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policies.MasterAssessor)]
public class ClassificationController : BaseController
{
    private readonly IClassificationService _classificationService;
    private readonly IClassificationCategoryService _classificationCategoryService;
    private readonly ICountryService _countryService;
    private readonly IDashboardService _dashboardService;

    public ClassificationController(
        IClassificationService classificationService,
        IClassificationCategoryService classificationCategoryService,
        ICountryService countryService,
        IDashboardService dashboardService,
        IUserSessionService userSessionService,
        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _classificationService = classificationService;
        _classificationCategoryService = classificationCategoryService;
        _countryService = countryService;
        _dashboardService = dashboardService;
    }

    [HttpGet()]
    public async Task<IActionResult> Index()
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return View(new ClassifyReferenceModel());
        }

        var completedCount = await _classificationService.GetClassifiedCount(selectedImport.ImportId);
        var todoCount = await _classificationService.GetToDoCount(selectedImport.ImportId);
        var classificationCategories = await _classificationCategoryService.GetAllAsync();
        var countries = await _countryService.GetAllAsync();

        return View(new ClassifyReferenceModel()
        {
            SelectedImport = selectedImport,
            CompletedCount = completedCount,
            TodoCount = todoCount,
            ClassificationCategories = classificationCategories,
            Countries = countries
        });
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> GetAllPreClassified()
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return Json(new List<ReferenceClassificationWithReferenceModel>());
        }

        var classifications = await _classificationService.GetReferencesToClassify(selectedImport.ImportId);
        return Json(classifications);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> GetAllClassified()
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return Json(new List<ReferenceClassificationWithReferenceModel>());
        }

        var classifiedReferences = await _classificationService.GetAllClassified(selectedImport.ImportId);
        return Json(classifiedReferences);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Classify([FromBody] ReferenceClassificationModel model)
    {
        await _classificationService.Classify(model);
        return Ok();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> ReClassify([FromBody] ReferenceClassificationModel model)
    {
        await _classificationService.ReClassify(model);
        return Ok();
    }

    [HttpGet("[action]"), DigitalSignature]
    public async Task<IActionResult> Sign()
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            AddNotification("No import selected.", UserNotificationType.Failed);
            return RedirectToAction(nameof(Index));
        }
        else
        {
            var todoCount = await _classificationService.GetToDoCount(selectedImport.ImportId);
            if (todoCount > 0)
            {
                AddNotification("There are still classifications to do.", UserNotificationType.Failed);
                return RedirectToAction(nameof(Index));
            }

            var forClassificationCount = (await _classificationService.GetReferencesToClassify(selectedImport.ImportId)).Count();
            if (forClassificationCount > 0)
            {
                AddNotification("There are still classifications waiting for master approval.", UserNotificationType.Failed);
                return RedirectToAction(nameof(Index));
            }

            await _classificationService.Sign(selectedImport.ImportId);

            AddNotification("Classifications were signed successfully.", UserNotificationType.Confirm);
            return RedirectToAction(nameof(Index));
        }
    }
}