﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Utilities;

public class UrlRenderHelperTests
{
    private readonly Mock<IPubMedUrlRenderHelper> _pubMedUrlRenderHelper = new();
    private readonly IUrlRenderHelper _urlRenderHelper;
    private readonly Mock<ILogger<UrlRenderHelper>> _mockLogger = new();
    private readonly Mock<IWebsiteUriProvider> _websiteUriProvider = new();
    private const string WebsiteUri = "https://localhost:5001";

    public UrlRenderHelperTests()
    {

        _websiteUriProvider.Setup(x => x.Provide()).Returns(new Uri(WebsiteUri));

        var fakeFactory = new FakeLoggerFactory<UrlRenderHelper>(_mockLogger);
        _urlRenderHelper = new UrlRenderHelper(fakeFactory, _pubMedUrlRenderHelper.Object, _websiteUriProvider.Object);
    }

    [Fact]
    public void GetUrlLinkBasedOnSourceSystem_ForNonPubMedReturns_UrlWithId()
    {
        var sendGridTemplateModel = GetSendGridTemplate(SourceSystem.Manual);

        // Act
        var nonPubMedUrl = _urlRenderHelper.GetUrlLinkBasedOnSourceSystem(sendGridTemplateModel);

        // Assert
        Assert.Equal("https://localhost:5001/References/Classifications/458443", nonPubMedUrl);
    }

    [Fact]
    public void GetUrlLinkBasedOnSourceSystem_ForPubMedReturns_AbstractAndUrlConcatenated()
    {
        var sendGridTemplateModel = GetSendGridTemplate(SourceSystem.PubMed);
        var pubMedString =
            "Reference abstract <a href=\"https://pubmed.ncbi.nlm.nih.gov/12345\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ";
        _pubMedUrlRenderHelper.Setup(x => x.GetAbstractAndUrlConcatenated(It.IsAny<ReferenceModel>())).Returns(pubMedString);
        // Act
        var pubMedUrl = _urlRenderHelper.GetUrlLinkBasedOnSourceSystem(sendGridTemplateModel);

        // Assert
        Assert.Equal(pubMedString, pubMedUrl);
    }

    private static DailyClassificationEmailSendGridTemplateModelRow GetSendGridTemplate(SourceSystem sourceSystem)
    {

        var reference = new ReferenceSendGridTemplateModel
        {
            Abstract = "Reference abstract",
            Id = 200,
            SourceId = "12345",
            Title = "Reference title",
            SourceSystem = sourceSystem
        };

        var referenceClassification = new ReferenceClassificationSendGridTemplateModel
        {
            Id = 458443
        };

        return new DailyClassificationEmailSendGridTemplateModelRow(1, EmailReason.None, reference, referenceClassification);
    }
}