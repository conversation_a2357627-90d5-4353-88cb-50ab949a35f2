parameters:
- name: environments
  type: object
  default:
    - name: 'dev'
      azureSubscription: 'Vigilit_Development'

trigger: none

schedules:
- cron: "0 15 * * 0"
  displayName: Weekly Index Maintenance
  branches:
    include:
    - main
  always: true

pool: 'pv-windows-pool'

variables:
  nameOfPlatform: 'vgt'
  region: 'eun'

stages:
- ${{ each env in parameters.environments }}:
  - stage: Maintenance_${{ env.name }}
    displayName: 'Database Maintenance - ${{ env.name }}'
    dependsOn: []
    jobs:
      - job: DB_Maintenance
        displayName: 'Database Maintenance - ${{ env.name }}'
        steps:
        - task: SqlAzureDacpacDeployment@1
          displayName: 'Azure SQL SqlTask'
          inputs:
            azureSubscription: ${{ env.azureSubscription }}
            AuthenticationType: servicePrincipal
            ServerName: '${{ variables.nameOfPlatform }}-${{ env.name }}-sqlserver-${{ variables.region }}.database.windows.net'
            DatabaseName: '${{ variables.nameOfPlatform }}-${{ env.name }}-default-${{ variables.region }}'
            deployType: SqlTask
            SqlFile: 'recreateMsSqlProcedure.sql'
            IpDetectionMethod: 'AutoDetect'
        - task: SqlAzureDacpacDeployment@1
          displayName: 'Rebuild All Indexes'
          inputs:
            azureSubscription: ${{ env.azureSubscription }}
            AuthenticationType: servicePrincipal
            ServerName: '${{ variables.nameOfPlatform }}-${{ env.name }}-sqlserver-${{ variables.region }}.database.windows.net'
            DatabaseName: '${{ variables.nameOfPlatform }}-${{ env.name }}-default-${{ variables.region }}'
            deployType: InlineSqlTask
            SqlInline: |
              EXEC sp_MSforeachtable 'SET QUOTED_IDENTIFIER ON; ALTER INDEX ALL ON ? REBUILD'
              GO