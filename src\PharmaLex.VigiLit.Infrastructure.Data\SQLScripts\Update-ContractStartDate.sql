﻿
-- Set contract start date to be V6 timestamp for migrated contracts and CreatedDate for post-migrated contracts:
-- V6 contract dates
update Contracts
	set ContractStartDate = cv.TimeStamp
from Contracts c 
inner join ContractVersions cv on c.id = cv.ContractId
where cv.id in
(
	select min(id) from [dbo].[ContractVersions] where createdby = '<EMAIL>' group by contractid
)

-- V7 new contracts
update Contracts set ContractStartDate = CreatedDate where createdby <> '<EMAIL>'
