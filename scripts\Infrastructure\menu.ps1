. "..\Helpers\ContainerFunctions.ps1"
. "..\Helpers\DaemonFunctions.ps1"

do { 
	Write-Host Infrastructure Menu -ForegroundColor DarkCyan 
	Write-Host =================== -ForegroundColor DarkCyan 
	Write-Host A. -NoNewLine -ForegroundColor Cyan 
	Write-Host Build SQL Image 
	Write-Host B.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Restart SQL Container 
	Write-Host C.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Deploy db 
	Write-Host D.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Seed db
	Write-Host E.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Start Portainer 
	Write-Host F.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Start Azurite 
	Write-Host H.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Start RabbitMq
	Write-Host [Backspace].  -NoNewLine -ForegroundColor Cyan 
	Write-Host '<== Back to Main Menu' 
	
		$key = [Console]::ReadKey($true) 
	
	$keyChar = $key.KeyChar 
	
	if ($keyChar -eq 'a') 
	{ 
		CD .\Sql
		.\BuildSqlContainer.ps1 
		CD ..
	} 
	elseif ($keyChar -eq 'b') 
	{ 
		SwitchDaemon "linux"
		$containerName = "sqlserver"
		$dockerRunCommand = 'docker run --name sqlserver -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=Password1." -e "MSSQL_PID=Enterprise" -p 1433:1433 --restart=always -d pharmalex/mssql/server '
		ManageContainer -containerName $containerName -dockerRunCommand $dockerRunCommand
	}  
	elseif ($keyChar -eq 'c') 
	{ 
		CD .\Sql
		.\DeployDbProj.ps1 
		CD ..
	} 
	elseif ($keyChar -eq 'd') 
	{ 
		CD .\Sql
		.\SeedDb.ps1 
		CD ..
	} 
	elseif ($keyChar -eq 'e') 
	{ 
		.\Portainer\RunPortainer.ps1 
	} 
	elseif ($keyChar -eq 'f') 
	{ 
		SwitchDaemon "linux"
		$containerName = "azurite"
		$dockerRunCommand = "docker run --name azurite -d -p 10000:10000 -p 10001:10001 -p 10002:10002 -v c:/azurite:/data mcr.microsoft.com/azure-storage/azurite "
		ManageContainer -containerName $containerName -dockerRunCommand $dockerRunCommand
	} 
	elseif ($keyChar -eq 'h') 
	{ 
		SwitchDaemon "linux"
		CD .\RabbitMq
		docker-compose up -d
		CD ..
	} 

} until($key.Key -eq 'Backspace' )  
