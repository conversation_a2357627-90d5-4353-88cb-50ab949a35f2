﻿using Microsoft.AspNetCore.Http;
using System;
using PharmaLex.VigiLit.Domain;

namespace PharmaLex.VigiLit.Web.Helpers;

public class WebsiteUriProvider : IWebsiteUriProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public WebsiteUriProvider(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public Uri Provide()
    {
        var request = _httpContextAccessor.HttpContext?.Request;
        if (request == null)
            throw new InvalidOperationException("Could not obtain request from HTTP context.");

        return new ($"{request.Scheme}://{request.Host}");
    }
}
