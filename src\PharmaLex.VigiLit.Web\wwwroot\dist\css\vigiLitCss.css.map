{"version": 3, "file": "css/vigiLitCss.css", "mappings": ";;;AAAC;;;;;;;;CAAA;ACAA;AAID;AAkFA;AAcA;ACpGC;ACAA;;;EAGG;ACeJ;;ADZA;;;EAGI;EACA;ACeJ;;ADZA;EACI;EACA,cFmDI;AGpCR;;ADZA;EACI;ACeJ;;ADZA;EACI;ACeJ;;ADZA;EACI,oEF6Dc;AG9ClB;;ADZA;EACI;EACA;ACeJ;;ADZA;EACI;EACA;ACeJ;;ADZA;EACI;ACeJ;;ADZA;EACI;ACeJ;ADdI;EACI;ACgBR;;ADZA;EACI;ACeJ;;ADZA;EACI;EACA;ACeJ;;ADZA;EACI;EACA;ACeJ;;AC7EC;EACG;ADgFJ;;AC7EA;EACI;ADgFJ;;AC7EA;EACI;ADgFJ;;AC7EA;EACI;EACA;ADgFJ;;AC7EA;EACI;ADgFJ;;AC7EA;EACI;ADgFJ;;AC7EA;EACI;EACA;ADgFJ;AC7EQ;EACI;AD+EZ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI,mBJVe;AGuFnB;;AC1EA;;EAEI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;;AC1EA;EACI;AD6EJ;AC3EI;EACI;EACA;AD6ER;;ACzEA;EACI;MAAA;UAAA;AD4EJ;;ACzEA;EACI;EAAA;EAAA;EACA;EACA;AD4EJ;AC1EI;EACI;EACA;EACA;EACA;EACA;EACA,cJhEC;EIiED;AD4ER;AC1EQ;EACI;AD4EZ;AC1EY;EACI;AD4EhB;ACzEY;EACI,cJ1CR;EI2CQ;UAAA;AD2EhB;ACvEQ;EACI;EACA;EACA,cJnFH;AG4JT;ACvEY;EACI,cJrDR;AG8HR;ACrEQ;EACI,cJ1DJ;EI2DI;EACA;EACA;ADuEZ;ACrEY;EACI,cJ/DD;AGsIf;ACrEgB;EACI,cJnEZ;AG0IR;AClEQ;EACI;EACA;EACA;EACA;EACA;ADoEZ;ACjEQ;EACI;ADmEZ;AC/DI;EACI;EACA;EACA;EACA;EACA;EACA,WJ7FD;EI8FC,yBJ5FA;EI6FA;ADiER;AC/DQ;EACI,yBJhGJ;AGiKR;AC9DQ;EACI,WJtGL;AGsKP;AC5DI;EACI;AD8DR;AC3DY;EACI,cJ9IP;AG2MT;AC1DY;EACI,WJnHT;AG+KP;;ACtDA;EACI;ADyDJ;ACvDI;EACI;ADyDR;;ACrDA;EACI;EACA;EACA;ADwDJ;;ACrDA;EACI;EACA;EACA;ADwDJ;;ACrDA;EACI;EACA;ADwDJ;;ACrDA;EACI;EACA;EACA;EAAA;ADwDJ;ACtDI;EACI,cJtJA;EIuJA;UAAA;ADwDR;;ACpDA;EACI,cJ9LQ;EI+LR;ADuDJ;;ACpDA;EACI;ADuDJ;;ACpDA;EACI;ADuDJ;;ACpDA;EACI;ADuDJ;;ACpDA;EACI;EACA,yBJrOO;EIsOP;EACA;EACA;EACA;EACA;EACA;EACA;ADuDJ;;ACpDA;EACI;EACA;EACA;ADuDJ;;ACpDA;EACI;EACA;EACA;EACA;ADuDJ;;ACpDA;EACI;EACA;EACA;EACA;EACA;EACA;ADuDJ;;ACpDA;EACI;EACA;EACA;ADuDJ;;ACpDA;EACI;EACA;EACA;ADuDJ;ACrDI;EACI;EACA;MAAA;EACA,sBJ9ND;EI+NC;EACA;ADuDR;ACnDQ;EADJ;IAEQ;QAAA;EDsDV;AACF;;AClDA;EACI;ADqDJ;;AClDA;EACI;ADqDJ;;AClDA;EACI,mBJjPI;EIkPJ,WJpPG;EIqPH;EACA;ADqDJ;;AClDA;EACI;ADqDJ;;AClDA;EACI;KAAA;MAAA;UAAA;ADqDJ;ACnDI;EACI;ADqDR;AClDI;EACI;EACA,WJtQD;EIuQC;EACA;EACA;EACA;EACA,qBJpOO;EIqOP;ADoDR;AClDQ;EACI;ADoDZ;AC/CQ;EACI;UAAA;ADiDZ;AC9CQ;EACI;EACA;EACA;ADgDZ;;AC3CA;EACI;KAAA;MAAA;UAAA;EACA;AD8CJ;AC5CI;EACI;AD8CR;AC3CI;EACI;EACA,WJ1SD;EI2SC;EACA;EACA;EACA;EACA;EACA;AD6CR;AC3CQ;EACI;AD6CZ;ACxCQ;EACI;UAAA;AD0CZ;ACvCQ;EACI;EACA;EACA;EACA;ADyCZ;;ACrCA;AACA;;EAEI;EACA;EACA;EACA;EACA,WJ3UG;EI4UH;EACA;EACA;EACA,yBJ7UI;AGqXR;;ACrCA;EACI,yBJlXK;AG0ZT;;ACrCA;EACI,yBJlWS;EImWT;ADwCJ;;ACrCA;EACI,yBJjXM;AGyZV;;ACrCA;EACI,yBJxYQ;AGgbZ;;ACrCA;EACI,yBJ/WS;AGuZb;;ACrCA;EACI,yBJtWI;AG8YR;;ACrCA;EACI,yBJhaQ;AGwcZ;;ACrCA;EACI;EACA;EACA;ADwCJ;;ACpCA;EACI;EACA,gBJxXG;AG+ZP;ACrCI;EACI;ADuCR;ACpCI;EACI;ADsCR;ACnCI;EACI,cJjYA;AGsaR;;ACjCA;EACI;EAAA;EAAA;EACA;EACA;EACA;ADoCJ;AClCI;EACI;ADoCR;ACjCI;EACI;ADmCR;AChCI;EACI;ADkCR;AC/BI;EACI;ADiCR;AC9BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA,cJjaO;EIkaP;ADgCR;AC7BI;;EAEI;EACA;AD+BR;;ACzBI;EACI;AD4BR;ACzBI;EACI;AD2BR;ACxBI;EACI;AD0BR;ACvBI;EACI;MAAA;UAAA;EACA;ADyBR;ACtBI;EACI;ADwBR;ACrBI;EACI;ADuBR;;ACnBA;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADsBJ;ACpBI;EACI;EAAA;EAAA;EACA;ADsBR;ACpBQ;EACI;MAAA;UAAA;ADsBZ;ACnBQ;;EAEI;MAAA;UAAA;ADqBZ;;ACfA;EACI;EAAA;EAAA;EACA;MAAA;EACA;EACA;EACA;ADkBJ;AChBI;EACI;ADkBR;ACfI;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;ADiBR;ACfQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;ADiBZ;ACfY;EACI;MAAA;UAAA;ADiBhB;ACdY;EACI;MAAA;UAAA;ADgBhB;ACZQ;EACI;EACA;EACA;EACA;ADcZ;ACZY;EACI;ADchB;ACTI;EACI;MAAA;UAAA;EACA;ADWR;ACTQ;EACI;ADWZ;ACRQ;EACI;EACA;ADUZ;;ACLA;EACI;MAAA;UAAA;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADQJ;ACNI;EACI;ADQR;ACLI;EACI;MAAA;UAAA;ADOR;ACLQ;EACI;ADOZ;ACHI;EACI;EAAA;EAAA;ADKR;ACHQ;EACI;MAAA;UAAA;ADKZ;ACFQ;EACI;MAAA;UAAA;EACA;ADIZ;ACFY;EACI;EACA;EACA;EACA,yBJ7mBJ;EI8mBI;EACA,WJ3jBT;EI4jBS;EACA;EACA;EACA;ADIhB;ACFgB;EACI;EACA;EACA;UAAA;ADIpB;ACEY;EACI;ADAhB;ACEgB;EACI;ADApB;ACMI;EACI;EACA;EACA;EACA;ADJR;ACOI;EACI;ADLR;ACQI;EACI;EACA;EACA;ADNR;ACSI;EACI;ADPR;ACUI;EACI;ADRR;ACWI;EACI;ADTR;ACcQ;EACI;ADZZ;ACeQ;EACI;ADbZ;ACgBQ;EACI;ADdZ;ACiBQ;EACI;ADfZ;ACkBQ;EACI;ADhBZ;ACmBQ;EACI;ADjBZ;ACoBQ;EACI;ADlBZ;ACqBQ;EACI;EACA;EACA;EACA;ADnBZ;ACqBY;EACI;EACA;EACA;EACA;ADnBhB;ACsBY;EACI;EAAA;EAAA;ADpBhB;ACsBgB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADpBpB;ACsBoB;EACI;EACA;ADpBxB;ACuBoB;EACI;EACA;EACA;EACA;ADrBxB;ACuBwB;EACI;ADrB5B;ACyBoB;EACI;ADvBxB;ACyBwB;EACI;EACA;EACA;ADvB5B;AC0BwB;EACI;ADxB5B;AC6BgB;EACI;EACA;EACA;EACA;EACA;AD3BpB;ACgCQ;EACI;EACA;AD9BZ;ACgCY;EACI;AD9BhB;ACiCY;EACI;EACA;EACA;EACA;AD/BhB;ACkCY;EACI;ADhChB;ACkCgB;EACI;EACA;ADhCpB;ACoCY;EACI;EACA;ADlChB;ACsCQ;EACI;EACA;ADpCZ;ACsCY;EACI;ADpChB;ACuCY;EACI;ADrChB;ACyCQ;EACI;EACA;ADvCZ;AC0CQ;EACI;EACA;ADxCZ;AC4CI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD1CR;AC4CQ;EACI;EACA;EACA;EACA;AD1CZ;AC6CQ;EACI;AD3CZ;AC6CY;EACI;EACA;EACA;AD3ChB;AC6CgB;EACI;AD3CpB;ACiDI;EACI;EACA;EACA;AD/CR;;ACmDA;EACI,WJhzBG;AGgwBP;;ACmDA;EACI;EACA;EAAA;EAAA;ADhDJ;;ACmDA;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;ADhDJ;;ACmDA;EACI;EACA;ADhDJ;ACkDI;EACI;ADhDR;ACmDI;EACI;EACA;EACA;ADjDR;ACoDI;EACI;EACA;EACA;EACA;EAAA;EAAA;ADlDR;ACoDQ;EACI;ADlDZ;ACqDQ;EACI;EACA;UAAA;ADnDZ;ACqDY;EACI;ADnDhB;ACuDQ;EACI;ADrDZ;ACyDI;EACI;EACA;EACA;EACA;ADvDR;AC0DI;EACI;EAAA;EAAA;EACA;MAAA;EACA;MAAA;UAAA;ADxDR;;AC4DA;EACI;EACA;EAAA;ADzDJ;;ACgEY;EACI;MAAA;UAAA;AD7DhB;ACgEY;EACI;AD9DhB;ACkEQ;EACI;EACA;EACA;ADhEZ;ACoEoB;EACI;ADlExB;ACqEoB;EACI,yBJ36Bf;AGw2BT;;AC6EQ;EACI;AD1EZ;AC8EI;EACI;AD5ER;;ACgFA;EACI;AD7EJ;;ACgFA;EACI,WJz6BG;EI06BH;EACA;EACA;AD7EJ;;ACgFA;EACI,yBJn8BE;AGs3BN;;ACgFA;EACI,yBJ57BS;AG+2Bb;;ACgFA;EACI,yBJv9BK;AG04BT;;ACgFA;EACI,cJ/8BE;AGk4BN;;ACgFA;EACI,cJx8BS;AG23Bb;;ACgFA;EACI,cJn+BK;AGs5BT;;ACiFI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD9ER;;ACmFA;EACI;ADhFJ;;ACmFA;EACI;ADhFJ;;ACmFA;EACI;ADhFJ;;ACmFA;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADhFJ;ACkFI;EACI;ADhFR;ACkFQ;EACI;ADhFZ;ACmFQ;EACI;ADjFZ;ACmFY;EACI;ADjFhB;ACuFY;EACI;EACA;EACA;ADrFhB;ACuFgB;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADrFpB;ACuFoB;EACI;EACA;EACA;EACA;EACA;ADrFxB;ACuFwB;EACI;EAAA;EAAA;EACA;MAAA;EACA;EACA;EACA;ADrF5B;ACuF4B;EACI;EACA;ADrFhC;ACuFgC;EACI;EACA;EACA;ADrFpC;ACuFoC;EACI,cJ/kC5B;AG0/BZ;ACwFoC;EACI,cJ7hChC;AGu8BR;AC0FgC;EACI;EACA;EACA;EACA;ADxFpC;AC2FgC;EACI;EACA;EACA;ADzFpC;AC6F4B;EACI;EACA;AD3FhC;AC6FgC;EACI;EACA;EACA;EACA;AD3FpC;AC8FgC;EACI;EACA;EACA,cJlnCzB;AGshCX;ACiGwB;EACI;EAAA;EAAA;EACA;MAAA;EACA;EACA;EACA;EACA;EACA;AD/F5B;ACiG4B;EACI;AD/FhC;ACiGgC;EACI;AD7FpC;AC4FgC;EAEI;EACA;UAAA;AD/FpC;ACmG4B;EACI;EACA;ADjGhC;ACoG4B;EACI;EACA;ADlGhC;ACoGgC;EACI;ADlGpC;ACqGgC;EACI;EACA;EACA;ADnGpC;ACqGoC;EACI;EACA;EACA;UAAA;ADnGxC;ACsGoC;EACI;EACA;ADpGxC;AC0GwB;EACI;ADxG5B;AC0G4B;EACI;EACA;ADxGhC;AC2GoC;EACI;ADzGxC;AC8G4B;EACI;AD5GhC;AC8GgC;EACI;AD5GpC;ACoHoB;EACI;ADlHxB;ACyHI;EACI;EACA;ADvHR;AC2HY;EACI;EACA;EACA;ADzHhB;AC2HgB;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADzHpB;AC2HoB;EACI;EACA;EACA;EACA;EACA;EACA;ADzHxB;AC2HwB;EACI;EACA;ADzH5B;AC4HwB;EACI;EACA;EACA;AD1H5B;AC+HgB;EACI;EACA;AD7HpB;AC+HoB;EACI;AD7HxB;AC+HwB;EACI;AD7H5B;ACgIwB;EACI;AD9H5B;ACkIoB;EACI;EACA;ADhIxB;ACsIQ;EACI;ADpIZ;ACsIY;EACI;EACA;EACA;ADpIhB;ACuIY;EACI;ADrIhB;;AC2IA;EA0FI;ADjOJ;ACyII;EACI;EACA;EACA;ADvIR;ACyIQ;EACI,cJ7uCJ;AGsmCR;AC2II;EACI;EACA;EACA;EACA;ADzIR;AC2IQ;EACI;ADzIZ;AC4IQ;EACI;EACA;EACA;AD1IZ;AC8II;EACI;EACA;EACA;AD5IR;AC+II;EACI;EACA;AD7IR;AC+IQ;EACI;AD7IZ;ACgJQ;EACI;AD9IZ;ACkJI;EACI;ADhJR;ACkJQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;ADhJZ;ACkJY;EACI;MAAA;ADhJhB;ACqJI;EACI;EACA;EACA;ADnJR;ACqJQ;EACI;ADnJZ;ACsJQ;EACI;ADpJZ;ACuJQ;EACI;EACA;EACA;ADrJZ;ACwJQ;EACI;MAAA;UAAA;EACA;ADtJZ;ACwJY;EACI;MAAA;ADtJhB;AC4JI;EACI,yBJ/0CK;AGqrCb;;AC8JA;EAcI;ADxKJ;AC2JI;EACI;EACA;ADzJR;AC4JI;EACI;EACA;AD1JR;AC6JI;EACI;AD3JR;AC8JI;EACI;EACA;EACA;AD5JR;ACgKQ;EACI;AD9JZ;ACkKI;EACI;EACA;EACA;EACA;EAAA;ADhKR;;ACqKI;EACI;EAAA;EACA;EACA;ADlKR;ACoKQ;EACI;EACA;EACA;EAAA;KAAA;UAAA;EACA;EAAA;ADlKZ;ACoKY;EANJ;IAOQ;EDjKd;AACF;ACmKY;EACI;EACA;EACA;EACA;EACA;ADjKhB;ACmKgB;EACI;ADjKpB;ACoKgB;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;EACA;ADlKpB;ACoKoB;EACI;EACA;EACA;ADlKxB;ACqKoB;EACI;EACA;EACA;ADnKxB;ACqKwB;EACI,yBJh6Cf;EIi6Ce,cJr4CrB;AGkuCP;ACqK4B;EACI,yBJ37CvB;AGwxCT;AC2KQ;EACI;ADzKZ;;AC8KA;EACI;EACA;EACA;AD3KJ;AC6KI;EACI;EACA;EACA;EACA;AD3KR;AC8KI;EACI;EACA;EACA;EACA;AD5KR;;ACgLA;EACI;EACA;EACA;AD7KJ;AC+KI;EACI;EACA;EACA;EACA;AD7KR;ACgLI;EACI;EACA;EACA;EACA;EACA;AD9KR;;ACkLA;EACI;EACA;EACA;AD/KJ;ACiLI;EACI;EACA;EACA;EACA;EACA;EACA;AD/KR;ACkLI;EACI;EACA;EACA;EACA;EACA;ADhLR;;ACoLA;EACI;EACA;EACA;ADjLJ;ACmLI;EACI;EACA;EACA;EACA;ADjLR;ACoLI;EACI;EACA;EACA;EACA;ADlLR;;ACsLA;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;EACA;EACA;MAAA;UAAA;ADnLJ;ACqLI;EACI;ADnLR;ACsLI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAA;ADpLR;ACsLQ;EACI;ADpLZ;;ACyLA;AAEA;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;EACA;EACA;EACA;ADvLJ;;AC0LA;EACI;ADvLJ;;AC0LA;EACI;EACA;EACA;ADvLJ;ACyLI;EACI;ADvLR;AC0LI;EACI;ADxLR;;AC4LA;EACI;ADzLJ;;AC4LA;EACI;EAAA;EAAA;EACA;EACA;EAAA;EAAA;ADzLJ;;AC4LA;EACI;MAAA;EACA;ADzLJ;;AC4LA;EACI;EACA;EACA;EACA;UAAA;EACA;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;EACA;ADzLJ;;AC4LA;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADzLJ;;AC4LA;EACI;EACA;ADzLJ;;AC4LA;EACI;EACA;ADzLJ;;AC4LA;EACI;ADzLJ;;AC4LA;;;EAGI;EACA;EACA;ADzLJ;;AC4LA;EACI;EAAA;EAAA;EACA;EACA;ADzLJ;;AC4LA;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;EACA;MAAA;EACA;ADzLJ;;AC6LA;EACI;AD1LJ;;AC6LA;EACI;AD1LJ;;AC6LA;EACI;AD1LJ;;AC6LA;EACI;EACA;EACA;EACA;EACA;AD1LJ;;AC6LA;EACI;EACA;EACA;EACA;AD1LJ;;AC6LA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD1LJ;;AC6LA;EACI;EACA;EAAA;EAAA;EACA;MAAA;UAAA;EACA;EACA;MAAA;UAAA;EACA;EACA;EACA;AD1LJ;AC4LI;EACI;AD1LR;;AC8LA;EACI;EACA;EACA;EAAA;EAAA;EACA;EACA;MAAA;UAAA;AD3LJ;AC6LI;EACI;EACA;AD3LR;AC8LI;EACI;EACA;AD5LR;AC+LI;EACI;EACA;AD7LR;;ACiMA;EACI;AD9LJ;;ACiMA;EACI;AD9LJ;;ACkMI;EACI;EACA;AD/LR;ACkMI;EACI;EACA;EACA;ADhMR;;ACoMA;EACI;ADjMJ;;ACoMA;AACA,gBACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADjMJ;;ACoMA;;;EAGI;EAGA;ADjMJ;;ACoMA;EACI;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;ADjMJ;;ACoMA;EACI;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;UAAA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;EACA;ADjMJ;;ACoMA;EACI;EACA;EACA;EACA;EACA;ADjMJ;AC4NA;EACI;IACI;EDtMN;ECyME;IACI;EDvMN;EC0ME;IACI;EDxMN;EC2ME;IACI;IACA;EDzMN;EC4ME;IACI;IACA;ED1MN;AACF;AC6MA;EACI;EACA;AD3MJ;;AC8MA;EACI;EACA;AD3MJ;;AC8MA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD3MJ;;AC8MA;EACI;EACA;EACA;AD3MJ;;AC8MA;EACI;EACA;AD3MJ;;AC8MA;EACI;AD3MJ;;AC8MA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AD3MJ;;AC8MA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD3MJ;;AC8MA;EACI;AD3MJ;;AC8MA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD3MJ;;AC8MA;EACI;AD3MJ;;AC8MA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AD3MJ;;AC8MA;EACI;AD3MJ;;AC8MA;EACI;EACA;EAAA;EAAA;EACA;EACA;MAAA;UAAA;EACA;AD3MJ;AC6MI;EACI;AD3MR;AC8MI;EACI,oBJn8DO;AGuvDf;AC+MI;EACI;EAAA;EACA;EACA;EACA;KAAA;MAAA;UAAA;EACA;AD7MR;;ACiNA;EACI;EACA;EACA;EACA;EAAA;AD9MJ;;ACiNA;EACI;EACA;EACA;EACA,WJjgEG;EIkgEH;AD9MJ;;ACiNA;EACI,yBJ9iEQ;AGg2DZ;;ACiNA;EACI,yBJ9hEO;AGg1DX;;ACoNQ;EACI,cJhjEH;AG+1DT;ACoNQ;EACI,cJnhEJ;AGi0DR;;ACyNQ;EACI;EACA;ADtNZ;;AC4NI;EACI;ADzNR;AC4NY;EACI;EACA;EACA;EACA;AD1NhB;AC4NgB;EACI;EACA;EACA;AD1NpB;AC6NgB;EACI;EACA;EAAA;EAAA;EACA,WJrgET;AG0yDX;ACiOY;EACI;AD/NhB;;ACoPI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;ADjPR;ACoPI;EACI;EACA;EACA;ADlPR;AC6PQ;EAtCJ,WA+BmB;EA9BnB,YA8BmB;EA7BnB;EACA;UAAA;EAqCQ;EACA;EACA;ADxPZ;AC4PY;EACI;EACA;EAhDZ,WA+BmB;EA9BnB,YA8BmB;EA7BnB;EACA;UAAA;EAIA;EACA;EACA;EACA;EAyCY;EACA;EACA;ADpPhB;ACuPY;EACI;EACA;EA1DZ,WA+BmB;EA9BnB,YA8BmB;EA7BnB;EACA;UAAA;EAIA;EACA;EACA;EACA;EAmDY;EACA;EACA;EACA;UAAA;AD/OhB;ACmPQ;EACI;IACI;YAAA;EDjPd;ECoPU;IACI;YAAA;EDlPd;AACF;AC2OQ;EACI;IACI;YAAA;EDjPd;ECoPU;IACI;YAAA;EDlPd;AACF;ACwPQ;EACI;ADtPZ;ACyPQ;EACI;MAAA;UAAA;ADvPZ;AC0PQ;EACI;ADxPZ;AC2PQ;EACI;ADzPZ;AC4PQ;EACI;EACA;EAAA;AD1PZ;;ACgQI;EACI;EACA;EACA;EACA;EACA;AD7PR;AC+PQ;EACI;AD7PZ;;ACmQI;EACI;ADhQR;ACkQQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADhQZ;ACkQY;EACI;EACA;EACA;EACA;EACA;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADhQhB;ACkQgB;EACI;EACA;MAAA;UAAA;ADhQpB;ACkQoB;EACI;EACA;EACA;EACA;ADhQxB;ACoQgB;EACI;EACA;ADlQpB;ACoQoB;EACI;ADlQxB;;AC4QI;EACI;ADzQR;AC2QQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADzQZ;AC2QY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADzQhB;AC2QgB;EACI;MAAA;UAAA;ADzQpB;AC2QoB;EACI;EACA;EACA;EACA;ADzQxB;AC6QgB;EACI;EACA;AD3QpB;AC6QoB;EACI;AD3QxB;ACiRQ;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;AD/QZ;ACkRQ;EACI;EACA;EACA;EACA;ADhRZ;ACmRQ;EACI;EACA;EACA;EACA;ADjRZ;ACoRQ;EACI;EACA;EACA;ADlRZ;ACqRQ;EACI;EACA;EACA;ADnRZ;ACsRQ;EACI;EACA;ADpRZ;ACuRQ;EACI;EACA;EACA;ADrRZ;ACwRQ;EACI;EACA;EACA;EACA;EACA;EACA;ADtRZ;ACyRQ;EACI;EACA;EACA;EACA;EACA;EACA;ADvRZ;AC0RQ;EACI;EACA;ADxRZ;AC2RQ;EACI;EACA;EACA;EACA;EACA;ADzRZ;AC4RQ;EACI;EAAA;EAAA;EACA;AD1RZ;AC6RQ;EACI;EAAA;EAAA;EACA;EACA;AD3RZ;AC8RQ;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;AD5RZ;AC+RQ;EACI;EACA;AD7RZ;ACgSQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AD9RZ;;ACmSA;EACI;EACA;EACA;EACA;EACA;ADhSJ;;ACmSA;EACI;EACA;EACA;EACA;EACA;EACA;ADhSJ;;ACmSA;EACI;EACA;ADhSJ;;ACmSA;EACI;EAAA;ADhSJ;;ACmSA;EACI;EACA;EAAA;MAAA;UAAA;EACA;EACA;EACA;EACA;UAAA;EACA;ADhSJ;ACkSI;EACI;EAAA;ADhSR;;ACoSA;EACI;EACA;ADjSJ;;ACoSA;EACI;ADjSJ;;ACoSA;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;ADjSJ;;ACoSA;EACI;EACA;ADjSJ;;ACoSA;EACI;EACA;ADjSJ;;ACqSI;EACI;ADlSR;ACoSQ;EACI;EACA;EACA;ADlSZ;ACoSY;EACI;ADlShB;ACqSY;EACI;ADnShB;ACuSQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;ADrSZ;ACuSY;EACI;EACA;EACA;ADrShB;ACuSgB;EACI;EACA;EACA;EACA;ADrSpB;ACuSoB;EACI;EACA;EACA;ADrSxB;ACwSoB;EACI;EACA;EACA;ADtSxB;AC0SgB;EACI;EACA;EACA;EACA;EACA;ADxSpB;AC0SoB;EACI;EACA;ADxSxB;AC6SoB;EACI;AD3SxB;AC6SwB;EACI;AD3S5B;AC+SoB;EACI;AD7SxB;ACkToB;EACI;ADhTxB;ACkTwB;EACI;ADhT5B;ACoToB;EACI;ADlTxB;ACoTwB;EACI;ADlT5B;;AC6TI;EACI;AD1TR;;AC+TI;EACI;AD5TR;AC8TQ;EACI;EACA;EACA;AD5TZ;AC8TY;EACI;AD5ThB;ACgUQ;EACI;EAAA;EAAA;EACA;MAAA;EACA;KAAA;UAAA;EACA;AD9TZ;ACgUY;EACI;EACA;EACA;AD9ThB;ACgUgB;EACI;EACA;EACA;EACA;AD9TpB;ACgUoB;EACI;EACA;EACA;AD9TxB;ACiUoB;EACI;EACA;EACA;AD/TxB;ACmUgB;EACI;EACA;EACA;EACA;EACA;ADjUpB;ACmUoB;EACI;EACA;ADjUxB;ACsUoB;EACI;ADpUxB;ACsUwB;EACI;ADpU5B;ACwUoB;EACI;ADtUxB;;ACgVQ;EACI;AD7UZ;;ACkVA;;;;EAII,qBJ1pFK;EI2pFL,cJ3pFK;AG40ET;;ACkVA;EACI;EACA;AD/UJ;;ACoVI;EACI;EAAA;EAAA;ADjVR;ACoVI;EACI;EACA;EACA;ADlVR;ACqVI;EACI;MAAA;UAAA;ADnVR;ACuVQ;EACI;EACA;ADrVZ;ACuVY;EACI;ADrVhB;ACwVY;EACI;EACA;ADtVhB;AC0VQ;EACI;EAAA;ADxVZ;AC6VQ;EACI;EACA;AD3VZ;AC6VY;EACI;EACA;EACA;AD3VhB;ACkWY;EACI;EACA;ADhWhB;ACmWY;EACI;EACA;EACA;ADjWhB;ACqWQ;EACI;ADnWZ;ACsWY;EACI;ADpWhB;ACyWI;EACI;EACA;EACA;ADvWR;ACyWQ;EACI;ADvWZ;;AC6WI;EACI;EACA;AD1WR;;AC+WI;EACI,yBJvxFI;EIwxFJ;AD5WR;AC8WQ;EACI,yBJnwFH;AGu5ET;AC+WQ;EACI;EACA;EACA;AD7WZ;;ACmXI;EACI;EACA;ADhXR;ACmXI;EACI;EACA;EACA,yBJ1wFI;EI2wFJ;EACA;EACA;ADjXR;ACoXI;EACI;EACA;EACA;EACA;EACA;ADlXR;ACsXI;EACI;EACA;EACA;ADpXR;ACsXQ;EACI;EACA;ADpXZ;ACuXQ;EACI;EACA;ADrXZ;;AC0XA;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;ADvXJ;ACyXI;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;EACA;ADvXR;AC0XI;EACI;ADxXR;;AEh/EA;;EAAA;AAGA;;;;EAAA;AAKA;AAEA;EACI;AFk/EJ;;AE/+EA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AFk/EJ;;AE/+EA;EACI;AFk/EJ;;AE/+EA;EACI;EAGA;EACA;EAEA;EAAA;EAAA;AFk/EJ;;AE/+EA;EACI;EAGA;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EACI;EACA;EACA;AFk/EJ;;AE/+EA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AFk/EJ;;AE/+EA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EACI;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EAGI;AFk/EJ;;AEr/EA;EACI;EACA;AFm/EJ;;AE/+EA;EACI;EACA;EACA;AFk/EJ;;AE/+EA;EACI;EACA;AFk/EJ;;AE/+EA;EACI;AFk/EJ;;AE/+EA;EACI;AFk/EJ;;AE/+EA;EACI;EACA;EACA;EACA;EACA;AFk/EJ;;AE/+EA;EACI;IACI;IACA;EFk/EN;EE/+EE;IACI;IACA;EFi/EN;EE9+EE;IACI;EFg/EN;AACF;AE7+EA;EACI;IACI;EF++EN;AACF;AE5+EA;;;;;;EAMI;EACA;AF8+EJ;;AE3+EA;;;EAGI;AF8+EJ;;AE3+EA;EACI;EACA;EACA;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;EACA;EACA;EACA;EACA;EACA;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AE3+EA;EACI;AF8+EJ;;AGvsFA;AACA;EACI;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA,sBN2DG;EM1DH;MAAA;UAAA;EACA;EACA;KAAA;MAAA;UAAA;EACA;EACA;EACA;EACA;AH0sFJ;AGxsFI;EACI;AH0sFR;AGvsFI;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;EACA;MAAA;UAAA;EACA;EACA;AHysFR;AGvsFQ;EACI;EACA;MAAA;EACA;EACA;EACA;EACA;EACA;EACA,WN5BJ;AGquFR;AGtsFQ;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;EACA;EACA;EACA,yBNsBJ;EMrBI,WNmBL;EMlBK;EACA;EACA;AHwsFZ;AGrsFQ;EACI;EACA;EACA;AHusFZ;AGnsFI;EACI;EAAA;EAAA;EACA;MAAA;EACA;EAAA;MAAA;UAAA;EACA;EACA;EACA;AHqsFR;AGlsFI;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;AHosFR;AGlsFQ;EACI,yBNtCH;EMuCG;EACA;AHosFZ;AGjsFQ;EACI;AHmsFZ;AGjsFY;EACI,gBNnBT;AGstFP;AG/rFQ;EACI;EACA;EACA;EACA;AHisFZ;AG9rFQ;EACI;EACA;EACA;EACA,WN5FJ;AG4xFR;AG7rFQ;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;AH+rFZ;AG3rFI;EACI;AH6rFR;AG1rFI;EACI;AH4rFR;AGzrFI;EACI;AH2rFR;AGzrFQ;EACI;AH2rFZ;AGvrFI;EACI;EACA;AHyrFR;AGvrFQ;EACI;AHyrFZ;AGtrFQ;EACI;AHwrFZ;;AGnrFA;AACA;EACI;EACA;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA,sBN/EG;EMgFH;MAAA;UAAA;EACA;EACA;KAAA;MAAA;UAAA;EACA;EACA;EACA;EACA;AHsrFJ;AGprFI;EACI;AHsrFR;AGnrFI;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;EACA;EACA;AHqrFR;AGnrFQ;EACI;EACA;MAAA;EACA;EACA;EACA;EACA;AHqrFZ;AGlrFQ;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;EACA;EACA;EACA,yBNjHJ;EMkHI,WNpHL;EMqHK;EACA;EACA;AHorFZ;AGhrFI;EACI;EAAA;EAAA;EACA;MAAA;EACA;EAAA;MAAA;UAAA;EACA;EACA;EACA;AHkrFR;AG/qFI;EACI;EAAA;EAAA;EACA;EAAA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;AHirFR;AG/qFQ;EACI;AHirFZ;AG/qFY;EACI,gBN9IT;AG+zFP;AG7qFQ;EACI;EACA;EACA;EACA;AH+qFZ;AG5qFQ;EACI;EACA;AH8qFZ;AG3qFQ;EACI;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;AH6qFZ;AGzqFI;EACI;AH2qFR;AGxqFI;EACI;AH0qFR;AGvqFI;EACI;AHyqFR;AGvqFQ;EACI;AHyqFZ;;AGpqFA;EACI;EACA;EAAA;EAAA;EACA;MAAA;UAAA;EACA;MAAA;UAAA;EACA;EACA;AHuqFJ,C", "sources": ["webpack://PharmaLex.VigiLit.v2/./src/scss/_CODEGEN.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/_variables.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/_application-variables.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/application-typography.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/index.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/application.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/modal.scss", "webpack://PharmaLex.VigiLit.v2/./src/scss/components.scss"], "sourcesContent": ["﻿/*\r\n\r\n\r\n    YOU ARE VIEWING THE OUTPUT CSS FROM A CODE-GENERATION (VIA WEBPACK) PROCESS\r\n    \r\n    PLEASE DO NOT MODIFY THIS FILE AS CHANGES WILL BE OVERWRITTEN\r\n\r\n\r\n*/\r\n\r\n", "﻿/*Units*/\r\n\r\n$default-unit: 1rem!default;\r\n\r\n/*Colours*/\r\n\r\n$black: #000!default;\r\n\r\n$blue-900: #062239!default;\r\n$blue-800: #0A3150!default;\r\n$blue-700: #043E67!default;\r\n$blue-600: #005C98!default;\r\n$blue-dark: #0073BE!default;\r\n$blue-400: #0391D7!default;\r\n$blue: #00B4E6!default;\r\n$blue-disabled: #B3D5EC;\r\n$blue-alt: #80DEFF !default;\r\n$blue-extra-light: #B7E8FE!default;\r\n$blue-ultra-light: #E7F7FF!default;\r\n\r\n$green-900: #0B2517!default;\r\n$green-800: #0C3722!default;\r\n$green-700: #09442A!default;\r\n$green-dark: #095F3B!default;\r\n$green-500: #007F50!default;\r\n$green-400: #119D63!default;\r\n$green-300: #06C07A!default;\r\n$green: #00DC8C!default;\r\n$green-light: #8AF6BB!default;\r\n$green-50: #D8FEE7!default;\r\n$green-alt: #B3D9CB!default;\r\n\r\n$grey-dark: #1E1E1E;\r\n$grey-1: #3B3B3B!default;\r\n$grey-2: #8A8A8A!default; \r\n$grey-3: #CACACA!default;\r\n$grey-4: #E8E8E8!default;\r\n$grey-5: #F5F5F5!default;\r\n$grey-6: #f1f1f1!default;\r\n$grey-ultra-light: #F5F5F5!default; \r\n\r\n$red-900: #420F03!default;\r\n$red-800: #5D1602!default;\r\n$red-700: #761701!default;\r\n$red-dark: #9E2305!default;\r\n$red: #E22F00!default;\r\n$red-400: #F35439!default;\r\n$red-300: #F8866D!default;\r\n$red-200: #FDAC9A!default;\r\n$red-light: #FCD9D1!default;\r\n$red-50: #FEEFEB!default;\r\n\r\n$yellow-900: #A04223!default;\r\n$yellow-800: #C2551F!default;\r\n$yellow-700: #DD7005!default;\r\n$yellow-dark: #FFA400!default;\r\n$yellow-500: #FBC618!default;\r\n$yellow-400: #FBE12A!default;\r\n$yellow: #FAEB1E!default;\r\n$yellow-200: #FBF175!default;\r\n$yellow-100: #FFF8BA!default;\r\n$yellow-50: #FFFCDB!default;\r\n\r\n\r\n$white:#fff!default;\r\n\r\n$brand: #461E96!default;\r\n$brand-darker: #211359 !default;\r\n$completed: $blue-dark;\r\n$quality: $blue-dark;\r\n$reg: $brand;\r\n$safety: $red-700;\r\n$stats: $yellow-800;\r\n$sub-brand: $brand!default;\r\n\r\n\r\n$neutral: $grey-3!default;\r\n\r\n$success: $green-500;\r\n$warning: $yellow-500;\r\n$error: $red;\r\n$active: $green-400;\r\n$active-alt: $yellow-800;\r\n$active-alt-secondary: $red-dark;\r\n$text: #1E1E1E!default;\r\n\r\n/*Fonts*/\r\n\r\n$cencora-regular: 'cencora-gilroy', Arial, Helvetica, Verdana, sans-serif;\r\n$cencora-bold: 'cencora-gilroy-bold', Arial, Helvetica, Verdana, sans-serif;\r\n$main-font: Arial, Helvetica, Verdana, sans-serif;\r\n\r\n$h1-font-size: 1.875rem!default;\r\n$h2-font-size: 1.5rem!default;\r\n$h3-font-size: 1rem!default;\r\n$h4-font-size: .8rem!default;\r\n$paragraph-font-size: .85rem!default;\r\n$table-header-font-size: .85rem!default;\r\n$table-cell-font-size: .85rem!default;\r\n\r\n/*Space*/\r\n\r\n$margin-unit: .5rem!default;\r\n$padding-unit: .5rem!default;\r\n\r\n$radius-small: 0.25rem;\r\n$radius: 0.375rem;\r\n$radius-large: 0.5rem;\r\n\r\n$space-XXL: 3rem;\r\n$space-XL: 2rem;\r\n$space-L: 1.5rem;\r\n$space-M: 1rem;\r\n$space-S: 0.75rem;\r\n$space-XS: 0.5rem;\r\n$space-XXS: 0.25rem;\r\n$space-XXXS: 0.125rem;\r\n", "﻿/*This will apply the quality color to the brand variable, globally (within the context of this application).*/\r\n$sub-brand: $safety!global;\r\n\r\n\r\n", "﻿html, body, p, select, input, th, td, label,\r\n.pager, .no-records-container, button, a.button, .core-content ul li,\r\n.autocomplete .autocomplete-items li {\r\n    font-weight: normal;\r\n}\r\n\r\ninput[type=text], input[type=url], input[type=email], input[type=search],\r\ninput[type=number], input[type=tel], input[type=date], input[type=password],\r\ntextarea, select, .fake-input, .fake-textarea {\r\n    font: 13px $main-font;\r\n    font-weight: normal;\r\n}\r\n\r\nspan.preClassifierName {\r\n    font-size: 17px;\r\n    color: $brand;\r\n}\r\n\r\nul.tabs li span.tab-badge {\r\n    font-size: 12px;\r\n}\r\n\r\nth, label, .chip {\r\n    font-weight: bold;\r\n}\r\n\r\n.page-header, .sub-header {\r\n    font-family: $cencora-regular;\r\n}\r\n\r\n.sub-header h2 { \r\n    font-size: .875rem;\r\n    font-weight: 500; \r\n}\r\n\r\nh2.dashboard {\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.core-container > header a.app-title {\r\n    min-width: 125px;\r\n}\r\n\r\n.nav-menu > li {\r\n    font-size: 17px;\r\n    a {\r\n        color: #1F394A;\r\n    }\r\n}\r\n\r\n.nav-menu > li .flyout li {\r\n    font-size: 17px;\r\n}\r\n\r\nsub {\r\n    vertical-align: sub !important;\r\n    font-size: smaller !important;\r\n}\r\n\r\nsup {\r\n    vertical-align: super !important;\r\n    font-size: smaller !important;\r\n}\r\n", "/*\n\n\n    YOU ARE VIEWING THE OUTPUT CSS FROM A CODE-GENERATION (VIA WEBPACK) PROCESS\n\n    PLEASE DO NOT MODIFY THIS FILE AS CHANGES WILL BE OVERWRITTEN\n\n\n*/\n/*Units*/\n/*Colours*/\n/*Fonts*/\n/*Space*/\n/*This will apply the quality color to the brand variable, globally (within the context of this application).*/\nhtml, body, p, select, input, th, td, label,\n.pager, .no-records-container, button, a.button, .core-content ul li,\n.autocomplete .autocomplete-items li {\n  font-weight: normal;\n}\n\ninput[type=text], input[type=url], input[type=email], input[type=search],\ninput[type=number], input[type=tel], input[type=date], input[type=password],\ntextarea, select, .fake-input, .fake-textarea {\n  font: 13px Arial, Helvetica, Verdana, sans-serif;\n  font-weight: normal;\n}\n\nspan.preClassifierName {\n  font-size: 17px;\n  color: #461E96;\n}\n\nul.tabs li span.tab-badge {\n  font-size: 12px;\n}\n\nth, label, .chip {\n  font-weight: bold;\n}\n\n.page-header, .sub-header {\n  font-family: \"cencora-gilroy\", Arial, Helvetica, Verdana, sans-serif;\n}\n\n.sub-header h2 {\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\nh2.dashboard {\n  font-size: 1.25rem;\n  font-weight: 600;\n}\n\n.core-container > header a.app-title {\n  min-width: 125px;\n}\n\n.nav-menu > li {\n  font-size: 17px;\n}\n.nav-menu > li a {\n  color: #1F394A;\n}\n\n.nav-menu > li .flyout li {\n  font-size: 17px;\n}\n\nsub {\n  vertical-align: sub !important;\n  font-size: smaller !important;\n}\n\nsup {\n  vertical-align: super !important;\n  font-size: smaller !important;\n}\n\ninput:disabled, select:disabled {\n  opacity: 0.7;\n}\n\nselect option.inactive {\n  background: #DDD;\n}\n\n.buttons {\n  margin-top: 1rem;\n}\n\ntable thead {\n  background-color: white;\n  border-bottom: none;\n}\n\ntable tr th {\n  padding: 0.6rem 0.75rem;\n}\n\ntable tr.selectable:hover td {\n  background-color: #e9e9e9;\n}\n\n.pager {\n  background-color: white;\n  border-radius: 0.6rem;\n}\n.pager .page-size label {\n  margin-bottom: 0;\n}\n\ntable tr td {\n  vertical-align: middle;\n}\n\ntable {\n  border-bottom: solid 2px #f0f7f7;\n}\n\ntable tr:last-of-type {\n  border-bottom: none;\n}\n\ntable tbody tr:nth-child(odd) {\n  background: #F5F5F5;\n}\n\ntable th,\ntable td {\n  padding: 0.6rem 0.75rem;\n}\n\n.data-table-actions {\n  text-align: right;\n}\n\n.data-table-actions div {\n  float: right;\n}\n\n.dataTable th {\n  overflow: visible;\n}\n\n.input-validation-error {\n  border: 1px solid #8f0101 !important;\n}\n\nspan.validation-error {\n  padding: 0;\n}\nspan.validation-error span {\n  font-size: 1rem;\n  color: #8f0101;\n}\n\n.page-size {\n  flex: 0 240px !important;\n}\n\n.tabs {\n  display: flex;\n  border-bottom: 2px solid #E8E8E8;\n  margin-bottom: 1rem;\n}\n.tabs li {\n  margin-bottom: -2px;\n  margin-right: 1em;\n  cursor: pointer;\n  vertical-align: middle;\n  text-align: center;\n  color: #3B3B3B;\n  padding: 5px 0 10px 0;\n}\n.tabs li span {\n  vertical-align: middle;\n}\n.tabs li span:first-child {\n  margin-right: 7px;\n}\n.tabs li span:hover {\n  color: #461E96;\n  transition-duration: 0.5s;\n}\n.tabs li a {\n  vertical-align: middle;\n  margin-right: 7px;\n  color: #3B3B3B;\n}\n.tabs li a:hover {\n  color: #461E96;\n}\n.tabs li.active {\n  color: #461E96;\n  padding-bottom: -2px;\n  border-bottom: 2px solid #461E96;\n  cursor: default;\n}\n.tabs li.active a {\n  color: #211359;\n}\n.tabs li.active a:hover {\n  color: #461E96;\n}\n.tabs li.separator {\n  display: inline-block;\n  border-right: 2px solid #CACACA;\n  cursor: default;\n  margin-top: 10px;\n  margin-left: 3px;\n}\n.tabs li:last-of-type {\n  margin-bottom: -2px;\n}\n.tabs .tab-badge {\n  display: inline-block;\n  line-height: 1rem;\n  margin: 1px auto;\n  text-align: center;\n  border-radius: 15px;\n  color: #fff;\n  background-color: #461E96;\n  padding: 5px 10px 4px;\n}\n.tabs .tab-badge.completed {\n  background-color: #461E96;\n}\n.tabs .tab-badge:hover {\n  color: #fff;\n}\n.tabs li.nolink {\n  cursor: default;\n}\n.tabs li.nolink span:hover {\n  color: #3B3B3B;\n}\n.tabs li.nolink span.tab-badge {\n  color: #fff;\n}\n\n.tabs.borderless {\n  border: none;\n}\n.tabs.borderless li.active {\n  border: none;\n}\n\n.field-validation-error {\n  color: #8f0101;\n  padding-left: 0.8rem;\n  line-height: 1.6rem;\n}\n\n.sub-nav {\n  height: 35px;\n  padding-top: 8px;\n  padding-left: 10px;\n}\n\n.sub-nav li {\n  float: left;\n  margin-right: 22px;\n}\n\n.sub-nav a {\n  color: #727272;\n  cursor: pointer;\n  text-decoration: none;\n}\n.sub-nav a:hover {\n  color: #461E96;\n  transition-duration: 0.5s;\n}\n\n.sub-nav a.active {\n  color: #1E1E1E;\n  border-bottom: 2px solid #461E96;\n}\n\n.sub-layout-header-buttons {\n  margin-bottom: 1rem;\n}\n\n.core-content.has-background {\n  background-image: url(/src/images/splash.jpg);\n}\n\n.z-index-1 {\n  z-index: 1;\n}\n\n.chip {\n  border-radius: 15px;\n  background-color: #005C98;\n  color: #fff;\n  height: 25px;\n  display: inline-block;\n  font-weight: bold;\n  margin: 0 10px 10px 0;\n  padding: 0 0 0 10px;\n  vertical-align: middle;\n}\n\n.chip span {\n  position: relative;\n  top: 1px;\n  padding-right: 2px;\n}\n\n.chip i {\n  position: relative;\n  top: 5px;\n  font-size: 1rem;\n  margin-right: -10px;\n}\n\n.sub-header {\n  margin-left: -2rem;\n  margin-right: -2rem;\n  margin-bottom: 1rem;\n  position: sticky;\n  top: -1rem;\n  z-index: 99;\n}\n\n.core-content {\n  padding-left: 30px;\n  padding-right: 1.875rem;\n  padding-top: 0;\n}\n\n.core-content section.card-container {\n  background: none;\n  padding-left: 0;\n  padding-right: 0;\n}\n.core-content section.card-container .section-card {\n  border-radius: 4px;\n  flex-basis: 30%;\n  background-color: #fff;\n  position: relative;\n  padding: 1.5rem;\n}\n@media screen and (max-width: 1200px) {\n  .core-content section.card-container .section-card.expand {\n    flex-basis: 40%;\n  }\n}\n\n.flex-gap {\n  gap: 1rem;\n}\n\n.flex-align-center label {\n  margin-bottom: 0rem;\n}\n\n.version-label {\n  background: #461E96;\n  color: #fff;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n}\n\n.full-width-rule {\n  margin: 0 -1.5rem;\n}\n\n.switch-container.switch-active-inactive {\n  user-select: none;\n}\n.switch-container.switch-active-inactive input.switch {\n  margin: 0;\n}\n.switch-container.switch-active-inactive label.switch {\n  text-indent: unset;\n  color: #fff;\n  font-weight: 400;\n  width: 80px;\n  text-align: right;\n  padding-top: 6px;\n  padding-right: 0.5rem;\n  margin: 0;\n}\n.switch-container.switch-active-inactive label.switch:before {\n  content: \"Inactive\";\n}\n.switch-container.switch-active-inactive input.switch:checked + label:after {\n  transform: translateX(200%);\n}\n.switch-container.switch-active-inactive input.switch:checked + label:before {\n  content: \"Active\";\n  position: absolute;\n  left: 0.75rem;\n}\n\n.switch-container.switch-enabled-disabled {\n  user-select: none;\n  margin-top: 0.75rem;\n}\n.switch-container.switch-enabled-disabled input.switch {\n  margin: 0;\n}\n.switch-container.switch-enabled-disabled label.switch {\n  text-indent: unset;\n  color: #fff;\n  font-weight: 400;\n  width: 80px;\n  text-align: right;\n  padding-top: 6px;\n  padding-right: 0.3rem;\n  margin: 0;\n}\n.switch-container.switch-enabled-disabled label.switch:before {\n  content: \"Disabled\";\n}\n.switch-container.switch-enabled-disabled input.switch:checked + label:after {\n  transform: translateX(200%);\n}\n.switch-container.switch-enabled-disabled input.switch:checked + label:before {\n  margin-right: 50px;\n  content: \"Enabled\";\n  position: absolute;\n  left: 0.45rem;\n}\n\n/* Status Lozenge */\n[class^=state-indicator-],\n[class*=state-indicator-] {\n  display: inline-block;\n  width: 100%;\n  line-height: 1.5rem;\n  border-radius: 1rem;\n  color: #fff;\n  text-align: center;\n  font-size: 0.75rem;\n  min-width: 3.125rem;\n  background-color: #461E96;\n}\n\n.state-indicator-new {\n  background-color: #3B3B3B;\n}\n\n.state-indicator-updated {\n  background-color: #C2551F;\n  margin-bottom: 5px;\n}\n\n.state-indicator-preclassified {\n  background-color: #761701;\n}\n\n.state-indicator-approved {\n  background-color: #007F50;\n}\n\n.state-indicator-reclassified {\n  background-color: #C2551F;\n}\n\n.state-indicator-signed {\n  background-color: #461E96;\n}\n\n.state-indicator-inactive {\n  background-color: #0073BE;\n}\n\n.support-reference {\n  border: 1px solid #CACACA;\n  border-radius: 10px;\n  padding: 1rem;\n}\n\n.support-container {\n  padding: 1rem;\n  background: #fff;\n}\n.support-container li a {\n  color: #6c767d;\n}\n.support-container li.active a {\n  color: #127f92;\n}\n.support-container ul li.active {\n  color: #461E96;\n}\n\n.support-lozenge {\n  display: flex;\n  border-radius: 10px;\n  margin-bottom: 1rem;\n  cursor: pointer;\n}\n.support-lozenge.selected {\n  border: 2px solid #461E96;\n}\n.support-lozenge.selectable {\n  border: 2px solid #CACACA;\n}\n.support-lozenge div.lozenge-container {\n  padding: 5px 20px 5px 5px;\n}\n.support-lozenge div.action-container {\n  padding: 5px 5px 5px 0;\n}\n.support-lozenge .version-badge {\n  display: inline-block;\n  margin: 0.2rem auto;\n  width: 2.2rem;\n  line-height: 2.2rem;\n  text-align: center;\n  border-radius: 50%;\n  color: #211359;\n  background-color: rgba(70, 30, 150, 0.1);\n}\n.support-lozenge [class^=state-indicator-],\n.support-lozenge [class*=state-indicator-] {\n  width: 8rem;\n  line-height: 30px;\n}\n\n#support-history-modal .modal-container {\n  border-radius: 5px;\n}\n#support-history-modal .modal-header {\n  border: none;\n}\n#support-history-modal input {\n  height: 10vh;\n}\n#support-history-modal .dialog-custom-content div:nth-child(2) {\n  align-items: center;\n  margin-left: auto;\n}\n#support-history-modal a {\n  margin: 1.5rem 0 0 0.5rem;\n}\n#support-history-modal #dialog-closer {\n  display: none;\n}\n\n.reference-container {\n  display: flex;\n  flex-direction: column;\n}\n.reference-container .reference-group {\n  display: flex;\n  margin-bottom: 1rem;\n}\n.reference-container .reference-group label {\n  flex: 1 0 12%;\n}\n.reference-container .reference-group span,\n.reference-container .reference-group a {\n  flex: 1 0 88%;\n}\n\n.reference-container-row {\n  display: flex;\n  flex-wrap: wrap;\n  border: 1px solid #CACACA;\n  border-radius: 0.6rem;\n  background-color: #fff;\n}\n.reference-container-row > div {\n  padding: 1rem;\n}\n.reference-container-row .reference-info {\n  display: flex;\n  flex-direction: column;\n  flex: 0 0 280px;\n  position: relative;\n}\n.reference-container-row .reference-info .reference-group {\n  display: flex;\n  flex-wrap: wrap;\n  word-break: break-all;\n}\n.reference-container-row .reference-info .reference-group label {\n  flex: 1 0 25%;\n}\n.reference-container-row .reference-info .reference-group span {\n  flex: 1 0 75%;\n}\n.reference-container-row .reference-info .positionBottom {\n  position: absolute;\n  bottom: 1rem;\n  display: block;\n  width: 90%;\n}\n.reference-container-row .reference-info .positionBottom label {\n  padding-right: 10px;\n}\n.reference-container-row .reference-content {\n  flex: 5 1 25rem;\n  border-right: 1px solid #CACACA;\n}\n.reference-container-row .reference-content .reference-group {\n  margin-bottom: 1rem;\n}\n.reference-container-row .reference-content label {\n  display: block;\n  margin-bottom: 0;\n}\n\n.reference-classification {\n  flex: 0 0 350px;\n  display: flex;\n  flex-direction: column;\n}\n.reference-classification .form-group:last-of-type {\n  margin-bottom: 1.5rem;\n}\n.reference-classification.ai-expanded {\n  flex: 0 0 370px;\n}\n.reference-classification.ai-expanded .form-group.ai-suggestion, .reference-classification.ai-expanded .form-group.ai-status {\n  margin-bottom: 10px;\n}\n.reference-classification .dosage-form {\n  display: flex;\n}\n.reference-classification .dosage-form div:first-child {\n  flex: 1 1 30%;\n}\n.reference-classification .dosage-form .dosage-form-badges {\n  flex: 1 1 70%;\n  margin-left: 1rem;\n}\n.reference-classification .dosage-form .dosage-form-badges > span {\n  display: inline-block;\n  min-width: 1.5rem;\n  height: 1.5rem;\n  background-color: #0073BE;\n  text-align: center;\n  color: #fff;\n  margin: 0 0 0.5rem 0.5rem;\n  line-height: 1.5rem;\n  padding: 0 1rem;\n  border-radius: 0.9rem;\n}\n.reference-classification .dosage-form .dosage-form-badges > span:hover {\n  cursor: pointer;\n  opacity: 0.8;\n  transition-duration: 0.2s;\n}\n.reference-classification .dosage-form .dosage-form-badges.disabled > span {\n  opacity: 0.5;\n}\n.reference-classification .dosage-form .dosage-form-badges.disabled > span:hover {\n  cursor: default;\n}\n.reference-classification .ai-status i {\n  font-size: 30px;\n  width: 30px;\n  cursor: default;\n  opacity: 1;\n}\n.reference-classification ai-status i:hover {\n  opacity: 1;\n}\n.reference-classification .ai-status div {\n  margin-left: 5px;\n  padding: 6px 12px 6px 12px;\n  border-radius: 0px 8px 8px 8px;\n}\n.reference-classification .ai-status-awaiting-response div {\n  background: #FFF8BA;\n}\n.reference-classification .ai-status-success div {\n  background: #D8FEE7;\n}\n.reference-classification .ai-status-failed div {\n  background: #FCD9D1;\n}\n.reference-classification .ai-suggestion.category.rejected {\n  margin-bottom: 19px;\n}\n.reference-classification .ai-suggestion.pending > div {\n  background: #E7F7FF;\n}\n.reference-classification .ai-suggestion.accepted > div {\n  background: #D8FEE7;\n}\n.reference-classification .ai-suggestion.rejected > div {\n  background: #F9F9F9;\n}\n.reference-classification .ai-suggestion.pending .header {\n  background: #CEEDFC;\n}\n.reference-classification .ai-suggestion.accepted .header {\n  background: #C4F5D8;\n}\n.reference-classification .ai-suggestion.rejected .header {\n  background: #F5F5F5;\n}\n.reference-classification .ai-suggestion .header {\n  width: 100%;\n  padding: 10px 20px 10px 20px;\n  border-radius: 8px 8px 0px 0px;\n  border-bottom: 1px solid white;\n}\n.reference-classification .ai-suggestion .header .header-text {\n  font-weight: bold;\n  color: #461E96;\n  line-height: 25px;\n  width: 240px;\n}\n.reference-classification .ai-suggestion .header .header-buttons {\n  display: flex;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button {\n  display: inline-block;\n  width: 25px;\n  height: 25px;\n  text-align: center;\n  margin-left: 5px;\n  border: 1px solid #CACACA;\n  border-radius: 5px;\n  background: #FFF;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button:hover {\n  background: #F5F5F5;\n  cursor: pointer;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button i {\n  font-size: 17px;\n  position: relative;\n  left: -2px;\n  top: 3px;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button i:hover {\n  opacity: 1;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled {\n  opacity: 0.5;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled:hover {\n  opacity: 0.5;\n  background: #FFF;\n  cursor: default;\n}\n.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled i:hover {\n  cursor: default;\n}\n.reference-classification .ai-suggestion .header .header-buttons .undo-button {\n  display: inline-block;\n  position: relative;\n  padding: 5px;\n  margin-left: 20px;\n  cursor: pointer;\n}\n.reference-classification .ai-suggestion .suggested-value {\n  padding: 15px 20px;\n  border-bottom: 1px solid white;\n}\n.reference-classification .ai-suggestion .suggested-value div:first-child {\n  font-weight: bold;\n}\n.reference-classification .ai-suggestion .suggested-value div:last-child.value-box {\n  background: white;\n  border-radius: 5px;\n  padding: 11px 15px 12px 15px;\n  margin-top: 5px;\n}\n.reference-classification .ai-suggestion .suggested-value div:last-child .dosage-form {\n  display: inline-block;\n}\n.reference-classification .ai-suggestion .suggested-value div:last-child .dosage-form .dosage-form-badges {\n  margin-left: 0;\n  margin-top: 10px;\n}\n.reference-classification .ai-suggestion .suggested-value div:last-child select, .reference-classification .ai-suggestion .suggested-value div:last-child input {\n  background: white;\n  margin-top: 5px;\n}\n.reference-classification .ai-suggestion .reasoning {\n  padding: 15px 20px;\n  border-bottom: 1px solid white;\n}\n.reference-classification .ai-suggestion .reasoning div:first-child {\n  font-weight: bold;\n}\n.reference-classification .ai-suggestion .reasoning div:last-child {\n  margin-top: 5px;\n}\n.reference-classification .ai-suggestion .previous-value {\n  padding: 15px 20px;\n  border-bottom: 1px solid white;\n}\n.reference-classification .ai-suggestion > div:last-child {\n  border-radius: 0px 0px 8px 8px;\n  padding-bottom: 15px;\n}\n.reference-classification .form-group.actions {\n  height: 42px;\n  margin-top: auto;\n  width: 108%;\n  text-align: right;\n  border-top: 1px solid #CACACA;\n  margin-left: -1rem;\n  margin-right: -3rem;\n  padding-top: 1rem;\n}\n.reference-classification .form-group.actions .preClassifierName {\n  float: left;\n  text-align: left;\n  padding-left: 1rem;\n  padding-top: 5px;\n}\n.reference-classification .form-group.actions button, .reference-classification .form-group.actions div {\n  margin: 0 1rem;\n}\n.reference-classification .form-group.actions button .switch-container, .reference-classification .form-group.actions div .switch-container {\n  position: relative;\n  top: -2px;\n  right: -8px;\n}\n.reference-classification .form-group.actions button .switch-container:focus, .reference-classification .form-group.actions div .switch-container:focus {\n  border: 2px solid #111;\n}\n.reference-classification .separated {\n  margin: 0 -1rem;\n  padding: 1rem;\n  border-top: 1px solid #CACACA;\n}\n\n.switch-container input.switch:checked + label {\n  color: #fff;\n}\n\n.page-header .main-navigation-wrapper {\n  width: 100%;\n  display: flex;\n}\n\n.flyout-header.account {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.horizontal-filter {\n  background-color: #EEE;\n  padding: 1rem;\n}\n.horizontal-filter .input-group {\n  margin-bottom: 1rem;\n}\n.horizontal-filter select {\n  width: auto;\n  background-color: white;\n  margin-right: 10px;\n}\n.horizontal-filter .horizontal-filter-item {\n  display: inline-block;\n  margin: 0 10px 0 0;\n  padding: 0;\n  max-width: min-content;\n}\n.horizontal-filter .horizontal-filter-item #btnLabelSearch {\n  margin-top: 0.5rem;\n}\n.horizontal-filter .horizontal-filter-item .reset-button {\n  background-color: #737373;\n  transition-duration: 0.5s;\n}\n.horizontal-filter .horizontal-filter-item .reset-button:hover {\n  background-color: #454545;\n}\n.horizontal-filter .horizontal-filter-item select {\n  margin: 0;\n}\n.horizontal-filter i {\n  display: block;\n  position: absolute;\n  left: 0;\n  top: 30px;\n}\n.horizontal-filter.flex {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: flex-start;\n}\n\nabbr[title] {\n  border-bottom: none;\n  text-decoration: none;\n}\n\n#substance #synonyms-column .form-group #addSynonym {\n  flex-grow: 1;\n}\n#substance #synonyms-column .form-group #addBtn {\n  margin-left: 10px;\n}\n#substance #synonyms-column #table-synonyms {\n  border-bottom: none;\n  max-width: 600px;\n  margin-bottom: 1rem;\n}\n#substance #synonyms-column #table-synonyms tbody tr td .m-icon {\n  float: right;\n}\n#substance #synonyms-column #table-synonyms tbody tr:nth-child(odd) {\n  background-color: #F5F5F5;\n}\n\n.table-filter-items input[type=search] {\n  padding-right: 12px;\n}\n.table-filter-items .m-icon.close {\n  opacity: 1;\n}\n\n.emailBounceText {\n  height: 280px;\n}\n\n.emailSuppressTableText {\n  color: #fff;\n  margin-left: 30px;\n  padding: 5px;\n  border-radius: 5px;\n}\n\n.email-bounce-background {\n  background-color: #E22F00;\n}\n\n.email-block-background {\n  background-color: #FBC618;\n}\n\n.email-spam-background {\n  background-color: #3B3B3B;\n}\n\n.email-bounce-foreground {\n  color: #E22F00;\n}\n\n.email-block-foreground {\n  color: #FBC618;\n}\n\n.email-spam-foreground {\n  color: #3B3B3B;\n}\n\n#company-user-edit .suppression-reason {\n  border: 1px solid #c4c4c4;\n  opacity: 0.7;\n  border-radius: 0.5rem;\n  background: #f3f6f9;\n  padding: 0.75rem;\n  margin-bottom: 1.5rem;\n  height: 175px;\n  overflow-y: scroll;\n}\n\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n\n.pointer-events-none {\n  pointer-events: none;\n}\n\n.disabled {\n  opacity: 0.7;\n}\n\n#dashboard {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 25px;\n  margin: 15px auto;\n}\n#dashboard #import-dashboard-container {\n  margin-bottom: 30px;\n}\n#dashboard #import-dashboard-container.narrow {\n  width: 680px;\n}\n#dashboard #import-dashboard-container.wide {\n  max-width: 1340px;\n}\n#dashboard #import-dashboard-container.wide.empty {\n  min-width: 680px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content {\n  background: white;\n  border-radius: 10px;\n  padding: 20px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 20px;\n  row-gap: 10px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card {\n  border: 1px solid #C4C4C4;\n  border-radius: 10px;\n  padding: 0;\n  width: 310px;\n  line-height: normal;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info {\n  display: flex;\n  flex-wrap: nowrap;\n  width: 100%;\n  padding: 16px 20px;\n  height: 96px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text {\n  width: 36%;\n  text-align: left;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type {\n  font-size: 17px;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type.scheduled {\n  color: #0073BE;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type.adhoc {\n  color: #461E96;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-date {\n  font-size: 14px;\n  color: #1F394A;\n  padding-top: 5px;\n  padding-bottom: 7px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-status {\n  font-size: 12px;\n  font-weight: bold;\n  color: #000000;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number {\n  width: 64%;\n  text-align: right;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number .todo-count-label {\n  font-size: 12px;\n  color: #1F394A;\n  padding-top: 2px;\n  padding-bottom: 5px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number .todo-count-number {\n  font-size: 38px;\n  font-weight: bold;\n  color: #0391D7;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons {\n  display: flex;\n  flex-wrap: nowrap;\n  border-top: 1px solid #C4C4C4;\n  width: 100%;\n  background: transparent;\n  padding: 10px 20px;\n  margin: 0;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons button, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons a.button {\n  padding: 8px 16px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons button:focus, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons a.button:focus {\n  border: 2px solid black !important;\n  padding: 7px 15px;\n  transition-duration: 0s;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .left {\n  text-align: left;\n  width: 30%;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right {\n  text-align: right;\n  width: 70%;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button {\n  margin-right: 5px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button {\n  background: white;\n  color: black;\n  border: 1px solid black;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button:hover, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button:hover {\n  background: #1f394a;\n  color: white;\n  transition-duration: 0.2s;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button:disabled, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button:disabled {\n  background: white;\n  color: black;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected {\n  border: 3px solid #007F50;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .info {\n  padding: 14px 18px 16px;\n  height: 94px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .info .text .import-type {\n  color: #028930;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .buttons {\n  padding: 10px 18px 8px;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .buttons .deselect-button {\n  background: #028930;\n}\n#dashboard #import-dashboard-container #import-dashboard .content .empty p {\n  margin: 0;\n}\n#dashboard #email-dashboard-container {\n  width: 680px;\n  margin-bottom: 10px;\n}\n#dashboard #email-dashboard-container #email-dashboard .content {\n  background: white;\n  border-radius: 10px;\n  padding: 20px;\n}\n#dashboard #email-dashboard-container #email-dashboard .content .cards {\n  display: flex;\n  flex-wrap: nowrap;\n  column-gap: 20px;\n  margin-bottom: 20px;\n}\n#dashboard #email-dashboard-container #email-dashboard .content .cards .card {\n  border: 1px solid #C4C4C4;\n  border-radius: 10px;\n  width: 200px;\n  text-align: center;\n  padding: 40px 30px;\n  line-height: normal;\n}\n#dashboard #email-dashboard-container #email-dashboard .content .cards .card .text {\n  font-size: 16px;\n  font-weight: bold;\n}\n#dashboard #email-dashboard-container #email-dashboard .content .cards .card .value {\n  font-size: 70px;\n  color: #572F8C;\n  font-weight: bold;\n}\n#dashboard #email-dashboard-container #email-dashboard .content #table-container {\n  border: 1px solid #C4C4C4;\n  border-radius: 10px;\n}\n#dashboard #email-dashboard-container #email-dashboard .content #table-container table {\n  background: transparent;\n}\n#dashboard #email-dashboard-container #email-dashboard .content #table-container table thead {\n  background: transparent;\n}\n#dashboard #email-dashboard-container #email-dashboard .content #table-container table td, #dashboard #email-dashboard-container #email-dashboard .content #table-container table th {\n  padding: 15px 20px 14px;\n}\n#dashboard #email-dashboard-container #email-dashboard .content #table-container .pager {\n  padding-left: 6px;\n  padding-right: 6px;\n}\n#dashboard #email-dashboard-container #build-info {\n  margin-top: 30px;\n}\n#dashboard #email-dashboard-container #build-info .content {\n  background: white;\n  border-radius: 10px;\n  padding: 20px;\n}\n#dashboard #email-dashboard-container #build-info p:last-of-type {\n  margin-bottom: 0;\n}\n\n#reference-details-container {\n  /* Reference History actions status styling */\n}\n#reference-details-container #reference-substance-section, #reference-details-container #reference-history-section {\n  border: solid 1px #c4c4c4;\n  border-radius: 5px;\n  padding: 20px;\n}\n#reference-details-container #reference-substance-section li:hover, #reference-details-container #reference-history-section li:hover {\n  color: #461E96;\n}\n#reference-details-container #substance-list {\n  cursor: pointer;\n  border: 2px solid #c4c4c4;\n  border-radius: 5px;\n  padding: 10px;\n}\n#reference-details-container #substance-list span.substance-name {\n  font-weight: bold;\n}\n#reference-details-container #substance-list span.plxid {\n  display: inline-block;\n  padding-top: 5px;\n  font-size: 11px;\n}\n#reference-details-container .reference-details-section {\n  border: solid 1px #c4c4c4;\n  border-radius: 5px;\n  padding: 0;\n}\n#reference-details-container #reference-details-info {\n  border-bottom: solid 1px #c4c4c4;\n  padding: 10px 10px 0;\n}\n#reference-details-container #reference-details-info label {\n  padding: 10px 6px 10px 10px;\n}\n#reference-details-container #reference-details-info span {\n  margin-right: 16px;\n}\n#reference-details-container #reference-details-content {\n  padding: 20px;\n}\n#reference-details-container #reference-details-content .form-group-display {\n  display: flex;\n  flex-basis: 50%;\n  margin-bottom: 1rem;\n}\n#reference-details-container #reference-details-content .form-group-display label {\n  flex-basis: 40%;\n}\n#reference-details-container #classification-section {\n  border-left: solid 1px #c4c4c4;\n  padding: 20px;\n  min-width: 250px;\n}\n#reference-details-container #classification-section .tile {\n  padding: 0;\n}\n#reference-details-container #classification-section .form-group-display {\n  margin-bottom: 1rem;\n}\n#reference-details-container #classification-section .reason-for-change {\n  display: block;\n  width: 200px;\n  word-wrap: break-word;\n}\n#reference-details-container #classification-section .reference-classification {\n  flex: none;\n  width: 350px;\n}\n#reference-details-container #classification-section .reference-classification label {\n  flex-basis: unset;\n}\n#reference-details-container .state-indicator-update, #reference-details-container .state-indicator-silent {\n  background-color: #C2551F;\n}\n\n#import-dashboard-details {\n  /* Clear floats after the columns */\n}\n#import-dashboard-details .row {\n  border-bottom: 1px solid #EEE;\n  margin-bottom: 10px;\n}\n#import-dashboard-details .column {\n  float: left;\n  width: 33.33%;\n}\n#import-dashboard-details .centered {\n  text-align: center;\n}\n#import-dashboard-details .row:after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n#import-dashboard-details .paginator span {\n  margin-right: 10px;\n}\n#import-dashboard-details .show-contract-icon {\n  font-family: \"MaterialIconsOutlined\", sans-serif;\n  font-size: 1.6rem;\n  cursor: pointer;\n  text-decoration: none;\n}\n\n#locks #locks-dashboard {\n  background-color: unset;\n  padding-left: 0;\n  padding-right: 0;\n}\n#locks #locks-dashboard .cards {\n  display: grid;\n  grid-template-columns: repeat(6, 1fr);\n  column-gap: 1rem;\n  row-gap: 1rem;\n}\n@media screen and (max-width: 1200px) {\n  #locks #locks-dashboard .cards {\n    grid-template-columns: repeat(5, 1fr);\n  }\n}\n#locks #locks-dashboard .cards .card {\n  padding: 20px 20px 5px 20px;\n  border-radius: 4px;\n  font-size: 15px;\n  width: 100%;\n  background: white;\n}\n#locks #locks-dashboard .cards .card p {\n  font-weight: 400;\n}\n#locks #locks-dashboard .cards .card .flex-container {\n  display: flex;\n  flex-flow: row wrap;\n  width: 100%;\n  margin-top: 30px;\n}\n#locks #locks-dashboard .cards .card .flex-container .locksCount {\n  width: 50%;\n  margin-bottom: 15px;\n  font-size: 40px;\n}\n#locks #locks-dashboard .cards .card .flex-container .release-button {\n  width: 50%;\n  text-align: right;\n  margin-bottom: 15px;\n}\n#locks #locks-dashboard .cards .card .flex-container .release-button button {\n  background-color: #FBC618;\n  color: #1E1E1E;\n}\n#locks #locks-dashboard .cards .card .flex-container .release-button button:hover, #locks #locks-dashboard .cards .card .flex-container .release-button button:active {\n  background-color: #3B3B3B;\n}\n#locks #locks-dashboard p {\n  margin-bottom: 0;\n}\n\n.no-import-selected {\n  padding: 30px 20px 20px;\n  margin: 0 auto;\n  width: 400px;\n}\n.no-import-selected i {\n  font-size: 30px;\n  display: inline-block;\n  padding-right: 40px;\n  font-weight: bold;\n}\n.no-import-selected h2 {\n  display: inline-block;\n  margin: 0;\n  position: relative;\n  top: -5px;\n}\n\n.preClassificationCompleted {\n  padding: 30px 20px 20px;\n  margin: 0 auto;\n  width: 400px;\n}\n.preClassificationCompleted i {\n  font-size: 30px;\n  display: inline-block;\n  padding-right: 40px;\n  font-weight: bold;\n}\n.preClassificationCompleted h2 {\n  display: inline-block;\n  margin: 0;\n  font-size: 20px;\n  position: relative;\n  top: -5px;\n}\n\n.pickReturnedNothing {\n  padding: 30px 20px 20px;\n  margin: 0 auto;\n  width: 700px;\n}\n.pickReturnedNothing i {\n  font-size: 30px;\n  display: inline-block;\n  padding-right: 40px;\n  font-weight: bold;\n  position: relative;\n  top: -45px;\n}\n.pickReturnedNothing h2 {\n  display: inline-block;\n  margin: 0;\n  font-size: 20px;\n  position: relative;\n  top: -5px;\n}\n\n.classification-completed, .classification-signed {\n  padding: 30px 20px 20px;\n  margin: 0 auto;\n  width: 400px;\n}\n.classification-completed i, .classification-signed i {\n  font-size: 30px;\n  display: inline-block;\n  padding-right: 40px;\n  font-weight: bold;\n}\n.classification-completed h2, .classification-signed h2 {\n  display: inline-block;\n  margin: 0;\n  position: relative;\n  top: -5px;\n}\n\n.new-company-project-header, .case-upload-header {\n  display: flex;\n  justify-content: space-between;\n  padding: 1rem 2rem 0 2rem;\n  border-bottom: 1px solid #E8E8E8;\n  align-items: center;\n}\n.new-company-project-header h2, .case-upload-header h2 {\n  font-weight: 600;\n}\n.new-company-project-header .case-status, .case-upload-header .case-status {\n  background-color: #026fa8;\n  color: #fff;\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  margin-top: 0px;\n  font-size: 1rem;\n  font-weight: 400;\n  height: fit-content;\n}\n.new-company-project-header .case-status.pending, .case-upload-header .case-status.pending {\n  background-color: #bb5c20;\n}\n\n/* Case Upload Popup */\n.case-upload-form-left {\n  display: flex;\n  flex-direction: column;\n  border-right: 1px solid #e5e5e5;\n  gap: 1rem;\n  width: 40%;\n  padding: 1rem 2rem 0 2rem;\n}\n\n.case-upload-substance-wrapper .case-upload-label {\n  margin-bottom: 0;\n}\n\n.case-upload-divider {\n  height: 2px;\n  border-top: 1px solid #e5e5e5;\n  margin: 0.8rem -2rem;\n}\n.case-upload-divider.first-substance-divider {\n  margin-top: 3px;\n}\n.case-upload-divider.last-substance-divider {\n  margin-bottom: 3px;\n}\n\n.case-upload-form-right {\n  padding: 2rem;\n}\n\n.case-upload-content {\n  height: fit-content;\n  position: relative;\n  display: flex;\n}\n\n.case-upload-input {\n  align-self: stretch;\n  position: relative;\n}\n\n.case-upload-input {\n  border-radius: 8px;\n  background-color: #fff;\n  border: 1px solid #c4c4c4;\n  box-sizing: border-box;\n  height: 32px;\n  display: flex;\n  flex-direction: row;\n  padding: 8px 12px;\n  align-items: center;\n  justify-content: flex-start;\n  gap: 10px;\n  font-size: var(--body-main-body-size);\n}\n\n.case-upload-psur-dropdown-wrapper, .case-upload-mlm-dropdown-wrapper {\n  display: flex;\n  flex-direction: column;\n}\n\n.case-upload-mlm-dropdown-wrapper {\n  right: 70%;\n  left: 17%;\n}\n\n.case-upload-comment-wrapper textarea {\n  resize: none;\n  margin-bottom: 1rem;\n}\n\n.case-upload-company-dropdown-wrapper {\n  min-height: 50px;\n}\n\n.case-upload-company-dropdown-wrapper #CompanyLabelNoPlxId,\n.case-upload-company-dropdown-wrapper #CompanyLabelInvalidPlxId,\n.case-upload-company-dropdown-wrapper #CompanyLabelNoCompany {\n  display: block;\n  color: #AD0000;\n  font-weight: normal;\n}\n\n.case-upload-dropdown-group {\n  display: flex;\n  gap: 1rem;\n  padding-top: 0.5rem;\n}\n\n.case-upload-plxid-wrapper {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: flex-start;\n  font-family: var(--body-main-body);\n  align-self: stretch;\n  padding-top: 1rem;\n}\n\n.case-upload-substance-wrapper {\n  font-family: var(--body-main-body);\n}\n\n.case-upload-substance-wrapper label:first-of-type {\n  margin-right: 61px;\n}\n\n.case-upload-substance-break-top, .case-upload-substance-break-bottom {\n  position: absolute;\n}\n\n.case-upload-substance-break-top {\n  height: 0%;\n  width: 40.8%;\n  top: 21%;\n  left: -1.7%;\n  border-top: 1px solid #e5e5e5;\n}\n\n.case-upload-substance-break-bottom {\n  width: 40.8%;\n  top: 14%;\n  left: -1.7%;\n  border-top: 1px solid #e5e5e5;\n}\n\n.case-upload-filedrop-wrapper {\n  position: absolute;\n  height: 24%;\n  width: 56%;\n  top: 5%;\n  right: 2%;\n  bottom: 61%;\n  left: 42%;\n  text-align: center;\n}\n\n.case-upload-file-item {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  justify-content: space-between;\n  border-bottom: 1px solid #CACACA;\n  padding: 0.3rem 0;\n  margin: 0;\n}\n.case-upload-file-item:first-of-type {\n  border-top: 1px solid #CACACA;\n}\n\n.case-upload-file-options {\n  font-family: \"MaterialIconsOutlined\", sans-serif;\n  font-size: 1.75rem;\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.case-upload-file-options div {\n  padding: 0.25rem;\n  cursor: pointer;\n}\n.case-upload-file-options div.disabled {\n  opacity: 0.5;\n  pointer-events: none;\n}\n.case-upload-file-options .case-upload-file-delete {\n  padding-bottom: 0.4rem;\n  padding-right: 0rem;\n}\n\n.flex.flex-nowrap.justify-end i.m-icon {\n  margin-left: 3px;\n}\n\n.table-row.delete-highlighted {\n  background-color: #ffdede;\n}\n\n#modal-dialog-case-delete p {\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n#modal-dialog-case-delete #dialog-closer {\n  position: absolute;\n  right: 5px;\n  top: 10px;\n}\n\n#caseCommentInput {\n  min-height: 82px;\n}\n\n/* Button Spinner Animation */\n.btn .spinner {\n  -ms-transform: scale(0.7, 0.7); /* IE 9 */\n  -webkit-transform: scale(0.7, 0.7); /* Safari */\n  transform: scale(0.7, 0.7);\n  position: absolute;\n  width: 32px;\n  height: 32px;\n  top: 50%;\n  margin-top: -16px;\n  opacity: 0;\n  background-image: url(data:image/gif;base64,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);\n}\n\n.btn,\n.btn .spinner,\n.btn .btn-label {\n  -webkit-transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;\n  -moz-transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;\n  -ms-transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;\n  transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;\n}\n\n.btn.btn-spinner {\n  overflow: hidden;\n  position: relative;\n}\n\n.btn.btn-spinner span {\n  display: inherit;\n  margin: inherit;\n  color: inherit;\n  font-weight: inherit;\n  font-size: inherit;\n}\n\n.btn.btn-spinner .btn-label {\n  position: relative;\n  margin: 0;\n}\n\n.btn.btn-spinner .spinner {\n  left: 50%;\n  margin-left: -16px;\n  margin-top: 1em;\n}\n\n.btn.btn-spinner[data-loading] .btn-label {\n  opacity: 0;\n  margin: 0;\n}\n\n.btn.btn-spinner[data-loading] .spinner {\n  opacity: 1;\n  margin-top: -16px;\n}\n\n.file-drop-loading-wrapper {\n  position: absolute;\n  left: 178px;\n  top: 45px;\n}\n\n.file-drop-loading-wrapper div {\n  height: 20px;\n  width: 20px;\n  border-radius: 50%;\n  float: left;\n  margin: 0 3px;\n  background: #E1F5F9;\n  transform: scale(0);\n}\n\n.file-drop-loading-wrapper .file-drop-loading-ball1 {\n  z-index: 1;\n  -moz-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation: grow 4.4s infinite ease-in-out;\n  opacity: 0.8;\n}\n\n.file-drop-loading-wrapper .file-drop-loading-ball2 {\n  -moz-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation-delay: 0.3s;\n  animation-delay: 0.3s;\n  opacity: 0.8;\n}\n\n.file-drop-loading-wrapper .file-drop-loading-ball3 {\n  -moz-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation-delay: 0.6s;\n  animation-delay: 0.6s;\n  opacity: 0.8;\n}\n\n.file-drop-loading-wrapper .file-drop-loading-ball4 {\n  -moz-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation: grow 4.4s infinite ease-in-out;\n  -webkit-animation-delay: 0.9s;\n  animation-delay: 0.9s;\n  opacity: 0.8;\n}\n\n@-moz-keyframes grow {\n  0% {\n    -moz-transform: scale(0);\n  }\n  25% {\n    -moz-transform: scale(1);\n  }\n  50% {\n    -moz-transform: scale(0);\n  }\n  75% {\n    -moz-transform: scale(1);\n    background: #72c2c2;\n  }\n  100% {\n    -moz-transform: scale(0);\n    background: #72c2c2;\n  }\n}\n@-webkit-keyframes grow {\n  0% {\n    -webkit-transform: scale(0);\n  }\n  25% {\n    -webkit-transform: scale(1);\n  }\n  50% {\n    -webkit-transform: scale(0);\n  }\n  75% {\n    -webkit-transform: scale(1);\n    background: #72c2c2;\n  }\n  100% {\n    -webkit-transform: scale(0);\n    background: #72c2c2;\n  }\n}\n.case-upload-file-list {\n  margin-top: 19px;\n  text-align: left;\n}\n\n.case-upload-file-list-item {\n  border-bottom: 1px solid #E5E5E5;\n  padding: 5px 0px 5px 0px;\n}\n\n.file-drop-area {\n  border: 2px dashed #C4C4C4;\n  border-radius: 0.75rem;\n  position: relative;\n  height: 117px;\n  width: 480px;\n  max-width: 100%;\n  margin: 0 auto;\n  padding: 26px 20px 30px;\n  -webkit-transition: 0.2s;\n  transition: 0.2s;\n}\n\n.file-drop-area.disabled {\n  background: #fbfbfb;\n  border: 2px dashed #dbdbdb;\n  cursor: not-allowed;\n}\n\n.file-drop-area.is-active {\n  background-color: #E1F5F9;\n  border: 2px dashed #008489;\n}\n\n.file-drop-area.dropping {\n  border: none;\n}\n\n.file-drop-mask {\n  top: 0px;\n  bottom: 0px;\n  position: absolute;\n  z-index: 99;\n  margin: -2px;\n  left: 0px;\n  right: 0px;\n}\n\n.file-drop-msg {\n  font-weight: 400;\n  font-size: 13px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: calc(100% - 180px);\n  vertical-align: middle;\n  display: block;\n  line-height: 60px;\n  left: 121px;\n}\n\n.file-drop-msg.disabled {\n  color: #343434;\n}\n\n.file-drop-msg-sub {\n  font-weight: 400;\n  font-size: 11px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: calc(100% - 180px);\n  vertical-align: middle;\n  display: block;\n  top: -25px;\n  left: 163px;\n  color: #343434;\n}\n\n.file-drop-msg-sub.disabled {\n  color: #343434;\n}\n\n.file-drop-input {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 100%;\n  cursor: pointer;\n  opacity: 0;\n}\n\n.file-drop-input:focus {\n  outline: none;\n}\n\n.reference-info-container {\n  width: 100%;\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  border-bottom: 1px solid #CACACA;\n}\n.reference-info-container h3, .reference-info-container label {\n  margin: 0;\n}\n.reference-info-container label {\n  margin-right: 0.5rem;\n}\n.reference-info-container [class*=state-indicator-] {\n  width: unset;\n  margin-bottom: 0;\n  padding: 0.25rem 0.5rem;\n  user-select: none;\n  pointer-events: none;\n}\n\n.details-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: auto;\n  gap: 1rem;\n}\n\n.badge-yes, .badge-no {\n  text-align: center;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  color: #fff;\n  width: 2.75rem;\n}\n\n.badge-yes {\n  background-color: #007F50;\n}\n\n.badge-no {\n  background-color: #9E2305;\n}\n\n#edit-contract .sub-header h2 a {\n  color: #3B3B3B;\n}\n#edit-contract .sub-header h2 a:hover {\n  color: #461E96;\n}\n\n#contractDetails .form-group .contractHistoryLabel {\n  font-weight: bold;\n  margin-bottom: 0.4rem;\n}\n\n#help section {\n  border-radius: 5px;\n}\n#help section .files .file {\n  border: 1px solid #CACACA;\n  border-radius: 5px;\n  padding: 20px 20px 20px;\n  width: 290px;\n}\n#help section .files .file span {\n  display: inline-block;\n  width: 75px;\n  font-weight: bold;\n}\n#help section .files .file .file-buttons {\n  padding-top: 5px;\n  display: flex;\n  gap: 0.5rem;\n}\n#help section #build-info p:last-of-type {\n  margin-bottom: 0;\n}\n\n#preclassification .locking-error-message, #preclassification .in-progress-message {\n  position: absolute;\n  top: 123px;\n  right: 28px;\n  background: #FEFEFE;\n  border-radius: 5px;\n  padding: 5px 10px;\n  min-width: 215px;\n}\n#preclassification .locking-error-message {\n  background: #FFE4E1;\n  border-left: 2px solid #B22222;\n  border-right: 2px solid #B22222;\n}\n#preclassification .in-progress-message .spinner {\n  width: 16px;\n  height: 16px;\n  display: inline-block;\n  box-sizing: border-box;\n  position: relative;\n  left: -3px;\n  top: 2px;\n}\n#preclassification .in-progress-message .spinner.round::before {\n  border-radius: 50%;\n  content: \" \";\n  width: 16px;\n  height: 16px;\n  display: inline-block;\n  box-sizing: border-box;\n  border-top: solid 3.2px #CACACA;\n  border-right: solid 3.2px #CACACA;\n  border-bottom: solid 3.2px #CACACA;\n  border-left: solid 3.2px #CACACA;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n#preclassification .in-progress-message .spinner.round::after {\n  border-radius: 50%;\n  content: \" \";\n  width: 16px;\n  height: 16px;\n  display: inline-block;\n  box-sizing: border-box;\n  border-top: solid 3.2px #461E96;\n  border-right: solid 3.2px transparent;\n  border-bottom: solid 3.2px transparent;\n  border-left: solid 3.2px transparent;\n  position: absolute;\n  top: 0;\n  left: 0;\n  animation: round-animate 1s ease-in-out infinite;\n}\n@keyframes round-animate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n#preclassification .modal-wrapper .icon-button-cancel {\n  display: none;\n}\n#preclassification .modal-wrapper .dialog-custom-content {\n  align-items: center;\n}\n#preclassification .modal-wrapper #dialog-closer {\n  display: none;\n}\n#preclassification .modal-wrapper a.button.icon-button-tick {\n  font-size: 0;\n}\n#preclassification .modal-wrapper a.button.icon-button-tick:after {\n  content: \"OK\";\n  font-size: initial;\n}\n\n#search .classificationSearchFullTextErrorMessage {\n  border: 1px solid #8A1A1E;\n  border-radius: 6px;\n  background: #F9E1E0;\n  padding: 15px;\n  margin: 13px 0;\n}\n#search .classificationSearchFullTextErrorMessage p {\n  margin-bottom: 0;\n}\n\n#reports section {\n  border-radius: 5px;\n}\n#reports section .cards {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 20px;\n  row-gap: 10px;\n}\n#reports section .cards .card {\n  border: 1px solid #CACACA;\n  border-radius: 5px;\n  width: 290px;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n}\n#reports section .cards .card .card-info {\n  padding: 20px 20px 10px;\n  flex: 1;\n}\n#reports section .cards .card .card-info h2 {\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n  overflow: hidden;\n}\n#reports section .cards .card .card-buttons {\n  border-top: 1px solid #CACACA;\n  padding: 10px 20px 10px;\n}\n#reports section .cards .card .card-buttons .button {\n  margin-right: 15px;\n}\n\n#importcards section {\n  border-radius: 5px;\n}\n#importcards section .cards {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 20px;\n  row-gap: 10px;\n}\n#importcards section .cards .card {\n  border: 1px solid #CACACA;\n  border-radius: 5px;\n  width: 390px;\n  height: 150px;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n}\n#importcards section .cards .card .card-info {\n  flex: 1;\n}\n#importcards section .cards .card .card-info h2 {\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n  overflow: hidden;\n}\n#importcards section .cards .card .card-buttons {\n  border-top: 1px solid #CACACA;\n  padding: 10px 20px 10px;\n}\n#importcards section .cards .card .card-buttons .button {\n  margin-right: 15px;\n}\n#importcards section .import-card-type {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n}\n#importcards section .import-card-title-text {\n  color: #461E96;\n  font-size: 17px;\n  font-weight: bold;\n  padding: 10px 20px 0px;\n}\n#importcards section .import-pubmed-card-contract-text {\n  font-weight: bold;\n  margin-bottom: 10px;\n  padding: 0px 20px 0px;\n  min-height: 50px;\n}\n#importcards section .import-card-contract-text {\n  font-weight: bold;\n  margin-bottom: 10px;\n  padding: 0px 20px 0px;\n}\n#importcards section .import-card-date-title {\n  color: #8a8a8a;\n  font-size: 12px;\n  padding: 10px 12px 0px;\n}\n#importcards section .import-card-date {\n  font-size: 12px;\n  padding: 0px 12px 0px;\n}\n#importcards section .import-card-updatedby-title {\n  color: #8a8a8a;\n  font-size: 12px;\n  padding: 10px 12px 0px;\n}\n#importcards section .import-card-updatedby {\n  font-size: 12px;\n  padding: 0px 12px 0px;\n  width: 200px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n#importcards section .import-card-createdby {\n  font-size: 12px;\n  padding: 0px 12px 0px;\n  width: 170px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n#importcards section .import-card-journal-title {\n  font-weight: bold;\n  padding: 0px 20px 0px;\n}\n#importcards section .import-card-text {\n  font-size: 12px;\n  width: 400px;\n  padding: 0px 20px 0px;\n  word-break: break-word;\n  min-height: 40px;\n}\n#importcards section .import-card-mod {\n  display: flex;\n  padding: 0px 10px 0px;\n}\n#importcards section .import-card-line {\n  display: flex;\n  border-top: 1px solid #CACACA;\n  padding: 0px 10px 0px;\n}\n#importcards section .import-card-mod-date {\n  display: flex;\n  flex-direction: column;\n  margin-right: 15px;\n}\n#importcards section .import-card-title {\n  width: 100px;\n  height: 30px;\n}\n#importcards section .import-card-file-import-lozenge {\n  background-color: #E7F7FF;\n  padding: 2px 10px 2px 10px;\n  margin-right: 5px;\n  border-radius: 15px;\n  border-color: #CFEFFE;\n  border-style: solid;\n  border-width: 3px;\n}\n\n.fileimportbutton {\n  height: 40px;\n  margin-left: 10px;\n  padding-left: 10px;\n  line-height: 25px;\n  text-align: center;\n}\n\n.ellipsis-button {\n  border: 2px solid;\n  border-color: #e5e5e5;\n  border-radius: 4px;\n  height: 25px;\n  position: relative;\n  color: white;\n}\n\n.ellipsis-button:hover {\n  color: white;\n  background-color: #ededed;\n}\n\n.ellipsis-button > a > .nav-menu-user-icon {\n  margin-right: initial;\n}\n\n.ellipsis-dropdown-content {\n  display: none;\n  flex-direction: column;\n  position: absolute;\n  background-color: white;\n  min-width: 90px;\n  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n}\n.ellipsis-dropdown-content ul > li {\n  margin-bottom: initial;\n}\n\n.ellipsis-dropdown-content > ul > li:hover {\n  background-color: #ddd;\n  cursor: pointer;\n}\n\n.ellipsis-container {\n  position: relative;\n}\n\n.ellipsis-container:hover .ellipsis-dropdown-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.ellipsis-dropdown-content > ul > li:nth-child(1) {\n  margin-top: 10px;\n  padding-left: 15px;\n}\n\n.ellipsis-dropdown-content > ul li:nth-child(2) {\n  margin-bottom: 10px;\n  padding-left: 15px;\n}\n\n#tracking-sheets section {\n  border-radius: 5px;\n}\n#tracking-sheets section .filters {\n  position: absolute;\n  top: 150px;\n  right: 80px;\n}\n#tracking-sheets section .filters select#company {\n  width: 250px;\n}\n#tracking-sheets section .filters select#year {\n  width: 90px;\n}\n#tracking-sheets section .cards {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 10px;\n  margin: 10px auto;\n}\n#tracking-sheets section .cards .card {\n  width: 140px;\n  padding: 0;\n  margin: 0 0 10px;\n}\n#tracking-sheets section .cards .card .card-info {\n  padding: 10px 10px 10px;\n  background: #E3F2FD;\n  border-top-left-radius: 5px;\n  border-top-right-radius: 5px;\n}\n#tracking-sheets section .cards .card .card-info h2 {\n  color: #1F394A;\n  font-size: 24px;\n  padding: 5px 0 0;\n}\n#tracking-sheets section .cards .card .card-info p {\n  color: #1F394A;\n  font-size: 13px;\n  margin: 0;\n}\n#tracking-sheets section .cards .card .card-buttons {\n  padding: 10px 10px 10px;\n  background: #CCE6F8;\n  border-bottom-left-radius: 5px;\n  border-bottom-right-radius: 5px;\n  font-size: 13px;\n}\n#tracking-sheets section .cards .card .card-buttons a {\n  color: #01699f;\n  margin-right: 15px;\n}\n#tracking-sheets section .cards .card.future .card-info {\n  background: #F3F6F9;\n}\n#tracking-sheets section .cards .card.future .card-info h2, #tracking-sheets section .cards .card.future .card-info p {\n  color: #E5E5E5;\n}\n#tracking-sheets section .cards .card.future .card-buttons {\n  background: #E5E5E5;\n}\n#tracking-sheets section .cards .card.missing .card-info {\n  background: #F8E5C4;\n}\n#tracking-sheets section .cards .card.missing .card-info h2, #tracking-sheets section .cards .card.missing .card-info p {\n  color: #1F394A;\n}\n#tracking-sheets section .cards .card.missing .card-buttons {\n  background: #F0DF9D;\n}\n#tracking-sheets section .cards .card.missing .card-buttons a {\n  color: #1F394A;\n}\n\n#split-references .company-multi-select {\n  height: 60px;\n}\n\n#apogepha-client-report section, #merz-client-report section, #accovion-client-report section {\n  border-radius: 5px;\n}\n#apogepha-client-report section .filters, #merz-client-report section .filters, #accovion-client-report section .filters {\n  position: absolute;\n  top: 150px;\n  right: 80px;\n}\n#apogepha-client-report section .filters select#year, #merz-client-report section .filters select#year, #accovion-client-report section .filters select#year {\n  width: 90px;\n}\n#apogepha-client-report section .cards, #merz-client-report section .cards, #accovion-client-report section .cards {\n  display: flex;\n  flex-wrap: wrap;\n  column-gap: 10px;\n  margin: 10px auto;\n}\n#apogepha-client-report section .cards .card, #merz-client-report section .cards .card, #accovion-client-report section .cards .card {\n  width: 140px;\n  padding: 0;\n  margin: 0 0 10px;\n}\n#apogepha-client-report section .cards .card .card-info, #merz-client-report section .cards .card .card-info, #accovion-client-report section .cards .card .card-info {\n  padding: 10px 10px 10px;\n  background: #E3F2FD;\n  border-top-left-radius: 5px;\n  border-top-right-radius: 5px;\n}\n#apogepha-client-report section .cards .card .card-info h2, #merz-client-report section .cards .card .card-info h2, #accovion-client-report section .cards .card .card-info h2 {\n  color: #1F394A;\n  font-size: 18px;\n  padding: 5px 0 0;\n}\n#apogepha-client-report section .cards .card .card-info p, #merz-client-report section .cards .card .card-info p, #accovion-client-report section .cards .card .card-info p {\n  color: #1F394A;\n  font-size: 13px;\n  margin: 0;\n}\n#apogepha-client-report section .cards .card .card-buttons, #merz-client-report section .cards .card .card-buttons, #accovion-client-report section .cards .card .card-buttons {\n  padding: 10px 10px 10px;\n  background: #CCE6F8;\n  border-bottom-left-radius: 5px;\n  border-bottom-right-radius: 5px;\n  font-size: 13px;\n}\n#apogepha-client-report section .cards .card .card-buttons a, #merz-client-report section .cards .card .card-buttons a, #accovion-client-report section .cards .card .card-buttons a {\n  color: #01699f;\n  margin-right: 15px;\n}\n#apogepha-client-report section .cards .card.future .card-info, #merz-client-report section .cards .card.future .card-info, #accovion-client-report section .cards .card.future .card-info {\n  background: #F3F6F9;\n}\n#apogepha-client-report section .cards .card.future .card-info h2, #apogepha-client-report section .cards .card.future .card-info p, #merz-client-report section .cards .card.future .card-info h2, #merz-client-report section .cards .card.future .card-info p, #accovion-client-report section .cards .card.future .card-info h2, #accovion-client-report section .cards .card.future .card-info p {\n  color: #E5E5E5;\n}\n#apogepha-client-report section .cards .card.future .card-buttons, #merz-client-report section .cards .card.future .card-buttons, #accovion-client-report section .cards .card.future .card-buttons {\n  background: #E5E5E5;\n}\n\n.custom-select.small:before {\n  top: 9px;\n}\n\nbutton.secondary:disabled,\nbutton.secondary.disabled,\na.button.secondary:disabled,\na.button.secondary.disabled {\n  border-color: #CACACA;\n  color: #CACACA;\n}\n\n.nav-menu > li .flyout {\n  top: 51px;\n  left: 1px;\n}\n\n#settings .global-container {\n  display: flex;\n}\n#settings .sidebar {\n  width: 200px;\n  margin-top: 10px;\n  margin-right: 50px;\n}\n#settings .main-content {\n  flex-grow: 2;\n}\n#settings .menu li {\n  padding: 5px;\n  margin-right: 5px;\n}\n#settings .menu li:hover {\n  background-color: #e7f7ff;\n}\n#settings .menu li.active {\n  background-color: #e7f7ff;\n  font-weight: 600;\n}\n#settings .menu a {\n  text-decoration: none;\n}\n#settings #journals-table .sub-header {\n  border: none;\n  background: none;\n}\n#settings #journals-table .sub-header h2 {\n  font-weight: 700;\n  margin-left: 10px;\n  font-size: 1.2rem;\n}\n#settings #journal-modal .modal-header h2 {\n  margin-right: 320px;\n  font-weight: 600;\n}\n#settings #journal-modal .modal-header button {\n  font-size: 1.5rem;\n  color: black;\n  background: none;\n}\n#settings #journal-modal .modal-body {\n  padding: 0 20px 10px;\n}\n#settings #journal-modal .modal-footer .button {\n  margin-right: 5px;\n}\n#settings .ai-suggestion-toggle-panel {\n  background-color: #EEE;\n  padding: 1rem;\n  max-width: 800px;\n}\n#settings .ai-suggestion-toggle-panel h2, #settings .ai-suggestion-toggle-panel p {\n  margin-left: 15px;\n}\n\n.ai-retry button {\n  width: 100%;\n  margin-bottom: 8rem;\n}\n\n#ad-hoc-list select {\n  background-color: #0073BE;\n  color: #ffffff;\n}\n#ad-hoc-list select option:disabled {\n  background-color: #E8E8E8;\n}\n#ad-hoc-list select option {\n  background-color: #ffffff;\n  color: #333;\n  padding: 8px;\n}\n\n#ad-hoc-manual-entry-form h1 {\n  padding: 20px;\n  font-weight: 500;\n}\n#ad-hoc-manual-entry-form #doi-exist-message, #ad-hoc-manual-entry-form #doi-invalid-message {\n  display: none;\n  padding: 5px;\n  background-color: #FCD9D1;\n  border: 1px solid #ccc;\n  border-radius: 5px;\n  margin-top: 5px;\n}\n#ad-hoc-manual-entry-form .error-message {\n  display: none;\n  padding: 5px;\n  color: red;\n  margin-top: 5px;\n  font-weight: 500;\n}\n#ad-hoc-manual-entry-form #sources-wrapper {\n  margin: 15px;\n  border: 1px solid #E8E8E8;\n  border-radius: 10px;\n}\n#ad-hoc-manual-entry-form #sources-wrapper h2 {\n  padding-left: 25px;\n  margin-top: 20px;\n}\n#ad-hoc-manual-entry-form #sources-wrapper .form-group {\n  padding-left: 20px;\n  padding-top: 10px;\n}\n\n#file-import-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n#file-import-container .button-container {\n  display: flex;\n  justify-content: flex-end;\n  width: 100%;\n  margin-top: 10px;\n}\n#file-import-container .button {\n  margin-right: 10px;\n}\n\n/*!\n * Generated using the Bootstrap Customizer (https://getbootstrap.com/docs/3.4/customize/)\n */\n/*!\n * Bootstrap v3.4.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\n.modal-open {\n  overflow: hidden;\n}\n\n.modal {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1050;\n  display: none;\n  overflow: hidden;\n  -webkit-overflow-scrolling: touch;\n  outline: 0;\n}\n\n.modal-body.dropping {\n  cursor: wait;\n}\n\n.modal.fade .modal-dialog {\n  -webkit-transform: translate(0, -25%);\n  -ms-transform: translate(0, -25%);\n  -o-transform: translate(0, -25%);\n  transform: translate(0, -25%);\n  -webkit-transition: -webkit-transform 0.3s ease-out;\n  -o-transition: -o-transform 0.3s ease-out;\n  transition: transform 0.3s ease-out;\n}\n\n.modal.in .modal-dialog {\n  -webkit-transform: translate(0, 0);\n  -ms-transform: translate(0, 0);\n  -o-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 10px;\n}\n\n.modal-content {\n  position: relative;\n  background-color: #ffffff;\n  -webkit-background-clip: padding-box;\n  background-clip: padding-box;\n  border: 1px solid #999999;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n  outline: 0;\n}\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1040;\n  background-color: #000000;\n}\n\n.modal-backdrop.fade {\n  filter: alpha(opacity=0);\n  opacity: 0;\n}\n\n.modal-backdrop.in {\n  filter: alpha(opacity=50);\n  opacity: 0.5;\n}\n\n.modal-header {\n  padding: 15px;\n  border-bottom: 1px solid #e5e5e5;\n}\n\n.modal-header .close {\n  margin-top: -2px;\n}\n\n.modal-title {\n  margin: 0;\n  line-height: 1.42857143;\n}\n\n.modal-body {\n  position: relative;\n  margin: 0px;\n  overflow: auto !important;\n}\n\n.modal-footer {\n  padding: 15px;\n  text-align: right;\n  border-top: 1px solid #e5e5e5;\n}\n\n.modal-footer .btn + .btn {\n  margin-bottom: 0;\n  margin-left: 5px;\n}\n\n.modal-footer .btn-group .btn + .btn {\n  margin-left: -1px;\n}\n\n.modal-footer .btn-block + .btn-block {\n  margin-left: 0;\n}\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n@media (min-width: 768px) {\n  .modal-dialog {\n    width: 600px;\n    margin: 30px auto;\n  }\n  .modal-content {\n    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\n  }\n  .modal-sm {\n    width: 300px;\n  }\n}\n@media (min-width: 992px) {\n  .modal-lg {\n    width: 900px;\n  }\n}\n.clearfix:before,\n.clearfix:after,\n.modal-header:before,\n.modal-header:after,\n.modal-footer:before,\n.modal-footer:after {\n  display: table;\n  content: \" \";\n}\n\n.clearfix:after,\n.modal-header:after,\n.modal-footer:after {\n  clear: both;\n}\n\n.center-block {\n  display: block;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.pull-right {\n  float: right !important;\n}\n\n.pull-left {\n  float: left !important;\n}\n\n.hide {\n  display: none !important;\n}\n\n.show {\n  display: block !important;\n}\n\n.invisible {\n  visibility: hidden;\n}\n\n.text-hide {\n  font-size: 0;\n  line-height: 0;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n.hidden {\n  display: none !important;\n}\n\n.affix {\n  position: fixed;\n}\n\n/* Multi-select */\n.multi-select {\n  position: absolute;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  align-items: flex-start;\n  z-index: 1;\n  user-select: none;\n  border: 1px solid #CACACA;\n  border-radius: 8px;\n  overflow: hidden;\n  width: inherit;\n}\n.multi-select label {\n  margin-bottom: 0px;\n}\n.multi-select .preview-box {\n  display: flex;\n  flex-direction: row;\n  align-self: stretch;\n  align-items: center;\n  padding: 6px 10px;\n  border-bottom: 1px solid transparent;\n}\n.multi-select .preview-box .selected-label {\n  display: block;\n  align-self: center;\n  margin-right: auto;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: normal;\n  color: #000;\n}\n.multi-select .preview-box .selected-count {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  justify-self: end;\n  margin-left: 10px;\n  text-align: center;\n  background-color: #461E96;\n  color: #fff;\n  min-height: calc(1.5rem + 2px);\n  min-width: calc(1.5rem + 2px);\n  border-radius: 50%;\n}\n.multi-select .preview-box .filter-multiselect {\n  height: 30px;\n  margin: 0px;\n  border: none;\n}\n.multi-select .item-container {\n  display: flex;\n  align-self: stretch;\n  flex-direction: column;\n  padding-bottom: 1px;\n  overflow-y: auto;\n  max-height: 16em;\n}\n.multi-select .multi-select-item {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: 0px 4px;\n}\n.multi-select .multi-select-item.disabled-item {\n  background-color: #E8E8E8;\n  opacity: 0.5;\n  pointer-events: none;\n}\n.multi-select .multi-select-item:hover {\n  background-color: #e9e9e9;\n}\n.multi-select .multi-select-item:hover input {\n  background: #fff;\n}\n.multi-select .multi-select-item input {\n  top: 0;\n  margin: 0;\n  margin-right: 5px;\n  border-radius: 3px;\n}\n.multi-select .multi-select-item .multiSelectItemLabel {\n  padding: 6px 0px;\n  width: 100%;\n  font-weight: normal;\n  color: #000;\n}\n.multi-select .multi-select-item .formatLabel {\n  display: flex;\n  align-items: center;\n  align-self: normal;\n}\n.multi-select .hidden {\n  display: none;\n}\n.multi-select:hover {\n  border: 1px solid #8A8A8A;\n}\n.multi-select.active {\n  border: 1px solid #461E96;\n}\n.multi-select.active .preview-box {\n  border-bottom: 1px solid #461E96;\n}\n.multi-select.disabled {\n  pointer-events: none;\n  background-color: #f2f4f6;\n}\n.multi-select.disabled .selected-label {\n  color: #949494;\n}\n.multi-select.disabled .selected-count {\n  background-color: #949494;\n}\n\n/* Companies Multi-select */\n.companies-multi-select {\n  position: absolute;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  align-items: flex-start;\n  z-index: 1;\n  user-select: none;\n  border: 1px solid #CACACA;\n  border-radius: 8px;\n  overflow: hidden;\n  width: calc(40% - 4rem);\n}\n.companies-multi-select label {\n  margin-bottom: 0px;\n}\n.companies-multi-select .select-box {\n  display: flex;\n  flex-direction: row;\n  align-self: stretch;\n  padding: 6px 10px;\n  border-bottom: 1px solid transparent;\n}\n.companies-multi-select .select-box .selected-label {\n  display: block;\n  align-self: center;\n  margin-right: auto;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.companies-multi-select .select-box .selected-count {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  justify-self: end;\n  margin-left: 10px;\n  text-align: center;\n  background-color: #461E96;\n  color: #fff;\n  min-height: 20px;\n  min-width: 20px;\n  border-radius: 50%;\n}\n.companies-multi-select .check-box {\n  display: flex;\n  align-self: stretch;\n  flex-direction: column;\n  padding-bottom: 1px;\n  overflow-y: auto;\n  max-height: 16em;\n}\n.companies-multi-select .companies-multi-select-item {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: 0px 4px;\n}\n.companies-multi-select .companies-multi-select-item:hover {\n  background-color: #e9e9e9;\n}\n.companies-multi-select .companies-multi-select-item:hover input {\n  background: #fff;\n}\n.companies-multi-select .companies-multi-select-item input {\n  top: 0;\n  margin: 0;\n  margin-right: 5px;\n  border-radius: 3px;\n}\n.companies-multi-select .companies-multi-select-item .multiSelectItemLabel {\n  padding: 6px 0px;\n  width: 100%;\n}\n.companies-multi-select .companies-multi-select-item .formatLabel {\n  display: flex;\n  align-items: center;\n  align-self: normal;\n}\n.companies-multi-select .hidden {\n  display: none;\n}\n.companies-multi-select:hover {\n  border: 1px solid #8A8A8A;\n}\n.companies-multi-select.active {\n  border: 1px solid #461E96;\n}\n.companies-multi-select.active .select-box {\n  border-bottom: 1px solid #461E96;\n}\n\n.switch-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 1rem;\n  width: 100%;\n}", "﻿input:disabled, select:disabled {\r\n    opacity: 0.7;\r\n}\r\n\r\nselect option.inactive {\r\n    background: #DDD;\r\n}\r\n\r\n.buttons {\r\n    margin-top: 1rem;\r\n}\r\n\r\ntable thead {\r\n    background-color: white;\r\n    border-bottom: none;\r\n}\r\n\r\ntable tr th {\r\n    padding: 0.6rem 0.75rem;\r\n}\r\n\r\ntable tr.selectable:hover td {\r\n    background-color: #e9e9e9;\r\n}\r\n\r\n.pager {\r\n    background-color: white;\r\n    border-radius: 0.6rem;\r\n\r\n    .page-size {\r\n        label {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\ntable tr td {\r\n    vertical-align: middle;\r\n}\r\n\r\ntable {\r\n    border-bottom: solid 2px #f0f7f7;\r\n}\r\n\r\ntable tr:last-of-type {\r\n    border-bottom: none;\r\n}\r\n\r\ntable tbody tr:nth-child(odd) {\r\n    background: $grey-ultra-light;\r\n}\r\n\r\ntable th,\r\ntable td {\r\n    padding: 0.6rem 0.75rem;\r\n}\r\n\r\n.data-table-actions {\r\n    text-align: right;\r\n}\r\n\r\n.data-table-actions div {\r\n    float: right;\r\n}\r\n\r\n.dataTable th {\r\n    overflow: visible;\r\n}\r\n\r\n.input-validation-error {\r\n    border: 1px solid #8f0101 !important;\r\n}\r\n\r\nspan.validation-error {\r\n    padding: 0;\r\n\r\n    span {\r\n        font-size: 1rem;\r\n        color: #8f0101;\r\n    }\r\n}\r\n\r\n.page-size {\r\n    flex: 0 240px !important;\r\n}\r\n\r\n.tabs {\r\n    display: flex;\r\n    border-bottom: 2px solid $grey-4;\r\n    margin-bottom: 1rem;\r\n\r\n    li {\r\n        margin-bottom: -2px;\r\n        margin-right: 1em;\r\n        cursor: pointer;\r\n        vertical-align: middle;\r\n        text-align: center;\r\n        color: $grey-1;\r\n        padding: 5px 0 10px 0;\r\n\r\n        span {\r\n            vertical-align: middle;\r\n\r\n            &:first-child {\r\n                margin-right: 7px;\r\n            }\r\n\r\n            &:hover {\r\n                color: $brand;\r\n                transition-duration: 0.5s;\r\n            }\r\n        }\r\n\r\n        a {\r\n            vertical-align: middle;\r\n            margin-right: 7px;\r\n            color: $grey-1;\r\n\r\n            &:hover {\r\n                color: $brand;\r\n            }\r\n        }\r\n\r\n        &.active {\r\n            color: $brand;\r\n            padding-bottom: -2px;\r\n            border-bottom: 2px solid $brand;\r\n            cursor: default;\r\n\r\n            a {\r\n                color: $brand-darker;\r\n\r\n                &:hover {\r\n                    color: $brand;\r\n                }\r\n            }\r\n        }\r\n\r\n        &.separator {\r\n            display: inline-block;\r\n            border-right: 2px solid $grey-3;\r\n            cursor: default;\r\n            margin-top: 10px;\r\n            margin-left: 3px;\r\n        }\r\n\r\n        &:last-of-type {\r\n            margin-bottom: -2px;\r\n        }\r\n    }\r\n\r\n    .tab-badge {\r\n        display: inline-block;\r\n        line-height: 1rem;\r\n        margin: 1px auto;\r\n        text-align: center;\r\n        border-radius: 15px;\r\n        color: $white;\r\n        background-color: $reg;\r\n        padding: 5px 10px 4px;\r\n\r\n        &.completed {\r\n            background-color: $brand;\r\n        }\r\n\r\n        &:hover {\r\n            color: $white;\r\n        }\r\n    }\r\n\r\n    li.nolink {\r\n        cursor: default;\r\n\r\n        span {\r\n            &:hover {\r\n                color: $grey-1;\r\n            }\r\n\r\n            &.tab-badge {\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tabs.borderless {\r\n    border: none;\r\n\r\n    li.active {\r\n        border: none;\r\n    }\r\n}\r\n\r\n.field-validation-error {\r\n    color: #8f0101;\r\n    padding-left: 0.8rem;\r\n    line-height: 1.6rem;\r\n}\r\n\r\n.sub-nav {\r\n    height: 35px;\r\n    padding-top: 8px;\r\n    padding-left: 10px;\r\n}\r\n\r\n.sub-nav li {\r\n    float: left;\r\n    margin-right: 22px;\r\n}\r\n\r\n.sub-nav a {\r\n    color: #727272;\r\n    cursor: pointer;\r\n    text-decoration: none;\r\n\r\n    &:hover {\r\n        color: $brand;\r\n        transition-duration: 0.5s;\r\n    }\r\n}\r\n\r\n.sub-nav a.active {\r\n    color: $grey-dark;\r\n    border-bottom: 2px solid $brand;\r\n}\r\n\r\n.sub-layout-header-buttons {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.core-content.has-background {\r\n    background-image: url(/src/images/splash.jpg);\r\n}\r\n\r\n.z-index-1 {\r\n    z-index: 1;\r\n}\r\n\r\n.chip {\r\n    border-radius: 15px;\r\n    background-color: $blue-600;\r\n    color: #fff;\r\n    height: 25px;\r\n    display: inline-block;\r\n    font-weight: bold;\r\n    margin: 0 10px 10px 0;\r\n    padding: 0 0 0 10px;\r\n    vertical-align: middle;\r\n}\r\n\r\n.chip span {\r\n    position: relative;\r\n    top: 1px;\r\n    padding-right: 2px;\r\n}\r\n\r\n.chip i {\r\n    position: relative;\r\n    top: 5px;\r\n    font-size: 1rem;\r\n    margin-right: -10px;\r\n}\r\n\r\n.sub-header {\r\n    margin-left: -2rem;\r\n    margin-right: -2rem;\r\n    margin-bottom: 1rem;\r\n    position: sticky;\r\n    top: -1rem;\r\n    z-index: 99;\r\n}\r\n\r\n.core-content {\r\n    padding-left: 30px;\r\n    padding-right: 1.875rem;\r\n    padding-top: 0;\r\n}\r\n\r\n.core-content section.card-container {\r\n    background: none;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n\r\n    .section-card {\r\n        border-radius: 4px;\r\n        flex-basis: 30%;\r\n        background-color: $white;\r\n        position: relative;\r\n        padding: 1.5rem;\r\n    }\r\n\r\n    .section-card.expand {\r\n        @media screen and (max-width: 1200px) {\r\n            flex-basis: 40%;\r\n        }\r\n    }\r\n}\r\n\r\n.flex-gap {\r\n    gap: 1rem;\r\n}\r\n\r\n.flex-align-center label {\r\n    margin-bottom: 0rem;\r\n}\r\n\r\n.version-label {\r\n    background: $brand;\r\n    color: $white;\r\n    padding: $padding-unit 1rem;\r\n    border-radius: 4px;\r\n}\r\n\r\n.full-width-rule {\r\n    margin: 0 -1.5rem;\r\n}\r\n\r\n.switch-container.switch-active-inactive {\r\n    user-select: none;\r\n\r\n    input.switch {\r\n        margin: 0;\r\n    }\r\n\r\n    label.switch {\r\n        text-indent: unset;\r\n        color: $white;\r\n        font-weight: 400;\r\n        width: 80px;\r\n        text-align: right;\r\n        padding-top: 6px;\r\n        padding-right: $padding-unit;\r\n        margin: 0;\r\n\r\n        &:before {\r\n            content: \"Inactive\";\r\n        }\r\n    }\r\n\r\n    input.switch:checked + label {\r\n        &:after {\r\n            transform: translateX(200%);\r\n        }\r\n\r\n        &:before {\r\n            content: \"Active\";\r\n            position: absolute;\r\n            left: 0.75rem;\r\n        }\r\n    }\r\n}\r\n\r\n.switch-container.switch-enabled-disabled {\r\n    user-select: none;\r\n    margin-top: .75rem;\r\n\r\n    input.switch {\r\n        margin: 0;\r\n    }\r\n\r\n    label.switch {\r\n        text-indent: unset;\r\n        color: $white;\r\n        font-weight: 400;\r\n        width: 80px;\r\n        text-align: right;\r\n        padding-top: 6px;\r\n        padding-right: 0.3rem;\r\n        margin: 0;\r\n\r\n        &:before {\r\n            content: \"Disabled\";\r\n        }\r\n    }\r\n\r\n    input.switch:checked + label {\r\n        &:after {\r\n            transform: translateX(200%);\r\n        }\r\n\r\n        &:before {\r\n            margin-right: 50px;\r\n            content: \"Enabled\";\r\n            position: absolute;\r\n            left: 0.45rem;\r\n        }\r\n    }\r\n}\r\n/* Status Lozenge */\r\n[class^=\"state-indicator-\"],\r\n[class*=\"state-indicator-\"] {\r\n    display: inline-block;\r\n    width: 100%;\r\n    line-height: 1.5rem;\r\n    border-radius: 1rem;\r\n    color: $white;\r\n    text-align: center;\r\n    font-size: .75rem;\r\n    min-width: 3.125rem;\r\n    background-color: $brand;\r\n}\r\n\r\n.state-indicator-new {\r\n    background-color: $grey-1;\r\n}\r\n\r\n.state-indicator-updated {\r\n    background-color: $stats;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.state-indicator-preclassified {\r\n    background-color: $safety;\r\n}\r\n\r\n.state-indicator-approved {\r\n    background-color: $success;\r\n}\r\n\r\n.state-indicator-reclassified {\r\n    background-color: $yellow-800;\r\n}\r\n\r\n.state-indicator-signed {\r\n    background-color: $brand;\r\n}\r\n\r\n.state-indicator-inactive {\r\n    background-color: $blue-dark;\r\n}\r\n\r\n.support-reference {\r\n    border: 1px solid $grey-3;\r\n    border-radius: 10px;\r\n    padding: 1rem;\r\n}\r\n\r\n// Support (History)\r\n.support-container {\r\n    padding: 1rem;\r\n    background: $white;\r\n\r\n    li a {\r\n        color: #6c767d;\r\n    }\r\n\r\n    li.active a {\r\n        color: #127f92;\r\n    }\r\n\r\n    ul li.active {\r\n        color: $brand;\r\n    }\r\n}\r\n\r\n.support-lozenge {\r\n    display: flex;\r\n    border-radius: 10px;\r\n    margin-bottom: 1rem;\r\n    cursor: pointer;\r\n\r\n    &.selected {\r\n        border: 2px solid $brand;\r\n    }\r\n\r\n    &.selectable {\r\n        border: 2px solid $grey-3;\r\n    }\r\n\r\n    div.lozenge-container {\r\n        padding: 5px 20px 5px 5px;\r\n    }\r\n\r\n    div.action-container {\r\n        padding: 5px 5px 5px 0;\r\n    }\r\n\r\n    .version-badge {\r\n        display: inline-block;\r\n        margin: 0.2rem auto;\r\n        width: 2.2rem;\r\n        line-height: 2.2rem;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n        color: $brand-darker;\r\n        background-color: rgba($brand, 0.1);\r\n    }\r\n\r\n    [class^=\"state-indicator-\"],\r\n    [class*=\"state-indicator-\"] {\r\n        width: 8rem;\r\n        line-height: 30px;\r\n    }\r\n}\r\n\r\n#support-history-modal {\r\n\r\n    .modal-container {\r\n        border-radius: 5px;\r\n    }\r\n\r\n    .modal-header {\r\n        border: none;\r\n    }\r\n\r\n    input {\r\n        height: 10vh;\r\n    }\r\n\r\n    .dialog-custom-content div:nth-child(2) {\r\n        align-items: center;\r\n        margin-left: auto;\r\n    }\r\n\r\n    a {\r\n        margin: 1.5rem 0 0 0.5rem;\r\n    }\r\n\r\n    #dialog-closer {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.reference-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .reference-group {\r\n        display: flex;\r\n        margin-bottom: 1rem;\r\n\r\n        label {\r\n            flex: 1 0 12%;\r\n        }\r\n\r\n        span,\r\n        a {\r\n            flex: 1 0 88%;\r\n        }\r\n    }\r\n}\r\n\r\n// Pre-classification\r\n.reference-container-row {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    border: 1px solid $grey-3;\r\n    border-radius: 0.6rem;\r\n    background-color: #fff;\r\n\r\n    > div {\r\n        padding: 1rem;\r\n    }\r\n\r\n    .reference-info {\r\n        display: flex;\r\n        flex-direction: column;\r\n        flex: 0 0 280px;\r\n        position: relative;\r\n\r\n        .reference-group {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            word-break: break-all;\r\n\r\n            label {\r\n                flex: 1 0 25%;\r\n            }\r\n\r\n            span {\r\n                flex: 1 0 75%;\r\n            }\r\n        }\r\n\r\n        .positionBottom {\r\n            position: absolute;\r\n            bottom: 1rem;\r\n            display: block;\r\n            width: 90%;\r\n\r\n            label {\r\n                padding-right: 10px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .reference-content {\r\n        flex: 5 1 25rem;\r\n        border-right: 1px solid $grey-3;\r\n\r\n        .reference-group {\r\n            margin-bottom: 1rem;\r\n        }\r\n\r\n        label {\r\n            display: block;\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.reference-classification {\r\n    flex: 0 0 350px;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .form-group:last-of-type {\r\n        margin-bottom: 1.5rem;\r\n    }\r\n\r\n    &.ai-expanded {\r\n        flex: 0 0 370px;\r\n\r\n        .form-group.ai-suggestion, .form-group.ai-status {\r\n            margin-bottom: 10px;\r\n        }\r\n    }\r\n\r\n    .dosage-form {\r\n        display: flex;\r\n\r\n        div:first-child {\r\n            flex: 1 1 30%;\r\n        }\r\n\r\n        .dosage-form-badges {\r\n            flex: 1 1 70%;\r\n            margin-left: 1rem;\r\n\r\n            > span {\r\n                display: inline-block;\r\n                min-width: 1.5rem;\r\n                height: 1.5rem;\r\n                background-color: $quality;\r\n                text-align: center;\r\n                color: $white;\r\n                margin: 0 0 0.5rem 0.5rem;\r\n                line-height: 1.5rem;\r\n                padding: 0 1rem;\r\n                border-radius: 0.9rem;\r\n\r\n                &:hover {\r\n                    cursor: pointer;\r\n                    opacity: 0.8;\r\n                    transition-duration: 0.2s;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dosage-form-badges.disabled {\r\n            > span {\r\n                opacity: 0.5;\r\n\r\n                &:hover {\r\n                    cursor: default;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .ai-status i {\r\n        font-size: 30px;\r\n        width: 30px;\r\n        cursor: default;\r\n        opacity: 1;\r\n    }\r\n\r\n    ai-status i:hover {\r\n        opacity: 1;\r\n    }\r\n\r\n    .ai-status div {\r\n        margin-left: 5px;\r\n        padding: 6px 12px 6px 12px;\r\n        border-radius: 0px 8px 8px 8px;\r\n    }\r\n\r\n    .ai-status-awaiting-response div {\r\n        background: #FFF8BA;\r\n    }\r\n\r\n    .ai-status-success div {\r\n        background: #D8FEE7;\r\n    }\r\n\r\n    .ai-status-failed div {\r\n        background: #FCD9D1;\r\n    }\r\n\r\n    .ai-suggestion {\r\n\r\n        &.category.rejected {\r\n            margin-bottom: 19px;\r\n        }\r\n\r\n        &.pending > div {\r\n            background: #E7F7FF;\r\n        }\r\n\r\n        &.accepted > div {\r\n            background: #D8FEE7;\r\n        }\r\n\r\n        &.rejected > div {\r\n            background: #F9F9F9;\r\n        }\r\n\r\n        &.pending .header {\r\n            background: #CEEDFC;\r\n        }\r\n\r\n        &.accepted .header {\r\n            background: #C4F5D8;\r\n        }\r\n\r\n        &.rejected .header {\r\n            background: #F5F5F5;\r\n        }\r\n\r\n        .header {\r\n            width: 100%;\r\n            padding: 10px 20px 10px 20px;\r\n            border-radius: 8px 8px 0px 0px;\r\n            border-bottom: 1px solid white;\r\n\r\n            .header-text {\r\n                font-weight: bold;\r\n                color: #461E96;\r\n                line-height: 25px;\r\n                width: 240px;\r\n            }\r\n\r\n            .header-buttons {\r\n                display: flex;\r\n\r\n                .icon-button {\r\n                    display: inline-block;\r\n                    width: 25px;\r\n                    height: 25px;\r\n                    text-align: center;\r\n                    margin-left: 5px;\r\n                    border: 1px solid #CACACA;\r\n                    border-radius: 5px;\r\n                    background: #FFF;\r\n\r\n                    &:hover {\r\n                        background: #F5F5F5;\r\n                        cursor: pointer;\r\n                    }\r\n\r\n                    i {\r\n                        font-size: 17px;\r\n                        position: relative;\r\n                        left: -2px;\r\n                        top: 3px;\r\n\r\n                        &:hover {\r\n                            opacity: 1;\r\n                        }\r\n                    }\r\n\r\n                    &.disabled {\r\n                        opacity: 0.5;\r\n\r\n                        &:hover {\r\n                            opacity: 0.5;\r\n                            background: #FFF;\r\n                            cursor: default;\r\n                        }\r\n\r\n                        i:hover {\r\n                            cursor: default;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                .undo-button {\r\n                    display: inline-block;\r\n                    position: relative;\r\n                    padding: 5px;\r\n                    margin-left: 20px;\r\n                    cursor: pointer;\r\n                }\r\n            }\r\n        }\r\n\r\n        .suggested-value {\r\n            padding: 15px 20px;\r\n            border-bottom: 1px solid white;\r\n\r\n            div:first-child {\r\n                font-weight: bold;\r\n            }\r\n\r\n            div:last-child.value-box {\r\n                background: white;\r\n                border-radius: 5px;\r\n                padding: 11px 15px 12px 15px;\r\n                margin-top: 5px;\r\n            }\r\n\r\n            div:last-child .dosage-form {\r\n                display: inline-block;\r\n\r\n                .dosage-form-badges {\r\n                    margin-left: 0;\r\n                    margin-top: 10px;\r\n                }\r\n            }\r\n\r\n            div:last-child select, div:last-child input {\r\n                background: white;\r\n                margin-top: 5px;\r\n            }\r\n        }\r\n\r\n        .reasoning {\r\n            padding: 15px 20px;\r\n            border-bottom: 1px solid white;\r\n\r\n            div:first-child {\r\n                font-weight: bold;\r\n            }\r\n\r\n            div:last-child {\r\n                margin-top: 5px;\r\n            }\r\n        }\r\n\r\n        .previous-value {\r\n            padding: 15px 20px;\r\n            border-bottom: 1px solid white;\r\n        }\r\n\r\n        & > div:last-child {\r\n            border-radius: 0px 0px 8px 8px;\r\n            padding-bottom: 15px;\r\n        }\r\n    }\r\n\r\n    .form-group.actions {\r\n        height: 42px;\r\n        margin-top: auto;\r\n        width: 108%;\r\n        text-align: right;\r\n        border-top: 1px solid $grey-3;\r\n        margin-left: -1rem;\r\n        margin-right: -3rem;\r\n        padding-top: 1rem;\r\n\r\n        .preClassifierName {\r\n            float: left;\r\n            text-align: left;\r\n            padding-left: 1rem;\r\n            padding-top: 5px;\r\n        }\r\n\r\n        button, div {\r\n            margin: 0 1rem;\r\n\r\n            .switch-container {\r\n                position: relative;\r\n                top: -2px;\r\n                right: -8px;\r\n\r\n                &:focus {\r\n                    border: 2px solid #111;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .separated {\r\n        margin: 0 -1rem;\r\n        padding: 1rem;\r\n        border-top: 1px solid $grey-3;\r\n    }\r\n}\r\n\r\n.switch-container input.switch:checked + label {\r\n    color: $white;\r\n}\r\n\r\n.page-header .main-navigation-wrapper {\r\n    width: 100%;\r\n    display: flex;\r\n}\r\n\r\n.flyout-header.account {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.horizontal-filter {\r\n    background-color: #EEE;\r\n    padding: 1rem;\r\n\r\n    .input-group {\r\n        margin-bottom: 1rem;\r\n    }\r\n\r\n    select {\r\n        width: auto;\r\n        background-color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .horizontal-filter-item {\r\n        display: inline-block;\r\n        margin: 0 10px 0 0;\r\n        padding: 0;\r\n        max-width: min-content;\r\n\r\n        #btnLabelSearch {\r\n            margin-top: .5rem;\r\n        }\r\n\r\n        .reset-button {\r\n            background-color: #737373;\r\n            transition-duration: 0.5s;\r\n\r\n            &:hover {\r\n                background-color: #454545;\r\n            }\r\n        }\r\n\r\n        select {\r\n            margin: 0;\r\n        }\r\n    }\r\n\r\n    i {\r\n        display: block;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 30px;\r\n    }\r\n\r\n    &.flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: flex-start;\r\n    }\r\n}\r\n\r\nabbr[title] {\r\n    border-bottom: none;\r\n    text-decoration: none;\r\n}\r\n\r\n// Edit Substance page\r\n#substance {\r\n    #synonyms-column {\r\n        .form-group {\r\n            #addSynonym {\r\n                flex-grow: 1;\r\n            }\r\n\r\n            #addBtn {\r\n                margin-left: 10px;\r\n            }\r\n        }\r\n\r\n        #table-synonyms {\r\n            border-bottom: none;\r\n            max-width: 600px;\r\n            margin-bottom: 1rem;\r\n\r\n            tbody {\r\n                tr {\r\n                    td .m-icon {\r\n                        float: right;\r\n                    }\r\n\r\n                    &:nth-child(odd) {\r\n                        background-color: $grey-5;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.table-filter-items {\r\n    input {\r\n        &[type=search] {\r\n            padding-right: 12px;\r\n        }\r\n    }\r\n\r\n    .m-icon.close {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.emailBounceText {\r\n    height: 280px;\r\n}\r\n\r\n.emailSuppressTableText {\r\n    color: $white;\r\n    margin-left: 30px;\r\n    padding: 5px;\r\n    border-radius: 5px;\r\n}\r\n\r\n.email-bounce-background {\r\n    background-color: $error;\r\n}\r\n\r\n.email-block-background {\r\n    background-color: $warning;\r\n}\r\n\r\n.email-spam-background {\r\n    background-color: $grey-1;\r\n}\r\n\r\n.email-bounce-foreground {\r\n    color: $error;\r\n}\r\n\r\n.email-block-foreground {\r\n    color: $warning;\r\n}\r\n\r\n.email-spam-foreground {\r\n    color: $grey-1;\r\n}\r\n\r\n#company-user-edit {\r\n    .suppression-reason {\r\n        border: 1px solid #c4c4c4;\r\n        opacity: 0.7;\r\n        border-radius: 0.5rem;\r\n        background: #f3f6f9;\r\n        padding: .75rem;\r\n        margin-bottom: 1.5rem;\r\n        height: 175px;\r\n        overflow-y: scroll;\r\n    }\r\n}\r\n\r\n\r\n.cursor-not-allowed {\r\n    cursor: not-allowed;\r\n}\r\n\r\n.pointer-events-none {\r\n    pointer-events: none;\r\n}\r\n\r\n.disabled {\r\n    opacity: 0.7;\r\n}\r\n\r\n#dashboard {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    column-gap: 25px;\r\n    margin: 15px auto;\r\n\r\n    #import-dashboard-container {\r\n        margin-bottom: 30px;\r\n\r\n        &.narrow {\r\n            width: 680px;\r\n        }\r\n\r\n        &.wide {\r\n            max-width: 1340px;\r\n\r\n            &.empty {\r\n                min-width: 680px;\r\n            }\r\n        }\r\n\r\n        #import-dashboard {\r\n\r\n            .content {\r\n                background: white;\r\n                border-radius: 10px;\r\n                padding: 20px;\r\n\r\n                .cards {\r\n                    display: flex;\r\n                    flex-wrap: wrap;\r\n                    column-gap: 20px;\r\n                    row-gap: 10px;\r\n\r\n                    .card {\r\n                        border: 1px solid #C4C4C4;\r\n                        border-radius: 10px;\r\n                        padding: 0;\r\n                        width: 310px;\r\n                        line-height: normal;\r\n\r\n                        .info {\r\n                            display: flex;\r\n                            flex-wrap: nowrap;\r\n                            width: 100%;\r\n                            padding: 16px 20px;\r\n                            height: 96px;\r\n\r\n                            .text {\r\n                                width: 36%;\r\n                                text-align: left;\r\n\r\n                                .import-type {\r\n                                    font-size: 17px;\r\n                                    font-weight: bold;\r\n                                    text-transform: uppercase;\r\n\r\n                                    &.scheduled {\r\n                                        color: $blue-dark;\r\n                                    }\r\n\r\n                                    &.adhoc {\r\n                                        color: $brand;\r\n                                    }\r\n                                }\r\n\r\n                                .import-date {\r\n                                    font-size: 14px;\r\n                                    color: #1F394A;\r\n                                    padding-top: 5px;\r\n                                    padding-bottom: 7px;\r\n                                }\r\n\r\n                                .import-status {\r\n                                    font-size: 12px;\r\n                                    font-weight: bold;\r\n                                    color: #000000;\r\n                                }\r\n                            }\r\n\r\n                            .number {\r\n                                width: 64%;\r\n                                text-align: right;\r\n\r\n                                .todo-count-label {\r\n                                    font-size: 12px;\r\n                                    color: #1F394A;\r\n                                    padding-top: 2px;\r\n                                    padding-bottom: 5px;\r\n                                }\r\n\r\n                                .todo-count-number {\r\n                                    font-size: 38px;\r\n                                    font-weight: bold;\r\n                                    color: $blue-400;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        .buttons {\r\n                            display: flex;\r\n                            flex-wrap: nowrap;\r\n                            border-top: 1px solid #C4C4C4;\r\n                            width: 100%;\r\n                            background: transparent;\r\n                            padding: 10px 20px;\r\n                            margin: 0;\r\n\r\n                            button, a.button {\r\n                                padding: 8px 16px;\r\n\r\n                                &:focus {\r\n                                    border: 2px solid black !important;\r\n                                    padding: 7px 15px;\r\n                                    transition-duration: 0s;\r\n                                }\r\n                            }\r\n\r\n                            .left {\r\n                                text-align: left;\r\n                                width: 30%;\r\n                            }\r\n\r\n                            .right {\r\n                                text-align: right;\r\n                                width: 70%;\r\n\r\n                                .details-button {\r\n                                    margin-right: 5px;\r\n                                }\r\n\r\n                                .details-button, .archive-button {\r\n                                    background: white;\r\n                                    color: black;\r\n                                    border: 1px solid black;\r\n\r\n                                    &:hover {\r\n                                        background: #1f394a;\r\n                                        color: white;\r\n                                        transition-duration: 0.2s;\r\n                                    }\r\n\r\n                                    &:disabled {\r\n                                        background: white;\r\n                                        color: black;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        &.selected {\r\n                            border: 3px solid $green-500;\r\n\r\n                            .info {\r\n                                padding: 14px 18px 16px;\r\n                                height: 94px;\r\n\r\n                                .text {\r\n                                    .import-type {\r\n                                        color: #028930;\r\n                                    }\r\n                                }\r\n                            }\r\n\r\n                            .buttons {\r\n                                padding: 10px 18px 8px;\r\n\r\n                                .deselect-button {\r\n                                    background: #028930;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                .empty {\r\n                    p {\r\n                        margin: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    #email-dashboard-container {\r\n        width: 680px;\r\n        margin-bottom: 10px;\r\n\r\n        #email-dashboard {\r\n\r\n            .content {\r\n                background: white;\r\n                border-radius: 10px;\r\n                padding: 20px;\r\n\r\n                .cards {\r\n                    display: flex;\r\n                    flex-wrap: nowrap;\r\n                    column-gap: 20px;\r\n                    margin-bottom: 20px;\r\n\r\n                    .card {\r\n                        border: 1px solid #C4C4C4;\r\n                        border-radius: 10px;\r\n                        width: 200px;\r\n                        text-align: center;\r\n                        padding: 40px 30px;\r\n                        line-height: normal;\r\n\r\n                        .text {\r\n                            font-size: 16px;\r\n                            font-weight: bold;\r\n                        }\r\n\r\n                        .value {\r\n                            font-size: 70px;\r\n                            color: #572F8C;\r\n                            font-weight: bold;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                #table-container {\r\n                    border: 1px solid #C4C4C4;\r\n                    border-radius: 10px;\r\n\r\n                    table {\r\n                        background: transparent;\r\n\r\n                        thead {\r\n                            background: transparent;\r\n                        }\r\n\r\n                        td, th {\r\n                            padding: 15px 20px 14px;\r\n                        }\r\n                    }\r\n\r\n                    .pager {\r\n                        padding-left: 6px;\r\n                        padding-right: 6px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        #build-info {\r\n            margin-top: 30px;\r\n\r\n            .content {\r\n                background: white;\r\n                border-radius: 10px;\r\n                padding: 20px;\r\n            }\r\n\r\n            p:last-of-type {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n#reference-details-container {\r\n\r\n    #reference-substance-section, #reference-history-section {\r\n        border: solid 1px #c4c4c4;\r\n        border-radius: 5px;\r\n        padding: 20px;\r\n\r\n        li:hover {\r\n            color: $brand;\r\n        }\r\n    }\r\n\r\n    #substance-list {\r\n        cursor: pointer;\r\n        border: 2px solid #c4c4c4;\r\n        border-radius: 5px;\r\n        padding: 10px;\r\n\r\n        span.substance-name {\r\n            font-weight: bold;\r\n        }\r\n\r\n        span.plxid {\r\n            display: inline-block;\r\n            padding-top: 5px;\r\n            font-size: 11px;\r\n        }\r\n    }\r\n\r\n    .reference-details-section {\r\n        border: solid 1px #c4c4c4;\r\n        border-radius: 5px;\r\n        padding: 0;\r\n    }\r\n\r\n    #reference-details-info {\r\n        border-bottom: solid 1px #c4c4c4;\r\n        padding: 10px 10px 0;\r\n\r\n        label {\r\n            padding: 10px 6px 10px 10px;\r\n        }\r\n\r\n        span {\r\n            margin-right: 16px;\r\n        }\r\n    }\r\n\r\n    #reference-details-content {\r\n        padding: 20px;\r\n\r\n        .form-group-display {\r\n            display: flex;\r\n            flex-basis: 50%;\r\n            margin-bottom: 1rem;\r\n\r\n            label {\r\n                flex-basis: 40%;\r\n            }\r\n        }\r\n    }\r\n\r\n    #classification-section {\r\n        border-left: solid 1px #c4c4c4;\r\n        padding: 20px;\r\n        min-width: 250px;\r\n\r\n        .tile {\r\n            padding: 0;\r\n        }\r\n\r\n        .form-group-display {\r\n            margin-bottom: 1rem;\r\n        }\r\n\r\n        .reason-for-change {\r\n            display: block;\r\n            width: 200px;\r\n            word-wrap: break-word;\r\n        }\r\n\r\n        .reference-classification {\r\n            flex: none;\r\n            width: 350px;\r\n\r\n            label {\r\n                flex-basis: unset;\r\n            }\r\n        }\r\n    }\r\n    /* Reference History actions status styling */\r\n\r\n    .state-indicator-update, .state-indicator-silent {\r\n        background-color: $yellow-800;\r\n    }\r\n}\r\n\r\n#import-dashboard-details {\r\n    .row {\r\n        border-bottom: 1px solid #EEE;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .column {\r\n        float: left;\r\n        width: 33.33%;\r\n    }\r\n\r\n    .centered {\r\n        text-align: center\r\n    }\r\n    /* Clear floats after the columns */\r\n    .row:after {\r\n        content: \"\";\r\n        display: table;\r\n        clear: both;\r\n    }\r\n\r\n    .paginator {\r\n        span {\r\n            margin-right: 10px;\r\n        }\r\n    }\r\n\r\n    .show-contract-icon {\r\n        font-family: 'MaterialIconsOutlined', sans-serif;\r\n        font-size: 1.60rem;\r\n        cursor: pointer;\r\n        text-decoration: none;\r\n    }\r\n}\r\n\r\n#locks {\r\n    #locks-dashboard {\r\n        background-color: unset;\r\n        padding-left: 0;\r\n        padding-right: 0;\r\n\r\n        .cards {\r\n            display: grid;\r\n            grid-template-columns: repeat(6, 1fr);\r\n            column-gap: 1rem;\r\n            row-gap: 1rem;\r\n\r\n            @media screen and (max-width: 1200px) {\r\n                grid-template-columns: repeat(5, 1fr);\r\n            }\r\n\r\n            .card {\r\n                padding: 20px 20px 5px 20px;\r\n                border-radius: 4px;\r\n                font-size: 15px;\r\n                width: 100%;\r\n                background: white;\r\n\r\n                p {\r\n                    font-weight: 400;\r\n                }\r\n\r\n                .flex-container {\r\n                    display: flex;\r\n                    flex-flow: row wrap;\r\n                    width: 100%;\r\n                    margin-top: 30px;\r\n\r\n                    .locksCount {\r\n                        width: 50%;\r\n                        margin-bottom: 15px;\r\n                        font-size: 40px;\r\n                    }\r\n\r\n                    .release-button {\r\n                        width: 50%;\r\n                        text-align: right;\r\n                        margin-bottom: 15px;\r\n\r\n                        button {\r\n                            background-color: $warning;\r\n                            color: $text;\r\n\r\n                            &:hover, &:active {\r\n                                background-color: $grey-1;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        p {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.no-import-selected {\r\n    padding: 30px 20px 20px;\r\n    margin: 0 auto;\r\n    width: 400px;\r\n\r\n    i {\r\n        font-size: 30px;\r\n        display: inline-block;\r\n        padding-right: 40px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    h2 {\r\n        display: inline-block;\r\n        margin: 0;\r\n        position: relative;\r\n        top: -5px;\r\n    }\r\n}\r\n\r\n.preClassificationCompleted {\r\n    padding: 30px 20px 20px;\r\n    margin: 0 auto;\r\n    width: 400px;\r\n\r\n    i {\r\n        font-size: 30px;\r\n        display: inline-block;\r\n        padding-right: 40px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    h2 {\r\n        display: inline-block;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        position: relative;\r\n        top: -5px;\r\n    }\r\n}\r\n\r\n.pickReturnedNothing {\r\n    padding: 30px 20px 20px;\r\n    margin: 0 auto;\r\n    width: 700px;\r\n\r\n    i {\r\n        font-size: 30px;\r\n        display: inline-block;\r\n        padding-right: 40px;\r\n        font-weight: bold;\r\n        position: relative;\r\n        top: -45px;\r\n    }\r\n\r\n    h2 {\r\n        display: inline-block;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        position: relative;\r\n        top: -5px;\r\n    }\r\n}\r\n\r\n.classification-completed, .classification-signed {\r\n    padding: 30px 20px 20px;\r\n    margin: 0 auto;\r\n    width: 400px;\r\n\r\n    i {\r\n        font-size: 30px;\r\n        display: inline-block;\r\n        padding-right: 40px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    h2 {\r\n        display: inline-block;\r\n        margin: 0;\r\n        position: relative;\r\n        top: -5px;\r\n    }\r\n}\r\n\r\n.new-company-project-header, .case-upload-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 1rem 2rem 0 2rem;\r\n    border-bottom: 1px solid $grey-4;\r\n    align-items: center;\r\n\r\n    h2 {\r\n        font-weight: 600;\r\n    }\r\n\r\n    .case-status {\r\n        background-color: #026fa8;\r\n        color: #fff;\r\n        padding: 0.25rem 0.5rem;\r\n        border-radius: 0.25rem;\r\n        margin-top: 0px;\r\n        font-size: 1rem;\r\n        font-weight: 400;\r\n        height: fit-content;\r\n\r\n        &.pending {\r\n            background-color: #bb5c20;\r\n        }\r\n    }\r\n}\r\n\r\n/* Case Upload Popup */\r\n\r\n.case-upload-form-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    border-right: 1px solid #e5e5e5;\r\n    gap: 1rem;\r\n    width: 40%;\r\n    padding: 1rem 2rem 0 2rem;\r\n}\r\n\r\n.case-upload-substance-wrapper .case-upload-label {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.case-upload-divider {\r\n    height: 2px;\r\n    border-top: 1px solid #e5e5e5;\r\n    margin: 0.8rem -2rem;\r\n\r\n    &.first-substance-divider {\r\n        margin-top: 3px;\r\n    }\r\n\r\n    &.last-substance-divider {\r\n        margin-bottom: 3px;\r\n    }\r\n}\r\n\r\n.case-upload-form-right {\r\n    padding: 2rem;\r\n}\r\n\r\n.case-upload-content {\r\n    height: fit-content;\r\n    position: relative;\r\n    display: flex;\r\n}\r\n\r\n.case-upload-input {\r\n    align-self: stretch;\r\n    position: relative;\r\n}\r\n\r\n.case-upload-input {\r\n    border-radius: 8px;\r\n    background-color: #fff;\r\n    border: 1px solid #c4c4c4;\r\n    box-sizing: border-box;\r\n    height: 32px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    padding: 8px 12px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n    gap: 10px;\r\n    font-size: var(--body-main-body-size);\r\n}\r\n\r\n.case-upload-psur-dropdown-wrapper, .case-upload-mlm-dropdown-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.case-upload-mlm-dropdown-wrapper {\r\n    right: 70%;\r\n    left: 17%;\r\n}\r\n\r\n.case-upload-comment-wrapper textarea {\r\n    resize: none;\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.case-upload-company-dropdown-wrapper {\r\n    min-height: 50px;\r\n}\r\n\r\n.case-upload-company-dropdown-wrapper #CompanyLabelNoPlxId,\r\n.case-upload-company-dropdown-wrapper #CompanyLabelInvalidPlxId,\r\n.case-upload-company-dropdown-wrapper #CompanyLabelNoCompany {\r\n    display: block;\r\n    color: #AD0000;\r\n    font-weight: normal;\r\n}\r\n\r\n.case-upload-dropdown-group {\r\n    display: flex;\r\n    gap: 1rem;\r\n    padding-top: 0.5rem;\r\n}\r\n\r\n.case-upload-plxid-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    justify-content: flex-start;\r\n    font-family: var(--body-main-body);\r\n    align-self: stretch;\r\n    padding-top: 1rem;\r\n}\r\n\r\n\r\n.case-upload-substance-wrapper {\r\n    font-family: var(--body-main-body);\r\n}\r\n\r\n.case-upload-substance-wrapper label:first-of-type {\r\n    margin-right: 61px;\r\n}\r\n\r\n.case-upload-substance-break-top, .case-upload-substance-break-bottom {\r\n    position: absolute;\r\n}\r\n\r\n.case-upload-substance-break-top {\r\n    height: 0%;\r\n    width: 40.8%;\r\n    top: 21%;\r\n    left: -1.7%;\r\n    border-top: 1px solid #e5e5e5;\r\n}\r\n\r\n.case-upload-substance-break-bottom {\r\n    width: 40.8%;\r\n    top: 14%;\r\n    left: -1.7%;\r\n    border-top: 1px solid #e5e5e5;\r\n}\r\n\r\n.case-upload-filedrop-wrapper {\r\n    position: absolute;\r\n    height: 24%;\r\n    width: 56%;\r\n    top: 5%;\r\n    right: 2%;\r\n    bottom: 61%;\r\n    left: 42%;\r\n    text-align: center;\r\n}\r\n\r\n.case-upload-file-item {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 1rem;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid $grey-3;\r\n    padding: 0.3rem 0;\r\n    margin: 0;\r\n\r\n    &:first-of-type {\r\n        border-top: 1px solid $grey-3;\r\n    }\r\n}\r\n\r\n.case-upload-file-options {\r\n    font-family: 'MaterialIconsOutlined', sans-serif;\r\n    font-size: 1.75rem;\r\n    display: flex;\r\n    gap: 0.5rem;\r\n    align-items: center;\r\n\r\n    div {\r\n        padding: 0.25rem;\r\n        cursor: pointer;\r\n    }\r\n\r\n    div.disabled {\r\n        opacity: 0.5;\r\n        pointer-events: none;\r\n    }\r\n\r\n    .case-upload-file-delete {\r\n        padding-bottom: 0.4rem;\r\n        padding-right: 0rem;\r\n    }\r\n}\r\n\r\n.flex.flex-nowrap.justify-end i.m-icon {\r\n    margin-left: 3px;\r\n}\r\n\r\n.table-row.delete-highlighted {\r\n    background-color: #ffdede;\r\n}\r\n\r\n#modal-dialog-case-delete {\r\n    p {\r\n        margin-bottom: 15px;\r\n        font-size: 1rem;\r\n    }\r\n\r\n    #dialog-closer {\r\n        position: absolute;\r\n        right: 5px;\r\n        top: 10px;\r\n    }\r\n}\r\n\r\n#caseCommentInput {\r\n    min-height: 82px;\r\n}\r\n\r\n/* Button Spinner Animation */\r\n.btn .spinner {\r\n    -ms-transform: scale(0.7,0.7); /* IE 9 */\r\n    -webkit-transform: scale(0.7,0.7); /* Safari */\r\n    transform: scale(0.7,0.7);\r\n    position: absolute;\r\n    width: 32px;\r\n    height: 32px;\r\n    top: 50%;\r\n    margin-top: -16px;\r\n    opacity: 0;\r\n    background-image: url( data:image/gif;base64,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);\r\n}\r\n\r\n.btn,\r\n.btn .spinner,\r\n.btn .btn-label {\r\n    -webkit-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;\r\n    -moz-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;\r\n    -ms-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;\r\n    transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;\r\n}\r\n\r\n.btn.btn-spinner {\r\n    overflow: hidden;\r\n    position: relative;\r\n}\r\n\r\n.btn.btn-spinner span {\r\n    display: inherit;\r\n    margin: inherit;\r\n    color: inherit;\r\n    font-weight: inherit;\r\n    font-size: inherit;\r\n}\r\n\r\n.btn.btn-spinner .btn-label {\r\n    position: relative;\r\n    margin: 0;\r\n}\r\n\r\n.btn.btn-spinner .spinner {\r\n    left: 50%;\r\n    margin-left: -16px;\r\n    margin-top: 1em;\r\n}\r\n\r\n.btn.btn-spinner[data-loading] .btn-label {\r\n    opacity: 0;\r\n    margin: 0;\r\n}\r\n\r\n.btn.btn-spinner[data-loading] .spinner {\r\n    opacity: 1;\r\n    margin-top: -16px;\r\n}\r\n\r\n.file-drop-loading-wrapper {\r\n    position: absolute;\r\n    left: 178px;\r\n    top: 45px;\r\n}\r\n\r\n.file-drop-loading-wrapper div {\r\n    height: 20px;\r\n    width: 20px;\r\n    border-radius: 50%;\r\n    float: left;\r\n    margin: 0 3px;\r\n    background: #E1F5F9;\r\n    transform: scale(0);\r\n}\r\n\r\n.file-drop-loading-wrapper .file-drop-loading-ball1 {\r\n    z-index: 1;\r\n    -moz-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation: grow 4.4s infinite ease-in-out;\r\n    opacity: 0.8;\r\n}\r\n\r\n.file-drop-loading-wrapper .file-drop-loading-ball2 {\r\n    -moz-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation-delay: 0.3s;\r\n    animation-delay: 0.3s;\r\n    opacity: 0.8;\r\n}\r\n\r\n.file-drop-loading-wrapper .file-drop-loading-ball3 {\r\n    -moz-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation-delay: 0.6s;\r\n    animation-delay: 0.6s;\r\n    opacity: 0.8;\r\n}\r\n\r\n.file-drop-loading-wrapper .file-drop-loading-ball4 {\r\n    -moz-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation: grow 4.4s infinite ease-in-out;\r\n    -webkit-animation-delay: 0.9s;\r\n    animation-delay: 0.9s;\r\n    opacity: 0.8;\r\n}\r\n\r\n@-moz-keyframes grow {\r\n    0% {\r\n        -moz-transform: scale(0);\r\n    }\r\n\r\n    25% {\r\n        -moz-transform: scale(1);\r\n    }\r\n\r\n    50% {\r\n        -moz-transform: scale(0);\r\n    }\r\n\r\n    75% {\r\n        -moz-transform: scale(1);\r\n        background: #72c2c2;\r\n    }\r\n\r\n    100% {\r\n        -moz-transform: scale(0);\r\n        background: #72c2c2;\r\n    }\r\n}\r\n\r\n@-webkit-keyframes grow {\r\n    0% {\r\n        -webkit-transform: scale(0);\r\n    }\r\n\r\n    25% {\r\n        -webkit-transform: scale(1);\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: scale(0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: scale(1);\r\n        background: #72c2c2;\r\n    }\r\n\r\n    100% {\r\n        -webkit-transform: scale(0);\r\n        background: #72c2c2;\r\n    }\r\n}\r\n\r\n.case-upload-file-list {\r\n    margin-top: 19px;\r\n    text-align: left;\r\n}\r\n\r\n.case-upload-file-list-item {\r\n    border-bottom: 1px solid #E5E5E5;\r\n    padding: 5px 0px 5px 0px;\r\n}\r\n\r\n.file-drop-area {\r\n    border: 2px dashed #C4C4C4;\r\n    border-radius: 0.75rem;\r\n    position: relative;\r\n    height: 117px;\r\n    width: 480px;\r\n    max-width: 100%;\r\n    margin: 0 auto;\r\n    padding: 26px 20px 30px;\r\n    -webkit-transition: 0.2s;\r\n    transition: 0.2s;\r\n}\r\n\r\n.file-drop-area.disabled {\r\n    background: #fbfbfb;\r\n    border: 2px dashed #dbdbdb;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.file-drop-area.is-active {\r\n    background-color: #E1F5F9;\r\n    border: 2px dashed #008489;\r\n}\r\n\r\n.file-drop-area.dropping {\r\n    border: none;\r\n}\r\n\r\n.file-drop-mask {\r\n    top: 0px;\r\n    bottom: 0px;\r\n    position: absolute;\r\n    z-index: 99;\r\n    margin: -2px;\r\n    left: 0px;\r\n    right: 0px;\r\n}\r\n\r\n.file-drop-msg {\r\n    font-weight: 400;\r\n    font-size: 13px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: calc(100% - 180px);\r\n    vertical-align: middle;\r\n    display: block;\r\n    line-height: 60px;\r\n    left: 121px;\r\n}\r\n\r\n.file-drop-msg.disabled {\r\n    color: #343434;\r\n}\r\n\r\n.file-drop-msg-sub {\r\n    font-weight: 400;\r\n    font-size: 11px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: calc(100% - 180px);\r\n    vertical-align: middle;\r\n    display: block;\r\n    top: -25px;\r\n    left: 163px;\r\n    color: #343434;\r\n}\r\n\r\n.file-drop-msg-sub.disabled {\r\n    color: #343434;\r\n}\r\n\r\n.file-drop-input {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    cursor: pointer;\r\n    opacity: 0;\r\n}\r\n\r\n.file-drop-input:focus {\r\n    outline: none;\r\n}\r\n\r\n.reference-info-container {\r\n    width: 100%;\r\n    display: flex;\r\n    gap: 1rem;\r\n    align-items: center;\r\n    border-bottom: 1px solid $grey-3;\r\n\r\n    h3, label {\r\n        margin: 0;\r\n    }\r\n\r\n    label {\r\n        margin-right: $padding-unit;\r\n    }\r\n\r\n    [class*=\"state-indicator-\"] {\r\n        width: unset;\r\n        margin-bottom: 0;\r\n        padding: 0.25rem $padding-unit;\r\n        user-select: none;\r\n        pointer-events: none;\r\n    }\r\n}\r\n\r\n.details-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-template-rows: auto;\r\n    gap: 1rem;\r\n}\r\n\r\n.badge-yes, .badge-no {\r\n    text-align: center;\r\n    padding: 0.25rem $padding-unit;\r\n    border-radius: 4px;\r\n    color: $white;\r\n    width: 2.75rem;\r\n}\r\n\r\n.badge-yes {\r\n    background-color: $success;\r\n}\r\n\r\n.badge-no {\r\n    background-color: $active-alt-secondary;\r\n}\r\n\r\n// Contracts views\r\n#edit-contract {\r\n    .sub-header {\r\n        h2 a {\r\n            color: $grey-1;\r\n        }\r\n\r\n        h2 a:hover {\r\n            color: $brand;\r\n        }\r\n    }\r\n}\r\n\r\n#contractDetails {\r\n    .form-group {\r\n        .contractHistoryLabel {\r\n            font-weight: bold;\r\n            margin-bottom: .4rem;\r\n        }\r\n    }\r\n}\r\n\r\n#help {\r\n    section {\r\n        border-radius: 5px;\r\n\r\n        .files {\r\n            .file {\r\n                border: 1px solid $grey-3;\r\n                border-radius: 5px;\r\n                padding: 20px 20px 20px;\r\n                width: 290px;\r\n\r\n                span {\r\n                    display: inline-block;\r\n                    width: 75px;\r\n                    font-weight: bold;\r\n                }\r\n\r\n                .file-buttons {\r\n                    padding-top: 5px;\r\n                    display: flex;\r\n                    gap: $space-XS;\r\n                }\r\n            }\r\n        }\r\n\r\n        #build-info {\r\n            p:last-of-type {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@mixin display-box($size) {\r\n    width: $size;\r\n    height: $size;\r\n    display: inline-block;\r\n    box-sizing: border-box;\r\n}\r\n\r\n@mixin border($width, $colorTop, $colorRight, $colorBottom, $colorLeft) {\r\n    border-top: solid $width $colorTop;\r\n    border-right: solid $width $colorRight;\r\n    border-bottom: solid $width $colorBottom;\r\n    border-left: solid $width $colorLeft;\r\n}\r\n\r\n#preclassification {\r\n    .locking-error-message, .in-progress-message {\r\n        position: absolute;\r\n        top: 123px;\r\n        right: 28px;\r\n        background: #FEFEFE;\r\n        border-radius: 5px;\r\n        padding: 5px 10px;\r\n        min-width: 215px;\r\n    }\r\n\r\n    .locking-error-message {\r\n        background: #FFE4E1;\r\n        border-left: 2px solid #B22222;\r\n        border-right: 2px solid #B22222;\r\n    }\r\n\r\n    .in-progress-message {\r\n        $spinner-size: 16px !default;\r\n        $spinner-color: $brand !default;\r\n        $spinner-accent: $grey-3 !default;\r\n        $speed2x: 2s;\r\n        $speed3x: 1s;\r\n        $speed4x: .5s;\r\n\r\n        .spinner {\r\n            @include display-box($spinner-size);\r\n            position: relative;\r\n            left: -3px;\r\n            top: 2px;\r\n        }\r\n\r\n        .spinner.round {\r\n            &::before {\r\n                border-radius: 50%;\r\n                content: \" \";\r\n                @include display-box($spinner-size);\r\n                @include border(calc($spinner-size / 5), $spinner-accent, $spinner-accent, $spinner-accent, $spinner-accent);\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n            }\r\n\r\n            &::after {\r\n                border-radius: 50%;\r\n                content: \" \";\r\n                @include display-box($spinner-size);\r\n                @include border(calc($spinner-size / 5), $spinner-color, transparent, transparent, transparent);\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                animation: round-animate $speed3x ease-in-out infinite;\r\n            }\r\n        }\r\n\r\n        @keyframes round-animate {\r\n            0% {\r\n                transform: rotate(0deg);\r\n            }\r\n\r\n            100% {\r\n                transform: rotate(360deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .modal-wrapper {\r\n\r\n        .icon-button-cancel {\r\n            display: none;\r\n        }\r\n\r\n        .dialog-custom-content {\r\n            align-items: center;\r\n        }\r\n\r\n        #dialog-closer {\r\n            display: none;\r\n        }\r\n\r\n        a.button.icon-button-tick {\r\n            font-size: 0;\r\n        }\r\n\r\n        a.button.icon-button-tick:after {\r\n            content: 'OK';\r\n            font-size: initial;\r\n        }\r\n    }\r\n}\r\n\r\n#search {\r\n    .classificationSearchFullTextErrorMessage {\r\n        border: 1px solid #8A1A1E;\r\n        border-radius: 6px;\r\n        background: #F9E1E0;\r\n        padding: 15px;\r\n        margin: 13px 0;\r\n\r\n        p {\r\n            margin-bottom: 0\r\n        }\r\n    }\r\n}\r\n\r\n#reports {\r\n    section {\r\n        border-radius: 5px;\r\n\r\n        .cards {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            column-gap: 20px;\r\n            row-gap: 10px;\r\n\r\n            .card {\r\n                border: 1px solid $grey-3;\r\n                border-radius: 5px;\r\n                width: 290px;\r\n                padding: 0;\r\n                margin: 0;\r\n                display: flex;\r\n                flex-direction: column;\r\n\r\n                .card-info {\r\n                    padding: 20px 20px 10px;\r\n                    flex: 1;\r\n\r\n                    h2 {\r\n                        display: -webkit-box;\r\n                        -webkit-box-orient: vertical;\r\n                        -webkit-line-clamp: 3;\r\n                        overflow: hidden;\r\n                    }\r\n                }\r\n\r\n                .card-buttons {\r\n                    border-top: 1px solid $grey-3;\r\n                    padding: 10px 20px 10px;\r\n\r\n                    .button {\r\n                        margin-right: 15px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n#importcards {\r\n    section {\r\n        border-radius: 5px;\r\n\r\n        .cards {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            column-gap: 20px;\r\n            row-gap: 10px;\r\n\r\n            .card {\r\n                border: 1px solid $grey-3;\r\n                border-radius: 5px;\r\n                width: 390px;\r\n                height: 150px;\r\n                padding: 0;\r\n                margin: 0;\r\n                display: flex;\r\n                flex-direction: column;\r\n\r\n                .card-info {\r\n                    flex: 1;\r\n\r\n                    h2 {\r\n                        display: -webkit-box;\r\n                        -webkit-box-orient: vertical;\r\n                        -webkit-line-clamp: 3;\r\n                        overflow: hidden;\r\n                    }\r\n                }\r\n\r\n                .card-buttons {\r\n                    border-top: 1px solid $grey-3;\r\n                    padding: 10px 20px 10px;\r\n\r\n                    .button {\r\n                        margin-right: 15px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .import-card-type {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            margin-bottom: 5px;\r\n        }\r\n\r\n        .import-card-title-text {\r\n            color: #461E96;\r\n            font-size: 17px;\r\n            font-weight: bold;\r\n            padding: 10px 20px 0px;\r\n        }\r\n\r\n        .import-pubmed-card-contract-text {\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            padding: 0px 20px 0px;\r\n            min-height: 50px\r\n        }\r\n\r\n        .import-card-contract-text {\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            padding: 0px 20px 0px;\r\n        }\r\n\r\n        .import-card-date-title {\r\n            color: #8a8a8a;\r\n            font-size: 12px;\r\n            padding: 10px 12px 0px;\r\n        }\r\n\r\n        .import-card-date {\r\n            font-size: 12px;\r\n            padding: 0px 12px 0px;\r\n        }\r\n\r\n        .import-card-updatedby-title {\r\n            color: #8a8a8a;\r\n            font-size: 12px;\r\n            padding: 10px 12px 0px;\r\n        }\r\n\r\n        .import-card-updatedby {\r\n            font-size: 12px;\r\n            padding: 0px 12px 0px;\r\n            width: 200px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n        }\r\n\r\n        .import-card-createdby {\r\n            font-size: 12px;\r\n            padding: 0px 12px 0px;\r\n            width: 170px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n        }\r\n\r\n        .import-card-journal-title {\r\n            font-weight: bold;\r\n            padding: 0px 20px 0px;\r\n        }\r\n\r\n        .import-card-text {\r\n            font-size: 12px;\r\n            width: 400px;\r\n            padding: 0px 20px 0px;\r\n            word-break: break-word;\r\n            min-height: 40px;\r\n        }\r\n\r\n        .import-card-mod {\r\n            display: flex;\r\n            padding: 0px 10px 0px;\r\n        }\r\n\r\n        .import-card-line {\r\n            display: flex;\r\n            border-top: 1px solid $grey-3;\r\n            padding: 0px 10px 0px;\r\n        }\r\n\r\n        .import-card-mod-date {\r\n            display: flex;\r\n            flex-direction: column;\r\n            margin-right: 15px;\r\n        }\r\n\r\n        .import-card-title {\r\n            width: 100px;\r\n            height: 30px;\r\n        }\r\n\r\n        .import-card-file-import-lozenge {\r\n            background-color: #E7F7FF;\r\n            padding: 2px 10px 2px 10px;\r\n            margin-right: 5px;\r\n            border-radius: 15px;\r\n            border-color: #CFEFFE;\r\n            border-style: solid;\r\n            border-width: 3px;\r\n        }\r\n    }\r\n}\r\n\r\n.fileimportbutton {\r\n    height: 40px;\r\n    margin-left: 10px;\r\n    padding-left: 10px;\r\n    line-height: 25px;\r\n    text-align: center\r\n}\r\n\r\n.ellipsis-button {\r\n    border: 2px solid;\r\n    border-color: #e5e5e5;\r\n    border-radius: 4px;\r\n    height: 25px;\r\n    position: relative;\r\n    color: white;\r\n}\r\n\r\n.ellipsis-button:hover {\r\n    color: white;\r\n    background-color: #ededed;\r\n}\r\n\r\n.ellipsis-button > a > .nav-menu-user-icon {\r\n    margin-right: initial;\r\n}\r\n\r\n.ellipsis-dropdown-content {\r\n    display: none;\r\n    flex-direction: column;\r\n    position: absolute;\r\n    background-color: white;\r\n    min-width: 90px;\r\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\r\n    z-index: 1;\r\n\r\n    ul > li {\r\n        margin-bottom: initial;\r\n    }\r\n}\r\n\r\n.ellipsis-dropdown-content > ul > li:hover {\r\n    background-color: #ddd;\r\n    cursor: pointer;\r\n}\r\n\r\n.ellipsis-container {\r\n    position: relative\r\n}\r\n\r\n.ellipsis-container:hover .ellipsis-dropdown-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.ellipsis-dropdown-content > ul > li:nth-child(1) {\r\n    margin-top: 10px;\r\n    padding-left: 15px;\r\n}\r\n\r\n.ellipsis-dropdown-content > ul li:nth-child(2) {\r\n    margin-bottom: 10px;\r\n    padding-left: 15px;\r\n}\r\n\r\n#tracking-sheets {\r\n    section {\r\n        border-radius: 5px;\r\n\r\n        .filters {\r\n            position: absolute;\r\n            top: 150px;\r\n            right: 80px;\r\n\r\n            select#company {\r\n                width: 250px;\r\n            }\r\n\r\n            select#year {\r\n                width: 90px;\r\n            }\r\n        }\r\n\r\n        .cards {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            column-gap: 10px;\r\n            margin: 10px auto;\r\n\r\n            .card {\r\n                width: 140px;\r\n                padding: 0;\r\n                margin: 0 0 10px;\r\n\r\n                .card-info {\r\n                    padding: 10px 10px 10px;\r\n                    background: #E3F2FD;\r\n                    border-top-left-radius: 5px;\r\n                    border-top-right-radius: 5px;\r\n\r\n                    h2 {\r\n                        color: #1F394A;\r\n                        font-size: 24px;\r\n                        padding: 5px 0 0;\r\n                    }\r\n\r\n                    p {\r\n                        color: #1F394A;\r\n                        font-size: 13px;\r\n                        margin: 0;\r\n                    }\r\n                }\r\n\r\n                .card-buttons {\r\n                    padding: 10px 10px 10px;\r\n                    background: #CCE6F8;\r\n                    border-bottom-left-radius: 5px;\r\n                    border-bottom-right-radius: 5px;\r\n                    font-size: 13px;\r\n\r\n                    a {\r\n                        color: #01699f;\r\n                        margin-right: 15px;\r\n                    }\r\n                }\r\n\r\n                &.future {\r\n                    .card-info {\r\n                        background: #F3F6F9;\r\n\r\n                        h2, p {\r\n                            color: #E5E5E5;\r\n                        }\r\n                    }\r\n\r\n                    .card-buttons {\r\n                        background: #E5E5E5;\r\n                    }\r\n                }\r\n\r\n                &.missing {\r\n                    .card-info {\r\n                        background: #F8E5C4;\r\n\r\n                        h2, p {\r\n                            color: #1F394A;\r\n                        }\r\n                    }\r\n\r\n                    .card-buttons {\r\n                        background: #F0DF9D;\r\n\r\n                        a {\r\n                            color: #1F394A;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n#split-references {\r\n    // Split references page\r\n    .company-multi-select {\r\n        height: 60px;\r\n    }\r\n}\r\n\r\n#apogepha-client-report, #merz-client-report, #accovion-client-report {\r\n    section {\r\n        border-radius: 5px;\r\n\r\n        .filters {\r\n            position: absolute;\r\n            top: 150px;\r\n            right: 80px;\r\n\r\n            select#year {\r\n                width: 90px;\r\n            }\r\n        }\r\n\r\n        .cards {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            column-gap: 10px;\r\n            margin: 10px auto;\r\n\r\n            .card {\r\n                width: 140px;\r\n                padding: 0;\r\n                margin: 0 0 10px;\r\n\r\n                .card-info {\r\n                    padding: 10px 10px 10px;\r\n                    background: #E3F2FD;\r\n                    border-top-left-radius: 5px;\r\n                    border-top-right-radius: 5px;\r\n\r\n                    h2 {\r\n                        color: #1F394A;\r\n                        font-size: 18px;\r\n                        padding: 5px 0 0;\r\n                    }\r\n\r\n                    p {\r\n                        color: #1F394A;\r\n                        font-size: 13px;\r\n                        margin: 0;\r\n                    }\r\n                }\r\n\r\n                .card-buttons {\r\n                    padding: 10px 10px 10px;\r\n                    background: #CCE6F8;\r\n                    border-bottom-left-radius: 5px;\r\n                    border-bottom-right-radius: 5px;\r\n                    font-size: 13px;\r\n\r\n                    a {\r\n                        color: #01699f;\r\n                        margin-right: 15px;\r\n                    }\r\n                }\r\n\r\n                &.future {\r\n                    .card-info {\r\n                        background: #F3F6F9;\r\n\r\n                        h2, p {\r\n                            color: #E5E5E5;\r\n                        }\r\n                    }\r\n\r\n                    .card-buttons {\r\n                        background: #E5E5E5;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.custom-select {\r\n    &.small {\r\n        &:before {\r\n            top: 9px;\r\n        }\r\n    }\r\n}\r\n\r\nbutton.secondary:disabled,\r\nbutton.secondary.disabled,\r\na.button.secondary:disabled,\r\na.button.secondary.disabled {\r\n    border-color: $grey-3;\r\n    color: $grey-3;\r\n}\r\n\r\n.nav-menu > li .flyout {\r\n    top: 51px;\r\n    left: 1px;\r\n}\r\n//Settings page styling\r\n#settings {\r\n\r\n    .global-container {\r\n        display: flex;\r\n    }\r\n\r\n    .sidebar {\r\n        width: 200px;\r\n        margin-top: 10px;\r\n        margin-right: 50px;\r\n    }\r\n\r\n    .main-content {\r\n        flex-grow: 2\r\n    }\r\n\r\n    .menu {\r\n        li {\r\n            padding: 5px;\r\n            margin-right: 5px;\r\n\r\n            &:hover {\r\n                background-color: #e7f7ff;\r\n            }\r\n\r\n            &.active {\r\n                background-color: #e7f7ff;\r\n                font-weight: 600;\r\n            }\r\n        }\r\n\r\n        a {\r\n            text-decoration: none;\r\n        }\r\n    }\r\n    // Journals-tables-template styling\r\n    #journals-table {\r\n        .sub-header {\r\n            border: none;\r\n            background:none;\r\n\r\n            h2 {\r\n                font-weight: 700;\r\n                margin-left: 10px;\r\n                font-size: 1.2rem;\r\n            }\r\n        }\r\n    }\r\n    // Journal-modal-template styling\r\n    #journal-modal {\r\n        .modal-header {\r\n            h2 {\r\n                margin-right: 320px;\r\n                font-weight: 600;\r\n            }\r\n\r\n            button {\r\n                font-size: 1.5rem;\r\n                color: black;\r\n                background: none;\r\n            }\r\n        }\r\n\r\n        .modal-body {\r\n            padding: 0 20px 10px;\r\n        }\r\n        .modal-footer {\r\n            .button {\r\n                margin-right: 5px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .ai-suggestion-toggle-panel {\r\n        background-color: #EEE;\r\n        padding: 1rem;\r\n        max-width: 800px;\r\n\r\n        h2, p {\r\n            margin-left: 15px;\r\n        }\r\n    }\r\n}\r\n\r\n.ai-retry {\r\n    button {\r\n        width: 100%;\r\n        margin-bottom: 8.0rem;\r\n    }\r\n}\r\n\r\n#ad-hoc-list {\r\n    select {\r\n        background-color: $blue-dark;\r\n        color: #ffffff;\r\n\r\n        option:disabled {\r\n            background-color: $grey-4;\r\n        }\r\n\r\n        option {\r\n            background-color: #ffffff;\r\n            color: #333;\r\n            padding: 8px;\r\n        }\r\n    }\r\n}\r\n\r\n#ad-hoc-manual-entry-form {\r\n    h1 {\r\n        padding: 20px;\r\n        font-weight: 500;\r\n    }\r\n\r\n    #doi-exist-message, #doi-invalid-message {\r\n        display: none;\r\n        padding: 5px;\r\n        background-color: $red-light;\r\n        border: 1px solid #ccc;\r\n        border-radius: 5px;\r\n        margin-top: 5px;\r\n    }\r\n\r\n    .error-message {\r\n        display: none;\r\n        padding: 5px;\r\n        color: red;\r\n        margin-top: 5px;\r\n        font-weight: 500;\r\n    }\r\n\r\n\r\n    #sources-wrapper {\r\n        margin: 15px;\r\n        border: 1px solid $grey-4;\r\n        border-radius: 10px;\r\n\r\n        h2 {\r\n            padding-left: 25px;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .form-group {\r\n            padding-left: 20px;\r\n            padding-top: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n#file-import-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    .button-container {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n    }\r\n\r\n    .button {\r\n        margin-right: 10px;\r\n    }\r\n}\r\n", "/*!\r\n * Generated using the Bootstrap Customizer (https://getbootstrap.com/docs/3.4/customize/)\r\n */\r\n/*!\r\n * Bootstrap v3.4.1 (https://getbootstrap.com/)\r\n * Copyright 2011-2019 Twitter, Inc.\r\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\r\n */\r\n/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\r\n\r\n.modal-open {\r\n    overflow: hidden;\r\n}\r\n\r\n.modal {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: 1050;\r\n    display: none;\r\n    overflow: hidden;\r\n    -webkit-overflow-scrolling: touch;\r\n    outline: 0;\r\n}\r\n\r\n.modal-body.dropping {\r\n    cursor: wait;\r\n}\r\n\r\n.modal.fade .modal-dialog {\r\n    -webkit-transform: translate(0, -25%);\r\n    -ms-transform: translate(0, -25%);\r\n    -o-transform: translate(0, -25%);\r\n    transform: translate(0, -25%);\r\n    -webkit-transition: -webkit-transform 0.3s ease-out;\r\n    -o-transition: -o-transform 0.3s ease-out;\r\n    transition: transform 0.3s ease-out;\r\n}\r\n\r\n.modal.in .modal-dialog {\r\n    -webkit-transform: translate(0, 0);\r\n    -ms-transform: translate(0, 0);\r\n    -o-transform: translate(0, 0);\r\n    transform: translate(0, 0);\r\n}\r\n\r\n.modal-open .modal {\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n}\r\n\r\n.modal-dialog {\r\n    position: relative;\r\n    width: auto;\r\n    margin: 10px;\r\n}\r\n\r\n.modal-content {\r\n    position: relative;\r\n    background-color: #ffffff;\r\n    -webkit-background-clip: padding-box;\r\n    background-clip: padding-box;\r\n    border: 1px solid #999999;\r\n    border: 1px solid rgba(0, 0, 0, 0.2);\r\n    border-radius: 6px;\r\n    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\r\n    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\r\n    outline: 0;\r\n}\r\n\r\n.modal-backdrop {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: 1040;\r\n    background-color: #000000;\r\n}\r\n\r\n.modal-backdrop.fade {\r\n    filter: alpha(opacity=0);\r\n    opacity: 0;\r\n}\r\n\r\n.modal-backdrop.in {\r\n    filter: alpha(opacity=50);\r\n    opacity: 0.5;\r\n}\r\n\r\n.modal-header {\r\n    padding: 15px;\r\n    border-bottom: 1px solid #e5e5e5;\r\n}\r\n\r\n.modal-header .close {\r\n    margin-top: -2px;\r\n}\r\n\r\n.modal-title {\r\n    margin: 0;\r\n    line-height: 1.42857143;\r\n}\r\n\r\n.modal-body {\r\n    position: relative;\r\n    margin: 0px;\r\n    overflow: auto !important;\r\n}\r\n\r\n.modal-footer {\r\n    padding: 15px;\r\n    text-align: right;\r\n    border-top: 1px solid #e5e5e5;\r\n}\r\n\r\n.modal-footer .btn + .btn {\r\n    margin-bottom: 0;\r\n    margin-left: 5px;\r\n}\r\n\r\n.modal-footer .btn-group .btn + .btn {\r\n    margin-left: -1px;\r\n}\r\n\r\n.modal-footer .btn-block + .btn-block {\r\n    margin-left: 0;\r\n}\r\n\r\n.modal-scrollbar-measure {\r\n    position: absolute;\r\n    top: -9999px;\r\n    width: 50px;\r\n    height: 50px;\r\n    overflow: scroll;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .modal-dialog {\r\n        width: 600px;\r\n        margin: 30px auto;\r\n    }\r\n\r\n    .modal-content {\r\n        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\r\n        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\r\n    }\r\n\r\n    .modal-sm {\r\n        width: 300px;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .modal-lg {\r\n        width: 900px;\r\n    }\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after,\r\n.modal-header:before,\r\n.modal-header:after,\r\n.modal-footer:before,\r\n.modal-footer:after {\r\n    display: table;\r\n    content: \" \";\r\n}\r\n\r\n.clearfix:after,\r\n.modal-header:after,\r\n.modal-footer:after {\r\n    clear: both;\r\n}\r\n\r\n.center-block {\r\n    display: block;\r\n    margin-right: auto;\r\n    margin-left: auto;\r\n}\r\n\r\n.pull-right {\r\n    float: right !important;\r\n}\r\n\r\n.pull-left {\r\n    float: left !important;\r\n}\r\n\r\n.hide {\r\n    display: none !important;\r\n}\r\n\r\n.show {\r\n    display: block !important;\r\n}\r\n\r\n.invisible {\r\n    visibility: hidden;\r\n}\r\n\r\n.text-hide {\r\n    font-size: 0;\r\n    line-height: 0;\r\n    color: transparent;\r\n    text-shadow: none;\r\n    background-color: transparent;\r\n    border: 0;\r\n}\r\n\r\n.hidden {\r\n    display: none !important;\r\n}\r\n\r\n.affix {\r\n    position: fixed;\r\n}\r\n", "/* Multi-select */\r\n.multi-select {\r\n    position: absolute;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: $white;\r\n    align-items: flex-start;\r\n    z-index: 1;\r\n    user-select: none;\r\n    border: 1px solid $grey-3;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    width: inherit;\r\n\r\n    label {\r\n        margin-bottom: 0px;\r\n    }\r\n\r\n    .preview-box {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-self: stretch;\r\n        align-items: center;\r\n        padding: 6px 10px;\r\n        border-bottom: 1px solid transparent;\r\n\r\n        .selected-label {\r\n            display: block;\r\n            align-self: center;\r\n            margin-right: auto;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            font-weight: normal;\r\n            color: $black;\r\n        }\r\n\r\n        .selected-count {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            justify-self: end;\r\n            margin-left: 10px;\r\n            text-align: center;\r\n            background-color: $brand;\r\n            color: $white;\r\n            min-height: calc((3rem / 2) + 2px);\r\n            min-width: calc((3rem / 2) + 2px);\r\n            border-radius: 50%;\r\n        }\r\n\r\n        .filter-multiselect {\r\n            height: 30px;\r\n            margin: 0px;\r\n            border: none\r\n        }\r\n    }\r\n\r\n    .item-container {\r\n        display: flex;\r\n        align-self: stretch;\r\n        flex-direction: column;\r\n        padding-bottom: 1px;\r\n        overflow-y: auto;\r\n        max-height: 16em;\r\n    }\r\n\r\n    .multi-select-item {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        padding: 0px 4px;\r\n\r\n        &.disabled-item {\r\n            background-color: $grey-4;\r\n            opacity: 0.5;\r\n            pointer-events: none;\r\n        }\r\n\r\n        &:hover {\r\n            background-color: #e9e9e9;\r\n\r\n            input {\r\n                background: $white;\r\n            }\r\n        }\r\n\r\n        input {\r\n            top: 0;\r\n            margin: 0;\r\n            margin-right: 5px;\r\n            border-radius: 3px;\r\n        }\r\n\r\n        .multiSelectItemLabel {\r\n            padding: 6px 0px;\r\n            width: 100%;\r\n            font-weight: normal;\r\n            color: $black;\r\n        }\r\n\r\n        .formatLabel {\r\n            display: flex;\r\n            align-items: center;\r\n            align-self: normal;\r\n        }\r\n    }\r\n\r\n    .hidden {\r\n        display: none;\r\n    }\r\n\r\n    &:hover {\r\n        border: 1px solid $grey-2;\r\n    }\r\n\r\n    &.active {\r\n        border: 1px solid $brand;\r\n\r\n        .preview-box {\r\n            border-bottom: 1px solid $brand;\r\n        }\r\n    }\r\n\r\n    &.disabled {\r\n        pointer-events: none;\r\n        background-color: #f2f4f6;\r\n\r\n        .selected-label {\r\n            color: #949494;\r\n        }\r\n\r\n        .selected-count {\r\n            background-color: #949494;\r\n        }\r\n    }\r\n}\r\n\r\n/* Companies Multi-select */\r\n.companies-multi-select {\r\n    position: absolute;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: $white;\r\n    align-items: flex-start;\r\n    z-index: 1;\r\n    user-select: none;\r\n    border: 1px solid $grey-3;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    width: calc(40% - 4rem);\r\n\r\n    label {\r\n        margin-bottom: 0px;\r\n    }\r\n\r\n    .select-box {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-self: stretch;\r\n        padding: 6px 10px;\r\n        border-bottom: 1px solid transparent;\r\n\r\n        .selected-label {\r\n            display: block;\r\n            align-self: center;\r\n            margin-right: auto;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n        }\r\n\r\n        .selected-count {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            justify-self: end;\r\n            margin-left: 10px;\r\n            text-align: center;\r\n            background-color: $brand;\r\n            color: $white;\r\n            min-height: 20px;\r\n            min-width: 20px;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .check-box {\r\n        display: flex;\r\n        align-self: stretch;\r\n        flex-direction: column;\r\n        padding-bottom: 1px;\r\n        overflow-y: auto;\r\n        max-height: 16em;\r\n    }\r\n\r\n    .companies-multi-select-item {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        padding: 0px 4px;\r\n\r\n        &:hover {\r\n            background-color: #e9e9e9;\r\n\r\n            input {\r\n                background: $white;\r\n            }\r\n        }\r\n\r\n        input {\r\n            top: 0;\r\n            margin: 0;\r\n            margin-right: 5px;\r\n            border-radius: 3px;\r\n        }\r\n\r\n        .multiSelectItemLabel {\r\n            padding: 6px 0px;\r\n            width: 100%;\r\n        }\r\n\r\n        .formatLabel {\r\n            display: flex;\r\n            align-items: center;\r\n            align-self: normal;\r\n        }\r\n    }\r\n\r\n    .hidden {\r\n        display: none;\r\n    }\r\n\r\n    &:hover {\r\n        border: 1px solid $grey-2;\r\n    }\r\n\r\n    &.active {\r\n        border: 1px solid $brand;\r\n\r\n        .select-box {\r\n            border-bottom: 1px solid $brand;\r\n        }\r\n    }\r\n}\r\n\r\n.switch-wrapper {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    gap: 1rem;\r\n    width: 100%;\r\n}"], "names": [], "sourceRoot": ""}