﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
		<PackageReference Include="System.Formats.Asn1" Version="9.0.4" />
		<PackageReference Include="Telerik.Documents.Flow.FormatProviders.Pdf" Version="2024.1.124" />
	</ItemGroup>

	<ItemGroup>
    <ProjectReference Include="..\PharmaLex.Dotnet.Adapters\PharmaLex.Dotnet.Adapters.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Reporting.Unit.Tests"></InternalsVisibleTo>
	</ItemGroup>

</Project>
