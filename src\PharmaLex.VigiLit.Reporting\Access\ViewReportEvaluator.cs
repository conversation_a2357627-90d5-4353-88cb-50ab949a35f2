﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;

namespace PharmaLex.VigiLit.Reporting.Access;
internal class ViewReportEvaluator : PermissionEvaluator<ViewReportPermissionContext>
{
    private readonly IUserRepository _userRepository;
    private readonly IReportRepository _reportRepository;

    public ViewReportEvaluator(IUserRepository userRepository, IReportRepository reportRepository)
    {
        _userRepository = userRepository;
        _reportRepository = reportRepository;
    }

    private async Task<IUserEntity> GetSecurityUser(int userId)
    {
        return await _userRepository.GetForSecurity(userId);
    }

    private async Task<bool> CanUserViewReport(int userId, string reportName)
    {
        var user = await GetSecurityUser(userId);

        return await _reportRepository.CanUserViewReport(user, reportName);
    }

    public override async Task<bool> HasPermissions(ViewReportPermissionContext context)
    {
        if (await CanUserViewReport(context.UserId, context.ReportName))
        {
            return true;
        }

        throw new UnauthorizedAccessException();
    }
}
