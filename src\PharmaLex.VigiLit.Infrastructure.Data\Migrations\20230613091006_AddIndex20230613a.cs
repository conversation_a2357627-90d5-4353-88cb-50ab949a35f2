﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndex20230613a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_EmailRelevantEvents_EmailRelevantEventEmailStatusType_CompanyInterestId",
                table: "EmailRelevantEvents",
                columns: new[] { "EmailRelevantEventEmailStatusType", "CompanyInterestId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_EmailRelevantEvents_EmailRelevantEventEmailStatusType_CompanyInterestId",
                table: "EmailRelevantEvents");
        }
    }
}
