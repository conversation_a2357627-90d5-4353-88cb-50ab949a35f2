{"version": 3, "file": "js/localStorageValidator.js", "mappings": ";;;AAAAA,sBAAwB,CACpBC,uBAAwB,SAASC,EAAc,KAAMC,EAAc,MAC/D,IAAKD,IAAgBC,EAEjB,OAAO,EAEX,IAMIC,EANAC,EAAWC,aAAaC,QAAQL,GACpC,IAAKG,EAED,OAAO,EAIX,IAEI,GAAY,OADZD,EAAOI,KAAKC,MAAMJ,IAEd,OAAO,CAEf,CAAE,MAAMK,GACJ,OAAO,CACX,CAEA,IAAK,MAAMC,KAAgBC,OAAOC,oBAAoBV,GAClD,IAAKS,OAAOE,OAAOV,EAAMO,GACrB,OAAO,EAIf,OAAO,CACX,EACAI,uBAAwB,SAASb,EAAaC,GAC5Ba,KAAKf,uBAAuBC,EAAaC,IAEnDG,aAAaW,WAAWf,EAEhC", "sources": ["webpack://PharmaLex.VigiLit.v2/./src/js/utilities/local-storage-validator.js"], "sourcesContent": ["LocalStorageValidator = {\r\n    validateJsonProperties: function(storageName = null, itemToModel = null) {\r\n        if (!storageName || !itemToModel) {\r\n            // Return false to clear storage in case of reference issues.\r\n            return false;\r\n        }\r\n        var itemJson = localStorage.getItem(storageName);\r\n        if (!itemJson) {\r\n            // Return true since no action is needed if storage item value is undefined.\r\n            return true;\r\n        }\r\n        \r\n        var item;\r\n        try {\r\n            item = JSON.parse(itemJson);\r\n            if (item == null) {\r\n                return false;\r\n            }\r\n        } catch(error) {\r\n            return false;\r\n        }\r\n        \r\n        for (const propertyName of Object.getOwnPropertyNames(itemToModel)) {\r\n            if (!Object.hasOwn(item, propertyName)) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    },\r\n    clearJsonItemIfInvalid: function(storageName, itemToModel) {\r\n        var isValid = this.validateJsonProperties(storageName, itemToModel);\r\n        if (!isValid) {\r\n            localStorage.removeItem(storageName);\r\n        }\r\n    }\r\n};"], "names": ["LocalStorageValidator", "validateJsonProperties", "storageName", "itemToModel", "item", "itemJson", "localStorage", "getItem", "JSON", "parse", "error", "propertyName", "Object", "getOwnPropertyNames", "hasOwn", "clearJsonItemIfInvalid", "this", "removeItem"], "sourceRoot": ""}