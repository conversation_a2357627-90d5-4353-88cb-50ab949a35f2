﻿@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.CompanyModel>

@{
    ViewData["Title"] = "Companies";
}

<div class="sub-header">
    <h2>Companies</h2>
    <div class="controls">
        <a asp-action="Create" class="button primary btn-default">Create Company</a>
    </div>
</div>

<div id="company" v-cloak>
    <section>
        <filtered-table :items="company" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {
<script type="text/javascript">

    var pageConfig = {
        appElement: "#company",
        data: function () {
            return {
                confirmResolve: null,
                confirmReject: null,
                link: '/Companies/edit/',
                company: @Html.Raw(AntiXss.ToJson(Model)),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'isActive',
                            sortKey: 'isActive',
                            header: 'Active',
                            type: 'bool',
                            style: 'width: 16%;'
                        },
                        {
                            dataKey: 'contactPersonName',
                            sortKey: 'contactPersonName',
                            header: 'Contact Person Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'contactPersonEmail',
                            sortKey: 'contactPersonEmail',
                            header: 'Contact Person Email',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'contactPersonName',
                        options: [],
                        type: 'search',
                        header: 'Search Contact Person Name',
                        fn: v => p => (p.contactPersonName || '').toLowerCase().includes((v || '').toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'contactPersonEmail',
                        options: [],
                        type: 'search',
                        header: 'Search Contact Person Email',
                        fn: v => p => (p.contactPersonEmail || '').toLowerCase().includes((v || '').toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
</script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}

