using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ReferenceManagement.Contracts;

public interface IImportingReferenceRepository : ITrackingRepository<Reference>
{
    // *** Used during importing
    Task<Reference> GetByIdForImport(int id);
    Task<Reference> GetReferenceById(int id);
    Task<Reference> GetReferenceBySourceId(string sourceId);
    Task<ReferenceIdentifiers> GetReferenceIdentifiers(string sourceId);
    void ClearChangeTracker();
}