﻿using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.Test.Framework.Fakes;

/// <summary>
/// Implementation of ILoggerFactory to allow creation of mock logger.
/// </summary>
/// <remarks>
/// CreateLogger is an extension method and so is static and can not be mocked. This allows an injected ILoggerFactory to return
/// a mock logger. We probably shouldn't be passing the factory into the constructor however this allows us to do so and so 
/// not change existing code whilst enabling unit testing.</remarks>
/// <typeparam name="T">Type of logger</typeparam>
/// <seealso cref="Microsoft.Extensions.Logging.ILoggerFactory" />
[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public class FakeLoggerFactory<T> : ILoggerFactory
{
    private readonly Mock<ILogger<T>> _mockLogger;

    public FakeLoggerFactory(Mock<ILogger<T>> mockLogger) 
    {
        _mockLogger = mockLogger;
    }

    public void AddProvider(ILoggerProvider provider)
    {
        throw new NotImplementedException();
    }

    public ILogger<T> CreateLogger()
    {
        return _mockLogger.Object;
    }

    public Microsoft.Extensions.Logging.ILogger CreateLogger(string categoryName)
    {
        if (typeof(T).FullName == categoryName)
        {
            return _mockLogger.Object;
        }

        throw new NotImplementedException();
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);
    }
}