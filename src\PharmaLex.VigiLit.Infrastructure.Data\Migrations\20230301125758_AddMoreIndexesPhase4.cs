﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddMoreIndexesPhase4 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SubstanceSynonyms_SubstanceId",
                table: "SubstanceSynonyms");

            migrationBuilder.DropIndex(
                name: "IX_Projects_CompanyId",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_Projects_Name_CompanyId",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_CompanyUsers_CompanyId",
                table: "CompanyUsers");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "SubstanceSynonyms",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubstanceSynonyms_SubstanceId_Name",
                table: "SubstanceSynonyms",
                columns: new[] { "SubstanceId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_Projects_CompanyId_Name",
                table: "Projects",
                columns: new[] { "CompanyId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Emails_StartDate",
                table: "Emails",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyUsers_CompanyId_UserId",
                table: "CompanyUsers",
                columns: new[] { "CompanyId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_Claims_Name",
                table: "Claims",
                column: "Name");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SubstanceSynonyms_SubstanceId_Name",
                table: "SubstanceSynonyms");

            migrationBuilder.DropIndex(
                name: "IX_Projects_CompanyId_Name",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_Emails_StartDate",
                table: "Emails");

            migrationBuilder.DropIndex(
                name: "IX_CompanyUsers_CompanyId_UserId",
                table: "CompanyUsers");

            migrationBuilder.DropIndex(
                name: "IX_Claims_Name",
                table: "Claims");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "SubstanceSynonyms",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubstanceSynonyms_SubstanceId",
                table: "SubstanceSynonyms",
                column: "SubstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_Projects_CompanyId",
                table: "Projects",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Projects_Name_CompanyId",
                table: "Projects",
                columns: new[] { "Name", "CompanyId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompanyUsers_CompanyId",
                table: "CompanyUsers",
                column: "CompanyId");
        }
    }
}
