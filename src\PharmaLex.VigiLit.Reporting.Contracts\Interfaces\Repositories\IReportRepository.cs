using PharmaLex.Core.UserManagement;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports;

namespace PharmaLex.VigiLit.Reporting.Interfaces.Repositories;

public interface IReportRepository : ITrackingRepository<IReportEntity>
{
    Task<IReportEntity> GetById(int id);
    Task<IEnumerable<ReportModel>> GetReports(IUserEntity user);
    Task<bool> CanUserViewReport(IUserEntity user, string controllerName);
}