﻿SET IDENTITY_INSERT [dbo].[Countries] ON 

INSERT INTO [dbo].[Countries] ([Id], [Name], [Iso], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) 
VALUES 
	  (1, 'Afghanistan', 'AF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (2, 'Albania', 'AL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (3, 'Algeria', 'DZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (4, 'American Samoa', 'AS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (5, 'Andorra', 'AD', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (6, 'Angola', 'AO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (7, '<PERSON><PERSON><PERSON>', 'AI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (8, 'Antarctica', 'AQ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (9, 'Antigua and Barbuda', 'AG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (10, 'Argentina', 'AR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (11, 'Armenia', 'AM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (12, 'Aruba', 'AW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (13, 'Australia', 'AU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (14, 'Austria', 'AT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (15, 'Azerbaijan', 'AZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (16, 'Bahamas', 'BS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (17, 'Bahrain', 'BH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (18, 'Bangladesh', 'BD', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (19, 'Barbados', 'BB', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (20, 'Belarus', 'BY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (21, 'Belgium', 'BE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (22, 'Belize', 'BZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (23, 'Benin', 'BJ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (24, 'Bermuda', 'BM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (25, 'Bhutan', 'BT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (26, 'Bosnia and Herzegovina', 'BA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (27, 'Botswana', 'BW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (28, 'Bouvet Island', 'BV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (29, 'Brazil', 'BR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (30, 'British Indian Ocean Territory', 'IO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (31, 'Brunei Darussalam', 'BN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (32, 'Bulgaria', 'BG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (33, 'Burkina Faso', 'BF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (34, 'Burundi', 'BI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (35, 'Cambodia', 'KH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (36, 'Cameroon', 'CM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (37, 'Canada', 'CA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (38, 'Cape Verde', 'CV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (39, 'Cayman Islands', 'KY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (40, 'Central African Republic', 'CF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (41, 'Chad', 'TD', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (42, 'Chile', 'CL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (43, 'China', 'CN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (44, 'Christmas Island', 'CX', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (45, 'Cocos (Keeling) Islands', 'CC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (46, 'Colombia', 'CO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (47, 'Comoros', 'KM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (48, 'Congo', 'CG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (49, 'Cook Islands', 'CK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (50, 'Costa Rica', 'CR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (51, 'Croatia', 'HR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (52, 'Cuba', 'CU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (53, 'Cyprus', 'CY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (54, 'Czech Republic', 'CZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (55, 'Denmark', 'DK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (56, 'Djibouti', 'DJ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (57, 'Dominica', 'DM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (58, 'Dominican Republic', 'DO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (59, 'Ecuador', 'EC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (60, 'Egypt', 'EG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (61, 'El Salvador', 'SV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (62, 'Equatorial Guinea', 'GQ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (63, 'Eritrea', 'ER', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (64, 'Estonia', 'EE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (65, 'Ethiopia', 'ET', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (66, 'Falkland Islands (Malvinas)', 'FK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (67, 'Faroe Islands', 'FO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (68, 'Fiji', 'FJ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (69, 'Finland', 'FI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (70, 'France', 'FR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (71, 'French Guiana', 'GF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (72, 'French Polynesia', 'PF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (73, 'French Southern Territories', 'TF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (74, 'Gabon', 'GA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (75, 'Gambia', 'GM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (76, 'Georgia', 'GE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (77, 'Germany', 'DE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (78, 'Ghana', 'GH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (79, 'Gibraltar', 'GI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (80, 'Greece', 'GR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (81, 'Greenland', 'GL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (82, 'Grenada', 'GD', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (83, 'Guadeloupe', 'GP', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (84, 'Guam', 'GU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (85, 'Guatemala', 'GT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (86, 'Guernsey', 'GG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (87, 'Guinea', 'GN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (88, 'Guinea-Bissau', 'GW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (89, 'Guyana', 'GY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (90, 'Haiti', 'HT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (91, 'Heard Island and McDonald Islands', 'HM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (92, 'Holy See (Vatican City State)', 'VA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (93, 'Honduras', 'HN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (94, 'Hong Kong', 'HK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (95, 'Hungary', 'HU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (96, 'Iceland', 'IS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (97, 'India', 'IN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (98, 'Indonesia', 'ID', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (99, 'Iraq', 'IQ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (100, 'Ireland', 'IE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (101, 'Islamic Republic of Iran', 'IR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (102, 'Isle of Man', 'IM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (103, 'Israel', 'IL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (104, 'Italy', 'IT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (105, 'Jamaica', 'JM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (106, 'Japan', 'JP', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (107, 'Jersey', 'JE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (108, 'Jordam', 'JO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (109, 'Kazakhstan', 'KZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (110, 'Kenya', 'KE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (111, 'Kiribati', 'KI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (112, 'Kuwait', 'KW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (113, 'Kyrgyzsta', 'KG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (114, 'Lao Peoples Democratic Republic', 'LA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (115, 'Latvia', 'LV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (116, 'Lebanon', 'LB', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (117, 'Lesotho', 'LS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (118, 'Liberia', 'LR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (119, 'Libya', 'LY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (120, 'Liechtenstein', 'LI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (121, 'Lithuania', 'LT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (122, 'Luxembourg', 'LU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (123, 'Macao', 'MO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (124, 'Madagascar', 'MG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (125, 'Malawi', 'MW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (126, 'Malaysia', 'MY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (127, 'Maldives', 'MV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (128, 'Mali', 'ML', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (129, 'Malta', 'MT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (130, 'Marshall Islands', 'MH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (131, 'Martinique', 'MQ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (132, 'Mauritania', 'MR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (133, 'Mauritius', 'MU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (134, 'Mayotte', 'YT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (135, 'Mexico', 'MX', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (136, 'Monaco', 'MC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (137, 'Mongolia', 'MN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (138, 'Montenegro', 'ME', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (139, 'Montserrat', 'MS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (140, 'Morocco', 'MA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (141, 'Mozambique', 'MZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (142, 'Myanmar', 'MM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (143, 'Namibia', 'NA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (144, 'Nauru', 'NR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (145, 'Nepal', 'NP', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (146, 'Netherlands', 'NL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (147, 'New Caledonia', 'NC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (148, 'New Zealand', 'NZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (149, 'Nicaragua', 'NI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (150, 'Niger', 'NE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (151, 'Nigeria', 'NG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (152, 'Niue', 'NU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (153, 'Norfolk Island', 'NF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (154, 'North Korea', 'KP', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (155, 'North Macedonia', 'MK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (156, 'Northern Mariana Islands', 'MP', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (157, 'Norway', 'NO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (158, 'Oman', 'OM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (159, 'Pakistan', 'PK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (160, 'Palau', 'PW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (161, 'Panama', 'PA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (162, 'Papua New Guinea', 'PG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (163, 'Paraguay', 'PY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (164, 'Peru', 'PE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (165, 'Philippines', 'PH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (166, 'Pitcairn', 'PN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (167, 'Poland', 'PL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (168, 'Portugal', 'PT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (169, 'Puerto Rico', 'PR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (170, 'Qatar', 'QA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (171, 'Romania', 'RO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (172, 'Russian Federation', 'RU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (173, 'Rwanda', 'RW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (174, 'Saint Kitts and Nevis', 'KN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (175, 'Saint Lucia', 'LC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (176, 'Saint Martin (French part)', 'MF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (177, 'Saint Pierre and Miquelon', 'PM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (178, 'Saint Vincent and the Grenadines', 'VC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (179, 'Samoa', 'WS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (180, 'San Marino', 'SM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (181, 'Sao Tome and Principe', 'ST', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (182, 'Saudi Arabia', 'SA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (183, 'Senegal', 'SN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (184, 'Serbia', 'RS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (185, 'Seychelles', 'SC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (186, 'Sierra Leone', 'SL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (187, 'Singapore', 'SG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (188, 'Sint Maarten (Dutch part)', 'SX', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (189, 'Slovakia', 'SK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (190, 'Slovenia', 'SI', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (191, 'Solomon Islands', 'SB', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (192, 'Somalia', 'SO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (193, 'South Africa', 'ZA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (194, 'South Georgia and the South Sandwich Islands', 'GS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (195, 'South Korea', 'KR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (196, 'South Sudan', 'SS', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (197, 'Spain', 'ES', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (198, 'Sri Lanka', 'LK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (199, 'Sudan', 'SD', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (200, 'Suriname', 'SR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (201, 'Svalbard and Jan Mayen', 'SJ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (202, 'Swaziland', 'SZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (203, 'Sweden', 'SE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (204, 'Switzerland', 'CH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (205, 'Syrian Arab Republic', 'SY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (206, 'Tajikistan', 'TJ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (207, 'Thailand', 'TH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (208, 'Timor-Leste', 'TL', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (209, 'Togo', 'TG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (210, 'Tokelau', 'TK', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (211, 'Tonga', 'TO', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (212, 'Trinidad and Tobago', 'TT', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (213, 'Tunisia', 'TN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (214, 'Turkey', 'TR', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (215, 'Turkmenistan', 'TM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (216, 'Turks and Caicos Islands', 'TC', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (217, 'Tuvalu', 'TV', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (218, 'Uganda', 'UG', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (219, 'Ukraine', 'UA', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (220, 'United Arab Emirates', 'AE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (221, 'United Kingdom', 'GB', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (222, 'United States', 'US', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (223, 'United States Minor Outlying Islands', 'UM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (224, 'Uruguay', 'UY', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (225, 'Uzbekistan', 'UZ', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (226, 'Vanuatu', 'VU', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (227, 'Viet Nam', 'VN', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (228, 'Wallis and Futuna', 'WF', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (229, 'Western Sahara', 'EH', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (230,	'Yemen', 'YE', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (231,	'Zambia', 'ZM', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')
	, (232, 'Zimbabwe', 'ZW', '2022-02-22', 'Initial Script', '2022-02-22', 'Initial Script')

SET IDENTITY_INSERT [dbo].[Countries] OFF
