DownloadFile = {
    fromUrl: async (url, data = null, onSuccess = null, onFailure = null, onComplete = null) => {
        var requestType = "GET";

        if (!url) {
            plx.toast.show('Invalid URL given for download.', 2, 'failed', null, 2500);
        }
        if (!token) {
            var token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        }
        if (data) {
            requestType = "POST";
        }
        
        await fetch(url, {
            method: requestType,
            credentials: 'same-origin',
            body: data,
            headers: {
                "RequestVerificationToken": token,
                "Content-Type": "application/json"
            }
        }).then(async (response) => {
            if (!response) {
                throw 'Did not get a response from the server.';
            }
            var headerInfo = response.headers.get('Content-Disposition');
            var fileName = headerInfo.split('filename=')[1].split(';')[0].replaceAll('"', '');
            var blob = await response.blob();

            var blobUrl = window.URL.createObjectURL(blob);
            var a = document.createElement("a");
            a.href = blobUrl;
            a.download = fileName;
            a.click();
            a.remove();
            
            if (onSuccess) {
                onSuccess();
            }
        }).catch(error => {
            console.log(error);
            if (onFailure) {
                onFailure();
            }
        }).then(() => {
            if (onComplete) {
                onComplete();
            }
        });
    }
}