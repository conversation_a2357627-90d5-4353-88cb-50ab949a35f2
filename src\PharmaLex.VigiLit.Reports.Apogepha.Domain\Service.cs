﻿using System.Globalization;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reports.Apogepha.Domain.Models;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

public class Service : IApogephaClientReportService
{
    private readonly IRepository _apogephaClientReportRepository;
    private readonly IExportService _exportService;

    public Service(
        IRepository apogephaClientReportRepository,
        IExportService exportService)
    {
        _apogephaClientReportRepository = apogephaClientReportRepository;
        _exportService = exportService;
    }

    public async Task<DownloadFile> Download(int year, int month)
    {
        if (!GetYears().Contains(year)) throw new ArgumentException("Invalid year.");

        if (month < 1 || month > 12) throw new ArgumentException("Invalid month.");

        var data = await _apogephaClientReportRepository.GetStoredProcResults(year, month);

        var exportConfig = new ExportConfiguration { Delimiter = ";", QuoteAllFields = true, IncludeSepLine = false };

        var export =
            _exportService.Export<ApogephaClientReportResult, ResultClassMap>(
                "ApogephaClientReport", data, exportConfig);

        return new DownloadFile
        {
            FileName = $"ApogephaClientReport_{year}_{month}.csv",
            ContentType = export.ContentType,
            Bytes = export.Data
        };
    }

    public PageModel GetPageModel()
    {
        var years = GetYears();
        var selectedYear = years.Max();
        var cards = GetCards(selectedYear);

        return new PageModel
        {
            Years = years,
            SelectedYear = selectedYear,
            Cards = cards
        };
    }

    public IEnumerable<CardModel> GetCards(int year)
    {
        if (!GetYears().Contains(year)) throw new ArgumentException("Invalid year.");

        var cards = new List<CardModel>();

        for (var month = 12; month >= 1; month--)
        {
            var card = new CardModel
            {
                Year = year,
                Month = month,
                MonthName = CultureInfo.InvariantCulture.DateTimeFormat.GetMonthName(month),
                IsFuture = IsFuture(year, month)
            };

            cards.Add(card);
        }

        return cards;
    }

    private static bool IsFuture(int year, int month)
    {
        var now = DateTime.UtcNow;
        return year == now.Year && month >= now.Month;
    }

    private static List<int> GetYears()
    {
        var startYear = 2024;
        var currentYear = DateTime.UtcNow.Year;

        return Enumerable.Range(startYear, currentYear - startYear + 1).Reverse().ToList();
    }
}