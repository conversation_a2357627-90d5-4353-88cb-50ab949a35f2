﻿using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;

public class ViewReferencePermissionContext : IAccessControlContext
{
    /// <summary>
    /// Context for checking permissions for a user to view a reference.
    /// </summary>
    /// <seealso cref="PharmaLex.VigiLit.AccessControl.IAccessControlContext" />
    public ViewReferencePermissionContext(int userId, int referenceId)
    {
        UserId = userId;
        ReferenceId = referenceId;
    }

    /// <summary>
    /// Gets the user identifier of the user who is requesting to view a reference.
    /// </summary>
    /// <value>
    /// The user identifier.
    /// </value>
    public int UserId { get; }

    /// <summary>
    /// Gets the reference identifier.
    /// </summary>
    /// <value>
    /// The reference identifier.
    /// </value>
    public int ReferenceId { get; }
}
