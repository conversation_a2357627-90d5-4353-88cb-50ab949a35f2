﻿<script type="text/x-template" id="day-of-week-cell-template">
    <td>{{day}}</td>
</script>

<script type="text/javascript">
        vueApp.component('day-of-week-cell', {
        template: '#day-of-week-cell-template',
        props: {
            val: Number
        },
        computed: {
            day() {
                switch(this.val) {
                    case 0: return 'No Auto Import'; break;
                    case 1: return 'Monday'; break;
                    case 2: return 'Tuesday'; break;
                    case 3: return 'Wednesday'; break;
                    case 4: return 'Thursday'; break;
                    case 5: return 'Friday'; break;
                    default: return '-';
                }
            }
        }
    });
</script>