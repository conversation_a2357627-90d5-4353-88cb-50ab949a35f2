﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class ReferenceFullTextIndex : Migration
    {
        /*
         * Full Text Indexes cannot currently be created with EF code first so has to be done directly with SQL
         * Needs Full Text Search to be set up on SQL Server
         * See https://phlexglobal.atlassian.net/wiki/spaces/VIG/pages/3262545965/SQL+Server+Full+Text+Search
         **/
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                sql: "IF  EXISTS (SELECT * FROM sys.fulltext_indexes fti WHERE fti.object_id = OBJECT_ID(N'[dbo].[References]'))\r\n" +
                "ALTER FULLTEXT INDEX ON [dbo].[References] DISABLE",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "IF  EXISTS (SELECT * FROM sys.fulltext_indexes fti WHERE fti.object_id = OBJECT_ID(N'[dbo].[References]'))\r\n" +
                "BEGIN\r\n" +
                "\tDROP FULLTEXT INDEX ON [dbo].[References]\r\n" +
                "END",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE [name]='FTCReferences')\r\n" +
                "BEGIN\r\n" +
                "\tDROP FULLTEXT CATALOG FTCReferences \r\n" +
                "END",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "CREATE FULLTEXT CATALOG FTCReferences AS DEFAULT;",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "CREATE FULLTEXT INDEX ON dbo.[References](Title,Keywords,MeshHeadings) KEY INDEX PK_References ON FTCReferences WITH STOPLIST = OFF, CHANGE_TRACKING AUTO;",
                suppressTransaction: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                sql: "IF  EXISTS (SELECT * FROM sys.fulltext_indexes fti WHERE fti.object_id = OBJECT_ID(N'[dbo].[References]'))\r\n" +
                "ALTER FULLTEXT INDEX ON [dbo].[References] DISABLE",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "IF  EXISTS (SELECT * FROM sys.fulltext_indexes fti WHERE fti.object_id = OBJECT_ID(N'[dbo].[References]'))\r\n" +
                "BEGIN\r\n" +
                "\tDROP FULLTEXT INDEX ON [dbo].[References]\r\n" +
                "END",
                suppressTransaction: true);

            migrationBuilder.Sql(
                sql: "IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE [name]='FTCReferences')\r\n" +
                "BEGIN\r\n" +
                "\tDROP FULLTEXT CATALOG FTCReferences \r\n" +
                "END",
                suppressTransaction: true);
        }
    }
}
