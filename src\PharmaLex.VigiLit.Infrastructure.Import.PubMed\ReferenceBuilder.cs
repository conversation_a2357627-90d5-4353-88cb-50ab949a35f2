﻿using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;
using System.Text;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public class ReferenceBuilder : IReferenceBuilder
{
    public ReferenceBuilder() { }

    public Reference Build(PubmedArticle pubMedArticle)
    {
        Article article = pubMedArticle.MedlineCitation.Article;
        Reference reference = new();

        FillPmid(pubMedArticle, reference);
        FillDoi(pubMedArticle, reference);
        FillDateRevised(pubMedArticle, reference);

        FillArticleData(article, reference);
        FillJournalData(pubMedArticle, article, reference);
        FillAuthors(article, reference);
        FillMeshHeadings(pubMedArticle, reference);
        FillKeywords(pubMedArticle, reference);
        FillAffiliationTextFirstAuthor(article, reference);
        FillCountryOfOccurrence(reference);

        return reference;
    }

    private static void FillPmid(PubmedArticle pubMedArticle, Reference reference)
    {
        reference.SourceId = pubMedArticle.MedlineCitation.PMID.Value.ToString();
    }

    private static void FillDoi(PubmedArticle pubMedArticle, Reference reference)
    {
        reference.Doi = pubMedArticle.PubmedData?.ArticleIdList?.Find(x => x.IdType == ArticleIdIdType.doi)?.Value;
    }

    private static void FillDateRevised(PubmedArticle pubMedArticle, Reference reference)
    {
        if (pubMedArticle.MedlineCitation != null)
        {
            reference.DateRevised = new DateTime(pubMedArticle.MedlineCitation.DateRevised.Year, int.Parse(pubMedArticle.MedlineCitation.DateRevised.Month), pubMedArticle.MedlineCitation.DateRevised.Day, 0, 0, 0, DateTimeKind.Utc);
        }
    }

    private void FillArticleData(Article article, Reference reference)
    {
        if (article.ArticleTitle?.Text != null && article.ArticleTitle.Text.Length > 0)
        {
            reference.Title = string.Join(string.Empty, article.ArticleTitle.Text);
        }

        reference.Abstract = GetAbstract(article.Abstract?.AbstractText);
        reference.Language = string.Join(",", article.Language);
        reference.FullPagination = article.Pagination?.MedlinePgn;
        reference.PublicationType = string.Join("; ", article.PublicationTypeList.Select(x => x.Value));
    }

    private string GetAbstract(List<AbstractText> abstractTexts)
    {
        if (abstractTexts == null || abstractTexts.Count == 0)
        {
            return string.Empty;
        }

        if (abstractTexts.Count == 1)
        {
            return GetAbstractFromSingleAbstractText(abstractTexts);
        }
        else
        {
            return GetAbstractFromMultipleAbstractTexts(abstractTexts);
        }
    }

    private static string GetAbstractFromSingleAbstractText(List<AbstractText> abstractTexts)
    {
        if (abstractTexts[0].Text != null)
        {
            return string.Join("", abstractTexts[0].Text);
        }

        return string.Empty;
    }

    private static string GetAbstractFromMultipleAbstractTexts(List<AbstractText> abstractTexts)
    {
        StringBuilder sb = new();

        foreach (var abstractText in abstractTexts)
        {
            if (!string.IsNullOrEmpty(abstractText.Label))
            {
                if (sb.Length != 0)
                {
                    sb.AppendLine();
                }

                sb.Append(abstractText.Label);
                sb.Append(": ");
            }

            if (abstractText.Text != null)
            {
                sb.Append(string.Join(string.Empty, abstractText.Text));
            }

            sb.Append("<br>");
        }

        return sb.ToString();
    }

    private void FillJournalData(PubmedArticle pubMedArticle, Article article, Reference reference)
    {
        if (article.Journal != null)
        {
            reference.Issn = article.Journal.ISSN?.Value;
            reference.VolumeAbbreviation = GetVolumeAbbreviation(pubMedArticle);
            reference.Issue = article.Journal.JournalIssue?.Issue;

            if (article.Journal.JournalIssue?.PubDate?.Year != null)
            {
                reference.PublicationYear = article.Journal.JournalIssue.PubDate.Year;
            }
            else if (article.Journal.JournalIssue?.PubDate?.MedlineDate != null)
            {
                //// This code may not be reachable.

                var match = Regex.Match(article.Journal.JournalIssue.PubDate.MedlineDate, @"\d{4}", RegexOptions.None, TimeSpan.FromSeconds(10));

                if (match.Success)
                {
                    reference.PublicationYear = ushort.Parse(match.Value);
                }
            }

            reference.Volume = article.Journal.JournalIssue?.Volume;
            reference.JournalTitle = article.Journal.Title;
        }
    }

    private string GetVolumeAbbreviation(PubmedArticle pubMedArticle)
    {
        if (pubMedArticle.MedlineCitation.MedlineJournalInfo != null)
        {
            return pubMedArticle.MedlineCitation.MedlineJournalInfo.MedlineTA;
        }
        else
        {
            return pubMedArticle.MedlineCitation.Article.Journal.ISOAbbreviation ?? null;
        }
    }

    private static void FillAuthors(Article article, Reference reference)
    {
        if (article.AuthorList?.Author != null && article.AuthorList?.Author?.Count != 0)
        {
            int maxAuthors = 6;

            List<string> authors = article.AuthorList.Author
                    .Take(maxAuthors)
                    .Select(a => $"{a.LastName} {a.Initials}")
                    .ToList();

            reference.Authors = string.Join(", ", authors);

            if (article.AuthorList?.Author?.Count > maxAuthors)
            {
                reference.Authors += ", et al";
            }
        }
    }

    private static void FillMeshHeadings(PubmedArticle pubMedArticle, Reference reference)
    {
        if (!pubMedArticle.MedlineCitation.MeshHeadingList.Any())
        {
            return;
        }

        var meshHeadingsSb = new StringBuilder();

        var meshHeadings = pubMedArticle.MedlineCitation.MeshHeadingList.OrderBy(x => x.DescriptorName.Value);

        foreach (var meshHeading in meshHeadings)
        {
            AppendMeshHeading(meshHeadingsSb, meshHeadings, meshHeading);
        }

        reference.MeshHeadings = meshHeadingsSb.ToString();
    }

    private static void AppendMeshHeading(StringBuilder meshHeadingsSb, IOrderedEnumerable<MeshHeading> meshHeadings, MeshHeading meshHeading)
    {
        if (string.IsNullOrWhiteSpace(meshHeading.DescriptorName.Value))
        {
            return;
        }

        if (meshHeading.DescriptorName.MajorTopicYN == EnumYN.Y)
        {
            meshHeading.DescriptorName.Value += "*";
        }

        meshHeadingsSb.Append($"{meshHeading.DescriptorName.Value}");

        if (meshHeading.QualifierName.Any())
        {
            meshHeadingsSb.Append($": {string.Join(" / ", meshHeading.QualifierName.OrderBy(x => x.Value).Select(a => a.Value + (a.MajorTopicYN == EnumYN.Y ? "*" : "")))}");
        }

        if (meshHeading != meshHeadings.Last())
        {
            meshHeadingsSb.Append("; ");
        }
    }

    private static void FillKeywords(PubmedArticle pubMedArticle, Reference reference)
    {
        if (pubMedArticle.MedlineCitation.KeywordList.Any())
        {
            var keywordList = new List<Keyword>();
            
            pubMedArticle.MedlineCitation.KeywordList.ForEach(x => keywordList.AddRange(x.Keyword));

            reference.Keywords = string.Join("; ", keywordList.OrderBy(x => x.Text).Select(y => y.Text + (y.MajorTopicYN == EnumYN.Y ? "*" : "")));
        }
    }

    private static void FillAffiliationTextFirstAuthor(Article article, Reference reference)
    {
        reference.AffiliationTextFirstAuthor = article.AuthorList?.Author.FirstOrDefault()?.AffiliationInfo.FirstOrDefault()?.Affiliation?.Text;
    }

    private static void FillCountryOfOccurrence(Reference reference)
    {
        if (!string.IsNullOrEmpty(reference.AffiliationTextFirstAuthor))
        {
            string pattern = @"^.*, +([A-Za-z ]+)\..*$";
            var match = Regex.Match(reference.AffiliationTextFirstAuthor, pattern, RegexOptions.None, TimeSpan.FromSeconds(10));
            if (match.Success)
            {
                reference.CountryOfOccurrence = match.Groups[1].Value;
            }
        }
    }
}
