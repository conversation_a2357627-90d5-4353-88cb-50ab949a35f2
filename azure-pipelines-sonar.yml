trigger:
  - master
  - develop
# pr trigger specifies which branches cause a pull request build to run.
pr:
  - master
  - develop

schedules:
  - cron: "0 22 * * 0-6"
    displayName: "Nightly Build"
    branches:
      include:
      - develop
    always: true

pool:
  vmImage: 'windows-latest'

parameters:
- name: RunCheckmarx
  displayName: Run Checkmarx?
  type: boolean
  default: false
- name: RunTests
  displayName: Run Tests?
  type: boolean
  default: true

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  Major: '1'
  Minor: '0'
  Patch:  $[counter('', 820)]
name: VigiLit $(Major).$(Minor).$(Patch).$(Build.SourceBranchName)

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git
  containers:
    - container: mssql
      image: mcr.microsoft.com/mssql/server:2022-latest
      ports: 
        - 1433:1433
      env:
        ACCEPT_EULA: Y
        SA_PASSWORD: <YourStrong!Passw0rd>
        MSSQL_PID: Express

    - container: rabbit
      image: rabbitmq:4.0.9-management-alpine
      env:
        RABBITMQ_ERLANG_COOKIE: "${RABBITMQ_ERLANG_COOKIE}"
        RABBITMQ_DEFAULT_USER: "test"
        RABBITMQ_DEFAULT_PASS: "test"
        RABBITMQ_DEFAULT_VHOST: "/"
      ports:
        - 5672:5672
stages:
- stage: BuildAndTest
  jobs:
    - job: BuildTest
      variables:
        - group: Nuget
        - name: NuGet_Source
          value: $[variables.Source]
        - name: LongLivedBranch
          ${{ if or(eq(variables['Build.SourceBranchName'], 'develop'), eq(variables['Build.SourceBranchName'], 'master')) }}:
            value : true
          ${{ else }}:
            value: false
        - name: TestProjects
          ${{ if eq(variables['Build.Reason'], 'Schedule') }}:
            value : '**/*Integration.Tests.csproj'
          ${{ elseif and(eq(parameters.RunTests, true), eq(variables['Build.Reason'], 'PullRequest')) }}:
            value : '**/*Unit.Tests.csproj'
          ${{ else }}:
            value : '**/*Ignore.Unit.Tests.csproj'
        - name: RunCheckmarx
          ${{ if or(eq(parameters.RunCheckmarx, true), eq(variables['Build.Reason'], 'Schedule')) }}:
            value : true
          ${{ else }}:
            value: false
             

      services:
        rabbit: rabbit
        mssql: mssql
      displayName: "Build And Test"
      pool:
        vmImage: 'ubuntu-latest'
      steps:
        - template: build-test-analyse.yml
          parameters:
            NugetSource: '$(NuGet_Source)'
            SourceBranch: "variables['Source_Branch']"
            VersionNumber: "$(Major).$(Minor).$(Patch)"
            SonarProjectKey: "phlexglobal_pharmalex-vigilit"
            SonarProjectName: "PharmaLex.Vigilit"
            SolutionName: '**/VigiLit.sln'
            TestProjects: ${{ variables.TestProjects }}
            AnalysePackages: true
            LongLivedBranch: ${{ variables.LongLivedBranch }}
            CheckmarxEnabled: ${{ variables.RunCheckmarx }}
            CheckmarxTeam: 'PV'


- stage: BuildArtifacts
  dependsOn:
    - BuildAndTest
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
  jobs:
    - job: BuildArtifacts
      displayName: "Build Artifacts"
      pool:
        vmImage: 'windows-latest'
      steps:
        - checkout: self
          submodules: true
          fetchDepth: 1
        - task: UseDotNet@2
          displayName: 'Install Dotnet Core 8' 
          inputs:
            packageType: 'sdk'
            version: '8.0.x'

        - task: NodeTool@0
          displayName: Install Node
          inputs:
            versionSpec: '20.x'
            checkLatest: true

        - task: DotNetCoreCLI@2
          displayName: 'dotnet restore'
          inputs:
            command: 'restore'
            projects: '**/*.csproj'
            feedsToUse: 'select'
            vstsFeed: '780c2041-bb89-4053-a6e6-13d4c16ae672'

        - task: CmdLine@2
          displayName: yarn install
          inputs:
            script: yarn install
            workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'

        - task: CmdLine@2
          displayName: yarn build
          inputs:
            script: yarn build-prod
            workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'

        - task: DotNetCoreCLI@2
          displayName: Install EF Tool
          inputs:
            command: custom
            custom: 'tool'
            arguments: 'install --global dotnet-ef --version 8.0.2'

        - task: DotNetCoreCLI@2
          displayName: Create SQL Scripts
          inputs:
            command: custom
            custom: 'ef '
            arguments: migrations script --project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" --startup-project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web\PharmaLex.VigiLit.Web.csproj" --output $(Build.artifactstagingdirectory)\Migrations\migration.sql --idempotent --verbose --configuration $(BuildConfiguration)
        - task: DotNetCoreCLI@2
          displayName: 'dotnet publish web'
          inputs:
            command: 'publish'
            publishWebProjects: true
            arguments: '--configuration $(BuildConfiguration) --output $(build.artifactstagingdirectory)'

        - task: DotNetCoreCLI@2
          displayName: 'dotnet publish import function'
          inputs:
            command: 'publish'
            publishWebProjects: false
            projects: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.ImportApp\PharmaLex.VigiLit.ImportApp.csproj'
            arguments: '--configuration Release --output $(build.artifactstagingdirectory)\import'
            zipAfterPublish: false
            modifyOutputPath: false

        - task: ArchiveFiles@2
          inputs:
            rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\import'
            includeRootFolder: false
            archiveType: 'zip'
            archiveFile: '$(Build.ArtifactStagingDirectory)\VigiLitImportApp.zip'
            replaceExistingArchive: true

        - task: PublishBuildArtifacts@1
          displayName: 'publish artifacts drop'
          inputs:
            PathtoPublish: '$(Build.ArtifactStagingDirectory)'
            ArtifactName: 'drop'
            publishLocation: 'Container'

        
