﻿using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceDetailedModel : ReferenceModel
{
    public string CountryOfOccurrence { get; set; }
    public string Language { get; set; }
    public string Authors { get; set; }
    public string MeshHeadings { get; set; }
    public string Doi { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }
    public string Keywords { get; set; }
    public DateTime DateRevised { get; set; }
}
