﻿using System.ComponentModel.DataAnnotations;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class ContractVersionEditModel
{
    public int Id { get; set; }
    [MaxLength(4000)]
    public string SearchString { get; set; }
    public int ContractVersionStatus { get; set; }
    public int Version { get; set; }
    public string ReasonForChange { get; set; }
    public int UserId { get; set; }
    public DateTime TimeStamp { get; set; }
    public ContractType ContractType { get; set; }
    public ContractWeekday ContractWeekday { get; set; }
    public int SearchPeriodDays { get; set; }
    public bool IsActive { get; set; }

    public int Country  { get; set; }

    public MultiSelectFilterRequest Journals { get; set; }

    public int ScreeningType { get; set; }

}
