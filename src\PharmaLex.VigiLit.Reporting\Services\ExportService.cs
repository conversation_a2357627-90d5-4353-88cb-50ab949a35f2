﻿using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using System.Globalization;

namespace PharmaLex.VigiLit.Reporting.Services;

public class ExportService : IExportService
{
    private const string CONTENT_TYPE = "text/csv";
    private const string DATE_FORMAT = "dd MMM yyyy";

    public ExportModel Export<TData, TClassMap>(string name, IEnumerable<TData> data)
        where TData : class
        where TClassMap : ClassMap<TData>
    {
        return Export<TData, TClassMap>(name, data, new ExportConfiguration());
    }

    public ExportModel Export<TData, TClassMap>(string name, IEnumerable<TData> data, ExportConfiguration configuration)
        where TData : class
        where TClassMap : ClassMap<TData>
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentNullException(nameof(name));
        }

        var fileName = $"{name.Trim()}-{DateTime.UtcNow:s}.csv";

        if (data == null)
        {
            return new ExportModel(fileName, CONTENT_TYPE, Array.Empty<byte>());
        }

        var csvConfiguration = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            Delimiter = configuration.Delimiter,
            InjectionOptions = InjectionOptions.Escape,
            NewLine = "\r\n",
            HasHeaderRecord = configuration.HasHeaderRecord
        };

        if (configuration.QuoteAllFields)
        {
            csvConfiguration.ShouldQuote = args => true;
        }

        using var memoryStream = new MemoryStream();
        using var streamWriter = new StreamWriter(memoryStream);
        using (var csvWriter = new CsvWriter(streamWriter, csvConfiguration))
        {
            var options = new TypeConverterOptions { Formats = new[] { DATE_FORMAT } };
            csvWriter.Context.TypeConverterOptionsCache.AddOptions<DateTime>(options);
            csvWriter.Context.TypeConverterOptionsCache.AddOptions<DateTime?>(options);

            csvWriter.Context.RegisterClassMap<TClassMap>();

            if (configuration.IncludeSepLine)
            {
                csvWriter.WriteField($"sep={configuration.Delimiter}", false);
                csvWriter.NextRecord();
            }

            csvWriter.WriteRecords(data);
        }

        var bytes = memoryStream.ToArray();

        return new ExportModel(fileName, CONTENT_TYPE, bytes);
    }
}