@model PharmaLex.VigiLit.ImportManagement.Ui.Dashboard.DashboardDetailsModel
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Import: " + @Model.Import.Name;
}

@Html.AntiForgeryToken()

<div id="import-dashboard-details" v-cloak>
    <div class="sub-header">
        <h2>Import: {{data.import.name}}</h2>
        <div class="controls">
            <a class="button btn-default" href="../">Back to Dashboard</a>
            <div>
                <button type="button" id="btnExport" class="btn btn-default btn-spinner" @@click="onExport" class="btn-default">
                    <span class="btn-label btn-span">Export</span>
                    <span class="spinner btn-span"></span>
                </button>
            </div>
        </div>
    </div>
    <section>
        <template v-if="data.rows.length > 0">
            <div class="row">
                <div class="column">
                    <p>Displaying results {{firstRow}}-{{lastRow}} of {{data.totalRowCount}}</p>
                </div>
                <div class="column centered">
                    <p class="paginator">
                        <span v-if="data.page > 1"><a :href="`../ImportDashboardDetails/${data.import.id}?page=1`">&#x21E4; First</a></span>
                        <span v-else>&#x21E4; First</span>
                        <span v-if="prevPage >= 1"><a :href="`../ImportDashboardDetails/${data.import.id}?page=${prevPage}`">&#x2190; Prev</a></span>
                        <span v-else>&#x2190; Prev</span>
                        <span>Page {{data.page}} of {{pagesCount}}</span>
                        <span v-if="nextPage <= pagesCount"><a :href="`../ImportDashboardDetails/${data.import.id}?page=${nextPage}`">Next &#x2192;</a></span>
                        <span v-else>Next &#x2192;</span>
                        <span v-if="data.page < pagesCount"><a :href="`../ImportDashboardDetails/${data.import.id}?page=${pagesCount}`">Last &#x21E5;</a></span>
                        <span v-else>Last &#x21E5;</span>
                    </p>
                </div>
                <div class="column">&nbsp;</div>
            </div>
            <table aria-label="Table of import search results">
                <thead>
                    <tr>
                        <th style="width:12.5%">Company</th>
                        <th style="width:12.5%">Project</th>
                        <th style="width:12.5%">Substance</th>
                        <th style="width:12.5%">Mod Date (ET)</th>
                        <th style="width:12.5%">Type</th>
                        <th style="width:12.5%">PLX ID</th>
                        <th style="width:12.5%">Source Id</th>
                        <th style="width:7.5%">Classif. Status</th>
                        <th style="width:5%"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="row in data.rows">
                        <td>{{row.companyName}}</td>
                        <td>{{row.projectName}}</td>
                        <td>{{row.substanceName}}</td>
                        <td>{{row.sourceModificationDate === '' ? 'N/A' : row.sourceModificationDate}}</td>
                        <td>{{row.icrcType}}</td>
                        <td><a :href="`/References/Classifications/${row.referenceClassificationId}`" target="_blank" rel="noopener noreferrer">{{row.referenceClassificationId}}</a></td>
                        <td>{{row.sourceId}}</td>
                        <td>{{row.classificationStatus}}</td>
                        <td>
                            <a :href="`/Companies/${row.companyId}/Contract/${row.contractId}`" class="show-contract-icon" alt="Show contract">description</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </template>
        <template v-else>
            <p>This import contains no data.</p>
        </template>
    </section>
</div>

@section Scripts {
    <script type="text/javascript">

        var pageConfig = {
            appElement: "#import-dashboard-details",
            data: function () {
                return {
                    data: @Html.Raw(AntiXss.ToJson(Model)),
                };
            },
            computed: {
                firstRow() {
                    return ((this.data.page - 1) * this.data.pageSize) + 1;
                },
                lastRow() {
                    return Math.min(this.data.totalRowCount, ((this.data.page - 1) * this.data.pageSize) + this.data.pageSize);
                },
                pagesCount() {
                    let fullPages = parseInt(this.data.totalRowCount / this.data.pageSize);
                    let remainder = parseInt(this.data.totalRowCount % this.data.pageSize);
                    return fullPages + (remainder > 0 ? 1 : 0);
                },
                prevPage() {
                    return this.data.page - 1;
                },
                nextPage() {
                    return this.data.page + 1;
                }
            },
            methods: {
                onExport: function () {
                    let exportUrl = `/ImportDashboardDetailsExport/${this.data.import.id}`;
                    $("#btnExport").attr('data-loading', '');
                    var onFailure = function () {
                        plx.toast.show('Unable to export, please try again', 2, 'failed', null, 2500);
                    }
                    var onComplete = function () {
                        $("#btnExport").removeAttr('data-loading');
                    }
                    DownloadFile.fromUrl(exportUrl, null, null, onFailure, onComplete);
                }
            }
        }

    </script>
}
