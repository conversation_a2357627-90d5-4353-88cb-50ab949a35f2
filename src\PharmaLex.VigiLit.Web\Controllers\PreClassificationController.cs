using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policies.Assessor)]
public class PreClassificationController : BaseController
{
    private readonly IDashboardService _dashboardService;
    private readonly IPreClassificationService _preClassificationService;

    public PreClassificationController(IDashboardService dashboardService,
                                        IPreClassificationService preClassificationService,
                                        IUserSessionService userSessionService,
                                        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _dashboardService = dashboardService;
        _preClassificationService = preClassificationService;
    }

    [HttpGet()]
    public async Task<IActionResult> Index()
    {
        var model = await _preClassificationService.GetFirst(); // Pick_InitialPageLoad
        return View(model);
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> PickNext()
    {
        var model = await _preClassificationService.GetNext(); // Pick_Subsequent
        return Json(model);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Release()
    {
        await _preClassificationService.Release();
        return Ok();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> PreclassifyAndPickNext([FromBody] IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels)
    {
        if (!await _preClassificationService.UserHasAllLocks(preClassifyReferenceModels)) // Should be a locking service (for this implementation of locks)
        {
            return BadRequest();
        }

        await _preClassificationService.PreClassifyAsync(preClassifyReferenceModels);
        var model = await _preClassificationService.GetNext();  // Pick_Subsequent 
        return Json(model);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Repreclassify([FromBody] ReferenceClassificationModel referenceClassification)
    {
        await _preClassificationService.RePreClassifyAsync(referenceClassification);
        return Ok();
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> GetPreclassifiedByCurrentUser()
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return Json(new List<ReferenceClassificationWithReferenceModel>());
        }

        var preClassified = await _preClassificationService.GetPreClassifiedByCurrentUser(selectedImport.ImportId);
        return Json(preClassified);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> RetryAiSuggestions([FromBody] ReferenceClassificationModel referenceClassification)
    {
        await _preClassificationService.RetryAiSuggestions(referenceClassification);
        return Ok();
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> GetSuggestionAiForReferenceAndSubstance(string sourceId, string substance)
    {
        var aiSuggestion = await _preClassificationService.GetExistingAiSuggestion(sourceId, substance);

        return Json(aiSuggestion);
    }
}
