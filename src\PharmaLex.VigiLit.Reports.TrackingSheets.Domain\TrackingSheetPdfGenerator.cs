﻿using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using Telerik.Windows.Documents.Fixed.Model;
using Telerik.Windows.Documents.Fixed.Model.Editing;
using Telerik.Windows.Documents.Flow.FormatProviders.Html;
using Telerik.Windows.Documents.Flow.FormatProviders.Pdf;
using Telerik.Windows.Documents.Flow.Model;
using Telerik.Windows.Documents.Model;
using Telerik.Windows.Documents.Primitives;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

[ExcludeFromCodeCoverage(Justification = "Can't assert on pdf contents.")]
internal class TrackingSheetPdfGenerator : ITrackingSheetPdfGenerator
{
    public byte[] GetPdfBytes(TrackingSheetPdfModel model)
    {
        var html = BuildHtml(model);
        using var stream = ConvertHtmlToPdf(html);
        var pdfBytes = AddPageNumbers(stream);
        return pdfBytes;
    }

    private static string BuildHtml(TrackingSheetPdfModel model)
    {
        StringBuilder html = new();

        html.AppendLine("<!DOCTYPE html>");
        html.AppendLine("<html>");
        AddStyles(html);
        html.AppendLine("<body>");
        AddHeading(html);
        AddFilters(html, model);
        AddResults(html, model);
        html.AppendLine("</body>");
        html.AppendLine("</html>");

        return html.ToString();
    }

    private static void AddStyles(StringBuilder html)
    {
        html.AppendLine("<head>");
        html.AppendLine("<style>");
        html.AppendLine(@"
            h1 { font-size: 22px; margin:0 0 3px; }
            h2 { font-size: 19px; font-weight: normal; margin:0 0 3px; }
            p { font-size: 13px; margin:0 0 3px; }
            th { font-size: 13px; padding: 5px; font-weight: bold }
            td { font-size: 13px; padding: 5px }
            td.filter-key { width: 85px; font-weight: bold; padding-left: 0; }
            table.import th { border: 1px solid black; }
            table.import td { border: 1px solid black; }
            .center { text-align: center; }
        ");
        html.AppendLine("</style>");
        html.AppendLine("</head>");
    }

    private static void AddHeading(StringBuilder html)
    {
        var generatedDate = DateTime.UtcNow;

        html.AppendLine("<h1>VigiLit Import Tracking Sheet</h1>");
        html.AppendLine("<p>Tracking Sheet Hits from searches and classification.</p>");
        html.AppendLine($"<p>Report generated on {generatedDate.ToString("d MMM yyyy")} at {generatedDate.ToString("HH:mm")} UTC.</p>");
        html.AppendLine("<p>Only references updated in PubMed since the last import are included in the search interval hits.</p>");
    }

    private static void AddFilters(StringBuilder html, TrackingSheetPdfModel model)
    {
        html.AppendLine("<br>");
        html.AppendLine("<table class='filter'>");
        html.AppendLine($"<tr><td class='filter-key'>Company</td><td>{model.CompanyName}</td></tr>");
        html.AppendLine($"<tr><td class='filter-key'>Week/Year</td><td>{string.Format("{0}/{1}", model.Week, model.Year)}</td></tr>");
        html.AppendLine("<tr><td class='filter-key'>Source</td><td>PubMed</td></tr>");
        html.AppendLine("</table>");
        html.AppendLine("<br>");
        html.AppendLine("<br>");
    }

    private static void AddResults(StringBuilder html, TrackingSheetPdfModel model)
    {
        if (model.Imports.Count > 0)
        {
            AddImports(html, model);
        }
        else
        {
            AddNoResults(html);
        }
    }

    private static void AddImports(StringBuilder html, TrackingSheetPdfModel model)
    {
        foreach (var import in model.Imports)
        {
            AddImport(html, import);
        }
    }

    private static void AddImport(StringBuilder html, TrackingSheetPdfImportModel import)
    {
        html.AppendLine($"<h2>{import.Name}</h2>");

        html.AppendLine("<table class='import'>");

        AddImportTableHeader(html);

        html.AppendLine("<tbody>");

        foreach (var row in import.Rows)
        {
            AddImportTableRow(html, row);
        }

        html.AppendLine("</tbody>");
        html.AppendLine("</table>");
        html.AppendLine("<br>");
        html.AppendLine("<br>");
    }

    private static void AddImportTableHeader(StringBuilder html)
    {
        html.AppendLine("<thead>");
        html.AppendLine("<tr>");
        html.AppendLine("<th style='width:210px'>Substance</th>");
        html.AppendLine("<th class='center' style='width:135px'>Version</th>");
        html.AppendLine("<th class='center' style='width:260px'>Search Interval</th>");
        html.AppendLine("<th class='center' style='width:130px'>Hits</th>");
        html.AppendLine("</tr>");
        html.AppendLine("</thead>");
    }

    private static void AddImportTableRow(StringBuilder html, TrackingSheetPdfImportRowModel model)
    {
        html.AppendLine("<tr>");
        html.AppendLine($"<td>{model.SubstanceName}</td>");
        html.AppendLine($"<td class='center'>{model.ContractVersion.ToString()}</td>");
        html.AppendLine($"<td class='center'>{model.SearchInterval}</td>");
        html.AppendLine($"<td class='center'>{model.Hits.ToString("N0")}</td>");
        html.AppendLine("</tr>");
    }

    private static void AddNoResults(StringBuilder html)
    {
        html.AppendLine("<h2>No Results</h2>");
        html.AppendLine("<p>This report contains no data.</p>");
    }

    private static Stream ConvertHtmlToPdf(string html)
    {
        HtmlFormatProvider htmlProvider = new();
        RadFlowDocument flowDocument = htmlProvider.Import(html);

        foreach (var section in flowDocument.Sections)
        {
            section.PageMargins = new Padding(30, 50, 30, 50);
            section.PageSize = PaperTypeConverter.ToSize(PaperTypes.A4);
            section.Rotate(PageOrientation.Portrait);
        }

        PdfFormatProvider flowPdfProvider = new();

        Stream stream = new MemoryStream();
        flowPdfProvider.Export(flowDocument, stream);

        return stream;
    }

    private static byte[] AddPageNumbers(Stream stream)
    {
        Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.PdfFormatProvider fixedPdfProvider = new();

        RadFixedDocument fixedDocument = fixedPdfProvider.Import(stream);

        foreach (RadFixedPage page in fixedDocument.Pages)
        {
            FixedContentEditor editor = new(page);
            editor.Position.Translate(page.Size.Width - 85, page.Size.Height - 35);

            int pageNum = fixedDocument.Pages.IndexOf(page) + 1;
            editor.DrawText("Page " + pageNum + "/" + fixedDocument.Pages.Count);
        }

        return fixedPdfProvider.Export(fixedDocument);
    }
}
