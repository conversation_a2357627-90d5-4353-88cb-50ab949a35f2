using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.UserManagement.Claims;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Test.Framework.Orderers;
using Xunit;
using User = PharmaLex.VigiLit.Domain.UserManagement.User;

namespace PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Repositories;

[TestCaseOrderer(
    ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
    ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
public class UserRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, UserRepositoryTests>>, IDisposable
{
    private readonly VigiLitDbContext _context;

    public UserRepositoryTests(DatabaseFixture<VigiLitDbContext, UserRepositoryTests> fixture)
    {
        _context = fixture.Context;
    }

    [Fact, TestPriority(1)]
    public async Task AddUser_Creates_user_in_database_without_error()
    {
        // Arrange
        var user = GetUser();

        var userRepository = GetUserRepository();

        // Act
        userRepository.Add(user);
        await userRepository.SaveChangesAsync();

        var result = await userRepository.GetAllAsync(false);

        // Assert
        Assert.True(true);
    }


    [Fact, TestPriority(10)]
    public async Task GetsAllUsers()
    {
        // Arrange
        var userRepository = GetUserRepository();

        // Act
        var userCollection = await userRepository.GetAllAsync(false);

        // Assert
        Assert.Single(userCollection);
    }

    private IUserRepository GetUserRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ClaimProfile());
            mc.AddProfile(new UserMappingProfile());
            mc.AddProfile(new EmailSuppressionMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var userRepository = new UserRepository(_context, mapper, userContext);
        return userRepository;
    }

    private static User GetUser()
    {
        var activationExpiryDate = new DateTime(2020, 1, 20, 1, 0, 0, DateTimeKind.Utc);
        var createdDate = new DateTime(2020, 1, 21, 1, 0, 0, DateTimeKind.Utc);
        var lastUpdatedDate = new DateTime(2020, 1, 22, 1, 0, 0, DateTimeKind.Utc);

        var user = new User("given", "family", "<EMAIL>")
        {

            ActivationExpiryDate = activationExpiryDate,
            CreatedDate = createdDate,
            CreatedBy = "Creation User",
            LastUpdatedDate = lastUpdatedDate,
            LastUpdatedBy = "Updating User",
        };
        user.UpdateLastLoginDate();
        return user;
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);
    }
}