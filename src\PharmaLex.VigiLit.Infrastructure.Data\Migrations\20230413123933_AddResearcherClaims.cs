﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddResearcherClaims : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("SET IDENTITY_INSERT [dbo].[Claims] ON; " +

                                 "INSERT INTO [dbo].[Claims] ( [Id], [Name], [ClaimType], [DisplayName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 7, 'PharmaLexResearcher', 'operator', 'PharmaLex Client Researcher', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[Claims] WHERE [Id] = 7); " +

                                 "INSERT INTO [dbo].[Claims] ( [Id], [Name], [ClaimType], [DisplayName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 8, 'PharmaLexClientResearcher', 'operator', 'PharmaLex Client Researcher', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[Claims] WHERE [Id] = 8); " +

                                 "SET IDENTITY_INSERT [dbo].[Claims] OFF; ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM [Claims] WHERE Id IN (7,8);");
        }
    }
}
