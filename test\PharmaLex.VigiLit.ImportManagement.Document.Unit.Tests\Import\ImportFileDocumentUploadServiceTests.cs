﻿using Microsoft.Extensions.Options;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Documents.Import;
using Xunit;


namespace PharmaLex.VigiLit.ImportManagement.Document.Unit.Tests.Import;
public class ImportFileDocumentUploadServiceTests
{


    private const string BATCH_ID = "e7b53b0d-12f7-4c79-92db-3c13b2d6dca7";
    private const string FILE_NAME = "Zinc.pdf";
    private const string ACCOUNT_NAME_UPLOAD = "vgtsharedeunupload";
    private const string CONTAINER_NAME_UPLOAD = "import-file-document-upload";
    private const string BLOB_NAME_UPLOAD = $"{BATCH_ID}/{FILE_NAME}";

    private readonly ImportFileDocumentService _importFileDocumentUploadService;

    private readonly ImportFileUploadDescriptor _importFileDocumentUploadDescriptor;
    private readonly DocumentDescriptor _documentDescriptorUpload;

    private readonly Mock<IDocumentService> _mockDocumentService = new();


    public ImportFileDocumentUploadServiceTests()
    {
        _importFileDocumentUploadDescriptor = new ImportFileUploadDescriptor(Guid.Parse(BATCH_ID), FILE_NAME);
        _documentDescriptorUpload = new DocumentDescriptor(ACCOUNT_NAME_UPLOAD, CONTAINER_NAME_UPLOAD, BLOB_NAME_UPLOAD);
        _mockDocumentService = new Mock<IDocumentService>();

        var mockUploadOptions = new Mock<IOptions<AzureStorageImportFileDocumentUploadOptions>>();
        mockUploadOptions.Setup(o => o.Value).Returns(new AzureStorageImportFileDocumentUploadOptions
        {
            AccountName = ACCOUNT_NAME_UPLOAD,
            ContainerName = CONTAINER_NAME_UPLOAD
        });
        var mockFileOptions = new Mock<IOptions<AzureStorageImportFileDocumentOptions>>();
        mockFileOptions.Setup(o => o.Value).Returns(new AzureStorageImportFileDocumentOptions
        {
            AccountName = ACCOUNT_NAME_UPLOAD,
            ContainerName = CONTAINER_NAME_UPLOAD
        });

        _importFileDocumentUploadService = new ImportFileDocumentService(
            _mockDocumentService.Object,
            mockUploadOptions.Object,
            mockFileOptions.Object
            );
    }
    [Fact]
    public async Task Create_WhenCalled_CallsCreate()
    {
        // Arrange
        var stream = new MemoryStream();

        _mockDocumentService.Setup(s => s.Create(_documentDescriptorUpload, stream, default));

        // Act
        await _importFileDocumentUploadService.Create(_importFileDocumentUploadDescriptor, stream);

        // Assert
        _mockDocumentService.Verify(s => s.Create(_documentDescriptorUpload, stream, default), Times.Once);
    }

    [Fact]
    public async Task Delete_BlobExists_CallsDelete()
    {
        // Arrange
        _mockDocumentService.Setup(s => s.Delete(_documentDescriptorUpload, default))
                            .Returns(Task.CompletedTask);

        // Act
        await _importFileDocumentUploadService.Delete(_importFileDocumentUploadDescriptor);

        // Assert
        _mockDocumentService.Verify(s => s.Delete(_documentDescriptorUpload, default), Times.Once);
    }
    [Fact]
    public async Task GetDocumentProperties_WhenCalled_ReturnsDocumentProperties()
    {
        // Arrange
        var descriptor = new ImportFileUploadDescriptor(Guid.Parse(BATCH_ID), FILE_NAME);
        var expectedDocumentProperties = new DocumentProperties(FILE_NAME, 1024, "https://example.com/file.pdf");


        var expectedDocumentDescriptor = new DocumentDescriptor(ACCOUNT_NAME_UPLOAD, CONTAINER_NAME_UPLOAD, descriptor.GetFullPath());

        _mockDocumentService
            .Setup(s => s.GetDocumentProperties(expectedDocumentDescriptor, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDocumentProperties);

        // Act
        var result = await _importFileDocumentUploadService.GetDocumentProperties(descriptor);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedDocumentProperties.FileName, result.FileName);
        Assert.Equal(expectedDocumentProperties.FileSize, result.FileSize);

        _mockDocumentService.Verify(s => s.GetDocumentProperties(expectedDocumentDescriptor, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task OpenRead_BlobExists_ReturnsStream()
    {
        // Arrange
        var descriptor = new ImportFileUploadDescriptor(Guid.Parse(BATCH_ID), FILE_NAME);
        var stream = new MemoryStream();
        _mockDocumentService.Setup(s => s.OpenRead(_documentDescriptorUpload, It.IsAny<CancellationToken>()))
            .ReturnsAsync(stream);

        // Act
        var result = await _importFileDocumentUploadService.OpenRead(descriptor);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(stream, result);
    }

    [Fact]
    public async Task Exists_BlobExists_ReturnsTrue()
    {
        // Arrange
        var descriptor = new ImportFileUploadDescriptor(Guid.Parse(BATCH_ID), FILE_NAME);
        _mockDocumentService.Setup(s => s.Exists(_documentDescriptorUpload, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _importFileDocumentUploadService.Exists(descriptor);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task Exists_BlobDoesNotExist_ReturnsFalse()
    {
        // Arrange
        var descriptor = new ImportFileUploadDescriptor(Guid.Parse(BATCH_ID), FILE_NAME);
        _mockDocumentService.Setup(s => s.Exists(_documentDescriptorUpload, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _importFileDocumentUploadService.Exists(descriptor);

        // Assert
        Assert.False(result);
    }
}
