﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class MoveContractFieldsToContractVersionsTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contracts_SourceProviders_SourceProviderId",
                table: "Contracts");

            migrationBuilder.DropTable(
                name: "ContractStates");

            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive_ContractType_ContractWeekday",
                table: "Contracts");

            migrationBuilder.DropIndex(
                name: "IX_Contracts_SourceProviderId",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "ContractType",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "ContractWeekday",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "SearchPeriodDays",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "SourceProviderId",
                table: "Contracts");

            migrationBuilder.CreateTable(
                name: "ContractVersions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ContractId = table.Column<int>(type: "int", nullable: false),
                    ContractVersionStatus = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    ContractType = table.Column<int>(type: "int", nullable: false),
                    ContractWeekday = table.Column<int>(type: "int", nullable: false),
                    SearchPeriodDays = table.Column<int>(type: "int", nullable: false),
                    SearchString = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Version = table.Column<int>(type: "int", nullable: false),
                    ReasonForChange = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    TimeStamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractVersions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractVersions_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ContractVersions_ContractId_ContractVersionStatus_IsActive_ContractType_ContractWeekday",
                table: "ContractVersions",
                columns: new[] { "ContractId", "ContractVersionStatus", "IsActive", "ContractType", "ContractWeekday" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ContractVersions");

            migrationBuilder.AddColumn<int>(
                name: "ContractType",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ContractWeekday",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Contracts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "SearchPeriodDays",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceProviderId",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "ContractStates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ContractId = table.Column<int>(type: "int", nullable: false),
                    ContractStateStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ImportParameter = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReasonForChange = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    Version = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractStates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractStates_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive_ContractType_ContractWeekday",
                table: "Contracts",
                columns: new[] { "IsActive", "ContractType", "ContractWeekday" });

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_SourceProviderId",
                table: "Contracts",
                column: "SourceProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractStates_ContractId_ContractStateStatus",
                table: "ContractStates",
                columns: new[] { "ContractId", "ContractStateStatus" });

            migrationBuilder.AddForeignKey(
                name: "FK_Contracts_SourceProviders_SourceProviderId",
                table: "Contracts",
                column: "SourceProviderId",
                principalTable: "SourceProviders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
