﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddCompanyInterests : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CompanyInterests",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CompanyId = table.Column<int>(type: "int", nullable: false),
                    ReferenceId = table.Column<int>(type: "int", nullable: false),
                    ReferenceClassificationId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyInterests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyInterests_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyInterests_ReferenceClassifications_ReferenceClassificationId",
                        column: x => x.ReferenceClassificationId,
                        principalTable: "ReferenceClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyInterests_References_ReferenceId",
                        column: x => x.ReferenceId,
                        principalTable: "References",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CompanyInterests_CompanyId_ReferenceClassificationId",
                table: "CompanyInterests",
                columns: new[] { "CompanyId", "ReferenceClassificationId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompanyInterests_CompanyId_ReferenceId",
                table: "CompanyInterests",
                columns: new[] { "CompanyId", "ReferenceId" });

            migrationBuilder.CreateIndex(
                name: "IX_CompanyInterests_ReferenceClassificationId",
                table: "CompanyInterests",
                column: "ReferenceClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyInterests_ReferenceId",
                table: "CompanyInterests",
                column: "ReferenceId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompanyInterests");
        }
    }
}
