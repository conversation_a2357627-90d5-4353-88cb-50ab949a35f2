﻿using Moq;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain.Unit.Tests;

public class ServiceTests
{
    private readonly Service _apogephaClientReportService;

    private readonly Mock<IRepository> _apogephaClientReportRepository = new();
    private readonly Mock<IExportService> _exportService = new();

    public ServiceTests()
    {
        _apogephaClientReportService = new Service(
            _apogephaClientReportRepository.Object,
            _exportService.Object);
    }

    [Fact]
    public void GetPageModel_Returns_Model()
    {
        // Arrange
        // Act
        var model = _apogephaClientReportService.GetPageModel();

        // Assert
        Assert.NotNull(model);
        Assert.NotEmpty(model.Years);
        Assert.Equal(model.Years.Max(), model.SelectedYear);
        Assert.Equal(12, model.Cards.Count());
    }

    [Fact]
    public void GetCards_Returns_Cards()
    {
        // Arrange
        // Act
        var cards = _apogephaClientReportService.GetCards(2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Equal(12, cards.Count());

        var lastCard = cards.Last();

        Assert.NotNull(lastCard);
        Assert.Equal(2024, lastCard.Year);
        Assert.Equal(1, lastCard.Month);
        Assert.Equal("January", lastCard.MonthName);
        Assert.False(lastCard.IsFuture);
    }

    [Fact]
    public void GetCards_InvalidYear_Throws_ArgumentException()
    {
        // Act
        // Assert
        Assert.Throws<ArgumentException>(() => _apogephaClientReportService.GetCards(2023));
    }

    [Fact]
    public async Task Download_Returns_DownloadFile()
    {
        // Arrange
        var export = new ExportModel("ApogephaClientReport.csv", "text/csv", Array.Empty<byte>());

        _exportService
            .Setup(x => x.Export<ApogephaClientReportResult, ResultClassMap>(
                "ApogephaClientReport",
                It.IsAny<List<ApogephaClientReportResult>>(),
                It.IsAny<ExportConfiguration>()))
            .Returns(export);

        // Act
        var downloadFile = await _apogephaClientReportService.Download(2024, 1);

        // Assert
        Assert.NotNull(downloadFile);
        Assert.Equal("ApogephaClientReport_2024_1.csv", downloadFile.FileName);
        Assert.Equal("text/csv", downloadFile.ContentType);
        Assert.True(export.Data.SequenceEqual(downloadFile.Bytes));
    }

    [Fact]
    public async Task Download_InvalidYear_Throws_ArgumentException()
    {
        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _apogephaClientReportService.Download(2023, 1));
    }

    [Fact]
    public async Task Download_InvalidMonth_Throws_ArgumentException()
    {
        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _apogephaClientReportService.Download(2024, 13));
    }
}
