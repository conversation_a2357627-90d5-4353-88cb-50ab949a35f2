﻿using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class TrackingSheetStoredProcResult : EntityBase
{
    public int ImportId { get; set; }
    public int ImportType { get; set; }
    public DateTime ImportDate { get; set; }
    public string SubstanceName { get; set; } = string.Empty;
    public int ContractVersion { get; set; }
    public string SearchInterval { get; set; } = string.Empty;
    public int Hits { get; set; }
}
