﻿using Moq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests;

public class AdHocServiceTests
{
    private readonly IAdHocService _adHocService;
    private readonly Mock<IAdHocImportRepository> _adHocImportRepository = new();

    public AdHocServiceTests()
    {
        _adHocService = new AdHocService(
            _adHocImportRepository.Object);
    }

    [Fact]
    public async Task CreateAdHocImport()
    {
        // Arrange
        var model = new AdHocImportModel()
        {
            SourceSearchStartDate = "2023-12-01",
            SourceSearchEndDate = "2023-12-05",
            SelectedContractIds = new List<int>() { 1, 2 }
        };

        _adHocImportRepository.Setup(x => x.Add(It.IsAny<AdHocImport>()));
        _adHocImportRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _adHocService.Add(model);

        // Assert
        _adHocImportRepository.Verify(x => x.Add(It.Is<AdHocImport>(x =>
            x.SourceSearchStartDate == new DateTime(2023, 12, 1, 0, 0, 0, DateTimeKind.Utc)
            && x.SourceSearchEndDate == new DateTime(2023, 12, 5, 0, 0, 0, DateTimeKind.Utc)
            && x.AdHocImportContracts.Count == 2
        )), Times.Once());

        _adHocImportRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task CreateAdHocImport_throws_exception_if_toDate_is_prior_fromDate()
    {
        // Arrange
        var model = new AdHocImportModel()
        {
            SourceSearchStartDate = "2023-12-05",
            SourceSearchEndDate = "2023-12-01",
            SelectedContractIds = new List<int>() { 1, 2 }
        };
        var expectedMessage = "To date cannot be prior from date";
        _adHocImportRepository.Setup(x => x.Add(It.IsAny<AdHocImport>()));
        _adHocImportRepository.Setup(x => x.SaveChangesAsync());

        // Act
        var exception = await Record.ExceptionAsync(() => _adHocService.Add(model));

        // Assert
        Assert.NotNull(exception);
        Assert.IsType<ArgumentException>(exception);
        Assert.Equal(expectedMessage, exception.Message);
    }
    [Fact]
    public async Task CreateAdHocImport_throws_no_exception_if_fromDate_is_prior_toDate()
    {
        // Arrange
        var model = new AdHocImportModel()
        {
            SourceSearchStartDate = "2023-12-01",
            SourceSearchEndDate = "2023-12-05",
            SelectedContractIds = new List<int>() { 1, 2 }
        };

        _adHocImportRepository.Setup(x => x.Add(It.IsAny<AdHocImport>()));
        _adHocImportRepository.Setup(x => x.SaveChangesAsync());

        // Act
        var exception = await Record.ExceptionAsync(() => _adHocService.Add(model));

        // Assert
        Assert.Null(exception);
    }
}

