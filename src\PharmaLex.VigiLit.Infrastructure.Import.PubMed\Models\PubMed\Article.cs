﻿using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class Article
{
    public Journal Journal { get; set; }

    public ArticleTitle ArticleTitle { get; set; }

    public ELocationID ELocationID { get; set; }

    public Pagination Pagination { get; set; }

    public Abstract Abstract { get; set; }

    public AuthorList AuthorList { get; set; }

    [XmlElement("Language")]
    public List<string> Language { get; set; } = new List<string>();

    // TODO: Is this used at all?
    public DataBankList DataBankList { get; set; }

    public GrantList GrantList { get; set; }

    [XmlArrayItem("PublicationType", IsNullable = false)]
    public List<PublicationType> PublicationTypeList { get; set; } = new List<PublicationType>();

    public VernacularTitle VernacularTitle { get; set; }

    [XmlElement("ArticleDate")]
    public List<ArticleDate> ArticleDate { get; set; } = new List<ArticleDate>();

    [XmlAttribute()]
    public ArticlePubModel PubModel { get; set; }
}
