@using Microsoft.AspNetCore.Mvc.TagHelpers
@using PharmaLex.Core.Web.Helpers

@model PharmaLex.VigiLit.Reports.Merz.Domain.Models.PageModel

@{
    ViewData["Title"] = "Merz Client Report";
}

<div id="merz-client-report" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2><a href="../Reports">Reports</a> > Merz Client Report</h2>
        <div class="controls">
        </div>
    </div>

    <section>
        <h2>Months</h2>
        <p>Choose a month below to view the report.</p>
        <br />

        <div class="filters">
            <div class="custom-select">
                <select id="year" name="year" class="white-background" v-model='selectedYear' v-on:change="loadCards" aria-label="Year">
                    <option v-for='year in years' :value='year'>{{ year }}</option>
                </select>
            </div>
        </div>

        <div>
            <h2>{{selectedYear}}</h2>
            <div class="cards-loading" v-if="cardsLoading">
                <p>Loading...</p>
            </div>
            <div class="cards" v-else>
                <div v-for="card in cards" :class="['card', {'future':card.isFuture}]">
                    <div class="card-info">
                        <h2>{{card.monthName}}</h2>
                        <p>&nbsp;</p>
                    </div>
                    <div class="card-buttons">
                        <a v-if="!card.isFuture" href="#" @@click.stop.prevent="download(card)">Download</a>
                        &nbsp;
                    </div>
                </div>
            </div>
        </div>

    </section>

</div>

@section Scripts {
    <script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageModel = @Html.Raw(AntiXss.ToJson(Model));

        var pageConfig = {
            appElement: "#merz-client-report",
            data: function () {
                return {
                    years: pageModel.years,
                    cards: pageModel.cards,
                    selectedYear: pageModel.selectedYear,
                    cardsLoading: false
                };
            },
            methods: {
                loadCards: function () {
                    this.cardsLoading = true;
                    this.cards = [];

                    let url = '/Reports/MerzClientReport/GetCards';

                    let params = { year: this.selectedYear };

                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(params),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.cards = data;
                        this.cardsLoading = false;
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                download: function (card) {
                    let url = `/Reports/MerzClientReport/Download/${card.year}/${card.month}`;

                    var onFailure = function () {
                        plx.toast.show('Unable to download, please try again', 2, 'failed', null, 2500);
                    }
                    var onComplete = function () {
                    }

                    DownloadFile.fromUrl(url, null, null, onFailure, onComplete);
                }
            }
        };

    </script>
}
