﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class SubstanceSynonymModel
{
    public int Id { get; set; }
    [Required]
    [MaxLength(250)]
    [RegularExpression("^\\S[\\S\\s]*$", ErrorMessage = "Invalid Name")]
    [Remote("ValidateSynonymName", "SubstanceSynonyms", AdditionalFields = "Id", ErrorMessage = "Substance Synonym already exists")]
    public string Name { get; set; }

    public int SubstanceId { get; set; }
}