﻿@model PharmaLex.VigiLit.Ui.ViewModels.UserManagement.CompanyUserListModel
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Company Users";
    Layout = "CompanyLayout";
}

@section Buttons {
    <a href="@($"/Companies/{Model.Id}/User/Create")" class="button btn-default">Add User</a>
}
<div id="companyusers" v-cloak>
    <section>
        <filtered-table :items="users"
                        :columns="columns"
                        :filters="filters"
                        :link="link">
        </filtered-table>
    </section>

    @Html.AntiForgeryToken()

</div>

@section SubPageScripts {

    <script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        let editUrl = "@($"/Companies/{@Model.Id}/User/Edit/")";
        let userModel = @Html.Raw(AntiXss.<PERSON><PERSON>son(Model.CompanyUsers));
        let activeTypes = [
            {
                id: true,
                name: 'Active'
            },
            {
                id: false,
                name: 'Inactive'
            }
        ];

        var pageConfig = {
            appElement: "#companyusers",
            data: function () {
                return {
                    link: editUrl,
                    users: userModel,

                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'familyName',
                                sortKey: 'familyName',
                                header: 'Last Name',
                                edit: {},
                                type: 'text',
                                style: 'width: 17%;'
                            },
                            {
                                dataKey: 'givenName',
                                sortKey: 'givenName',
                                header: 'First Name',
                                edit: {},
                                type: 'text',
                                style: 'width: 17%;'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                header: 'Email',
                                edit: {},
                                type: 'text',
                                style: 'width: 16%;'
                            },
                            {
                                dataKey: 'active',
                                sortKey: 'active',
                                header: 'Active',
                                edit: {},
                                type: 'bool',
                                style: 'width: 16%;'
                            },
                            {
                                dataKey: 'displayActivationExpiryDate',
                                sortKey: 'displayActivationExpiryDate',
                                header: 'Activation Expiry Date',
                                type: 'text',
                                style: 'width: 16%;'
                            },
                            {
                                dataKey: 'displayLastLoginDate',
                                sortKey: 'displayLastLoginDate',
                                header: 'Last Logged In',
                                type: 'text',
                                style: 'width: 16%;'
                            },
                            {
                                dataKey: 'emailSuppressionName',
                                sortKey: 'emailSuppressionName',
                                header: 'Suppressed',
                                type: 'image',
                                style: 'width: 16%;'
                            }

                        ]
                    },
                    filters: [
                        {
                            key: 'familyName',
                            options: [],
                            type: 'search',
                            header: 'Search Last Name',
                            fn: v => p => p.familyName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'givenName',
                            options: [],
                            type: 'search',
                            header: 'Search First Name',
                            fn: v => p => p.givenName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Search Email',
                            fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'active',
                            options: activeTypes,
                            filterCollection: 'active',
                            type: 'select',
                            fn: v => p => p.active == v,
                            convert: v => v
                        },
                        {
                            key: 'emailSuppressionName',
                            options: [],
                            type: 'search',
                            header: 'Search Suppression Status',
                            fn: v => p => (p.emailSuppressionName ?? '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                    ]
                };
            }
        };

    </script>
}