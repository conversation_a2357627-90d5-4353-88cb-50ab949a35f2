﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class CompanyHelper
{
    private readonly ICompanyRepository _companyRepository;

    public CompanyHelper(ICompanyRepository companyRepository)
    {
        _companyRepository = companyRepository;
    }

    public async Task<Company> AddCompany(string name, string person, string email, bool active)
    {
        var company = new Company(name, person, email, active);
        _companyRepository.Add(company);
        await _companyRepository.SaveChangesAsync();
        return company;
    }
}
