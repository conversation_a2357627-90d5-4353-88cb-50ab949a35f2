﻿CREATE OR ALTER PROC dbo.spCaseFileReport
/*---------------------------------------------------------------------------------------------------------------------
--
-- Summary:		Returns client case file report data
--
-- Parameters:	@Company   = company name
--				@StartDate = report start date in any date format, e.g '20230701', '1 Jul 2023', etc.
--				@EndDate   = report end date
--
-- Notes:		Used by company 'Accovion GmbH'
--
-- Usage:		exec spCaseFileReport 'Accovion GmbH', '20230701', '20230731' 
--
---------------------------------------------------------------------------------------------------------------------*/

    @Company varchar(100),
    @StartDate datetime,
    @EndDate datetime   
AS   	

    SET @EndDate = ISNULL(@EndDate,DATEADD(DAY, -1, DATEADD(MONTH, 1, @StartDate)));
    	
    DECLARE @CaseStatusPublished int = 2;

    ;WITH daySequence(n) AS 
    (
    	SELECT 0 UNION ALL SELECT n + 1 FROM daySequence
    	WHERE n < DATEDIFF(DAY, @StartDate, @EndDate)
    ),
    d(d) AS 
    (
    	SELECT DATEADD(DAY, n, @StartDate) FROM daySequence
    ),
    monthDay AS
    (
    	SELECT Date = CONVERT(date, d) FROM d
    )
    SELECT 
    	FORMAT(monthDay.Date, 'dd.MM.yyyy') as MonthlyDate,
    	ISNULL(
    			CASE emailData.Data 
    				WHEN NULL THEN ''
    				ELSE emailData.Data 
    			END,
    			''
    		) as EmailData,
			GetUtcDate() as CreatedDate,
			'' as CreatedBy,
			GetUtcDate() as LastUpdatedDate,
			'' as LastUpdatedBy
    FROM monthDay
    LEFT JOIN
    (    
    	SELECT	CONVERT(varchar,cas.CreatedDate,102) as Date, 
    			STRING_AGG(CAST(rc.id as varchar) + ' (' + subs.Name + ')',', ') as Data
    	FROM [Cases] cas 
    	INNER JOIN [CaseCompanies] cc on cas.id = cc.CaseId
    	INNER JOIN [Companies] c ON cc.CompanyId = c.Id
    	INNER JOIN [ReferenceClassifications] rc ON rc.id = cas.ReferenceClassificationId
    	INNER JOIN [Substances] subs ON subs.id = rc.SubstanceId
    	WHERE 
    		c.Name = @Company
    		AND cas.Status=@CaseStatusPublished						
    	GROUP BY CONVERT(varchar,cas.CreatedDate,102)
    ) emailData
    ON monthDay.Date = emailData.Date
    ORDER BY monthDay.Date
    OPTION (MAXRECURSION 0)  
GO
