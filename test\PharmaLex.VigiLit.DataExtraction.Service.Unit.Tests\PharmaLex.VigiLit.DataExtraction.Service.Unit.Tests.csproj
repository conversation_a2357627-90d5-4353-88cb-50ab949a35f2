﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<NoWarn>$(NoWarn);CA1859</NoWarn>
  </PropertyGroup>


	<ItemGroup>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="ResponseStreams\ResponseStreams.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ResponseStreams.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="ResponseStreams\ResponseStreams.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>ResponseStreams.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>


</Project>
