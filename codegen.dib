#!markdown

## Introduction 

PlxCodeGenerator is a dotnet core tool used to facilitate code development. It could be used to create entities and related files with fully customizable templates and solution layout.

#!markdown

### Configuration.

When the tool runs the init command, .codegenconfig is created with default project structure and entity description. Before updating the project we should first update the configuration. 
Running the command will open notepad with loaded configuration. Edit the configuration according to the project and save the file. 

#!pwsh

plx config edit

#!markdown

### Templates

When to tool is initialized, it copies all default templates in codegen folder. 

1. Template list

#!pwsh

plx template list

#!markdown

2. Edit template

#!pwsh

$templateName = Read-Host -Prompt 'Enter Template Name';
plx template edit -n $templateName

#!markdown

3. Add Template

#!pwsh

$templateName = Read-Host -Prompt 'Enter Template Name';
plx template add -n $templateName

#!markdown

3. Remove Template

#!pwsh

$templateName = Read-Host -Prompt 'Enter Template Name';
plx template remove -n $templateName

#!markdown

4. Template Model

plx tool uses mustache template engine for generating code. More information can be found at https://github.com/jehugaleahsa/mustache-sharp.
To list all available properties that could be used inside a template use the following command.

#!pwsh

plx template model

#!markdown

5. Reset Templates 

Removes all templates from the template folder and add copy the default ones.

#!pwsh

plx template reset 

#!markdown

### Database

1. Add database migration

#!pwsh

$migrationName = Read-Host -Prompt 'Enter Migration Name';
plx database add-migration -n $migrationName

#!markdown

2. Update database

#!pwsh

plx database update

#!markdown

### Run PharmaLex  CodeGen

Run template processor against the current project. 

#!pwsh

plx update
