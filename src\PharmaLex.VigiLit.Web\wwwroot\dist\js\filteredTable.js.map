{"version": 3, "file": "js/filteredTable.js", "mappings": ";;;AAACA,OAAOC,UAAU,kBAAmB,CACjCC,SAAU,4BACVC,MAAO,CACHC,IAAK,CACDC,UAAU,EACVC,KAAMC,QAGdC,SAAU,CACN,UAAAC,GACI,MAAO,yBACDC,KAAKN,IAAIO,MAAM,EAAGD,KAAKN,IAAIQ,QACxBC,KAAIC,GAAK,OAAOA,WAAUC,KAAK,WAE5C,KAIRf,OAAOC,UAAU,YAAa,CAC1BC,SAAU,sBACVC,MAAO,CACHC,IAAKY,OACLC,QAASC,WAIjBlB,OAAOC,UAAU,wBAAyB,CACtCC,SAAU,kCACV,IAAAiB,GACI,MAAO,CACHC,QAASV,KAAKS,KAAKC,QAE3B,EACAjB,MAAO,CACHgB,KAAME,QAEVC,QAAS,CACL,QAAAC,CAASC,GACDd,KAAKU,SACLI,EAAEC,OAAOC,gBACjB,EACA,OAAAC,CAAQH,GACJd,KAAKS,KAAKC,QAAUV,KAAKU,QAAUV,KAAKS,KAAKd,WAAamB,EAAEC,OAAOG,eACvE,KAIR5B,OAAOC,UAAU,uBAAwB,CACrCC,SAAU,iCACV,IAAAiB,GACI,MAAO,CACHC,QAASV,KAAKS,KAAKC,QAE3B,EACAjB,MAAO,CACHgB,KAAME,OACNQ,UAAW,CACPvB,KAAMU,OACNc,QAAS,SAGjBR,QAAS,CACL,QAAAC,CAASC,GACDd,KAAKU,SACLI,EAAEC,OAAOC,gBACjB,EACA,OAAAC,CAAQH,GACJd,KAAKS,KAAKC,QAAUV,KAAKU,QAAUV,KAAKS,KAAKd,WAAamB,EAAEC,OAAOG,eACvE,KAIR5B,OAAOC,UAAU,uBAAwB,CACrCC,SAAU,iCACV,IAAAiB,GACI,MAAO,CACHC,QAASV,KAAKS,KAAKC,QAE3B,EACAjB,MAAO,CACHgB,KAAME,OACNQ,UAAW,CACPvB,KAAMU,OACNc,QAAS,QAGjBR,QAAS,CACL,QAAAC,CAASC,GACDd,KAAKU,SACLI,EAAEC,OAAOC,gBAEjB,EACA,OAAAC,CAAQH,GACJd,KAAKS,KAAKC,QAAUV,KAAKU,QAAUV,KAAKS,KAAKd,WAAamB,EAAEC,OAAOG,eACvE,KAIR5B,OAAOC,UAAU,uBAAwB,CACrCC,SAAU,iCACV,IAAAiB,GACI,MAAO,CACHC,QAASV,KAAKS,KAAKC,QAE3B,EACAjB,MAAO,CACHgB,KAAME,OACNQ,UAAW,CACPvB,KAAMU,OACNc,QAAS,SAGjBR,QAAS,CACL,QAAAC,CAASC,GACDd,KAAKU,SACLI,EAAEC,OAAOC,gBAEjB,EACA,OAAAC,CAAQH,GACJd,KAAKS,KAAKC,QAAUV,KAAKU,QAAUV,KAAKS,KAAKd,WAAamB,EAAEC,OAAOG,eACvE,KAIR5B,OAAOC,UAAU,YAAa,CAAEC,SAAU,2FAA4FC,MAAO,CAAC,MAAO,UAErJH,OAAOC,UAAU,iBAAkB,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAE3EH,OAAOC,UAAU,YAAa,CAAEC,SAAU,yBAA0BC,MAAO,CAAC,SAE5EH,OAAOC,UAAU,cAAe,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAExEH,OAAOC,UAAU,aAAc,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAEvEH,OAAOC,UAAU,WAAY,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAErEH,OAAOC,UAAU,YAAa,CAAEC,SAAU,4CAA6CC,MAAO,CAAC,MAAO,aAEtGH,OAAOC,UAAU,gBAAiB,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAE1EH,OAAOC,UAAU,oBAAqB,CAAEC,SAAU,mBAAoBC,MAAO,CAAC,SAE9EH,OAAOC,UAAU,YAAa,CAC1BC,SAAU,sBACVC,MAAO,CACHC,IAAKc,WAIblB,OAAOC,UAAU,gBAAiB,CAC9BC,SAAU,0BACViB,KAAM,WACF,MAAO,CACHY,QAAQ,EACRC,UAAWtB,KAAKuB,OAAOC,QACvBC,UAAWzB,KAAKuB,OAAOG,QACvBC,QAAS,IACF3B,KAAK4B,WAGpB,EACAC,MAAO,CAAC,eAAgB,UACxBpC,MAAO,CACHqC,MAAO,CACHnC,UAAU,EACVyB,QAAS,IAEbG,OAAQ,CACJ3B,KAAMe,OACNhB,UAAU,GAEdoC,SAAU,CACNnC,KAAMC,MACNF,UAAU,GAEdiC,UAAWjB,QAEfb,SAAU,CACNkC,iBAAkB,WACd,MAAO,IAAI,IAAIC,IAAIjC,KAAK+B,SAAS5B,KAAI+B,GAAKA,EAAElC,KAAKuB,OAAOY,oBAAmBC,SACtEjC,KAAIkC,IACD,GAAIrC,KAAKsB,YAActB,KAAKyB,UACxB,MAAO,CACHa,IAAW,OAAND,EAAa,IAAMA,EACxBP,MAAa,OAANO,EAAarC,KAAK2B,QAAQY,QAAUF,GAInD,MAAMG,EAAIxC,KAAKuB,OAAOkB,QAAQC,MAAKtC,GAAKA,EAAEJ,KAAKyB,aAAeY,IAC9D,MAAO,CACHC,IAAW,OAAND,EAAa,IAAMA,EACxBP,MAAOU,EAAIA,EAAExC,KAAKsB,WAAatB,KAAK2B,QAAQY,QAC/C,IAEJI,MAAK,CAACC,EAAGC,IAAMD,EAAEd,MAAMgB,cAAcD,EAAEf,MAAO,KAAM,CAAEiB,YAAa,UAC5E,GAEJnC,QAAS,CACL,IAAAoC,GACIhD,KAAKqB,QAAUrB,KAAKqB,OACpBrB,KAAKiD,WAAU,WACXjD,KAAKkD,MAAMC,OAAOC,OACtB,GACJ,EACA,MAAAC,CAAO3D,GACHM,KAAKqB,QAAS,EACdrB,KAAKsD,MAAM,eAAgB5D,GAC3BM,KAAKsD,MAAM,SAAU,CAAEhB,IAAKtC,KAAKuB,OAAOe,KAC5C,EACA,kBAAAiB,CAAmBC,GACVxD,KAAKyD,IAAIC,SAASF,EAAIzC,UACvBf,KAAKqB,QAAS,EAEtB,EACA,KAAAsC,GACI3D,KAAKqB,QAAS,CAClB,GAEJuC,QAAS,WACLC,SAASC,iBAAiB,QAAS9D,KAAKuD,oBACxCQ,OAAOD,iBAAiB,SAAU9D,KAAK2D,MAC3C,EACAK,UAAW,WACPH,SAASI,oBAAoB,QAASjE,KAAKuD,oBAC3CQ,OAAOE,oBAAoB,SAAUjE,KAAK2D,MAC9C,IAGJrE,OAAOC,UAAU,mBAAoB,CACjCC,SAAU,6BACV,IAAAiB,GACI,MAAO,CACHkB,QAAS,IACF3B,KAAK4B,WAGpB,EACAC,MAAO,CAAC,mBAAoB,qBAC5BpC,MAAO,CACHyE,QAAS1D,QACT2D,UAAWC,OACXC,UAAWD,OACXE,SAAUF,OACVG,SAAU,CACN3E,KAAMU,OACNc,QAAS,UAEboD,WAAY,CACR5E,KAAMwE,OACNzE,UAAU,GAEdiC,UAAWjB,QAEfb,SAAU,CACN2E,WAAY,WACR,OAAOzE,KAAKsE,SAAWtE,KAAKqE,SAChC,EACAK,SAAU,WACN,OAAOC,KAAKC,IAAIC,SAAS7E,KAAKyE,YAAcI,SAAS7E,KAAKsE,UAAWtE,KAAKmE,UAC9E,EACAW,UAAW,WACP,IAAIC,EAAQ/E,KAAKmE,UAAYnE,KAAKsE,SAAW,EAAI,EACjD,OAAOK,KAAKK,MAAMhF,KAAKmE,UAAYnE,KAAKsE,UAAYS,CACxD,GAEJnE,QAAS,CACL,WAAAqE,CAAYC,KAAWC,GACnB,MAAMC,EAAQF,EAAOG,MAAM,MAC3B,IAAIC,EAASF,EAAM,GAOnB,OALAD,EAAKI,SAAQ,CAACC,EAAGnD,KACbiD,GAAUE,EACNJ,EAAMlF,OAASmC,EAAI,IAAGiD,GAAUF,EAAM/C,EAAI,GAAE,IAG7CiD,CACX,EACA,cAAAG,CAAeC,GACX1F,KAAKsD,MAAM,oBAAqBoC,EACpC,KAIRpG,OAAOC,UAAU,gBAAiB,CAC9BC,SAAU,0BACViB,KAAI,KACO,CACHY,QAAQ,IAGhBQ,MAAO,CAAC,eAAgB,UACxBpC,MAAO,CACHqC,MAAO,CACHnC,UAAU,EACVyB,QAAS,IAEbG,OAAQ,CACJ3B,KAAMe,OACNhB,UAAU,GAEdoC,SAAU,CACNnC,KAAMC,MACNF,UAAU,IAGlBiB,QAAS,CACL,IAAAoC,GACIhD,KAAKqB,QAAUrB,KAAKqB,OAChBrB,KAAKqB,QACLrB,KAAKiD,WAAU,WACXjD,KAAKkD,MAAMyC,OAAOvC,OACtB,GACR,EACA,MAAAC,CAAO3D,GACHM,KAAKsD,MAAM,eAAgB5D,GAC3BM,KAAKsD,MAAM,SAAU,CAAEhB,IAAKtC,KAAKuB,OAAOe,KAC5C,EACA,kBAAAiB,CAAmBC,GACVxD,KAAKyD,IAAIC,SAASF,EAAIzC,UACvBf,KAAKqB,QAAS,EAEtB,EACA,KAAAsC,GACI3D,KAAKqB,QAAS,CAClB,EACA,KAAAuE,GACI5F,KAAKqD,OAAO,IACZrD,KAAK2D,OACT,GAEJC,QAAS,WACLC,SAASC,iBAAiB,QAAS9D,KAAKuD,oBACxCQ,OAAOD,iBAAiB,SAAU9D,KAAK2D,MAC3C,EACAK,UAAW,WACPH,SAASI,oBAAoB,QAASjE,KAAKuD,oBAC3CQ,OAAOE,oBAAoB,SAAUjE,KAAK2D,MAC9C,IAGJrE,OAAOC,UAAU,iBAAkB,CAC/BC,SAAU,2BACViB,KAAM,WACF,MAAO,CACHA,KAAM,CACFoF,MAAO7F,KAAK8F,OAAO5F,QAAU,EAC7B4F,MAAO9F,KAAK8F,OAAO5F,OAASF,KAAK8F,MAAM3F,KAAIC,IAChC,IAAKA,MACX,GACL2F,OAAQ/F,KAAK8F,OAEjBJ,KAAM1F,KAAKsE,SACXD,UAAW,EACX2B,YAAahG,KAAKiG,QAAQC,QAAO,CAACC,EAAKzG,KACnCyG,EAAIzG,EAAI4C,KAAO,GACR6D,IACR,CAAC,GACJC,cAAe,GACfC,SAAUrG,KAAKsG,QAAQD,UAAY,SACnCE,UAAWvG,KAAKsG,QAAQ/E,OAAO2E,QAAO,CAACC,EAAKK,KACpCA,EAAEC,UACFN,EAAIK,EAAEC,SAAWD,EAAEE,eAAiB,GACjCP,IACR,CAAC,GACJQ,gBAAiB3G,KAAKiG,QAAQ9F,KAAIyG,IAC9B,IAAIC,EAAK,IAAKD,GAEVE,EAAWD,EAAGpE,QAAQ,IAAIsE,eAAe,MAAQ,KAAO,KACxDC,EAAaH,EAAGpE,QAAQ,IAAIsE,eAAe,QAAU,OAAS,OAQlE,OANAF,EAAGnF,QAAUmF,EAAGnF,QAAUmF,EAAGnF,QAAUoF,EACvCD,EAAGrF,QAAUqF,EAAGrF,QAAUqF,EAAGrF,QAAUwF,EAEvCH,EAAGI,QAAUJ,EAAGI,SAAW,CAACzE,GAAKA,GACjCqE,EAAGK,GAAKL,EAAGK,IAAM,CAAC1E,GAAKN,IAAwB,OAAlBA,EAAE2E,EAAGnF,SAAoB1B,KAAK2B,QAAQY,QAAUL,EAAE2E,EAAGnF,UAAUyF,cAAcC,SAAS5E,EAAE2E,gBAE9GN,CAAE,IAEbQ,WAAY,GAAGtD,OAAOQ,SAAS+C,QAAQtH,KAAKuH,OAAOC,IAAM,MACzDC,SAAU,KACVC,SAAS,EACT/F,QAAS,CAEDgG,iBAAkB,4BAClBC,aAAc,aACdC,eAAgB,eAChBC,eAAgB,eAChBC,aAAc,gBACdC,QAAS,WACTC,KAAM,OACNC,OAAQ,SACRC,KAAM,OACNC,OAAQ,SACR7F,QAAS,cACT8F,MAAO,CACHC,cAAe,iCACfC,sBAAuB,qBACvBjE,SAAU,YACVkE,MAAO,QACPC,SAAU,WACVC,KAAM,OACNC,KAAM,WAGX3I,KAAK4B,WAGpB,EACAC,MAAO,CAAC,SAAU,uBAClBpC,MAAO,CACHqG,MAAOjG,MACPoG,QAAS,CACLrG,KAAMC,MACNuB,QAAS,IAAM,IAEnBkD,SAAU,CACN1E,KAAMwE,OACNhD,QAAS,IAEbwH,cAAe,CACXhJ,KAAMU,OACNc,QAAS,UAEbkF,QAAS3F,OACTkI,QAASvI,OACTwI,KAAMxI,OACNyI,SAAU,CACNnJ,KAAMU,OACNc,QAAS2C,OAAOQ,SAAS+C,KAAO,UAEpC0B,SAAU1I,OACV2I,OAAQ3I,OACRsB,UAAW,CACPhC,KAAMe,OACNS,QAAO,KACI,CAAC,IAGhB8H,WAAY,CACRtJ,KAAMY,QACNY,SAAS,IAGjBtB,SAAU,CACNqJ,eAAgB,WACZ,OAAOxI,OAAOwE,KAAKnF,KAAKgG,aAAaE,QAAO,CAACC,EAAKiD,KAC9C,IAAIC,EAAOrJ,KAAKgG,YAAYoD,GAE5B,GAAIC,IAAiB,IAATA,GAA2B,IAATA,EAAY,CACtC,IAAIzC,EAAI5G,KAAKsJ,eAAeF,GAC5BjD,EAAIiD,GAAQxC,EAAEM,GAAGN,EAAEK,QAAQoC,GAC/B,CACA,OAAOlD,CAAG,GACX,CAAC,EACR,EACAoD,aAAc,WACV,OAAO5I,OAAOwE,KAAKnF,KAAKuG,WAAWL,QAAO,CAACC,EAAKqD,KAC5C,IAAIC,EAAMzJ,KAAKsG,QAAQ/E,OAAOmB,MAAKtC,GAAKA,EAAEqG,UAAY+C,IAClDE,EAAmB1J,KAAKS,KAAKqF,MAAM,UAAY9F,KAAKS,KAAKqF,MAAM,GAAG2D,EAAIhD,SAAW,KACjFkD,EAAeF,EAAMA,EAAIG,cAAgBF,EAAmB,KAchE,MARqB,WAAjBC,IACAA,EAAe,UAGnBxD,EAAI0D,KAAK,CACLvH,IAAKkH,EACLM,SAAU9J,KAAK+J,YAAYP,EAAMxJ,KAAKuG,UAAUiD,GAAOG,KAEpDxD,CAAG,GACX,GACP,EACAhC,UAAW,WACP,OAAOnE,KAAKS,KAAKsF,MAAQ/F,KAAKS,KAAKoF,MAAQ7F,KAAKgK,YAAY9J,MAChE,EACA8J,YAAa,WACT,OAAIhK,KAAKS,KAAKsF,MACH/F,KAAKS,KAAKqF,OAGrB9F,KAAKqE,UAAY,EACV1D,OAAOwE,KAAKnF,KAAKmJ,gBAAgBjD,QAAO,CAAC+D,EAAU3H,IAC/C2H,EAASC,OAAOlK,KAAKmJ,eAAe7G,KAC5CtC,KAAKS,KAAKqF,OACjB,EACAqE,UAAW,WACP,OAAInK,KAAKS,KAAKsF,MACH/F,KAAKgK,YAAY/J,QAGrBD,KAAKuJ,aAAarD,QAAO,CAACC,EAAKzG,IAC3ByG,EAAIxD,KAAKjD,EAAIoK,WACrB9J,KAAKgK,YAAY/J,QACxB,EACAmK,UAAW,WACP,OAAIpK,KAAKS,KAAKsF,MACH/F,KAAKmK,UAAUlK,QAGnBD,KAAKmK,UAAUlK,MAAMD,KAAK0F,KAAO1F,KAAKqE,UAAWrE,KAAK0F,MAAQ1F,KAAKqE,UAAY,GAC1F,EACA,YAAAgG,GACI,OAAOrK,KAAKyH,WAAazH,KAAKyH,SAASD,GAAK,CAACxH,KAAKyH,YAAazH,KAAKoK,WAAapK,KAAKoK,SAC1F,EACA,UAAAE,GACI,QAAS3J,OAAOwE,KAAKnF,KAAKmJ,gBAAgBjJ,MAC9C,EACA,WAAAqK,GACI,OAAOvK,KAAKqK,aAAanK,QAAUF,KAAKqK,aAAaH,QAAO9J,GAAKJ,KAAKoG,cAAcgB,SAAShH,EAAEoH,MAAKtH,QAAUF,KAAKqK,aAAanK,MACpI,GAEJU,QAAS,CACL,WAAAqE,CAAYC,KAAWC,GACnB,MAAMC,EAAQF,EAAOG,MAAM,MAC3B,IAAIC,EAASF,EAAM,GAOnB,OALAD,EAAKI,SAAQ,CAACC,EAAGnD,KACbiD,GAAUE,EACNJ,EAAMlF,OAASmC,EAAI,IAAGiD,GAAUF,EAAM/C,EAAI,GAAE,IAG7CiD,CACX,EACA,SAAAkF,GACI,IACI,IAAIC,EAAcC,aAAaC,QAAQ3K,KAAKqH,YACxCuD,EAAQH,EAAcI,KAAKC,MAAML,GAAe,CAAEM,KAAM,IAAIC,OAgCpE,SAA0BJ,GACtB,IAAIK,EAAM,IAAID,KACVE,EAAY,IAAIF,KAAKJ,EAAMG,MAC/B,OAAOE,EAAIE,WAAaD,EAAUC,UAC9BF,EAAIG,aAAeF,EAAUE,YAC7BH,EAAII,YAAcH,EAAUG,SACpC,CApCQC,CAAiBV,GAyBdF,aAAaa,WAAWvL,KAAKqH,aAvB5BuD,EAAM5E,cACNhG,KAAKgG,YAAc4E,EAAM5E,YACzBrF,OAAOwE,KAAKnF,KAAKgG,aAAaT,SAAQjD,IAC9BtC,KAAKgG,YAAY1D,IACjBtC,KAAKkK,OAAO,CAAE5H,OAAM,KAI5BsI,EAAMrE,WACNqE,EAAMrE,UAAUhB,SAAQiG,IACpB,IAAKlJ,EAAKmJ,GAASD,EACnBxL,KAAKuG,UAAUjE,GAAOmJ,CAAK,IAInCzL,KAAK0F,KAAOkF,EAAMtG,UAAYtE,KAAK0F,KAC/B1F,KAAKS,KAAKsF,MACV/F,KAAKqE,UAAYuG,EAAM7D,eAAe,aAAe6D,EAAMvG,UAAYrE,KAAKqE,UAE5ErE,KAAKiD,WAAU,WACXjD,KAAKqE,UAAYuG,EAAM7D,eAAe,aAAe6D,EAAMvG,UAAYrE,KAAKqE,SAChF,IAGZ,CAAE,MACEqG,aAAaa,WAAWvL,KAAKqH,WACjC,CASJ,EACA,WAAAqE,CAAYC,GACR,IAAIlB,EAAcC,aAAaC,QAAQ3K,KAAKqH,YACxCuD,EAAQH,EAAcI,KAAKC,MAAML,GAAe,CAAC,EAErDkB,EAAU,IAAKA,EAASZ,KAAM,IAAIC,MAClCN,aAAakB,QAAQ5L,KAAKqH,WAAYwD,KAAKgB,UAAUlL,OAAOmL,OAAOlB,EAAOe,IAC9E,EACA,WAAAI,CAAYC,EAAKC,GACb,OAAIjM,KAAKyH,UAAUD,KAAOwE,EAAIhM,KAAKsG,QAAQ4F,QAAUD,EAAOhE,KACjD,GAAGgE,EAAOrM,aAAaqM,EAAOhE,KAAKrI,YAEnC,GAAGqM,EAAOrM,WAEzB,EACA,OAAAuM,CAAQC,EAAMH,GACV,GAAqB,SAAjBA,GAAQrM,KAAiB,OAAOwM,EAAKH,EAAOvK,SAChD,IAAK1B,KAAK8I,KAAM,MAAO,IAEvB,IAAKuD,EAAOC,GAAStM,KAAK8I,KAAKuD,MAAM,qBAAuB,GAC5D,OAAIC,EACOtM,KAAK8I,KAAKyD,QAAQF,EAAOD,EAAKE,IAGlCtM,KAAK8I,KAAOsD,EAAKpM,KAAKsG,QAAQ4F,MACzC,EACA,UAAAM,CAAWJ,GACP,IAAIpM,KAAKyH,SAET,GAAIzH,KAAK8I,MAAM5I,OAAS,EAAG,CACvB,IAAIoH,EAAOtH,KAAKmM,QAAQC,GAExB,GAAsB,WAAlBpM,KAAKgJ,SAAuB,CAC5B,IAAIF,EAAOjF,SAAS4I,cAAc,KAClC3D,EAAKxB,KAAOA,EACZwB,EAAK4D,aAAa,SAAU,UAC5B5D,EAAK6D,OACT,MACI5I,OAAOQ,SAAS+C,KAAOA,CAC/B,MACItH,KAAKsD,MAAM,cAAe8I,EAElC,EACArC,YAAW,CAACzH,EAAKsK,EAAY,EAAG9C,EAAW,WACtB,WAAbA,EACO,CAAClH,EAAGC,KACP,IAAIgK,EAAOjK,EAAEN,IAAQ,EACjBwK,EAAOjK,EAAEP,IAAQ,EAErB,OAAIuK,IAASC,EAAa,EACtBD,EAAOC,EAAa,EAAIF,GAEpB,EAAIA,CAAS,EAGZ,WAAb9C,EACO,CAAClH,EAAGC,KACP,IAAIgK,EAAOjK,EAAEN,IAAQ,GACjBwK,EAAOjK,EAAEP,IAAQ,GAErB,OAAOsK,EAAY,EAAO9J,cAAcgK,EAAM,KAAM,CAAE/J,YAAa,QAAS,EAGnE,YAAb+G,EACO,CAAClH,EAAGC,KACP,IAAIgK,EAAOjK,EAAEN,GAAO,EAAI,EACpBwK,EAAOjK,EAAEP,GAAO,EAAI,EAExB,OAAOsK,GAAaC,EAAOC,EAAK,EAGvB,SAAbhD,EACO,CAAClH,EAAGC,KACP,IAAIkK,EAAUnK,EAAEN,IAAQ,WACpB0K,EAAUnK,EAAEP,IAAQ,WACpBuK,EAAO,IAAI7B,KAAK+B,GAChBD,EAAO,IAAI9B,KAAKgC,GAEpB,OAAID,IAAYC,EAAgB,EAC5BH,EAAOC,EAAa,EAAIF,GAEpB,EAAIA,CAAS,EAGtB,IAAM,EAEjB,IAAAjK,CAAKsJ,GACD,GAAIA,EAAOxF,QAAS,CAChB,IAAIgF,EAAQ,EACRwB,EAAcjN,KAAKuG,UAAU0F,EAAOxF,SAEnCwG,EAEoB,IAAhBA,IACLxB,GAAS,GAFTA,EAAQ,EAIU,WAAlBzL,KAAKqG,UACL1F,OAAOwE,KAAKnF,KAAKuG,WAAWhB,SAAQC,GAAKxF,KAAKuG,UAAUf,GAAK,IAGjExF,KAAKuG,UAAU0F,EAAOxF,SAAWgF,EAEjCzL,KAAK0L,YAAY,CACbnF,UAAW5F,OAAOuM,QAAQlN,KAAKuG,aAEnCvG,KAAKmN,WACT,CACJ,EACA,MAAAjD,EAAO,IAAE5H,IACL,IAAI5C,EAAMM,KAAKgG,YAAY1D,GAQ3B,GAPAtC,KAAKsD,MAAM,SAAU,CAAEhB,MAAK5C,QAE5BM,KAAK0L,YAAY,CACb1F,YAAahG,KAAKgG,YAClB3B,UAAW,IAGXrE,KAAKkJ,WAAY,CACjB,MAAMkE,EAAgBpN,KAAKoG,cAAclG,OACzCF,KAAKoG,cAAgBpG,KAAKoG,cAAc8D,QAAO9J,GAAKJ,KAAKgK,YAAY7J,KAAIkN,GAAKA,EAAE7F,KAAIJ,SAAShH,KAEzFgN,IAAkBpN,KAAKoG,cAAclG,QACrCF,KAAKsD,MAAM,sBAAuBtD,KAAKoG,cAE/C,CACJ,EACA,cAAAkD,CAAehH,GACX,OAAOtC,KAAK2G,gBAAgBjE,MAAKkE,GAAKA,EAAEtE,MAAQA,GACpD,EACA,MAAAa,CAAOqE,GACH,IAAI8F,EAActN,KAAKoG,cAAcmH,QAAQ/F,GACzC8F,GAAe,EACftN,KAAKoG,cAAcoH,OAAOF,EAAa,GAEvCtN,KAAKoG,cAAcyD,KAAKrC,GAG5BxH,KAAKsD,MAAM,sBAAuBtD,KAAKoG,cAC3C,EACA,SAAAqH,CAAUC,GAEF1N,KAAKoG,cADLsH,EACqB,IAAI,IAAIzL,IAAI,IAAIjC,KAAKoG,iBAAkBpG,KAAKqK,aAAalK,KAAIC,GAAKA,EAAEoH,QAEpExH,KAAKoG,cAAc8D,QAAO9J,IAAMJ,KAAKqK,aAAalK,KAAIkN,GAAKA,EAAE7F,KAAIJ,SAAShH,KAGnGJ,KAAKsD,MAAM,sBAAuBtD,KAAKoG,cAC3C,EACA,cAAAuH,CAAe1B,GACX,IAAIT,EAAI,GAER,MADoB,SAAhBS,EAAOrM,OAAiB4L,GAAK,KACzBS,EAAO2B,OAAS,IAAMpC,CAClC,EACA,KAAA5F,GACI5F,KAAK2G,gBAAgBpB,SAAQ,CAACqB,EAAGvE,KAC7BrC,KAAKgG,YAAYY,EAAEtE,KAAO,GAC1BtC,KAAK0L,YAAY,CAAE1F,YAAahG,KAAKgG,aAAc,IAGvDhG,KAAKsD,MAAM,SACf,EACA,UAAAuK,CAAWC,GACP9N,KAAKqE,WAAayJ,EAClB9N,KAAK0L,YAAY,CAAErH,UAAWrE,KAAKqE,YACnCrE,KAAKmN,WACT,EACA,cAAAY,CAAerI,GACX1F,KAAK6N,WAAW,GAChB7N,KAAK0F,MAAQA,EACb1F,KAAK0L,YAAY,CAAEpH,SAAUtE,KAAK0F,OAClC1F,KAAKmN,WACT,EACA,OAAAa,GACIC,MAAM,YAAa,CACfC,aAAa,EACbC,UAAW,WACXC,aAAa,GAErB,EACA,eAAAC,CAAgBjC,GACZ,IAAIpM,KAAK0H,UACT1H,KAAK0H,SAAU,EACX1H,KAAKyH,UAAU,CACf,GAAIzH,KAAKsG,QAAQ/E,OAAOmB,MAAK,EAAGhB,aAAc1B,KAAKyH,SAAS/F,GAAShB,UAEjE,YADAV,KAAK0H,SAAU,GAiBnB,MAAM4G,EAAeC,IAEjB,GADAvO,KAAKyH,SAAW,KACZ2E,EAAK5E,GAAI,CACT,MAAMgH,EAAMxO,KAAKS,KAAKqF,MAAM2I,WAAUrO,GAAKA,EAAEJ,KAAKsG,QAAQ4F,SAAWE,EAAK5E,KAC1ExH,KAAKS,KAAKqF,MAAM0H,OAAOgB,EAAK,EAAGpC,GAC/BpM,KAAKsD,MAAM,OAAQ8I,EACvB,MAEIA,EAAK5E,GAAK+G,EAAI/G,GACV+G,EAAIG,OAAMtC,EAAKsC,KAAOH,EAAIG,MAC9B1O,KAAKsD,MAAM,MAAO8I,GAClBpM,KAAKS,KAAKqF,MAAM+D,KAAKuC,GAEzBpM,KAAK0H,SAAU,CAAK,EAGlBiH,GA7BNvC,EAAOpM,KAAKsG,QAAQ/E,OAAO2E,QAAO,CAACC,EAAKyI,KACpC,MAAMnF,EAAMmF,EAAKlN,QACXwI,EAASlK,KAAK2G,gBAAgBjE,MAAKtC,GAAKA,EAAEkC,MAAQmH,IAClDoF,EAAW3E,GAAQ/H,kBAAoBsH,EAEvCqF,EAAgBF,EAAK3G,KAAO,KAAOiC,GAAQzH,QAAQ,GAKzD,OAJA0D,EAAI0I,GAAY7O,KAAKyH,SAASgC,GAAKsF,UAAYD,GAAetH,IAAM,KAChEqH,IAAapF,IACbtD,EAAIsD,GAAOtD,EAAI0I,GAAY3E,GAAQzH,QAAQC,MAAKtC,GAAKA,EAAEoH,KAAOrB,EAAI0I,KAAWG,KAAOF,GAAeE,MAEhG7I,CAAG,GACX,IAAKiG,KAkBkB5E,GAAKxH,KAAKsG,QAAQ2B,KAAOjI,KAAKsG,QAAQ2I,KAC5DN,GAAcO,SACZP,EAAaQ,cAAe/C,EAAKuC,EAAaQ,eAC5CR,EAAaS,MACbT,EAAaS,MAAMhD,GAAMiD,KAAKf,GAAa,KAAQtO,KAAK0H,SAAU,CAAK,IAIvE4G,IAGZ,CACJ,EACA,eAAAgB,CAAgBlD,EAAO,CAAC,GACpBpM,KAAKyH,SAAWzH,KAAKsG,QAAQ/E,OAAO2E,QAAO,CAACC,EAAKyI,KAC7C,MAAMnF,EAAMmF,EAAKlN,QACXwI,EAASlK,KAAK2G,gBAAgBjE,MAAKtC,GAAKA,EAAEkC,MAAQmH,IAClDoF,EAAW3E,GAAQ/H,kBAAoBsH,EACvC8F,EAAc,MAAMX,EAAKY,QAAU/F,OAEzC,IAAIgG,GAAY,EAgBhB,OAfKtJ,EAAIqB,IAAMoH,EAAK3G,MAAMtI,WACtB8P,GAAarD,EAAKyC,KAAcD,EAAK3G,MAAMnG,OAG/CqE,EAAIsD,GAAO,CACPsF,SAAU3C,EAAKyC,IAAaD,EAAK3G,MAAMnG,OAAS,GAChD4N,aAActD,EAAK3C,IAAQmF,EAAK3G,MAAMnG,OAASoI,GAAQzH,QAAQmM,EAAK3G,MAAM7G,SAAW,IAAI4N,KACzFvM,QAAS,IAAIyH,GAAQzH,SAAW,IAChCoM,WACAU,cACA7O,QAAS+O,KACNb,EAAK3G,MAAQ,CAAC,GAGrB9B,EAAIsD,GAAKhH,QAAQ+K,OAAO,EAAG,EAAG,CAAEhG,GAAI,GAAIwH,KAAMO,IACvCpJ,CAAG,GACX,CAAEqB,GAAI4E,EAAKpM,KAAKsG,QAAQ4F,QAC/B,EACA,iBAAAyD,CAAkBvD,GACd,MAAMkC,EAAc,KAChBtO,KAAKS,KAAKqF,MAAM0H,OAAOxN,KAAKS,KAAKqF,MAAMyH,QAAQnB,GAAO,GACtDpM,KAAKsD,MAAM,SAAU8I,EAAK,GAE1BpM,KAAKsG,QAAQ4B,QAAQgH,SACnBlP,KAAKsG,QAAQ4B,OAAO0H,gBAAiBxD,EAAKpM,KAAKsG,QAAQ4B,OAAO0H,iBAC5D5P,KAAKsG,QAAQ4B,OAAOkH,MACpBpP,KAAKsG,QAAQ4B,OAAOkH,MAAMhD,GAAMiD,KAAKf,GAAa,SAElDA,IAGZ,EACA,cAAAuB,GACI,GAAI7P,KAAKiJ,OAEL,OADAlF,OAAOQ,SAAS+C,KAAOtH,KAAKiJ,OACrBlF,OAAOQ,SAAS+C,KAG3BtH,KAAKsP,iBACT,EACA,SAAAnC,GACI,GAAInN,KAAK+I,UAAY/I,KAAKS,KAAKsF,MAAO,CAClCpF,OAAOwE,KAAKnF,KAAKgG,aAAaE,QAAO,CAACC,EAAK7D,KACvC6D,EAAI,GAAK7D,EACT6D,EAAI,GAAKnG,KAAKgG,YAAY1D,GACnB6D,IACR,CAAC,GAAI,KAER,IAAI2J,EAASnP,OAAOwE,KAAKnF,KAAKuG,WAAW7D,MAAKtC,GAAKJ,KAAKuG,UAAUnG,MAAOO,OAAOwE,KAAKnF,KAAKuG,WAAW,GAEjGwJ,EAAwB/P,KAAKuG,UAAUuJ,GAAU,EAAI,OAAS,GAC9DpJ,EAAgB1G,KAAKuG,UAAUuJ,GAAU,EAAI,MAAQC,EAErDC,EAAgB,CAChBC,KAAMjQ,KAAKqE,UACXC,SAAUtE,KAAK0F,KACfoK,OAAQA,EACRpJ,cAAeA,EACfwJ,WAAY,CAAC,QACbC,aAAc,CAAC,OAGnBC,MAAM,GAAGpQ,KAAK+I,WAAY,CACtBsH,OAAQ,OACRC,QAAS,CACL,eAAgB,oBAEpBC,KAAM1F,KAAKgB,UAAUmE,KACtBX,MAAK/J,GAAUA,EAAOkL,SACpBnB,MAAK5O,IACFT,KAAKS,KAAO,IAAKT,KAAKS,QAASA,EAAM,GAEjD,CACJ,GAEJ,OAAAgQ,GACIzQ,KAAKiD,WAAU,WACXjD,KAAKgO,SACT,GACJ,EACA,OAAApK,GACI5D,KAAKgO,UACLhO,KAAKwK,YACLxK,KAAKmN,WACT", "sources": ["webpack://PharmaLex.VigiLit.v2/./src/js/vue/v3/filtered-table-v3.js"], "sourcesContent": ["﻿vueApp.component('collection-cell', {\r\n    template: '#collection-cell-template',\r\n    props: {\r\n        val: {\r\n            required: true,\r\n            type: Array\r\n        }\r\n    },\r\n    computed: {\r\n        tipContent() {\r\n            return `<ul>\r\n                ${this.val.slice(1, this.val.length)\r\n                    .map(x => `<li>${x}</li>`).join(' ')\r\n                }</ul>`;\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('text-cell', {\r\n    template: '#text-cell-template',\r\n    props: {\r\n        val: String,\r\n        isYesNo: Boolean\r\n    }\r\n});\r\n\r\nvueApp.component('text-edit-select-cell', {\r\n    template: '#text-edit-select-cell-template',\r\n    data() {\r\n        return {\r\n            invalid: this.data.invalid\r\n        }\r\n    },\r\n    props: {\r\n        data: Object\r\n    },\r\n    methods: {\r\n        onChange(e) {\r\n            if (this.invalid)\r\n                e.target.reportValidity();\r\n        },\r\n        onInput(e) {\r\n            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('text-edit-plain-cell', {\r\n    template: '#text-edit-plain-cell-template',\r\n    data() {\r\n        return {\r\n            invalid: this.data.invalid\r\n        }\r\n    },\r\n    props: {\r\n        data: Object,\r\n        inputType: {\r\n            type: String,\r\n            default: 'text'\r\n        }\r\n    },\r\n    methods: {\r\n        onChange(e) {\r\n            if (this.invalid)\r\n                e.target.reportValidity();\r\n        },\r\n        onInput(e) {\r\n            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('link-edit-plain-cell', {\r\n    template: '#text-edit-plain-cell-template',\r\n    data() {\r\n        return {\r\n            invalid: this.data.invalid\r\n        }\r\n    },\r\n    props: {\r\n        data: Object,\r\n        inputType: {\r\n            type: String,\r\n            default: 'url'\r\n        }\r\n    },\r\n    methods: {\r\n        onChange(e) {\r\n            if (this.invalid) {\r\n                e.target.reportValidity();\r\n            }\r\n        },\r\n        onInput(e) {\r\n            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('date-edit-plain-cell', {\r\n    template: '#text-edit-plain-cell-template',\r\n    data() {\r\n        return {\r\n            invalid: this.data.invalid\r\n        }\r\n    },\r\n    props: {\r\n        data: Object,\r\n        inputType: {\r\n            type: String,\r\n            default: 'date'\r\n        }\r\n    },\r\n    methods: {\r\n        onChange(e) {\r\n            if (this.invalid) {\r\n                e.target.reportValidity();\r\n            }\r\n        },\r\n        onInput(e) {\r\n            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('link-cell', { template: '<td><a class=\"action-link\" :href=\"href\" target=\"_blank\" v-on:click.stop>{{val}}</a></td>', props: ['val', 'href'] });\r\n\r\nvueApp.component('multiline-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('html-cell', { template: '<td v-html=\"val\"></td>', props: ['val'] });\r\n\r\nvueApp.component('number-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('email-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('url-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('date-cell', { template: '<td>{{convert ? convert(val) : val}}</td>', props: ['val', 'convert'] });\r\n\r\nvueApp.component('picklist-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('relationship-cell', { template: '<td>{{val}}</td>', props: ['val'] });\r\n\r\nvueApp.component('bool-cell', {\r\n    template: '#bool-cell-template',\r\n    props: {\r\n        val: Boolean\r\n    }\r\n});\r\n\r\nvueApp.component('select-filter', {\r\n    template: '#select-filter-template',\r\n    data: function () {\r\n        return {\r\n            isOpen: false,\r\n            textField: this.config.display,\r\n            dataField: this.config.dataKey,\r\n            strings: {\r\n                ...this.resources\r\n            }\r\n        };\r\n    },\r\n    emits: ['update:value', 'filter'],\r\n    props: {\r\n        value: {\r\n            required: true,\r\n            default: ''\r\n        },\r\n        config: {\r\n            type: Object,\r\n            required: true\r\n        },\r\n        filtered: {\r\n            type: Array,\r\n            required: true\r\n        },\r\n        resources: Object\r\n    },\r\n    computed: {\r\n        availableOptions: function () {\r\n            return [...new Set(this.filtered.map(p => p[this.config.filterCollection]).flat())]\r\n                .map(i => {\r\n                    if (this.textField === this.dataField) {\r\n                        return {\r\n                            key: i === null ? '-' : i,\r\n                            value: i === null ? this.strings.noValue : i\r\n                        };\r\n                    }\r\n\r\n                    const v = this.config.options.find(x => x[this.dataField] === i);\r\n                    return {\r\n                        key: i === null ? '-' : i,\r\n                        value: v ? v[this.textField] : this.strings.noValue\r\n                    };\r\n                })\r\n                .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));\r\n        }\r\n    },\r\n    methods: {\r\n        open() {\r\n            this.isOpen = !this.isOpen;\r\n            this.$nextTick(function () {\r\n                this.$refs.select.focus();\r\n            });\r\n        },\r\n        change(val) {\r\n            this.isOpen = false;\r\n            this.$emit('update:value', val);\r\n            this.$emit('filter', { key: this.config.key });\r\n        },\r\n        handleClickOutside(evt) {\r\n            if (!this.$el.contains(evt.target)) {\r\n                this.isOpen = false;\r\n            }\r\n        },\r\n        close() {\r\n            this.isOpen = false;\r\n        }\r\n    },\r\n    mounted: function () {\r\n        document.addEventListener('click', this.handleClickOutside);\r\n        window.addEventListener('resize', this.close);\r\n    },\r\n    destroyed: function () {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n        window.removeEventListener('resize', this.close);\r\n    }\r\n});\r\n\r\nvueApp.component('data-table-pager', {\r\n    template: '#data-table-pager-template',\r\n    data() {\r\n        return {\r\n            strings: {\r\n                ...this.resources\r\n            }\r\n        };\r\n    },\r\n    emits: ['page-size-change', 'page-index-change'],\r\n    props: {\r\n        isPaged: Boolean,\r\n        itemCount: Number,\r\n        pageIndex: Number,\r\n        pageSize: Number,\r\n        location: {\r\n            type: String,\r\n            default: 'bottom'\r\n        },\r\n        totalItems: {\r\n            type: Number,\r\n            required: true\r\n        },\r\n        resources: Object\r\n    },\r\n    computed: {\r\n        startIndex: function () {\r\n            return this.pageSize * this.pageIndex;\r\n        },\r\n        endIndex: function () {\r\n            return Math.min(parseInt(this.startIndex) + parseInt(this.pageSize), this.itemCount);\r\n        },\r\n        pageCount: function () {\r\n            let extra = this.itemCount % this.pageSize ? 1 : 0;\r\n            return Math.floor(this.itemCount / this.pageSize) + extra;\r\n        }\r\n    },\r\n    methods: {\r\n        interpolate(format, ...keys) {\r\n            const parts = format.split('{}');\r\n            let result = parts[0];\r\n\r\n            keys.forEach((k, i) => {\r\n                result += k;\r\n                if (parts.length > i + 1) result += parts[i + 1];\r\n            });\r\n\r\n            return result;\r\n        },\r\n        pageSizeChange(size) {\r\n            this.$emit('page-size-change', +size);\r\n        }\r\n    }\r\n});\r\n\r\nvueApp.component('search-filter', {\r\n    template: '#search-filter-template',\r\n    data() {\r\n        return {\r\n            isOpen: false\r\n        };\r\n    },\r\n    emits: ['update:value', 'filter'],\r\n    props: {\r\n        value: {\r\n            required: true,\r\n            default: ''\r\n        },\r\n        config: {\r\n            type: Object,\r\n            required: true\r\n        },\r\n        filtered: {\r\n            type: Array,\r\n            required: true\r\n        }\r\n    },\r\n    methods: {\r\n        open() {\r\n            this.isOpen = !this.isOpen;\r\n            if (this.isOpen)\r\n                this.$nextTick(function () {\r\n                    this.$refs.search.focus();\r\n                });\r\n        },\r\n        change(val) {\r\n            this.$emit('update:value', val);\r\n            this.$emit('filter', { key: this.config.key });\r\n        },\r\n        handleClickOutside(evt) {\r\n            if (!this.$el.contains(evt.target)) {\r\n                this.isOpen = false;\r\n            }\r\n        },\r\n        close() {\r\n            this.isOpen = false;\r\n        },\r\n        clear() {\r\n            this.change('');\r\n            this.close();\r\n        }\r\n    },\r\n    mounted: function () {\r\n        document.addEventListener('click', this.handleClickOutside);\r\n        window.addEventListener('resize', this.close);\r\n    },\r\n    destroyed: function () {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n        window.removeEventListener('resize', this.close);\r\n    }\r\n});\r\n\r\nvueApp.component('filtered-table', {\r\n    template: '#filtered-table-template',\r\n    data: function () {\r\n        return {\r\n            data: {\r\n                count: this.items?.length || 0,\r\n                items: this.items?.length ? this.items.map(x => {\r\n                    return { ...x };\r\n                }) : [],\r\n                paged: !this.items\r\n            },\r\n            size: this.pageSize,\r\n            pageIndex: 0,\r\n            filterModel: this.filters.reduce((acc, val) => {\r\n                acc[val.key] = '';\r\n                return acc;\r\n            }, {}),\r\n            selectedItems: [],\r\n            sortMode: this.columns.sortMode || 'single',\r\n            sortModel: this.columns.config.reduce((acc, c) => {\r\n                if (c.sortKey)\r\n                    acc[c.sortKey] = c.sortDirection || 0;\r\n                return acc;\r\n            }, {}),\r\n            internalFilters: this.filters.map(f => {\r\n                let fi = { ...f };\r\n\r\n                let optionId = fi.options[0]?.hasOwnProperty('id') ? 'id' : 'Id';\r\n                let optionName = fi.options[0]?.hasOwnProperty('name') ? 'name' : 'Name';\r\n\r\n                fi.dataKey = fi.dataKey ? fi.dataKey : optionId;\r\n                fi.display = fi.display ? fi.display : optionName;\r\n\r\n                fi.convert = fi.convert || (v => v);\r\n                fi.fn = fi.fn || (v => p => (p[fi.dataKey] === null ? this.strings.noValue : p[fi.dataKey]).toLowerCase().includes(v.toLowerCase()));\r\n\r\n                return fi;\r\n            }),\r\n            storageKey: `${window.location.href}(${this.$attrs.id || ''})`,\r\n            editItem: null,\r\n            working: false,\r\n            strings: {\r\n                ...{\r\n                    noRecordsMessage: 'No matching records found',\r\n                    sortByFormat: 'Sort by {}',\r\n                    filterByFormat: 'Filter by {}',\r\n                    searchInFormat: 'Search in {}',\r\n                    clearFilters: 'Clear filters',\r\n                    addItem: 'Add item',\r\n                    edit: 'Edit',\r\n                    remove: 'Delete',\r\n                    save: 'Save',\r\n                    cancel: 'Cancel',\r\n                    noValue: '-- No Value',\r\n                    pager: {\r\n                        showingFormat: 'Showing {} to {} of {} entries',\r\n                        showingFilteredFormat: '(filtered from {})',\r\n                        pageSize: 'Page size',\r\n                        first: 'First',\r\n                        previous: 'Previous',\r\n                        next: 'Next',\r\n                        last: 'Last',\r\n                    }\r\n                },\r\n                ...this.resources\r\n            }\r\n        };\r\n    },\r\n    emits: ['filter', 'on-selection-change'],\r\n    props: {\r\n        items: Array,\r\n        filters: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        pageSize: {\r\n            type: Number,\r\n            default: 25\r\n        },\r\n        pagerLocation: {\r\n            type: String,\r\n            default: 'bottom'\r\n        },\r\n        columns: Object,\r\n        styling: String,\r\n        link: String,\r\n        itemsUrl: {\r\n            type: String,\r\n            default: window.location.href + '/paged'\r\n        },\r\n        behavior: String,\r\n        addurl: String,\r\n        resources: {\r\n            type: Object,\r\n            default() {\r\n                return {};\r\n            }\r\n        },\r\n        selectable: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    computed: {\r\n        appliedFilters: function () {\r\n            return Object.keys(this.filterModel).reduce((acc, fKey) => {\r\n                let fVal = this.filterModel[fKey];\r\n\r\n                if (fVal || fVal === false || fVal === 0) {\r\n                    let f = this.getFilterByKey(fKey);\r\n                    acc[fKey] = f.fn(f.convert(fVal));\r\n                }\r\n                return acc;\r\n            }, {});\r\n        },\r\n        appliedSorts: function () {\r\n            return Object.keys(this.sortModel).reduce((acc, sKey) => {\r\n                let col = this.columns.config.find(x => x.sortKey === sKey);\r\n                let dataTypeComparer = this.data.items[0] ? typeof this.data.items[0][col.sortKey] : null;\r\n                let comparerType = col ? col.sortComparer || dataTypeComparer : null;\r\n\r\n                //console.log({ sKey: sKey, comparerType: comparerType });\r\n                // HACK: if the first row was empty then it uses 'object' type - use 'string' instead.\r\n                // This is of course flawed if the column was meant to be another type, e.g. date.\r\n                // The proper solution is to not use this branch but instead use the actual shared code which apparently doesn't have this issue.\r\n                if (comparerType === 'object') {\r\n                    comparerType = 'string';\r\n                }\r\n\r\n                acc.push({\r\n                    key: sKey,\r\n                    comparer: this.getComparer(sKey, this.sortModel[sKey], comparerType)\r\n                });\r\n                return acc;\r\n            }, []);\r\n        },\r\n        itemCount: function () {\r\n            return this.data.paged ? this.data.count : this.filterItems.length;\r\n        },\r\n        filterItems: function () {\r\n            if (this.data.paged) {\r\n                return this.data.items;\r\n            }\r\n\r\n            this.pageIndex = 0;\r\n            return Object.keys(this.appliedFilters).reduce((toFilter, key) => {\r\n                return toFilter.filter(this.appliedFilters[key]);\r\n            }, this.data.items);\r\n        },\r\n        sortItems: function () {\r\n            if (this.data.paged) {\r\n                return this.filterItems.slice();\r\n            }\r\n\r\n            return this.appliedSorts.reduce((acc, val) => {\r\n                return acc.sort(val.comparer);\r\n            }, this.filterItems.slice());\r\n        },\r\n        pageItems: function () {\r\n            if (this.data.paged) {\r\n                return this.sortItems.slice();\r\n            }\r\n\r\n            return this.sortItems.slice(this.size * this.pageIndex, this.size * (this.pageIndex + 1));\r\n        },\r\n        pageItemsAdd() {\r\n            return this.editItem && !this.editItem.id ? [this.editItem, ...this.pageItems] : this.pageItems;\r\n        },\r\n        isFiltered() {\r\n            return !!Object.keys(this.appliedFilters).length;\r\n        },\r\n        allSelected() {\r\n            return this.pageItemsAdd.length && this.pageItemsAdd.filter(x => this.selectedItems.includes(x.id)).length == this.pageItemsAdd.length;\r\n        }\r\n    },\r\n    methods: {\r\n        interpolate(format, ...keys) {\r\n            const parts = format.split('{}');\r\n            let result = parts[0];\r\n\r\n            keys.forEach((k, i) => {\r\n                result += k;\r\n                if (parts.length > i + 1) result += parts[i + 1];\r\n            });\r\n\r\n            return result;\r\n        },\r\n        loadState() {\r\n            try {\r\n                let stateString = localStorage.getItem(this.storageKey);\r\n                let state = stateString ? JSON.parse(stateString) : { date: new Date() };\r\n                \r\n                if (stateIsFromToday(state)) {\r\n\r\n                    if (state.filterModel) {\r\n                        this.filterModel = state.filterModel;\r\n                        Object.keys(this.filterModel).forEach(key => {\r\n                            if (this.filterModel[key])\r\n                                this.filter({ key });\r\n                        });\r\n                    }\r\n\r\n                    if (state.sortModel) {\r\n                        state.sortModel.forEach(s => {\r\n                            let [key, order] = s;\r\n                            this.sortModel[key] = order;\r\n                        });\r\n                    }\r\n\r\n                    this.size = state.pageSize || this.size;\r\n                    if (this.data.paged) {\r\n                        this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;\r\n                    } else {\r\n                        this.$nextTick(function () {\r\n                            this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;\r\n                        });\r\n                    }\r\n                } else localStorage.removeItem(this.storageKey);\r\n            } catch {\r\n                localStorage.removeItem(this.storageKey);\r\n            }\r\n\r\n            function stateIsFromToday(state) {\r\n                let now = new Date();\r\n                let stateDate = new Date(state.date);\r\n                return now.getDay() === stateDate.getDay() &&\r\n                    now.getMonth() === stateDate.getMonth() &&\r\n                    now.getYear() === stateDate.getYear();\r\n            }\r\n        },\r\n        updateState(partial) {\r\n            let stateString = localStorage.getItem(this.storageKey);\r\n            let state = stateString ? JSON.parse(stateString) : {};\r\n\r\n            partial = { ...partial, date: new Date() };\r\n            localStorage.setItem(this.storageKey, JSON.stringify(Object.assign(state, partial)));\r\n        },\r\n        getCellType(row, column) {\r\n            if (this.editItem?.id === row[this.columns.idKey] && column.edit) {\r\n                return `${column.type}-edit-${column.edit.type}-cell`;\r\n            } else {\r\n                return `${column.type}-cell`;\r\n            }\r\n        },\r\n        getHref(item, column) {\r\n            if (column?.type === 'link') return item[column.dataKey];\r\n            if (!this.link) return '/';\r\n\r\n            let [match, field] = this.link.match(/\\{([a-z0-9]+)\\}/i) || [];\r\n            if (field) {\r\n                return this.link.replace(match, item[field]);\r\n            }\r\n\r\n            return this.link + item[this.columns.idKey];\r\n        },\r\n        rowClicked(item) {\r\n            if (this.editItem) return;\r\n\r\n            if (this.link?.length > 0) {\r\n                let href = this.getHref(item);\r\n\r\n                if (this.behavior === 'newtab') {\r\n                    let link = document.createElement('a');\r\n                    link.href = href;\r\n                    link.setAttribute('target', '_blank');\r\n                    link.click();\r\n                } else\r\n                    window.location.href = href;\r\n            } else {\r\n                this.$emit('row-clicked', item);\r\n            }\r\n        },\r\n        getComparer(key, direction = 0, comparer = 'string') {\r\n            if (comparer === 'number')\r\n                return (a, b) => {\r\n                    let akey = a[key] || 0,\r\n                        bkey = b[key] || 0;\r\n\r\n                    if (akey === bkey) return 0;\r\n                    if (akey > bkey) return 1 * direction;\r\n\r\n                    return -1 * direction;\r\n                };\r\n\r\n            if (comparer === 'string')\r\n                return (a, b) => {\r\n                    let akey = a[key] || '',\r\n                        bkey = b[key] || '';\r\n\r\n                    return direction * (akey).localeCompare(bkey, 'en', { sensitivity: 'base' });\r\n                };\r\n\r\n            if (comparer === 'boolean')\r\n                return (a, b) => {\r\n                    let akey = a[key] ? 1 : 0,\r\n                        bkey = b[key] ? 1 : 0;\r\n\r\n                    return direction * (akey - bkey);\r\n                };\r\n\r\n            if (comparer === 'date')\r\n                return (a, b) => {\r\n                    let akeyStr = a[key] || '1970-1-1',\r\n                        bkeyStr = b[key] || '1970-1-1';\r\n                    let akey = new Date(akeyStr),\r\n                        bkey = new Date(bkeyStr);\r\n\r\n                    if (akeyStr === bkeyStr) return 0;\r\n                    if (akey > bkey) return 1 * direction;\r\n\r\n                    return -1 * direction;\r\n                };\r\n\r\n            return () => 0;\r\n        },\r\n        sort(column) {\r\n            if (column.sortKey) {\r\n                let order = 0;\r\n                let currentSort = this.sortModel[column.sortKey];\r\n\r\n                if (!currentSort)\r\n                    order = 1;\r\n                else if (currentSort === 1)\r\n                    order = -1;\r\n\r\n                if (this.sortMode === 'single') {\r\n                    Object.keys(this.sortModel).forEach(k => this.sortModel[k] = 0);\r\n                }\r\n\r\n                this.sortModel[column.sortKey] = order;\r\n\r\n                this.updateState({\r\n                    sortModel: Object.entries(this.sortModel)\r\n                });\r\n                this.loadItems();\r\n            }\r\n        },\r\n        filter({ key }) {\r\n            let val = this.filterModel[key];\r\n            this.$emit('filter', { key, val });\r\n\r\n            this.updateState({\r\n                filterModel: this.filterModel,\r\n                pageIndex: 0\r\n            });\r\n\r\n            if (this.selectable) {\r\n                const selectedCount = this.selectedItems.length;\r\n                this.selectedItems = this.selectedItems.filter(x => this.filterItems.map(y => y.id).includes(x));\r\n\r\n                if (selectedCount !== this.selectedItems.length) {\r\n                    this.$emit('on-selection-change', this.selectedItems);\r\n                }\r\n            }\r\n        },\r\n        getFilterByKey(key) {\r\n            return this.internalFilters.find(f => f.key === key);\r\n        },\r\n        select(id) {\r\n            let toggleIndex = this.selectedItems.indexOf(id);\r\n            if (toggleIndex >= 0) {\r\n                this.selectedItems.splice(toggleIndex, 1);\r\n            } else {\r\n                this.selectedItems.push(id);\r\n            }\r\n\r\n            this.$emit('on-selection-change', this.selectedItems);\r\n        },\r\n        toggleAll(on) {\r\n            if (on) {\r\n                this.selectedItems = [...new Set([...this.selectedItems, ...this.pageItemsAdd.map(x => x.id)])]\r\n            } else {\r\n                this.selectedItems = this.selectedItems.filter(x => !this.pageItemsAdd.map(y => y.id).includes(x));\r\n            }\r\n\r\n            this.$emit('on-selection-change', this.selectedItems);\r\n        },\r\n        getColumnStyle(column) {\r\n            let s = '';\r\n            if (column.type === 'bool') s += '';\r\n            return (column.style || '') + s;\r\n        },\r\n        clear() {\r\n            this.internalFilters.forEach((f, i) => {\r\n                this.filterModel[f.key] = '';\r\n                this.updateState({ filterModel: this.filterModel });\r\n            });\r\n\r\n            this.$emit('filter');\r\n        },\r\n        changePage(index) {\r\n            this.pageIndex = +index;\r\n            this.updateState({ pageIndex: this.pageIndex });\r\n            this.loadItems();\r\n        },\r\n        changePageSize(size) {\r\n            this.changePage(0);\r\n            this.size = +size;\r\n            this.updateState({ pageSize: this.size });\r\n            this.loadItems();\r\n        },\r\n        tippify() {\r\n            tippy('.tipified', {\r\n                interactive: true,\r\n                placement: 'auto-end',\r\n                animateFill: false\r\n            });\r\n        },\r\n        saveItemClicked(item) {\r\n            if (this.working) return;\r\n            this.working = true;\r\n            if (this.editItem) {\r\n                if (this.columns.config.find(({ dataKey }) => this.editItem[dataKey].invalid)) {\r\n                    this.working = false;\r\n                    return;\r\n                }\r\n\r\n                item = this.columns.config.reduce((acc, conf) => {\r\n                    const col = conf.dataKey;\r\n                    const filter = this.internalFilters.find(x => x.key === col);\r\n                    const columnId = filter?.filterCollection ?? col;\r\n\r\n                    const defaultOption = conf.edit ? null : filter?.options[0];\r\n                    acc[columnId] = this.editItem[col].selected || defaultOption?.id || null;\r\n                    if (columnId !== col) {\r\n                        acc[col] = acc[columnId] ? filter?.options.find(x => x.id === acc[columnId]).name : defaultOption?.name;\r\n                    }\r\n                    return acc;\r\n                }, { ...item });\r\n\r\n                const applyChange = (res) => {\r\n                    this.editItem = null;\r\n                    if (item.id) {\r\n                        const idx = this.data.items.findIndex(x => x[this.columns.idKey] === item.id);\r\n                        this.data.items.splice(idx, 1, item);\r\n                        this.$emit('edit', item);\r\n                    }\r\n                    else {\r\n                        item.id = res.id;\r\n                        if (res.path) item.path = res.path;\r\n                        this.$emit('add', item);\r\n                        this.data.items.push(item);\r\n                    }\r\n                    this.working = false;\r\n                };\r\n\r\n                const actionConfig = item.id ? this.columns.edit : this.columns.add;\r\n                if (actionConfig?.enabled &&\r\n                    (!actionConfig.canEditProp || item[actionConfig.canEditProp])) {\r\n                    if (actionConfig.claim) {\r\n                        actionConfig.claim(item).then(applyChange, () => { this.working = false; });\r\n\r\n                    }\r\n                    else {\r\n                        applyChange();\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        editItemClicked(item = {}) {\r\n            this.editItem = this.columns.config.reduce((acc, conf) => {\r\n                const col = conf.dataKey;\r\n                const filter = this.internalFilters.find(x => x.key === col);\r\n                const columnId = filter?.filterCollection ?? col;\r\n                const placeholder = `-- ${conf.header || col} --`;\r\n\r\n                let isInvalid = false;\r\n                if (!acc.id && conf.edit?.required) {\r\n                    isInvalid = !item[columnId] && !conf.edit?.value;\r\n                }\r\n\r\n                acc[col] = {\r\n                    selected: item[columnId] || conf.edit?.value || '',\r\n                    selectedText: item[col] || conf.edit?.value || filter?.options[conf.edit?.default ?? 0]?.name,\r\n                    options: [...filter?.options || []],\r\n                    columnId,\r\n                    placeholder,\r\n                    invalid: isInvalid,\r\n                    ...conf.edit || {}\r\n                };\r\n\r\n                acc[col].options.splice(0, 0, { id: '', name: placeholder });\r\n                return acc;\r\n            }, { id: item[this.columns.idKey] });\r\n        },\r\n        removeItemClicked(item) {\r\n            const applyChange = () => {\r\n                this.data.items.splice(this.data.items.indexOf(item), 1);\r\n                this.$emit('remove', item);\r\n            };\r\n            if (this.columns.remove?.enabled &&\r\n                (!this.columns.remove.canRemoveProp || item[this.columns.remove.canRemoveProp])) {\r\n                if (this.columns.remove.claim) {\r\n                    this.columns.remove.claim(item).then(applyChange, () => { });\r\n                } else {\r\n                    applyChange();\r\n                }\r\n            }\r\n        },\r\n        addItemClicked() {\r\n            if (this.addurl) {\r\n                window.location.href = this.addurl\r\n                return window.location.href;\r\n            }\r\n\r\n            this.editItemClicked();\r\n        },\r\n        loadItems() {\r\n            if (this.itemsUrl && this.data.paged) {\r\n                Object.keys(this.filterModel).reduce((acc, key) => {\r\n                    acc[0] = key;\r\n                    acc[1] = this.filterModel[key];\r\n                    return acc;\r\n                }, [[], []]);\r\n\r\n                let sortBy = Object.keys(this.sortModel).find(x => this.sortModel[x]) || Object.keys(this.sortModel)[0];\r\n\r\n                let sortDirectionDescPart = this.sortModel[sortBy] < 0 ? 'desc' : '';\r\n                let sortDirection = this.sortModel[sortBy] > 0 ? 'asc' : sortDirectionDescPart;\r\n\r\n                let pagingRequest = {\r\n                    page: this.pageIndex,\r\n                    pageSize: this.size,\r\n                    sortBy: sortBy,\r\n                    sortDirection: sortDirection,\r\n                    filterKeys: ['type'],\r\n                    filterValues: ['ch']\r\n                };\r\n\r\n                fetch(`${this.itemsUrl}`, {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        \"Content-Type\": \"application/json\"\r\n                    },\r\n                    body: JSON.stringify(pagingRequest)\r\n                }).then(result => result.json())\r\n                    .then(data => {\r\n                        this.data = { ...this.data, ...data };\r\n                    });\r\n            }\r\n        }\r\n    },\r\n    updated() {\r\n        this.$nextTick(function () {\r\n            this.tippify();\r\n        });\r\n    },\r\n    mounted() {\r\n        this.tippify();\r\n        this.loadState();\r\n        this.loadItems();\r\n    }\r\n});"], "names": ["vueApp", "component", "template", "props", "val", "required", "type", "Array", "computed", "tipContent", "this", "slice", "length", "map", "x", "join", "String", "isYesNo", "Boolean", "data", "invalid", "Object", "methods", "onChange", "e", "target", "reportValidity", "onInput", "checkValidity", "inputType", "default", "isOpen", "textField", "config", "display", "dataField", "dataKey", "strings", "resources", "emits", "value", "filtered", "availableOptions", "Set", "p", "filterCollection", "flat", "i", "key", "noValue", "v", "options", "find", "sort", "a", "b", "localeCompare", "sensitivity", "open", "$nextTick", "$refs", "select", "focus", "change", "$emit", "handleClickOutside", "evt", "$el", "contains", "close", "mounted", "document", "addEventListener", "window", "destroyed", "removeEventListener", "isPaged", "itemCount", "Number", "pageIndex", "pageSize", "location", "totalItems", "startIndex", "endIndex", "Math", "min", "parseInt", "pageCount", "extra", "floor", "interpolate", "format", "keys", "parts", "split", "result", "for<PERSON>ach", "k", "pageSizeChange", "size", "search", "clear", "count", "items", "paged", "filterModel", "filters", "reduce", "acc", "selectedItems", "sortMode", "columns", "sortModel", "c", "sortKey", "sortDirection", "internalFilters", "f", "fi", "optionId", "hasOwnProperty", "optionName", "convert", "fn", "toLowerCase", "includes", "storageKey", "href", "$attrs", "id", "editItem", "working", "noRecordsMessage", "sortByFormat", "filterByFormat", "searchInFormat", "clearFilters", "addItem", "edit", "remove", "save", "cancel", "pager", "showingFormat", "showingFilteredFormat", "first", "previous", "next", "last", "pagerLocation", "styling", "link", "itemsUrl", "behavior", "addurl", "selectable", "appliedFilters", "fKey", "fVal", "getFilter<PERSON>y<PERSON>ey", "appliedSorts", "s<PERSON>ey", "col", "dataTypeComparer", "comparerType", "sortComparer", "push", "comparer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterItems", "to<PERSON><PERSON>er", "filter", "sortItems", "pageItems", "pageItemsAdd", "isFiltered", "allSelected", "loadState", "stateString", "localStorage", "getItem", "state", "JSON", "parse", "date", "Date", "now", "stateDate", "getDay", "getMonth", "getYear", "stateIsFromToday", "removeItem", "s", "order", "updateState", "partial", "setItem", "stringify", "assign", "getCellType", "row", "column", "id<PERSON><PERSON>", "getHref", "item", "match", "field", "replace", "rowClicked", "createElement", "setAttribute", "click", "direction", "akey", "bkey", "akeyStr", "bkeyStr", "currentSort", "entries", "loadItems", "selectedCount", "y", "toggleIndex", "indexOf", "splice", "toggleAll", "on", "getColumnStyle", "style", "changePage", "index", "changePageSize", "tippify", "tippy", "interactive", "placement", "animateFill", "saveItemClicked", "applyChange", "res", "idx", "findIndex", "path", "actionConfig", "conf", "columnId", "defaultOption", "selected", "name", "add", "enabled", "canEditProp", "claim", "then", "editItemClicked", "placeholder", "header", "isInvalid", "selectedText", "removeItemClicked", "canRemoveProp", "addItemClicked", "sortBy", "sortDirectionDescPart", "pagingRequest", "page", "filterKeys", "filterValues", "fetch", "method", "headers", "body", "json", "updated"], "sourceRoot": ""}