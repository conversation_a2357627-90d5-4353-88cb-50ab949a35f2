﻿using PharmaLex.VigiLit.DataExtraction.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service.Clients;

public class DataExtractionClient : IDataExtractionClient
{
    private readonly IExtractDataCommandHandler _handler;

    public DataExtractionClient(IExtractDataCommandHandler handler)
    {
        _handler = handler;
    }

    public async Task Send(ExtractDataCommand command)
    {
        await _handler.Consume(command);
    }
}
