﻿using PharmaLex.VigiLit.ContractManagement.Ui.Services;
using Xunit;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests;

public class IssnValidatorTests
{
    [Theory]
    [InlineData("2049-3630")]
    [InlineData("0317-8471")]
    [InlineData("2434-561X")]
    [InlineData("2434561X")]  // no hyphen
    public void ValidIssn_ShouldReturnTrue(string issn)
    {
        Assert.True(IssnValidator.IsValidIssn(issn));
    }

    [Theory]
    [InlineData("1234-5678")] // invalid check digit
    [InlineData("2049-3631")] // wrong check digit
    [InlineData("0317-847X")] // X in wrong place
    [InlineData("ABCD-EFGH")] // letters not allowed
    [InlineData("123456")]    // too short
    [InlineData("123456789")] // too long
    [InlineData("1234-56X")]  // too short with X
    [InlineData("")]          // empty string
    public void InvalidIssn_ShouldReturnFalse(string issn)
    {
        Assert.False(IssnValidator.IsValidIssn(issn));
    }
}
