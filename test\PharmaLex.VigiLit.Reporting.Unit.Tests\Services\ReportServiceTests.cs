﻿using Moq;
using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;
using PharmaLex.VigiLit.Reporting.Services;
using Xunit;

namespace PharmaLex.VigiLit.Reporting.Tests.Services;

public class ReportServiceTests
{
    private readonly IReportService _reportService;

    private readonly Mock<IReportRepository> _reportRepository = new();

    public ReportServiceTests()
    {
        _reportService = new ReportService(_reportRepository.Object);
    }

    [Fact]
    public async Task GetReport_Returns_Report()
    {
        // Arrange
        var report = new Report()
        {
            Name = "my report"
        };

        _reportRepository.Setup(x => x.GetById(1)).ReturnsAsync(report);

        // Act
        var model = await _reportService.GetReport(1);

        // Assert
        Assert.Equal(report.Name, model.Name);
    }

    [Fact]
    public async Task GetReportsPageModel_Returns_PageModel()
    {
        // Arrange
        var user = GetInternalUser();

        var reports = new List<ReportModel>(){
            new()
            {
                Name = "my report 1"
            },
            new()
            {
                Name = "my report 2"
            },
        };

        _reportRepository.Setup(x => x.GetReports(user)).ReturnsAsync(reports);

        // Act
        var model = await _reportService.GetReportsPageModel(user);

        // Assert
        Assert.Equal(reports[0].Name, model.Reports.ToList()[0].Name);
        Assert.Equal(reports[1].Name, model.Reports.ToList()[1].Name);
    }

    private static IUserEntity GetInternalUser()
    {
        return new User("FirstName", "LastName", "EmailAddress");
    }
}
