﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230413a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId_Id_ImportId",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "ReferenceClassificationId", "Id", "ImportId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ReferenceClassificationId_Id_ImportId",
                table: "ImportContractReferenceClassifications");
        }
    }
}
