﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.AutoMapper;

public class CompanyMappingProfileTests
{
    readonly IMapper _mapper;

    public CompanyMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CompanyMappingProfile>();
            cfg.AddProfile<CompanyUserMappingProfile>();
        });
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_Company_To_CompanyModel()
    {
        // Arrange
        var company = GetCompany();

        // Act
        var model = _mapper.Map<CompanyModel>(company);

        // Assert
        Assert.Equal("Company 1", model.Name);
        Assert.Equal("Contact person 1", model.ContactPersonName);
        Assert.Equal("Contact email", model.ContactPersonEmail);
        Assert.True(model.IsActive);
    }

    [Fact]
    public void Maps_Company_To_CompanyEmailModel()
    {
        // Arrange
        var company = new Company("Company 1", "Contact person 1", "Contact email", true)
        {
            CompanyUsers = new List<CompanyUser>()
             {
                 new CompanyUser{ Active = true, User = new User("Active Given Name 1", "Active Family Name 1", "<EMAIL>") },
                 new CompanyUser{ Active = false, User = new User("Inactive Given Name 2", "Active Family Name 2", "<EMAIL>") },
                 new CompanyUser{ Active = true, User = new User("Active Given Name 3", "Active Family Name 3", "<EMAIL>") },
                 new CompanyUser{ Active = false, User = new User("Inactive Given Name 4", "Active Family Name 4", "<EMAIL>") },
             }
        };

        // Act
        var model = _mapper.Map<CompanyEmailModel>(company);

        // Assert
        Assert.Equal(2, model.CompanyUsers.Count);
        Assert.Equal("Active Given Name 1", model.CompanyUsers[0].GivenName);
        Assert.Equal("Active Family Name 1", model.CompanyUsers[0].FamilyName);
    }

#pragma warning disable xUnit1004

    [Fact(Skip = "Technical debt")]
    public void Maps_Company_To_CompanyWithContractsModel()
    {
        // Technical debt
    }

    [Fact(Skip = "Technical debt")]
    public void Maps_Company_To_CompanyWithProjectsModel()
    {
        // Technical debt
    }

    [Fact(Skip = "Technical debt")]
    public void Maps_Company_To_CompanyWithUsersModel()
    {
        // Technical debt
    }

#pragma warning restore xUnit1004

    [Fact]
    public void Maps_Company_To_CompanyItemModel()
    {
        // Arrange
        var company = GetCompany();

        // Act
        var model = _mapper.Map<CompanyItemModel>(company);

        // Assert
        Assert.Equal(0, model.Id);
        Assert.Equal("Company 1", model.Name);
        Assert.True(model.IsActive);
    }

    [Fact]
    public void Maps_Company_To_CompanyUserListModel()
    {
        // Arrange
        var company = new Company("Company 1", "Contact person 1", "Contact email", true)
        {
            CompanyUsers = new List<CompanyUser>()
             {
                 new CompanyUser{ User = new User("Given Name 1", "Family Name 1", "<EMAIL>") },
                 new CompanyUser{ User = new User("Given Name 2", "Family Name 2", "<EMAIL>") },
             }
        };

        // Act
        var model = _mapper.Map<CompanyUserListModel>(company);

        // Assert
        Assert.True(model.CompanyUsers.Count == 2);
    }

    private static Company GetCompany()
    {
        return new Company("Company 1", "Contact person 1", "Contact email", true);
    }
}
