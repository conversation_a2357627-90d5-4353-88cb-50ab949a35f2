using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.ContractManagement.Enums;
using PharmaLex.VigiLit.ContractManagement.Ui.Services;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class CompaniesController : BaseController
{
    private readonly ICompanyService _companyService;
    private readonly IUserService _userService;
    private readonly IContractService _contractService;
    private readonly ISubstanceService _substanceService;
    private readonly IProjectService _projectService;
    private readonly ICompanyUserService _companyUserService;
    private readonly ICriteriaValidator _criteriaValidator;
    private readonly IFeatureManager _featureManager;

    private const string enableQueryStringValidation = "EnableContractQueryStringValidation";

#pragma warning disable S107
    public CompaniesController(ICompanyService companyService,
        IContractService contractService,
        ICriteriaValidator criteriaValidator,
        ISubstanceService substanceService,
        IProjectService projectService,
        ICompanyUserService companyUserService,
        IUserService userService,
        IUserSessionService userSessionService,
        IConfiguration configuration,
        IFeatureManager featureManager
        )
        : base(userSessionService, configuration)
    {
        _companyService = companyService;
        _contractService = contractService;
        _criteriaValidator = criteriaValidator;
        _substanceService = substanceService;
        _projectService = projectService;
        _companyUserService = companyUserService;
        _userService = userService;
        _featureManager = featureManager;
    }
#pragma warning restore S107

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var items = await _companyService.GetAllAsync();
        return View(items);
    }

    [HttpGet("[action]")]
    public IActionResult Create()
    {
        return View("Edit", new CompanyModel());
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Create(CompanyModel model)
    {
        if (ModelState.IsValid)
        {
            var isNameValid = await _companyService.ValidateNameAsync(model.Name, model.Id);
            if (!isNameValid)
            {
                ModelState.AddModelError("name", $"Company with name {model.Name} already exists in the system.");
                return View("Edit", model);
            }
            await _companyService.AddAsync(model);

            AddNotification($"Company {model.Name} was created successfully", UserNotificationType.Confirm);
            return RedirectToAction("Index");
        }

        return View("Edit", model);
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        var company = await _companyService.GetByIdAsync(id);

        return View(company);
    }

    [HttpPost("[action]/{id}")]
    public async Task<IActionResult> Edit(CompanyModel model)
    {
        if (ModelState.IsValid)
        {
            var isNameValid = await _companyService.ValidateNameAsync(model.Name, model.Id);
            if (!isNameValid)
            {
                ModelState.AddModelError("name", $"Company with name {model.Name} already exists in the system.");
                return View("Edit", model);
            }
            await _companyService.UpdateAsync(model);
            AddNotification($"Company {model.Name} was edited successfully", UserNotificationType.Confirm);
            return RedirectToAction("Index");
        }
        return View(model);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> ValidateCompanyName(string name, int id)
    {
        var isNameValid = await _companyService.ValidateNameAsync(name, id);
        return Json(isNameValid);
    }

    [HttpGet("{companyId}/Contracts")]
    public async Task<IActionResult> CompanyContracts(int companyId)
    {
        var company = await _companyService.GetCompanyWithContracts(companyId);
        return View(company);
    }

    [HttpGet("{id}/Contract/Create")]
    public async Task<IActionResult> CreateCompanyContract(int id)
    {
        await LoadRelatedEntitiesAsync(id);
        return View("CompanyContractEdit", new ContractModel() { CompanyId = id, ContractVersionCurrent = new ContractVersionEditModel { SearchPeriodDays = 56 } });
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> CreateCompanyContract(ContractModel model)
    {
        if (!ModelState.IsValid)
        {
            await LoadRelatedEntitiesAsync(model.CompanyId);
            return View("CompanyContractEdit", model);
        }

        await _contractService.AddAsync(model);
        AddNotification("Company Contract was created successfully.", UserNotificationType.Confirm);
        return RedirectToAction("CompanyContracts", new { companyId = model.CompanyId });
    }

    [HttpGet("{companyId}/Contract/{contractId}")]
    public async Task<IActionResult> EditCompanyContract(int companyId, int contractId)
    {
        var contract = await _contractService.GetByIdAsync(contractId);
        contract.CompanyId = companyId;

        await LoadRelatedEntitiesAsync(companyId);

        contract.RefererUrl = HttpContext.Request.Headers.Referer.FirstOrDefault(x => x.Contains("ImportDashboardDetails"));
        contract.ReadOnly = !string.IsNullOrEmpty(contract.RefererUrl);

        return View("CompanyContractEdit", contract);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> EditCompanyContract([FromBody] ContractModel model)
    {
        if (!ModelState.IsValid)
        {
            await LoadRelatedEntitiesAsync(model.CompanyId);
            return View("CompanyContractEdit", model);
        }

        // HACK: client doesn't send back the model properly with journals empty so we correct it here
        if (model.ContractVersionPending.ScreeningType == (int)ScreeningType.Global)
        {
            model.ContractVersionPending.Journals = new MultiSelectFilterRequest();
        }

        await _contractService.UpdateAsync(model);

        AddNotification("Contract was edited successfully.", UserNotificationType.Confirm);
        return RedirectToAction("CompanyContracts", new { companyId = model.CompanyId });
    }

    [HttpGet("[action]"), DigitalSignature]
    public async Task<IActionResult> ReAuthenticate()
    {
        var contract = HttpContext.Session.GetString("contract");
        var contractObj = JsonConvert.DeserializeObject<ContractModel>(contract);
        var mode = contractObj.SaveMode;
        if (string.IsNullOrWhiteSpace(mode))
        {
            throw new ArgumentException("Invalid save mode");
        }
        if (mode == "create")
        {
            return await CreateCompanyContract(contractObj);
        }
        else if (mode == "edit")
        {
            return await EditCompanyContract(contractObj);
        }
        return BadRequest($"Error cannot complete the action");
    }

    [HttpPost("[action]")]
    public IActionResult SaveContractInSession([FromBody] ContractModel contract)
    {
        HttpContext.Session.SetString("contract", JsonConvert.SerializeObject(contract));
        return Ok();
    }

    private async Task LoadRelatedEntitiesAsync(int companyId)
    {
        await LoadSubstancesAsync();
        await LoadProjectsAsync(companyId);
    }

    private async Task LoadSubstancesAsync()
    {
        var substances = (await _substanceService.GetAllAsync()).OrderBy(s => s.Name);
        ViewBag.Substances = new SelectList(substances, nameof(SubstanceModel.Id), nameof(SubstanceModel.Name));
    }

    private async Task LoadProjectsAsync(int companyid)
    {
        var projects = await _projectService.GetByCompanyIdAsync(companyid);
        ViewBag.Projects = new SelectList(projects, nameof(ProjectModel.Id), nameof(ProjectModel.Name));
    }

    [HttpGet("[action]/{companyId}")]
    public async Task<IActionResult> CompanyProjects(int companyId)
    {
        var company = await _companyService.GetCompanyWithProjects(companyId);
        return View(company);
    }

    [HttpGet("{companyId}/Project/Create")]
    public IActionResult CreateCompanyProject(int companyId)
    {
        return View("CompanyProjectEdit", new ProjectModel() { CompanyId = companyId });
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> CreateCompanyProject(ProjectModel model)
    {
        if (ModelState.IsValid)
        {
            await _projectService.AddAsync(model);
            AddNotification("Project was created successfully.", UserNotificationType.Confirm);
            return RedirectToAction("CompanyProjects", new { companyId = model.CompanyId });
        }
        return View("CompanyProjectEdit", model);
    }

    [HttpGet("{companyId}/Project/{projectId}")]
    public async Task<IActionResult> EditCompanyProject(int companyId, int projectId)
    {
        var project = await _projectService.GetByIdAsync(projectId);
        return View("CompanyProjectEdit", project);
    }

    [HttpPost("{companyId}/Project/{projectId}")]
    public async Task<IActionResult> EditCompanyProject(ProjectModel model)
    {
        if (ModelState.IsValid)
        {
            await _projectService.UpdateAsync(model);
            AddNotification("Project was updated successfully.", UserNotificationType.Confirm);
            return RedirectToAction("CompanyProjects", new { companyId = model.CompanyId });
        }

        return View("CompanyProjectEdit", model);
    }

    [HttpGet("{companyId}/CompanyUsers")]
    public async Task<IActionResult> CompanyUsers(int companyId)
    {
        var company = await _companyService.GetCompanyUsers(companyId);
        return View(company);
    }

    [HttpGet("{companyId}/User/Create")]
    public IActionResult CreateCompanyUser(int companyId)
    {
        return View("CompanyUserEdit", new CompanyUserModel() { CompanyId = companyId });
    }

    [HttpPost("/companies/resendsignoninvitation")]
    public async Task<IActionResult> ResendSignOnInvitation([FromBody] CompanyUserModel model)
    {
        var companyUser = await _companyUserService.ResendSignOnInvitation(model);
        return Json(JsonConvert.SerializeObject(companyUser));
    }

    [HttpPost("/companies/delete-user-from-bounce-list")]
    public async Task<IActionResult> DeleteUserFromBounceList([FromBody] CompanyUserModel model)
    {
        var companyUser = await _companyUserService.DeleteUserFromBounceList(model);
        return Json(JsonConvert.SerializeObject(companyUser));
    }

    [HttpPost("/companies/delete-user-from-block-list")]
    public async Task<IActionResult> DeleteUserFromBlockList([FromBody] CompanyUserModel model)
    {
        var companyUser = await _companyUserService.DeleteUserFromBlockList(model);
        return Json(JsonConvert.SerializeObject(companyUser));
    }

    [HttpGet("{companyId}/User/Edit/{userId}")]
    public async Task<IActionResult> EditCompanyUser(int companyId, int userId)
    {
        var user = await _companyUserService.GetCompanyUser(companyId, userId);
        return View("CompanyUserEdit", user);
    }

    [HttpPost("{companyId}/User/Edit/{userId}")]
    public async Task<IActionResult> EditCompanyUser(CompanyUserModel companyUser)
    {
        if (!ModelState.IsValid)
        {
            AddNotification("Please ensure all fields are filled in", UserNotificationType.Failed);
            return View("CompanyUserEdit", companyUser);
        }
        await _companyUserService.UpdateCompanyUser(companyUser.Id, companyUser.CompanyId, companyUser.Active, companyUser.GivenName, companyUser.FamilyName, companyUser.Email, companyUser.EmailPreferenceIds, companyUser.CompanyUserType);
        AddNotification("User was updated successfully", UserNotificationType.Info);

        return View("CompanyUserEdit", companyUser);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> CreateCompanyUser(CompanyUserModel model)
    {
        if (!ModelState.IsValid)
        {
            AddNotification("Please ensure all fields are filled in", UserNotificationType.Failed);
            return View("CompanyUserEdit", model);
        }

        if (model.CompanyUserType == CompanyUserType.External)
        {
            var result = await _companyUserService.AddNewCompanyUser(model);
            if (String.IsNullOrEmpty(result))
            {
                AddNotification("Company user was created successfully. An invitation email has been sent.", UserNotificationType.Confirm);
                return RedirectToAction("CompanyUsers", new { companyId = model.CompanyId });
            }
            else
            {
                AddNotification(result, UserNotificationType.Failed);
                return View("CompanyUserEdit", model);
            }
        }
        else if (model.CompanyUserType == CompanyUserType.Internal)
        {
            var result = await _companyUserService.AddNewInternalUser(model);
            if (String.IsNullOrEmpty(result))
            {
                AddNotification("Company user was created successfully.", UserNotificationType.Confirm);
                return RedirectToAction("CompanyUsers", new { companyId = model.CompanyId });
            }
            else
            {
                AddNotification(result, UserNotificationType.Failed);
                return View("CompanyUserEdit", model);
            }
        }
        else
        {
            AddNotification("Please ensure all fields are filled in", UserNotificationType.Failed);
            return View("CompanyUserEdit", model);
        }

    }

    [HttpGet("[action]")]
    public async Task<IActionResult> ValidateProjectName(string name, int id, int companyId)
    {
        var isNameValid = await _projectService.ValidateNameAsync(name, id, companyId);
        return Json(isNameValid);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> ValidateSearchString([FromBody] string searchString)
    {
        if (await _featureManager.IsEnabledAsync(enableQueryStringValidation) && !_criteriaValidator.TryValidate(searchString, out var errorMessages))
        {
            return Json(errorMessages);
        }

        return Json("");
    }
}