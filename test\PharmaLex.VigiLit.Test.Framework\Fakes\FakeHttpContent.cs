﻿using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace PharmaLex.VigiLit.Test.Framework.Fakes;

[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public class FakeHttpContent : HttpContent
{
    protected override Task SerializeToStreamAsync(Stream stream, TransportContext? context)
    {
        throw new NotImplementedException();
    }

    protected override bool TryComputeLength(out long length)
    {
        throw new NotImplementedException();
    }
}