﻿using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Extensions;
using PharmaLex.VigiLit.Application.Extensions;

public class ListExtensionsTests
{
    [Theory]
    [MemberData(nameof(ContentsEqualData))]
    public void ContentsEqual_Correctly_Compares_Lists(List<int> list1, List<int> list2, bool expected)
    {
        // Arrange
        // Act
        var result = list1.ContentsEqual(list2);

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [MemberData(nameof(OrderedContentsEqual))]
    public void OrderedContentsEqual_Correctly_Compares_Lists(List<int> list1, List<int> list2, bool expected)
    {
        // Arrange
        // Act
        var result = list1.OrderedContentsEqual(list2);

        // Assert
        Assert.Equal(expected, result);
    }

    public static IEnumerable<object[]> ContentsEqualData()
    {
        yield return new object[] { new List<int> { 1, 2, 3, 4, 5 }, new List<int> { 1, 2, 3, 4, 5 }, true };
        yield return new object[] { new List<int> { 2, 1, 3, 4, 5 }, new List<int> { 1, 2, 3, 4, 5 }, false };
        yield return new object[] { new List<int>(), new List<int>(), true };
    }

    public static IEnumerable<object[]> OrderedContentsEqual()
    {
        yield return new object[] { new List<int> { 1, 2, 3, 4, 5 }, new List<int> { 1, 2, 3, 4, 5 }, true };
        yield return new object[] { new List<int> { 2, 1, 3, 4, 5 }, new List<int> { 1, 2, 3, 4, 5 }, true };
        yield return new object[] { new List<int>(), new List<int>(), true };
    }
}
