@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.SubstanceModel>
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain;
@using PharmaLex.VigiLit.Domain.Enums;
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Substances";
}

<div class="sub-header">
    <h2>Substances</h2>
    <div class="controls">
        <a asp-action="Create" class="button primary btn-default">Create Substance</a>
    </div>
</div>

<div id="substance" v-cloak>
    <section>
        <filtered-table :items="items" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {
<script type="text/javascript">
    let substanceTypes = [
        {
            id: 'ch',
            name: 'Chemical'
        },
        {
            id: 'ph',
            name: '<PERSON><PERSON><PERSON> (Herbal)'
        }
    ];
    var pageConfig = {
        appElement: "#substance",
        data: function () {
            return {
                link: '/substances/edit/',
                items: @Html.Raw(AntiXss.ToJson(Model)),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text',
                            style: 'width: 33%;'
                        },
                        {
                            dataKey: 'type',
                            sortKey: 'type',
                            header: 'Type',
                            type: 'substance-type',
                            style: 'width: 20%;'
                        }
                    ],
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'type',
                        options: substanceTypes,
                        filterCollection: 'type',
                        type: 'select',
                        header: 'Filter by Type',
                        fn: v => p => p.type.toLowerCase() === v,
                        convert: v => v.toLowerCase()
                    }
                ]
            };
        }
    };
</script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
    <partial name="Components/SubstanceTypeCell" />
    <partial name="Components/DayOfWeekCell" />
}
