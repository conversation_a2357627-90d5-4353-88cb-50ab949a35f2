﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddEmailRelevantEvents : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmailRelevantEvents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CompanyInterestId = table.Column<int>(type: "int", nullable: false),
                    EmailRelevantEventActionType = table.Column<int>(type: "int", nullable: false),
                    ClassificationCategoryId = table.Column<int>(type: "int", nullable: true),
                    EmailRelevantEventEmailStatusType = table.Column<int>(type: "int", nullable: false),
                    EmailSentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EmailReason = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailRelevantEvents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmailRelevantEvents_ClassificationCategories_ClassificationCategoryId",
                        column: x => x.ClassificationCategoryId,
                        principalTable: "ClassificationCategories",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EmailRelevantEvents_CompanyInterests_CompanyInterestId",
                        column: x => x.CompanyInterestId,
                        principalTable: "CompanyInterests",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EmailRelevantEvents_ClassificationCategoryId",
                table: "EmailRelevantEvents",
                column: "ClassificationCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_EmailRelevantEvents_CompanyInterestId",
                table: "EmailRelevantEvents",
                column: "CompanyInterestId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmailRelevantEvents");
        }
    }
}
