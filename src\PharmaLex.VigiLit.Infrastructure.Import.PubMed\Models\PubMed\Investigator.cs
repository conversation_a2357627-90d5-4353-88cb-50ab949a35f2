using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class Investigator
{
    public string LastName { get; set; }

    public string ForeName { get; set; }

    public string Initials { get; set; }

    public Suffix Suffix { get; set; }

    [XmlElement("Identifier")]
    public List<Identifier> Identifier { get; set; } = new List<Identifier>();


    [XmlElement("AffiliationInfo")]
    public List<AffiliationInfo> AffiliationInfo { get; set; } = new List<AffiliationInfo>();

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(EnumYN.Y)]
    public EnumYN ValidYN { get; set; } = EnumYN.Y;
}