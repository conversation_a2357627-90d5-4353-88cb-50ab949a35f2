﻿using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.AccessControl.Unit.Tests;

[ExcludeFromCodeCoverage(Justification = "Test Object")]
internal class TestEvaluator2 : PermissionEvaluator<TestContext2>
{
#pragma warning disable CS1998 // Test Object - ignore
    public override async Task<bool> HasPermissions(TestContext2 context)
    {
        if (context.StringA == "YES")
        {
            return true;
        }
        throw new UnauthorizedAccessException();
    }
#pragma warning restore CS1998 
}
