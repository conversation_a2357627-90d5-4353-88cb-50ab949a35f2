trigger: none

variables:
  - template: azure-pipeline-variables.yaml

resources:
  repositories:
    - repository: devops-templates
      name: DevOps/DevOps.Pipelines.Templates
      type: git
    - repository: devops-templates
      name: DevOps/DevOps.Pipelines.Templates
      type: git
  pipelines:
    - pipeline: build-pipeline
      source: VigiLit.Build-Ai
      project: VigiLit
      trigger:
        branches:
          include:
            - develop

parameters:
  - name: clusters
    type: object
    default:
      # Azure does not support objects variables,
      # only parameters, we cannot put them in separate file
      - name: dev
        environment: vigilit_ai_dev
        subscription: Shared Infrastructure Development
        cluster: aksshared-sharednonprod-aks-eun
        rg: rg-aksshared-sharednonprod-eun
        keyVaults:
         - subscription: "DevOps Bastion"
           name: "pxv-vault-eun"
           secretsFilter: 'newrelic-pharmalex-license-key'
        dependsOn: [Checkov_scan]
        valuesOverrideFile: ["./deploy/helm/vigilit-ai/environments/development.values.yaml"]
      - name: int
        environment: vigilit_ai_int
        subscription: Shared Infrastructure Development
        cluster: aksshared-sharednonprod-aks-eun
        rg: rg-aksshared-sharednonprod-eun
        keyVaults:
         - subscription: "DevOps Bastion"
           name: "pxv-vault-eun"
           secretsFilter: 'newrelic-pharmalex-license-key'
        dependsOn: ["dev"]
        valuesOverrideFile: ["./deploy/helm/vigilit-ai/environments/integration.values.yaml"]
      - name: stg
        environment: vigilit_ai_stg
        subscription: Shared Infrastructure Development
        cluster: aksshared-sharednonprod-aks-eun
        rg: rg-aksshared-sharednonprod-eun
        keyVaults:
         - subscription: "DevOps Bastion"
           name: "pxv-vault-eun"
           secretsFilter: 'newrelic-pharmalex-license-key'
        dependsOn: ["int"]
        valuesOverrideFile: ["./deploy/helm/vigilit-ai/environments/staging.values.yaml"]
      - name: prod
        environment: vigilit_ai_prod
        subscription: Shared Infrastructure Production
        cluster: aksshared-sharedprod-aks-eun
        rg: rg-aksshared-sharedprod-eun
        keyVaults:
         - subscription: "DevOps Bastion"
           name: "pxv-vault-eun"
           secretsFilter: 'newrelic-pharmalex-license-key'
        dependsOn: ["stg"]
        valuesOverrideFile: ["./deploy/helm/vigilit-ai/environments/prodeu.values.yaml"]

stages:
  - stage: Deploy    
    jobs:
      - job: Initialize
        steps:
          - bash: |
              echo "Updating build number with pipeline run name"
              echo "##vso[build.updatebuildnumber]$(Build.BuildNumber) Build #$(resources.pipeline.build-pipeline.runName)"
        displayName: 'Update Pipeline Name'
  - stage: Checkov_scan
    displayName: "Checkov scan"
    dependsOn: []
    jobs:
      - job: Checkov_scan
        displayName: "Checkov scan"
        steps:
          - template: Checkov/Main_Scan/checkovscan.yaml@devops-templates
            parameters:
              ChartPath: 'deploy/helm/vigilit-ai'
              useCheckovHelmScan: true
  - ${{ each cluster in parameters.clusters }}:
    - template: Helm/deploy-single-oidc.yaml@devops-templates
      parameters:
        Environment: ${{ cluster.environment }}
        cluster: ${{ cluster.cluster }}
        resourceGroup: ${{ cluster.rg }}
        subscription: ${{ cluster.subscription }}
        ChartName: "deploy/helm/vigilit-ai"
        ReleaseName: vigilit-ai
        Namespace: 'vigilit-ai-${{ cluster.name }}'
        keyVaults: ${{ cluster.keyVaults }}
        valuesOverrideFiles: '${{ cluster.valuesOverrideFile }}'
        replaceSecrets: true
        additionalHelmArgs: '--set image.tag=$(resources.pipeline.build-pipeline.runName)'
        helmTest: true
        pool: pv-pool
