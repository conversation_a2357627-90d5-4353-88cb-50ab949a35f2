﻿using PharmaLex.VigiLit.Web.Controllers.CustomTypeFilters;

namespace PharmaLex.VigiLit.Web.Unit.Tests.Controllers.CustomTypeFilters;

public class AuthorizeOnAnyOnePolicyAttributeTests
{
    [Fact]
    public void Attribute_PolicyArgument_CorrectlySetInArguments()
    {
        // Arrange
        const string policies = "Policy1,Policy2";

        // Act
        var attribute = new AuthorizeOnAnyOnePolicyAttribute(policies);

        // Assert
        Assert.Equal(typeof(AuthorizeOnAnyOnePolicyFilter), attribute.ImplementationType);
        Assert.NotNull(attribute.Arguments);
        Assert.Single(attribute.Arguments);
        Assert.Equal(policies, attribute.Arguments[0]);
    }
}

