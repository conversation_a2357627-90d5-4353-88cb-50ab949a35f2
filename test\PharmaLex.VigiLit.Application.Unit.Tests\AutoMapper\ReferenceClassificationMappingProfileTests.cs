﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.AutoMapper;

public class ReferenceClassificationMappingProfileTests
{
    readonly IMapper _mapper;

    public ReferenceClassificationMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<ReferenceClassificationMappingProfile>());
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Classifier = new User("<PERSON>", "<PERSON>", "<EMAIL>"),
            ReferenceState = ReferenceState.Approved
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationModel>(referenceClassification);

        // Assert
        Assert.Equal("Approved", model.ReferenceStateText);
        Assert.Equal("John Smith", model.PreclassifierName);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationModel_When_no_classifier_returns_null()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationModel>(referenceClassification);

        // Assert
        Assert.Null(model.PreclassifierName);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithReferenceModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Classifier = new User("John", "Smith", "<EMAIL>"),
            ReferenceState = ReferenceState.Approved,
            PSURRelevanceAbstract = PSURRelevanceAbstract.Yes,
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithReferenceModel>(referenceClassification);

        // Assert
        Assert.Equal("Approved", model.ReferenceStateText);
        Assert.Equal("John Smith", model.PreclassifierName);
        Assert.Equal("1", model.PSURRelevanceAbstract);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithReferenceModel_When_no_classifier_returns_null()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithReferenceModel>(referenceClassification);

        // Assert
        Assert.Null(model.PreclassifierName);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithReferenceSupportModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Classifier = new User("John", "Smith", "<EMAIL>"),
            ReferenceState = ReferenceState.Approved,
            LastUpdatedDate = new DateTime(2000, 1, 8, 0, 0, 0, DateTimeKind.Utc),
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithReferenceSupportModel>(referenceClassification);

        // Assert
        Assert.Equal("Approved", model.ReferenceStateText);
        Assert.Equal("John Smith", model.PreclassifierName);
        Assert.Equal("08 January 2000", model.LastUpdatedDate);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithReferenceSupportModel_When_no_classifier_returns_null()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithReferenceSupportModel>(referenceClassification);

        // Assert
        Assert.Null(model.PreclassifierName);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithSubstanceModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Classifier = new User("John", "Smith", "<EMAIL>"),
            ReferenceState = ReferenceState.Approved,
            PSURRelevanceAbstract = PSURRelevanceAbstract.Yes,
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithSubstanceModel>(referenceClassification);

        // Assert
        Assert.Equal("Approved", model.ReferenceStateText);
        Assert.Equal("John Smith", model.PreclassifierName);
        Assert.Equal("Yes", model.PSURRelevanceAbstract);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationWithSubstanceModel_When_no_classifier_returns_null()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationWithSubstanceModel>(referenceClassification);

        // Assert
        Assert.Null(model.PreclassifierName);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_ReferenceClassificationSupportModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Reference = new Reference()
            {
                SourceId = "1",
                Doi = "my doi",
                Title = "my title",
                Abstract = "my abstract",
                Authors = "my authors",
                MeshHeadings = "my mesh headings",
                Keywords = "my keywords",
                AffiliationTextFirstAuthor = "my affiliation text first author",
                FullPagination = "my full pagination",
                Issn = "my issn",
                Issue = "my issue",
                Volume = "my volume",
                PublicationType = "my publication type",
                PublicationYear = 2024,
                JournalTitle = "my journal title"
            },

            ClassificationCategory = new FakeClassificationCategory("my classification category", false),
            Substance = new Substance("my substance", "type"),

            MinimalCriteria = "my minimal criteria",
            CountryOfOccurrence = "my country of occurrence",
            PSURRelevanceAbstract = PSURRelevanceAbstract.Yes,
            DosageForm = "my dosage form",

            CreatedDate = new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc),
            LastUpdatedDate = new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc)
        };

        // Act
        var model = _mapper.Map<ReferenceClassificationSupportModel>(referenceClassification);

        // Assert
        Assert.Equal("1", model.SourceId);
        Assert.Equal("my doi", model.Doi);
        Assert.Equal("my title", model.Title);
        Assert.Equal("my abstract", model.Abstract);
        Assert.Equal("my authors", model.Authors);
        Assert.Equal("my mesh headings", model.MeshTerms);
        Assert.Equal("my keywords", model.Keywords);
        Assert.Equal("my affiliation text first author", model.AffiliationTextFirstAuthor);
        Assert.Equal("my full pagination", model.FullPagination);
        Assert.Equal("my issn", model.Issn);
        Assert.Equal("my issue", model.Issue);
        Assert.Equal("my volume", model.Volume);
        Assert.Equal("my publication type", model.PublicationType);
        Assert.Equal((ushort?)2024, model.PublicationYear);
        Assert.Equal("my journal title", model.JournalTitle);

        Assert.Equal("my classification category", model.ClassificationCategory);
        Assert.Equal("my substance", model.Substance);

        Assert.Equal("my minimal criteria", model.MinimalCriteria);
        Assert.Equal("my country of occurrence", model.CountryOfOccurrence);
        Assert.Equal(PSURRelevanceAbstract.Yes.ToString(), model.PSURRelevanceAbstract);
        Assert.Equal("my dosage form", model.DosageForm);

        Assert.Equal(new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc), model.CreatedDate);
        Assert.Equal(new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc), model.LastUpdatedDate);
    }

    [Fact]
    public void Maps_ReferenceClassification_to_PrintPreviewSearchResultModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Reference = new Reference()
            {
                SourceId = "1",
                Doi = "my doi",
                DateRevised = new DateTime(2024, 03, 06, 0, 0, 0, DateTimeKind.Utc),
                Title = "my title",
                Abstract = "my abstract",
                Authors = "my authors",
                MeshHeadings = "my mesh headings",
                Keywords = "my keywords",
                AffiliationTextFirstAuthor = "my affiliation text first author"
            },

            ClassificationCategory = new FakeClassificationCategory("my classification category", false),
            Substance = new Substance("my substance", "type"),

            MinimalCriteria = "my minimal criteria",
            CountryOfOccurrence = "my country of occurrence",
            PSURRelevanceAbstract = PSURRelevanceAbstract.Yes,
            PvSafetyDatabaseId = "my pv safety database id",
            DosageForm = "my dosage form",

            CreatedDate = new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc),
            LastUpdatedDate = new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc)
        };

        // Act
        var model = _mapper.Map<PrintPreviewSearchResultModel>(referenceClassification);

        // Assert
        Assert.Equal("1", model.SourceId);
        Assert.Equal("my doi", model.Doi);
        Assert.Equal(new DateTime(2024, 03, 06, 0, 0, 0, DateTimeKind.Utc), model.DateRevised);
        Assert.Equal("my title", model.Title);
        Assert.Equal("my abstract", model.Abstract);
        Assert.Equal("my authors", model.Authors);
        Assert.Equal("my mesh headings", model.MeshTerms);
        Assert.Equal("my keywords", model.Keywords);
        Assert.Equal("my affiliation text first author", model.AffiliationTextFirstAuthor);

        Assert.Equal("my classification category", model.ClassificationCategory);
        Assert.Equal("my substance", model.Substance);

        Assert.Equal("my minimal criteria", model.MinimalCriteria);
        Assert.Equal("my country of occurrence", model.CountryOfOccurrence);
        Assert.Equal(PSURRelevanceAbstract.Yes.ToString(), model.PSURRelevanceAbstract);
        Assert.Equal("my pv safety database id", model.PvSafetyDatabaseId);
        Assert.Equal("my dosage form", model.DosageForm);

        Assert.Equal(new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc), model.CreatedDate);
        Assert.Equal(new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc), model.LastUpdatedDate);
    }
}
