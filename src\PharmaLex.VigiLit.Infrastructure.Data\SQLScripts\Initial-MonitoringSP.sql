CREATE PROCEDURE vgtlastscheduledimportduration
AS
DECLARE @startDate datetime2(7)
SET @startDate = (SELECT TOP 1 StartDate FROM [dbo].[Imports] WHERE ImportType = 10 AND ImportTriggerType = 1 AND ImportStatusType IN (30, 40) ORDER BY ID DESC);

DECLARE @endDate datetime2(7);
SET @endDate = (SELECT TOP 1 endDate FROM [dbo].[Imports] WHERE ImportType = 10 AND ImportTriggerType = 1 AND ImportStatusType IN (30, 40) ORDER BY ID DESC);

DECLARE @LastImportTodayInSeconds int
SET @LastImportTodayInSeconds = (SELECT DATEDIFF(ss, @startDate, @endDate))

DECLARE @Today datetime2(7)
SET @Today = (SELECT GETUTCDATE())

DECLARE @LastImportRanBeforeHours int
SET @LastImportRanBeforeHours = (SELECT DATEDIFF(HOUR, @startDate, @Today))

DECLARE @Weekday varchar(10)
SET @Weekday = (SELECT DATENAME(WEEKDAY, GETUTCDATE()))

SELECT (CASE 
		WHEN @LastImportRanBeforeHours < 25 AND @Weekday NOT IN ('Friday', 'Saturday') THEN @LastImportTodayInSeconds
		WHEN @Weekday IN ('Friday', 'Saturday') THEN 1
         ELSE 0
        END) AS LastImportTodayDuration;
GO

CREATE PROCEDURE [dbo].[vgtimports]
AS
SELECT
	Id,
	StartDate,
	EndDate,
	ImportTriggerType,
	ImportStatusType,
	ImportType,
	Datediff(SECOND, startdate, enddate) Duration
FROM [dbo].[Imports]
WHERE DATEDIFF(MM, startDate, GETUTCDATE()) < 1
ORDER BY Id DESC
GO
