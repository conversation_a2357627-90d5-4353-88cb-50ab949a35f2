﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Ui.Controllers;
using PharmaLex.VigiLit.Reports.Apogepha.Domain.Models;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

[Authorize]
[Route("/Reports/[controller]")]
public class ApogephaClientReportController: BaseReportController
{
    private readonly IAccessControlService _accessControlService;
    private readonly IApogephaClientReportService _apogephaClientReportService;

    public ApogephaClientReportController(
                        IAccessControlService accessControlService,
                        IApogephaClientReportService apogephaClientReportService,
                        IUserSessionService userSessionService,
                        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _accessControlService = accessControlService;
        _apogephaClientReportService = apogephaClientReportService;
    }

    [HttpGet]
    public  async Task<IActionResult> Index()
    {
        try
        {
            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            var model = _apogephaClientReportService.GetPageModel();

            return View("../Reports/ApogephaClientReport/Index", model);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("/Reports/[controller]/[action]")]
    public async Task<IActionResult> GetCards([FromBody] CardsRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            var cards = _apogephaClientReportService.GetCards(request.Year);

            return Ok(cards);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("/Reports/[controller]/[action]/{year}/{month}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Download(int year, int month)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            var downloadFile = await _apogephaClientReportService.Download(year, month);
            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }
}