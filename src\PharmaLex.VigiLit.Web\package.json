{"name": "PharmaLex.VigiLit.v2", "description": "demo project", "scripts": {"build": "webpack --config webpack.dev.js", "build-prod": "webpack --config webpack.prod.js", "watch": "webpack --watch --config webpack.dev.js"}, "devDependencies": {"copy-webpack-plugin": "^12.0.2", "eslint": "^9.28.0", "eslint-webpack-plugin": "^4.2.0", "webpack": "^5.97.1", "webpack-cli": "^5.1.4", "webpack-remove-empty-scripts": "^1.0.4"}, "dependencies": {"clean-webpack-plugin": "^4.0.0", "css-loader": "^7.1.2", "diff": "^5.2.0", "mini-css-extract-plugin": "^2.7.5", "postcss-loader": "^7.3.0", "postcss-preset-env": "^8.3.2", "sass": "^1.77.2", "sass-loader": "^13.2.2", "vue": "^3.2.26", "vue-loader": "^17.1.1", "vue-multiselect": "^2.1.7", "vue-style-loader": "^4.1.3"}, "overrides": {"nanoid": "3.3.8"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "private": true}