﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

public class CompanyUserModel : UserModel
{
    public int CompanyId { get; set; }

    public bool Active { get; set; }

    public List<UserEmailPreferenceModel> UserEmailPreferences { get; set; }

    public List<int> EmailPreferenceIds { get; set; }

    public CompanyUserType CompanyUserType { get; set; }



    public CompanyUserModel()
    {
        UserEmailPreferences = new List<UserEmailPreferenceModel>();
        EmailPreferenceIds = new List<int>();
    }
}