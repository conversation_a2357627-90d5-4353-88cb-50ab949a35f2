﻿using AutoMapper;
using Moq;
using PharmaLex.Caching.Data;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class SubstanceServiceTests
{
    private readonly Mock<ISubstanceRepository> _mockSubstanceRepository = new();
    private readonly Mock<ISubstanceSynonymRepository> _mockSubstanceSynonymRepository = new();
    private readonly Mock<IDistributedCacheServiceFactory> _mockDistributedCacheServiceFactory = new();
    private readonly ISubstanceService _substanceService;

    public SubstanceServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new SubstanceMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();

        _substanceService = new SubstanceService(_mockSubstanceRepository.Object, _mockSubstanceSynonymRepository.Object, _mockDistributedCacheServiceFactory.Object, mapper);
    }

    [Fact]
    public async Task GetActiveSubstancesWithNoClassificationsForReference_Returns_ExpectedValue()
    {
        // Arrange
        var substances = new List<SubstanceItemModel>
        {
            new() { Id = 1, Name = "Substance 1" },
            new() { Id = 2, Name = "Substance 2" }
        };

        _mockSubstanceRepository.Setup(x => x.GetActiveSubstancesWithNoClassificationsForReference(1)).ReturnsAsync(substances);

        // Act
        var result = await _substanceService.GetActiveSubstancesWithNoClassificationsForReference(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.Equal(1, result.ToList()[0].Id);
        Assert.Equal("Substance 1", result.ToList()[0].Name);
    }
}
