﻿CREATE OR ALTER PROC dbo.spTrackingSheetReport
/*---------------------------------------------------------------------------------------------------------------------
--
-- Summary:		Returns tracking sheet data for selected customer
--
-- Parameters:	@CompanyId = company ID
--				@StartDate = report start date
--				@EndDate   = report end date
--
-- Notes:		Used by company any company that requires tracking sheets
--
-- Usage:		exec spTrackingSheetReport 1, '2024-01-01', '2024-01-07'
--
---------------------------------------------------------------------------------------------------------------------*/

	@CompanyId int, 
	@StartDate datetime,
	@EndDate datetime
AS

	-- Tracking Sheet
     SELECT   
      Imports.Id As [ImportId],  
      Imports.ImportType As [ImportType],  
      Imports.ImportDate As [ImportDate],  
      Substances.[Name] As [SubstanceName],   
      ContractVersions.[Version] As [ContractVersion],  
      CASE   
       WHEN MIN(ImportContracts.PubMedCreateDateFrom) IS NULL THEN 'Unlimited'  
       ELSE FORMAT(MIN(ImportContracts.PubMedCreateDateFrom),'dd MMM yyyy') + ' - ' + FORMAT(MAX(ImportContracts.ImportDate),'dd MMM yyyy')  
      END As [SearchInterval],  
      SUM(ImportContracts.ReferencesCount) As [Hits],  
      GETUTCDATE() As [CreatedDate],   
      GETUTCDATE() As [LastUpdatedDate],   
      '' As [CreatedBy],   
      '' As [LastUpdatedBy]   
     FROM   
      ImportContracts,   
      Contracts,   
      Substances,   
      ContractVersions,   
      Projects,   
      Companies,   
      Imports   
     WHERE   
      Contracts.Id = ImportContracts.ContractId   
      AND Substances.Id = Contracts.SubstanceId   
      AND ContractVersions.Id = ImportContracts.ContractVersionId   
      AND Projects.Id = Contracts.ProjectId  
      AND Companies.Id = Projects.CompanyId  
      AND Imports.Id = ImportContracts.ImportId  
      AND Companies.Id = @CompanyId -- Filter: Company  
      AND Imports.ImportDate BETWEEN @StartDate AND @EndDate -- Filter: Date Range  
     GROUP BY   
      Substances.[Name],   
      Contracts.Id,   
      ContractVersions.[Version],   
      ContractVersions.ContractWeekday,  
      Imports.Id,  
      Imports.ImportDate,  
      Imports.ImportType  
     ORDER BY   
      Imports.ImportDate,  
      Substances.[Name]  
GO	