# Run Whitesource Scan

parameters:
  - name: SolutionName
    type: string
  - name: DotNetVersion
    type: string
    default: ''
  - name: AzureSubscription
    default: "DevOps Bastion"
  - name: KeyVaultName
    default: "pxv-vault-eun"
  - name: WSS_URL
    default: "https://saas-eu.whitesourcesoftware.com/agent"
  - name: LongLivedBranch
    default: false
  - name: NugetRestore
    default: true
  - name: WhiteSourceProjectName
    default: "$(Build.Repository.Name)_$(Build.SourceBranchName)"
  - name: WhiteSourceProductName
    default: $(System.TeamProject)_$(Build.SourceBranchName)

steps:
  - task: UseDotNet@2
    displayName: Install .NET Version
    condition: and(ne('${{ parameters.DotNetVersion }}', ''), eq(variables['Build.Reason'], 'Schedule'))
    inputs:
      version: ${{ parameters.DotNetVersion }}
      packageType: sdk
      installationPath: $(Agent.ToolsDirectory)/dotnet
  - task: DotNetCoreCLI@2
    displayName: "Nuget Restore"
    condition: and(succeeded(), eq(${{ parameters.NugetRestore }}, true), eq(variables['Build.Reason'], 'Schedule'))
    inputs:
      projects: '${{ parameters.SolutionName }}'
      command: 'restore'
      feedsToUse: 'select'
      vstsFeed: '780c2041-bb89-4053-a6e6-13d4c16ae672'
  - task: WhiteSource@21
    displayName: Mend Extension Scan
    condition: and(succeeded(), eq(${{ parameters.LongLivedBranch }}, false), eq(variables['Build.Reason'], 'Schedule'))
    inputs:
      cwd: '$(System.DefaultWorkingDirectory)'
      projectName: '$(Build.Repository.Name)_latest_build'
      configuration: |
       scanComment=$(Build.Repository.Name)_latest_build
       excludes=**/.*,**/node_modules,**/test,**/src/test,**/testdata,**/*sources.jar,**/*javadoc.jar,**/dotnet-reportgenerator-globaltool/**
       log.level=error
       log.files.level=Error
  - task: AzureKeyVault@2
    displayName: "Retrieve Secrets from KeyVault ${{ parameters.KeyVaultName }}"
    condition: and(succeeded(), eq(${{ parameters.LongLivedBranch }}, true), eq(variables['Build.Reason'], 'Schedule'))
    inputs:
      azureSubscription: "${{ parameters.AzureSubscription }}"
      KeyVaultName: "${{ parameters.KeyVaultName }}"
      SecretsFilter: 'whitesource-api-key,whitesource-user-key'
      RunAsPreJob: false 
  - script: |
      curl -LJO https://unified-agent.s3.amazonaws.com/wss-unified-agent.jar
      echo Unified Agent downloaded successfully
      java -jar wss-unified-agent.jar
    env:
      WS_APIKEY: $(whitesource-api-key)
      WS_USERKEY: $(whitesource-user-key)
      WS_WSS_URL: ${{ parameters.WSS_URL }}
      WS_PRODUCTNAME: ${{ parameters.WhiteSourceProductName }}
      WS_PROJECTNAME: ${{ parameters.WhiteSourceProjectName }}
      WS_SCANCOMMENT: $(Build.Repository.Name)_$(Build.SourceBranchName)
      WS_EXCLUDES: excludes=**/.*,**/node_modules,**/test,**/src/test,**/testdata,**/*sources.jar,**/*javadoc.jar,**/dotnet-reportgenerator-globaltool/**
    displayName: Mend Unified Agent Scan
    condition: and(succeeded(), eq(${{ parameters.LongLivedBranch }}, true), eq(variables['Build.Reason'], 'Schedule'))