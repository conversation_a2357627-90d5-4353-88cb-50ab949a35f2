﻿namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests;

public class DataExtractionServiceTests
{
    private readonly IDataExtractionService _dataExtractionService;

    public DataExtractionServiceTests()
    {
        _dataExtractionService = new DataExtractionService();
    }

    //[Fact]
    //public async Task GetDoIdDetails_Returns_True_When_DoiIdMatch()
    //{
    //    // Arrange
    //    _mockReferenceRepository.Setup(x => x.DoesDoiExist("10.1002/ece3.5646")).Returns(Task.FromResult(true));

    //    // Act
    //    var result = await _dataExtractionService.IsDoiExisting("10.1002/ece3.5646");

    //    // Assert
    //    Assert.True(result);

    //}

    //[Fact]
    //public async Task GetDoIdDetails_Returns_False_When_DoiIdNotMatching()
    //{
    //    // Arrange
    //    _mockReferenceRepository.Setup(x => x.DoesDoiExist("10.007/ece3.5646")).Returns(Task.FromResult(false));

    //    // Act
    //    var result = await _dataExtractionService.IsDoiExisting("10.1002/ece3.5646");

    //    // Assert
    //    Assert.False(result);

    //}
}