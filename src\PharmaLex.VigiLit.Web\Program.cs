﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using PharmaLex.Core.Configuration;
using System.Reflection;

namespace PharmaLex.VigiLit.Web;

public static class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .AddConfigurationIncludingKeyVault(Assembly.GetExecutingAssembly(), args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
            });
}
