# Build a .NET solution with dotnet, analyze code, run tests and analyse test coverage

parameters:
  NugetSource: ''
  BuildConfiguration: 'Release'
  VersionNumber: '1.0.0.0'
  SolutionName: ''
  TestProjects: ''
  TestOptions: '/p:Include="[*]*"'
  SonarProjectKey: ''
  SonarProjectName: ''
  AnalysePackages: false
  LongLivedBranch: false
  SonarExclusions: '**/test/**'
  ReportGeneratorVersion: ''
  AnalyseProjectName: '$(Build.Repository.Name)'
  CheckmarxProduct: '$(System.TeamProject)'
  CheckmarxTeam: 'Unset'
  CheckmarxProjectName: '$(System.TeamProject)_$(Build.Repository.Name)'
  CheckmarxEnabled: false
  CheckmarxFileFilter: '!test,!tests,!temp,!node_modules,!.github,!docker-compose*'


steps:
  - task: DotNetCoreCLI@2
    displayName: 'NuGet Authenticate'
    condition: ne('${{ parameters.NugetSource }}', '')
    inputs:
      command: 'custom'
      custom: 'nuget'
      arguments: 'add source ${{ parameters.NugetSource }} -n Phlexglobal -u ignore -p $(System.AccessToken) --store-password-in-clear-text'
  - task: SonarCloudPrepare@3
    displayName: 'Prepare SonarCloud'
    inputs:
        SonarQube: 'SonarCloud'
        organization: 'phlexglobal'
        scannerMode: 'dotnet'
        projectKey: '${{ parameters.SonarProjectKey }}'
        projectName: '${{ parameters.SonarProjectName }}'
        projectVersion: '${{ parameters.VersionNumber }}'
        extraProperties: |
         sonar.coverageReportPaths=$(Build.SourcesDirectory)/TestResults/sonarcloud.xml
         sonar.test.exclusions='${{ parameters.SonarExclusions }}'
  - template: analyse.yml
    parameters:
      LongLivedBranch: '${{ parameters.LongLivedBranch }}'
      SolutionName: '${{ parameters.SolutionName }}'

  - task: DotNetCoreCLI@2
    displayName: 'Build'
    inputs:
      command: 'build'
      projects: '${{ parameters.SolutionName }}'
      arguments: '--configuration ${{ parameters.BuildConfiguration }} -p:Version=${{ parameters.VersionNumber }}'
  - task: DotNetCoreCLI@2
    displayName: 'Run Unit Tests'
    enabled: true
    inputs:
      command: 'test'
      buildProperties: 
      publishTestResults: false
      arguments: '--configuration ${{ parameters.BuildConfiguration }} ${{ parameters.TestOptions }} --logger "xunit" --collect "XPlat Code coverage"'
      nobuild: true
      projects: '${{ parameters.TestProjects }}'
  - task: PublishTestResults@2
    displayName: 'Publishing Test Results'
    inputs:
       testResultsFormat: 'XUnit'
       testResultsFiles: '$(Build.SourcesDirectory)/**/*TestResults.xml'
  - task: Bash@3
    displayName: 'Install report generator'
    inputs:
       targetType: 'inline'
       script: |
             echo "### Install report generator..."
             if [ -z "${{ parameters.ReportGeneratorVersion }}" ]; then
                echo "### No version specified, using latest..."
                dotnet tool install dotnet-reportgenerator-globaltool --tool-path .
             else
                echo "### Version specified, using ${{ parameters.ReportGeneratorVersion }}..."
                dotnet tool install dotnet-reportgenerator-globaltool --tool-path . --version ${{ parameters.ReportGeneratorVersion }}
             fi
  - task: Bash@3
    displayName: 'Combine code coverge reports '
    inputs:
        targetType: 'inline'
        script: |
          ./reportgenerator -v:Verbose "-reports:$(Build.SourcesDirectory)/**/*.cobertura.xml" "-targetdir:$(Build.SourcesDirectory)/TestResults" "-reporttypes:Cobertura"
          ls -l $BUILD_SOURCESDIRECTORY/TestResults 
  - task: Bash@3
    displayName: 'Convert cobertura coverage to SonarCloud Format'
    inputs:
        targetType: 'inline'
        script: |
          ./reportgenerator -v:Verbose "-reports:$(Build.SourcesDirectory)/TestResults/Cobertura.xml" "-targetdir:$(Build.SourcesDirectory)/TestResults" "-reporttypes:SonarQube"
          echo "### Move converted report to results directory..."
          mv $BUILD_SOURCESDIRECTORY/TestResults/SonarQube.xml $BUILD_SOURCESDIRECTORY/TestResults/sonarcloud.xml
          ls $BUILD_SOURCESDIRECTORY/TestResults
  - task: SonarCloudAnalyze@3
    displayName: 'Analyze with SonarCloud'
    inputs:
      jdkversion: 'JAVA_HOME_17_X64'
  - task: SonarCloudPublish@3
    displayName: 'Publish Quality Gate Result'
    inputs:
      pollingTimeoutSec: '300'
  - task: PublishCodeCoverageResults@2
    displayName: "Publishing Code Coverage"
    enabled: true
    inputs:
      codeCoverageTool: 'Cobertura'
      summaryFileLocation: '$(Build.SourcesDirectory)/TestResults/Cobertura.xml'
  - template: Checkmarx/checkmarx-scan.yml@templates
    parameters:
      Product: '${{ parameters.CheckmarxProduct }}'
      Team: '${{ parameters.CheckmarxTeam }}'
      ProjectName: '${{ parameters.CheckmarxProjectName }}' 
      FileFilter: '${{ parameters.CheckmarxFileFilter }}' 
      ScanEnabled: ${{ and(ne(parameters.CheckmarxEnabled, false), or(eq(parameters.LongLivedBranch, true), eq(parameters.AnalysePackages, true), eq(parameters.CheckmarxEnabled, true))) }}
