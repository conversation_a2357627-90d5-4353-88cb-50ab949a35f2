﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35122.118
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{E923269D-C6A3-4B44-AA74-6780194155EE}"
	ProjectSection(SolutionItems) = preProject
		azure-pipelines-ai.yml = azure-pipelines-ai.yml
		README-AI-FEATURE.md = README-AI-FEATURE.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{31802168-187C-497B-BCC9-18FA51D0F154}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{F7AAEF81-3A48-4F82-A746-02131764C40A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Client", "src\PharmaLex.VigiLit.AiAnalysis.Client\PharmaLex.VigiLit.AiAnalysis.Client.csproj", "{BF315095-4348-4E10-A24F-08F273CBFC92}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Entities", "src\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj", "{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Service", "src\PharmaLex.VigiLit.AiAnalysis.Service\PharmaLex.VigiLit.AiAnalysis.Service.csproj", "{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.MessageBroker.Contracts", "src\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj", "{392BD804-B90E-4C32-BEA1-79A4B1B827D7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.DataAccessLayer", "src\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj", "{377A3656-0E99-4325-B4CA-BB306293AF2A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.Framework", "test\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj", "{9D94D149-2F09-4EFC-8B9E-6782447B9838}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore", "test\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore.csproj", "{FB461D08-D46D-41B4-948C-4C179D3C2DB5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Integration.Tests", "test\PharmaLex.VigiLit.AiAnalysis.Integration.Tests\PharmaLex.VigiLit.AiAnalysis.Integration.Tests.csproj", "{1390829A-CFCE-495A-9A49-6F360C99050A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{6B091BEC-B109-4116-8BD3-47BF7B7A736C}"
	ProjectSection(SolutionItems) = preProject
		docs\ai-architecture-overview.png = docs\ai-architecture-overview.png
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Infrastructure.Data", "src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj", "{66FAF850-CC59-433D-BA2A-8BF4B194E565}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Domain", "src\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj", "{DCE5F54E-D3A7-420E-B600-BB896AF067B6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.Core.Configuration", "src\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj", "{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.AiAnalysis.Unit.Tests", "test\PharmaLex.VigiLit.AiAnalysis.Unit.Tests\PharmaLex.VigiLit.AiAnalysis.Unit.Tests.csproj", "{E244883C-C74B-43A3-A367-CB840BCB9109}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|x64.Build.0 = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Debug|x86.Build.0 = Debug|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|x64.ActiveCfg = Release|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|x64.Build.0 = Release|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|x86.ActiveCfg = Release|Any CPU
		{BF315095-4348-4E10-A24F-08F273CBFC92}.Release|x86.Build.0 = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|x64.Build.0 = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Debug|x86.Build.0 = Debug|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|x64.ActiveCfg = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|x64.Build.0 = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|x86.ActiveCfg = Release|Any CPU
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F}.Release|x86.Build.0 = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|x64.Build.0 = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Debug|x86.Build.0 = Debug|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|x64.ActiveCfg = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|x64.Build.0 = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|x86.ActiveCfg = Release|Any CPU
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC}.Release|x86.Build.0 = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|x64.Build.0 = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Debug|x86.Build.0 = Debug|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|x64.ActiveCfg = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|x64.Build.0 = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|x86.ActiveCfg = Release|Any CPU
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7}.Release|x86.Build.0 = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|x64.Build.0 = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Debug|x86.Build.0 = Debug|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|x64.ActiveCfg = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|x64.Build.0 = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|x86.ActiveCfg = Release|Any CPU
		{377A3656-0E99-4325-B4CA-BB306293AF2A}.Release|x86.Build.0 = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|x64.Build.0 = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Debug|x86.Build.0 = Debug|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|Any CPU.Build.0 = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|x64.ActiveCfg = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|x64.Build.0 = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|x86.ActiveCfg = Release|Any CPU
		{9D94D149-2F09-4EFC-8B9E-6782447B9838}.Release|x86.Build.0 = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|x64.Build.0 = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Debug|x86.Build.0 = Debug|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|x64.ActiveCfg = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|x64.Build.0 = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|x86.ActiveCfg = Release|Any CPU
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5}.Release|x86.Build.0 = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|x64.Build.0 = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Debug|x86.Build.0 = Debug|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|Any CPU.Build.0 = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|x64.ActiveCfg = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|x64.Build.0 = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|x86.ActiveCfg = Release|Any CPU
		{1390829A-CFCE-495A-9A49-6F360C99050A}.Release|x86.Build.0 = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|x64.ActiveCfg = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|x64.Build.0 = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|x86.ActiveCfg = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Debug|x86.Build.0 = Debug|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|Any CPU.Build.0 = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|x64.ActiveCfg = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|x64.Build.0 = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|x86.ActiveCfg = Release|Any CPU
		{66FAF850-CC59-433D-BA2A-8BF4B194E565}.Release|x86.Build.0 = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|x64.Build.0 = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Debug|x86.Build.0 = Debug|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|x64.ActiveCfg = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|x64.Build.0 = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|x86.ActiveCfg = Release|Any CPU
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6}.Release|x86.Build.0 = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|x64.Build.0 = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Debug|x86.Build.0 = Debug|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|Any CPU.Build.0 = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|x64.ActiveCfg = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|x64.Build.0 = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|x86.ActiveCfg = Release|Any CPU
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6}.Release|x86.Build.0 = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|x64.Build.0 = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Debug|x86.Build.0 = Debug|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|Any CPU.Build.0 = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|x64.ActiveCfg = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|x64.Build.0 = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|x86.ActiveCfg = Release|Any CPU
		{E244883C-C74B-43A3-A367-CB840BCB9109}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{BF315095-4348-4E10-A24F-08F273CBFC92} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{E6CA4A9D-9242-4301-B972-4AC3B404CD2F} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{FFD568F1-64A6-4BEA-90DD-C48E89A541CC} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{392BD804-B90E-4C32-BEA1-79A4B1B827D7} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{377A3656-0E99-4325-B4CA-BB306293AF2A} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{9D94D149-2F09-4EFC-8B9E-6782447B9838} = {F7AAEF81-3A48-4F82-A746-02131764C40A}
		{FB461D08-D46D-41B4-948C-4C179D3C2DB5} = {F7AAEF81-3A48-4F82-A746-02131764C40A}
		{1390829A-CFCE-495A-9A49-6F360C99050A} = {F7AAEF81-3A48-4F82-A746-02131764C40A}
		{66FAF850-CC59-433D-BA2A-8BF4B194E565} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{DCE5F54E-D3A7-420E-B600-BB896AF067B6} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{508D474F-5A73-AB31-0D55-3AA05CEBD5A6} = {31802168-187C-497B-BCC9-18FA51D0F154}
		{E244883C-C74B-43A3-A367-CB840BCB9109} = {F7AAEF81-3A48-4F82-A746-02131764C40A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {842C8E36-213C-45B3-BB62-3CB768AFDD2C}
	EndGlobalSection
EndGlobal
