using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class URL
{
    [XmlAttribute()]
    public URLLang lang { get; set; }

    [XmlIgnore()]
    public bool langSpecified { get; set; }

    [XmlAttribute()]
    public URLType Type { get; set; }

    [XmlIgnore()]
    public bool TypeSpecified { get; set; }

    [XmlText()]
    public string Value { get; set; }
}