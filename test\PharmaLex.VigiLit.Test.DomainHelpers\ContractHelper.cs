﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System.Diagnostics;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class ContractHelper
{
    readonly IMapper _mapper;
    private readonly IContractRepository _contractRepository;

    public ContractHelper(IContractRepository contractRepository)
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<ContractVersionProfile>());
        _contractRepository = contractRepository;
        _mapper = config.CreateMapper();
    }

    public async Task<Contract> AddContract(User user, Substance substance, Project project, bool active, int version, int screeningType)
    {
        var (contract, contractVersionPending) = AddContractDetails(user, substance, project, active, version, screeningType);
        AddContractVersion(contract, contractVersionPending);
        _contractRepository.Add(contract);
        return await SaveContract(contract);
    }

    public async Task<Contract> UpdateContract(User user, Contract model, bool active)
    {
        var (contract, contractVersionPending) = await GetContractDetails(user, model, active);
        AddContractVersion(contract, contractVersionPending);
        return await SaveContract(contract);
    }

    private void AddContractVersion(Contract contract, ContractVersionEditModel contractVersionPending)
    {
        var contractVersion = _mapper.Map<ContractVersion>(contractVersionPending);
        contractVersion.SetContractVersion(ContractVersionStatus.Approved);
        contract.AddContractVersion(contractVersion);
    }

    public async Task<Contract> SaveContract(Contract contract)
    {
        await _contractRepository.SaveChangesAsync();
        contract.CurrentContractVersionId = contract.GetCurrentContractVersion().Id;
        await _contractRepository.SaveChangesAsync();
        return contract;
    }

    private async Task<(Contract, ContractVersionEditModel)> GetContractDetails(User user, Contract model, bool active)
    {
        var contract = await _contractRepository.GetByIdAsync(model.Id);
        var currentContractVersion = model.GetCurrentContractVersion();
        Debug.Assert(contract != null, nameof(contract) + " != null");
        contract.GetContractVersion(currentContractVersion.Version)?.SetContractVersion(ContractVersionStatus.History);

        var contractVersionPending = new ContractVersionEditModel()
        {
            Version = currentContractVersion.Version + 1,
            UserId = user.Id,
            TimeStamp = DateTime.UtcNow,
            IsActive = active
        };
        return (contract, contractVersionPending);
    }

    private static (Contract, ContractVersionEditModel) AddContractDetails(User user, Substance substance, Project project, bool active, int version, int screeningType)
    {
        var contract = new Contract(substance.Id, project.Id);
        var contractVersionPending = new ContractVersionEditModel()
        {
            Version = version,
            UserId = user.Id,
            TimeStamp = DateTime.UtcNow,
            IsActive = active,
            ScreeningType = screeningType
        };
        return (contract, contractVersionPending);
    }
}
