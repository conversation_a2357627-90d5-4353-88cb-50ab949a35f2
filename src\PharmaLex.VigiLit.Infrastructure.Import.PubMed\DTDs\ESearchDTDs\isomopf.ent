
<!--
     File isomopf.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     Entity names in this file are derived from files carrying the
     following notice:

     (C) International Organization for Standardization 1991
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.

-->

<!ENTITY % plane1D  "&#38;#38;#x1D">

<!ENTITY Aopf             "%plane1D;538;" ><!--/Bbb A, open face A -->
<!ENTITY Bopf             "%plane1D;539;" ><!--/Bbb B, open face B -->
<!ENTITY Copf             "&#x02102;" ><!--/Bbb C, open face C -->
<!ENTITY Dopf             "%plane1D;53B;" ><!--/Bbb D, open face D -->
<!ENTITY Eopf             "%plane1D;53C;" ><!--/Bbb E, open face E -->
<!ENTITY Fopf             "%plane1D;53D;" ><!--/Bbb F, open face F -->
<!ENTITY Gopf             "%plane1D;53E;" ><!--/Bbb G, open face G -->
<!ENTITY Hopf             "&#x0210D;" ><!--/Bbb H, open face H -->
<!ENTITY Iopf             "%plane1D;540;" ><!--/Bbb I, open face I -->
<!ENTITY Jopf             "%plane1D;541;" ><!--/Bbb J, open face J -->
<!ENTITY Kopf             "%plane1D;542;" ><!--/Bbb K, open face K  -->
<!ENTITY Lopf             "%plane1D;543;" ><!--/Bbb L, open face L  -->
<!ENTITY Mopf             "%plane1D;544;" ><!--/Bbb M, open face M  -->
<!ENTITY Nopf             "&#x02115;" ><!--/Bbb N, open face N -->
<!ENTITY Oopf             "%plane1D;546;" ><!--/Bbb O, open face O -->
<!ENTITY Popf             "&#x02119;" ><!--/Bbb P, open face P -->
<!ENTITY Qopf             "&#x0211A;" ><!--/Bbb Q, open face Q -->
<!ENTITY Ropf             "&#x0211D;" ><!--/Bbb R, open face R -->
<!ENTITY Sopf             "%plane1D;54A;" ><!--/Bbb S, open face S -->
<!ENTITY Topf             "%plane1D;54B;" ><!--/Bbb T, open face T -->
<!ENTITY Uopf             "%plane1D;54C;" ><!--/Bbb U, open face U -->
<!ENTITY Vopf             "%plane1D;54D;" ><!--/Bbb V, open face V -->
<!ENTITY Wopf             "%plane1D;54E;" ><!--/Bbb W, open face W -->
<!ENTITY Xopf             "%plane1D;54F;" ><!--/Bbb X, open face X -->
<!ENTITY Yopf             "%plane1D;550;" ><!--/Bbb Y, open face Y -->
<!ENTITY Zopf             "&#x02124;" ><!--/Bbb Z, open face Z -->
