﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.AutoMapper;

public class CompanyUserMappingProfileTests
{
    readonly IMapper _mapper;

    public CompanyUserMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CompanyUserMappingProfile>();
        });
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_CompanyUser_To_CompanyUserTableModel()
    {
        // Arrange

        DateTime lastLoginDate = new DateTime(2024, 1, 24, 00, 00, 00, DateTimeKind.Utc);
        DateTime activationExpiryDate = new DateTime(2024, 1, 30, 00, 00, 00, DateTimeKind.Utc);

        var fakeUser = new FakeUser("Given Name", "Family Name", "<EMAIL>", 1, lastLoginDate, activationExpiryDate)
        {
            EmailSuppression = new EmailSuppression
            {
                EmailSuppressionType = EmailSuppressionType.Block
            }
        };

        var companyUser = new CompanyUser
        {
            Active = true,
            User = fakeUser
        };

        // Act
        var model = _mapper.Map<CompanyUserTableModel>(companyUser);

        // Assert
        Assert.True(model.Active);
        Assert.Equal("Family Name", model.FamilyName);
        Assert.Equal("Given Name", model.GivenName);
        Assert.Equal("Block", model.EmailSuppressionName);
        Assert.Equal(lastLoginDate.ToString("dd MMMM yyyy HH:mm"), model.DisplayLastLoginDate);
        Assert.Equal(activationExpiryDate.ToString("dd MMMM yyyy HH:mm"), model.DisplayActivationExpiryDate);
    }
}
