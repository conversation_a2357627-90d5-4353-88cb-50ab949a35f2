﻿@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@model PharmaLex.VigiLit.Ui.ViewModels.CaseManagement.CaseSearchPageModel

@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "Case Files";
}

<div class="sub-header">
    <h2>Case Files</h2>
</div>

<div id="cases" v-cloak>
    @Html.AntiForgeryToken()

    <div class="horizontal-filter" autocomplete="off">
        <div class="form-group horizontal-filter-item">
            <label for="classSearch">PLX ID</label>
            <div class="input-group">
                <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                <input type="search" id="classSearch" name="classSearch" class="pl-4 white-background" style="width: 300px" placeholder="PLXID " v-model='searchReq.plxId'/>
            </div>
        </div>

        <div class="form-group horizontal-filter-item">
            <label for="substance">Substance</label>
            <div class="input-group custom-select">
                <select id="substance" name="substance" class="white-background" style="width: 250px" v-model='searchReq.substanceId'>
                    <option value='0'>-- Select Substance --</option>
                    <option v-for='substance in substances' :value='substance.id'>{{ substance.name }}</option>
                </select>
            </div>
        </div>

        <div class="form-group horizontal-filter-item">
            <label for="dateFrom">Created From</label>
            <div class="input-group">
                <input type="date" id="dateFrom" name="dateFrom" class="white-background" placeholder="Date From" style="width: 150px;" v-model='searchReq.createdFrom' v-on:change="onCreatedFromDateChanged">
            </div>
        </div>

        <div class="form-group horizontal-filter-item">
            <label for="dateTo">Created To</label>
            <div class="input-group">
                <input type="date" id="dateTo" name="dateTo" class="white-background" placeholder="Date To" style="width: 150px;" v-model='searchReq.createdTo' v-on:change="onCreatedToDateChanged">
            </div>
        </div>

        <div class="form-group horizontal-filter-item">
            <button type="button" id="btnClassSearch" name="btnClassSearch" class="btn btn-spinner" @@click="onSearch">
                <span class="btn-label">Search</span>
                <span class="spinner"></span>
            </button>
        </div>

        <div class="form-group horizontal-filter-item">
            <a class="button reset-button" @@click="onReset">Reset</a>
        </div>
    </div>
    <hr />
    <section>
        <filtered-table v-if="caseData !== null" :items="caseData" :columns="columns" :link="casesSearchLink"></filtered-table>
    </section>
</div>

@section Scripts {
    <script type="text/javascript">

        let model = @Html.Raw(AntiXss.ToJson(Model));

        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

        var pageConfig = {
            appElement: "#cases",
            data: function () {
                return {
                    caseData: null,
                    substances: model.substances,
                    casesSearchLink: '/Cases/',
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'plxId',
                                sortKey: 'plxId',
                                header: 'PLX ID',
                                type: 'number'
                            },
                            {
                                dataKey: 'noOfFiles',
                                sortKey: 'noOfFiles',
                                header: 'No of Files',
                                type: 'number'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                type: 'text'
                            },
                            {
                                dataKey: 'company',
                                sortKey: 'company',
                                header: 'Company',
                                type: 'text'
                            },
                            {
                                dataKey: 'psur',
                                sortKey: 'psur',
                                header: 'PSUR',
                                type: 'text',
                                isYesNo: true
                            },
                            {
                                dataKey: 'mlm',
                                sortKey: 'mlm',
                                header: 'MLM',
                                type: 'text',
                                isYesNo: true

                            },
                            {
                                dataKey: 'pvSafetyDatabaseId',
                                sortKey: 'pvSafetyDatabaseId',
                                header: 'PV Safety Database ID',
                                type: 'text'
                            },
                            {
                                dataKey: 'createdDate',
                                sortKey: 'createdDate',
                                header: 'Created On',
                                type: 'text'
                            },
                            {
                                dataKey: 'lastUpdatedBy',
                                sortKey: 'lastUpdatedBy',
                                header: 'Modified By',
                                type: 'text'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'substance',
                            options: [],
                            type: 'search',
                            header: 'Search Substance',
                            fn: v => p => p.substance.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    searchReq: {
                        plxId: null,
                        substanceId: 0,
                        createdFrom: null,
                        createdTo: null
                    }
                };
            },
            methods: {
                onSearch: function () {
                    this.caseData = null;
                    $("#btnClassSearch").attr("data-loading", "");
                    // Remember search term(s)
                    localStorage.prevCasesInternalSearchReq = JSON.stringify(this.searchReq);
                    //Property to force recalc when local storage is updated
                    this.refreshSearchReq++;
                    let url = '/Cases/PublishedSearch';

                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(this.searchReq),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.caseData = data;
                        $("#btnClassSearch").removeAttr('data-loading');
                    }).catch(error => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        $("#btnClassSearch").removeAttr('data-loading');
                    });
                },
                onReset: function () {
                    localStorage.removeItem('prevCasesInternalSearchReq');
                    this.searchReq.plxId = null;
                    this.searchReq.substanceId = 0;
                    this.searchReq.createdFrom = moment().subtract(1, 'months').format('YYYY-MM-DD');
                    this.searchReq.createdTo = null;
                    window.location.reload();
                },
                onCreatedFromDateChanged: function (e) {
                    //Revert date to null when clearing date input control which in turn binds an empty string value to our model
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdFrom == '') {
                        this.searchReq.createdFrom = null;
                    }
                },
                onCreatedToDateChanged: function (e) {
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdTo == '') {
                        this.searchReq.createdTo = null;
                    }
                }
            },
            mounted() {
                window.addEventListener('keyup', event => {
                    if (event.keyCode === 13) {
                        this.onSearch();
                    }
                });
                window.addEventListener('pageshow', event => {
                    LocalStorageValidator.clearJsonItemIfInvalid('prevCasesInternalSearchReq', this.searchReq);
                    if (localStorage.prevCasesInternalSearchReq) {
                        this.searchReq = JSON.parse(localStorage.prevCasesInternalSearchReq);
                    }
                    this.onSearch();
                })
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}