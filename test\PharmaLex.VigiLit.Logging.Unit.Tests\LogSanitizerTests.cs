namespace PharmaLex.VigiLit.Logging.Unit.Tests;

public class LogSanitizerTests
{
    [Fact]
    public void Given_input_is_empty_Return_empty_string()
    {
        // Arrange
        // Act
        var result = LogSanitizer.Sanitize(string.Empty);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void Given_input_contains_linefeed_Return_same_linefeed_removed()
    {
        // Arrange
        // Act
        var result = LogSanitizer.Sanitize("Some text\rWith linefeeds\r");

        // Assert
        Assert.Equal("Some textWith linefeeds", result);
    }

    [Fact]
    public void Given_input_contains_newlines_Return_same_newlines_removed()
    {
        // Arrange
        // Act
        var result = LogSanitizer.Sanitize("Some text\nWith new lines\n");

        // Assert
        Assert.Equal("Some textWith new lines", result);
    }

    [Fact]
    public void Given_input_contains_tabs_Return_same_tabs_removed()
    {
        // Arrange
        // Act
        var result = LogSanitizer.Sanitize("Some text\tWith tabs\t");

        // Assert
        Assert.Equal("Some textWith tabs", result);
    }

    [Fact]
    public void Given_input_contains_some_control_characters_Return_same_with_control_characters_removed()
    {
        // Arrange
        // Act
        var result = LogSanitizer.Sanitize("Some text\a\vWith characters removed");

        // Assert
        Assert.Equal("Some textWith characters removed", result);
    }
}