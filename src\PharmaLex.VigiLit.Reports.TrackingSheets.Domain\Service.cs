﻿using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.Models.Document.TrackingSheet;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;
using System.Globalization;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class Service : IService
{
    private readonly ILogger<Service> _logger;
    private readonly IReportingCompanyRepository _companyRepository;
    private readonly ITrackingSheetPdfGenerator _trackingSheetPdfGenerator;
    private readonly IRepository _trackingSheetRepository;
    private readonly ITrackingSheetDocumentService _trackingSheetDocumentService;
    private readonly IUserRepository<User> _userRepository;

    public Service(
        ILoggerFactory loggerFactory,
        IReportingCompanyRepository companyRepository,
        ITrackingSheetPdfGenerator trackingSheetPdfGenerator,
        IRepository trackingSheetRepository,
        ITrackingSheetDocumentService trackingSheetDocumentService,
        IUserRepository<User> userRepository)
    {
        _logger = loggerFactory.CreateLogger<Service>();
        _companyRepository = companyRepository;
        _trackingSheetPdfGenerator = trackingSheetPdfGenerator;
        _trackingSheetRepository = trackingSheetRepository;
        _trackingSheetDocumentService = trackingSheetDocumentService;
        _userRepository = userRepository;
    }

    public async Task<PageModel> GetTrackingSheetsPageModel(User user)
    {
        var companies = await _companyRepository.GetCompanyItems(user);
        var years = GetYears();
        var selectedYear = years.Max();
        var cards = await GetCards(user, 0, selectedYear);

        return new PageModel
        {
            Companies = companies,
            Years = years,
            SelectedYear = selectedYear,
            Cards = cards
        };
    }

    public async Task<IEnumerable<CardModel>> GetCards(User user, int companyId, int year)
    {
        if (!GetYears().Contains(year))
        {
            throw new ArgumentException("Invalid year.");
        }

        // When a non-company user first loads the page
        // they will have no company selected so should see no cards.
        if ((!user.IsCompanyUser()) && companyId <= 0)
        {
            return new List<CardModel>();
        }

        // A company user will not have a company dropdown to select from
        // so just use the company they belong to.
        if (user.IsCompanyUser() && user.HasActiveCompany())
        {
            companyId = user.GetActiveCompanyId();
        }

        // We haven't been able to resolve a companyId, so give up.
        if (companyId <= 0)
        {
            return new List<CardModel>();
        }

        return await CreateCards(companyId, year);
    }

    public async Task<DownloadFile> Download(int trackingSheetId)
    {
        TrackingSheet trackingSheet = await _trackingSheetRepository.GetById(trackingSheetId);

        var pdfBytes = await RetrieveDocument(trackingSheet);

        return new DownloadFile()
        {
            FileName = trackingSheet.FileName,
            ContentType = "application/pdf",
            Bytes = pdfBytes
        };
    }

    public async Task Create(CreateRequest request)
    {
        if (!GetYears().Contains(request.Year))
        {
            throw new ArgumentException("Invalid year.");
        }

        if (request.Week < 1 || request.Week > 53)
        {
            throw new ArgumentException("Invalid week.");
        }

        var company = await _companyRepository.GetByIdAsync(request.CompanyId);

        if (company == null)
        {
            throw new ArgumentException("Company not found.");
        }

        if (await _trackingSheetRepository.Exists(company.Id, request.Year, request.Week))
        {
            _logger.LogInformation("Tracking sheet already exists. Company: {companyName}, CompanyId: {companyId}, Year: {year}, Week: {week}.", company.Name, company.Id, request.Year, request.Week);
            return;
        }

        var pdfBytes = await GeneratePdf(company, request.Year, request.Week);

        var fileName = $"TrackingSheet_{request.Year}_{request.Week}.pdf";
        await StoreDocument(company.Id, request.Year, request.Week, fileName, pdfBytes);

        var trackingSheet = new TrackingSheet()
        {
            CompanyId = company.Id,
            Year = request.Year,
            Week = request.Week,
            FileName = fileName,
            FileSize = pdfBytes.Length
        };

        _trackingSheetRepository.Add(trackingSheet);

        await _trackingSheetRepository.SaveChangesAsync();

        _logger.LogInformation("Tracking sheet created. Company: {companyName}, CompanyId: {companyId}, Year: {year}, Week: {week}.", company.Name, company.Id, request.Year, request.Week);
    }

    public async Task CreateTrackingSheetsForLastWeek()
    {
        var date = GetReportDate();

        var year = ISOWeek.GetYear(date);
        var week = ISOWeek.GetWeekOfYear(date);

        var companies = await _companyRepository.GetActiveCompanies();

        _logger.LogInformation("Found {count} companies to generate tracking sheets for. Year: {year}, Week: {week}.", companies.Count(), year, week);

        foreach (var company in companies)
        {
            CreateRequest request = new()
            {
                CompanyId = company.Id,
                Year = year,
                Week = week
            };

            await Create(request);
        }
    }

    /// <summary>
    /// Gets the report date. This is the last completed Sunday. Reporting weeks are Mon-Sun and must be a complete week.
    /// </summary>
    /// <returns>The report date.</returns>
    private static DateTime GetReportDate()
    {
        var date = DateTime.UtcNow;

        // yesterday is the last complete day.
        date = date.AddDays(-1);

        // go back until we find a complete sunday
        while (date.DayOfWeek != DayOfWeek.Sunday)
        {
            date = date.AddDays(-1);
        }

        return date;
    }

    private async Task<byte[]> GeneratePdf(Company company, int year, int week)
    {
        var model = await _trackingSheetRepository.GetTrackingSheetPdfModel(company, year, week);

        return _trackingSheetPdfGenerator.GetPdfBytes(model);
    }

    private async Task StoreDocument(int companyId, int year, int week, string fileName, byte[] pdfBytes)
    {
        using var stream = new MemoryStream(pdfBytes);

        var documentDescriptor = new TrackingSheetDocumentDescriptor(companyId, year, week, fileName);

        var exists = await _trackingSheetDocumentService.Exists(documentDescriptor);

        if (exists)
        {
            await _trackingSheetDocumentService.Delete(documentDescriptor);
        }

        await _trackingSheetDocumentService.Create(documentDescriptor, stream);
    }

    private async Task<byte[]> RetrieveDocument(TrackingSheet trackingSheet)
    {
        var documentDescriptor = new TrackingSheetDocumentDescriptor(trackingSheet.CompanyId, trackingSheet.Year, trackingSheet.Week, trackingSheet.FileName);

        using var stream = await _trackingSheetDocumentService.OpenRead(documentDescriptor);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }

    private async Task<List<CardModel>> CreateCards(int companyId, int year)
    {
        var cards = new List<CardModel>();

        var trackingSheets = await _trackingSheetRepository.GetByCompanyAndYear(companyId, year);

        var weeksInYear = ISOWeek.GetWeeksInYear(year);
        var start = ISOWeek.GetYearStart(year);

        for (int weekNumber = 1; weekNumber <= weeksInYear; weekNumber++)
        {
            var card = new CardModel
            {
                WeekNumber = weekNumber,
                WeekStart = start.ToString("MMM d"),
                WeekEnd = start.AddDays(6).ToString("MMM d"),
                IsFuture = IsFuture(year, weekNumber),
                CompanyId = companyId,
                Year = year,
                TrackingSheetId = GetTrackingSheetId(trackingSheets, weekNumber)
            };

            cards.Add(card);

            start = start.AddDays(7);
        }

        cards.Sort((x, y) => y.WeekNumber.CompareTo(x.WeekNumber));

        return cards;
    }

    /// <summary>
    /// Determines whether the specified year and week number is in the future.
    /// The current incomplete week is the future.
    /// </summary>
    /// <param name="year">The year.</param>
    /// <param name="weekNumber">The week number.</param>
    /// <returns>
    ///   <c>true</c> if in the future; otherwise, <c>false</c>.
    /// </returns>
    private static bool IsFuture(int year, int weekNumber)
    {
        var now = DateTime.UtcNow;

        return year == now.Year && weekNumber >= ISOWeek.GetWeekOfYear(now);
    }

    /// <summary>
    /// Get years for the dropdown on the tracking sheets report page.
    /// </summary>
    /// <returns>
    /// Years from 2024 to the current year.
    /// </returns>
    private static List<int> GetYears()
    {
        var startYear = 2024;
        var currentYear = DateTime.UtcNow.Year;

        return Enumerable.Range(startYear, currentYear - startYear + 1).Reverse().ToList();
    }

    /// <summary>
    /// Gets the tracking sheet ID from a collection of tracking sheets for a specified week.
    /// </summary>
    /// <param name="trackingSheets">The tracking sheets.</param>
    /// <param name="week">The week.</param>
    /// <returns>The tracking sheet id, if there is a tracking sheet for the week; otherwise 0.</returns>
    private static int GetTrackingSheetId(IEnumerable<TrackingSheet> trackingSheets, int week)
    {
        var trackingSheetForWeek = trackingSheets.FirstOrDefault(x => x.Week == week);

        return trackingSheetForWeek != null ? trackingSheetForWeek.Id : 0;
    }

    private async Task<IUserEntity?> GetSecurityUser(int userContextUserId)
    {
        return await _userRepository.GetForSecurity(userContextUserId);
    }

    public async Task<bool> CanUserDownloadTrackingSheet(int userContextUserId, int trackingSheetId)
    {
        var user = await GetSecurityUser(userContextUserId);

        if (user == null) return false;

        if (TryDetermineAccessByUserType(user, out bool accessResult))
        {
            return accessResult;
        }

        var trackingSheet = await _trackingSheetRepository.GetById(trackingSheetId);

        return trackingSheet != null && user.GetActiveCompanyId() == trackingSheet.CompanyId;
    }

    /// <summary>
    /// Tries to evaluate access based on their user type.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <param name="accessResult">if returns <c>true</c> [access result].</param>
    /// <returns>
    /// Returns True if can evaluate what the access should be and returns that access result. 
    /// Returns False if further processing is necessary for the operation. 
    /// </returns>
    private static bool TryDetermineAccessByUserType(IUserEntity user, out bool accessResult)
    {
        accessResult = false;

        if (user == null)
        {
            accessResult = false;
            return true;
        }

        if (!user.IsCompanyUser())
        {
            accessResult = true;
            return true;
        }

        if (!user.HasActiveCompany())
        {
            accessResult = false;
            return true;
        }

        return false;
    }
}
