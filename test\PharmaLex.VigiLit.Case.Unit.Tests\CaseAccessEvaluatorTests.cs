﻿using Microsoft.AspNetCore.Authorization;
using Moq;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using System.Security.Claims;
using Claim = System.Security.Claims.Claim;

namespace PharmaLex.VigiLit.Case.Unit.Tests;

public class CaseAccessEvaluatorTests
{
    private readonly Mock<IAuthorizationService> _mockAuthorizationService = new();
    private readonly Mock<IUserRepository> _mockUserRepository = new();

    private readonly CaseAccessEvaluator _casesPermissions;

    public CaseAccessEvaluatorTests()
    {
        _casesPermissions = new CaseAccessEvaluator(_mockAuthorizationService.Object, _mockUserRepository.Object);
    }

    [Fact]
    public async Task HasPermissions_UserIsInternal_True_is_returned()
    {
        // Arrange
        var claimsPrincipal = new ClaimsPrincipal(new List<ClaimsIdentity>());
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Success);

        // Act
        var result = await _casesPermissions.HasPermissions(context);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task HasPermissions_UserIsNotCaseExternal_ExceptionIsThrown()
    {
        // Arrange
        var claimsPrincipal = new ClaimsPrincipal(new List<ClaimsIdentity>());
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Failed);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseExternal)).ReturnsAsync(AuthorizationResult.Failed);

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _casesPermissions.HasPermissions(context));
    }

    [Fact]
    public async Task HasPermissions_UserIsNotCompanyUser_ExceptionIsThrown()
    {
        // Arrange
        var claims = new List<Claim>()
        {
            new Claim("plx:userid", "1"),
        };
        var claimIdentities = new List<ClaimsIdentity>()
        {
            new ClaimsIdentity(claims)
        };

        var claimsPrincipal = new ClaimsPrincipal(claimIdentities);
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Failed);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseExternal)).ReturnsAsync(AuthorizationResult.Success);

        var user = new FakeUser(1)
        {
            CompanyUser = null
        };
        _mockUserRepository.Setup(x => x.GetForSecurity(1)).ReturnsAsync(user);

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _casesPermissions.HasPermissions(context));
    }

    [Fact]
    public async Task HasPermissions_UserIsCompanyUserForInactiveCompany_ExceptionIsThrown()
    {
        // Arrange
        var claims = new List<Claim>()
        {
            new Claim("plx:userid", "1"),
        };
        var claimIdentities = new List<ClaimsIdentity>()
        {
            new ClaimsIdentity(claims)
        };

        var claimsPrincipal = new ClaimsPrincipal(claimIdentities);
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Failed);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseExternal)).ReturnsAsync(AuthorizationResult.Success);

        var user = new FakeUser(1)
        {
            CompanyUser = new CompanyUser()
            {
                Active = false,
            },
        };
        _mockUserRepository.Setup(x => x.GetForSecurity(1)).ReturnsAsync(user);

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _casesPermissions.HasPermissions(context));
    }

    [Fact]
    public async Task HasPermissions_UserIsCompanyUserForActiveCompanyButDifferentCompanyFromCase_ExceptionIsThrown()
    {
        // Arrange
        const int usersCompanyId = 42;
        const int caseCompanyId = 43;
        var claims = new List<Claim>()
        {
            new Claim("plx:userid", "1"),
        };
        var claimIdentities = new List<ClaimsIdentity>()
        {
            new ClaimsIdentity(claims)
        };

        var claimsPrincipal = new ClaimsPrincipal(claimIdentities);
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Failed);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseExternal)).ReturnsAsync(AuthorizationResult.Success);

        var user = new FakeUser(1)
        {
            CompanyUser = new CompanyUser()
            {
                CompanyId = usersCompanyId,
                Active = true,
            },
        };
        _mockUserRepository.Setup(x => x.GetForSecurity(1)).ReturnsAsync(user);

        caseModel.CaseCompanies = new List<CaseCompanyModel>()
        {
            new CaseCompanyModel(1, caseCompanyId),
        };

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _casesPermissions.HasPermissions(context));
    }

    [Fact]
    public async Task HasPermissions_UserIsCompanyUserForActiveCompanyAndSameCompanyAsCase_True_is_returned()
    {
        // Arrange
        const int usersCompanyId = 42;
        const int caseCompanyId = 42;
        var claims = new List<Claim>()
        {
            new Claim("plx:userid", "1"),
        };
        var claimIdentities = new List<ClaimsIdentity>()
        {
            new ClaimsIdentity(claims)
        };

        var claimsPrincipal = new ClaimsPrincipal(claimIdentities);
        var caseModel = new CaseModel();
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.InternalUser)).ReturnsAsync(AuthorizationResult.Failed);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseExternal)).ReturnsAsync(AuthorizationResult.Success);

        var user = new FakeUser(1)
        {
            CompanyUser = new CompanyUser()
            {
                CompanyId = usersCompanyId,
                Active = true,
            },
        };
        _mockUserRepository.Setup(x => x.GetForSecurity(1)).ReturnsAsync(user);

        caseModel.CaseCompanies = new List<CaseCompanyModel>()
        {
            new CaseCompanyModel(1, caseCompanyId),
        };

        // Act
        var result = await _casesPermissions.HasPermissions(context);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task HasPermissions_CaseIsPendingAndUserIsNotCaseManagement_ExceptionIsThrown()
    {
        // Arrange
        var caseModel = new CaseModel()
        {
            Status = CaseStatus.Pending,
        };

        var claimsPrincipal = new ClaimsPrincipal(new List<ClaimsIdentity>());
        var context = new CaseAccessContext(claimsPrincipal, caseModel);
        _mockAuthorizationService.Setup(x => x.AuthorizeAsync(
            context.User, It.IsAny<object>(), Policies.CaseManagement)).ReturnsAsync(AuthorizationResult.Failed);

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _casesPermissions.HasPermissions(context));
    }
}