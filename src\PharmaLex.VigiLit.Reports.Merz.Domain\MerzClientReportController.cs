﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Ui.Controllers;
using PharmaLex.VigiLit.Reports.Merz.Domain.Models;

namespace PharmaLex.VigiLit.Reports.Merz.Domain;

[Authorize]
[Route("/Reports/[controller]")]
public class MerzClientReportController: BaseReportController
{
    private readonly IAccessControlService _accessControlService;
    private readonly IMerzClientReportService _merzClientReportService;

    public MerzClientReportController(
                        IAccessControlService accessControlService,
                        IMerzClientReportService merzClientReportService,
                        IUserSessionService userSessionService,
                        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _accessControlService = accessControlService;
        _merzClientReportService = merzClientReportService;
    }

    [HttpGet]
    public  async Task<IActionResult> Index()
    {
        try
        {
            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var model = _merzClientReportService.GetPageModel();
            return View("../Reports/MerzClientReport/Index", model);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("/Reports/[controller]/[action]")]
    public async Task<IActionResult> GetCards([FromBody] CardsRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var cards = _merzClientReportService.GetCards(request.Year);

            return Ok(cards);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("/Reports/[controller]/[action]/{year}/{month}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Download(int year, int month)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var downloadFile = await _merzClientReportService.Download(year, month);
            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }
}