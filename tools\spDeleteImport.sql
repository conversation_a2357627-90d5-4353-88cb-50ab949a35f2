﻿CREATE PROCEDURE dbo.spDeleteImport
	--------------------------------------------------------
	-- Deletes single specified import and related data
	--
	-- Only deletes records that haven't been classified (ReferenceState = 0), so keeps classifications and references	
	--
	-- Paramenters:
	-- @importId		Id of import to delete
	--
	-- Syntax:
	-- exec spDeleteImport 153
	--
	--------------------------------------------------------
	@importId int
AS
BEGIN

		DELETE FROM ReferenceClassificationLocks
			WHERE ReferenceClassificationId in
				(SELECT ReferenceClassificationId FROM ImportContractReferenceClassifications where importid = @importId)
		
		DELETE ReferenceHistoryActions
			FROM ReferenceHistoryActions rha 
			INNER JOIN ReferenceClassifications rc on rha.ReferenceClassificationId = rc.id
			 WHERE rc.id in
				(SELECT ReferenceClassificationId from [ImportContractReferenceClassifications] where importid = @importId)
				AND ReferenceState = 0

		DELETE CompanyInterests
			FROM CompanyInterests ci
			INNER JOIN ReferenceClassifications rc on ci.ReferenceClassificationId = rc.id
			 WHERE rc.id in
				(select ReferenceClassificationId from [ImportContractReferenceClassifications] where importid =@importId)
				and ReferenceState = 0
		
		-- Remove References where possible to avoid orphaned References, but don't delete ones that already have other classifications against them
		DELETE FROM [References] 
		WHERE Id in
		(
			SELECT rc.ReferenceId FROM [References] r
			INNER JOIN ReferenceClassifications rc on r.id = rc.ReferenceId
			WHERE r.id in
				(SELECT referenceid FROM ReferenceClassifications rc 
				INNER JOIN ImportContractReferenceClassifications icrc on rc.id = icrc.ReferenceClassificationId  
				WHERE icrc.importid = @importId  and rc.ReferenceState = 0)
			GROUP BY rc.ReferenceId
			HAVING COUNT(*) = 1
		)

		DELETE FROM ReferenceClassifications 
			WHERE id in
				(SELECT ReferenceClassificationId FROM ImportContractReferenceClassifications where importid = @importId)
			and ReferenceState = 0

		DELETE FROM ImportContractReferenceClassifications
			WHERE importid = @importId 
		
		DELETE FROM ImportContracts 	
			WHERE importid = @importId 

		DELETE FROM Imports 			
			WHERE id = @importId 
END
