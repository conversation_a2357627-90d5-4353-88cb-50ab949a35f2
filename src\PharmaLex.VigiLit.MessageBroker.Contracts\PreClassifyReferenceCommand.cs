﻿namespace PharmaLex.VigiLit.MessageBroker.Contracts;
public class PreClassifyReferenceCommand
{
    public PreClassifyReferenceCommand(string title, string @abstract, string recordIdentifier, string sourceSystem, string substance, List<string> synonyms)
    {
        Title = title;
        Abstract = @abstract;
        RecordIdentifier = recordIdentifier;
        SourceSystem = sourceSystem;
        Substance = substance;
        Synonyms = synonyms;
    }
    public string RecordIdentifier { get; set; }
    public string SourceSystem { get; set; }
    public string Substance { get; set; }
    public List<string> Synonyms { get; set; }
    public string Title { get; set; }
    public string Abstract { get; set; }
}
