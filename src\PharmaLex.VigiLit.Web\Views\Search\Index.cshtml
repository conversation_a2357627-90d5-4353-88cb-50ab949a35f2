@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.SearchPageModel

@{
    ViewData["Title"] = "Search";
}

<div id="search" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2>Search References</h2>
        <div class="controls">
            <div v-if="activeTab == 'classificationSearchTab'">
                <form method="post" action="Search/PrintPreview" target="_blank">
                    <input type="hidden" name="printPreviewRequest" :value="printPreviewRequest">
                    @Html.AntiForgeryToken()
                    <button class="btn-default" :disabled="buttonsDisabled">Print Preview</button>
                </form>
            </div>
            <div v-if="activeTab == 'classificationSearchTab'">
                <button type="button" id="btnExport" class="btn btn-default btn-spinner" @@click="onExport" :disabled="buttonsDisabled">
                    <span class="btn-label btn-span">Export</span>
                    <span class="spinner btn-span"></span>
                </button>
            </div>
        </div>
    </div>
    <section>
        <ul class="tabs">
            <li @@click="loadClassificationSearch" v-bind:class="{ active: activeTab == 'classificationSearchTab' }">
                <a @@click.prevent href="">Classification Search</a>
            </li>
            <li class="separator"></li>
            <li @@click="loadReferenceSearch" v-bind:class="{ active: activeTab == 'referenceSearchTab' }">
                <a @@click.prevent href="">Reference Search</a>
            </li>
        </ul>

        <div v-if="activeTab == 'referenceSearchTab'">
            <div class="horizontal-filter">
                <div class="form-group horizontal-filter-item">
                    <label for="refSearch">ID (Source Id / DOI)</label>
                    <div class="input-group inline-flex">
                        <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                        <input type="search" id="refSearch" name="refSearch" class="pl-4 white-background" style="width: 300px;" placeholder="Source Id / DOI" aria-label="Reference Search Term expects ID of type Source Id or DOI" v-model='referenceSearchString' v-on:keyup.enter="onReferenceSearch" />
                    </div>
                </div>
                <div class="form-group horizontal-filter-item">
                    <button v-on:click.prevent.stop='onReferenceSearch'>Search</button>
                </div>
            </div>
            <hr />
            <div v-if="referenceSearchResults">
                <filtered-table :items="referenceSearchResults" :columns="columns" :filters="filters" :link="referenceSearchLink"></filtered-table>
            </div>
        </div>

        <div v-if="activeTab == 'classificationSearchTab'">

            <div v-if="classificationSearchFullTextErrorMessage" class="classificationSearchFullTextErrorMessage">
                <h4>There are errors, please correct before continuing</h4>
                <p>{{classificationSearchFullTextErrorMessage}}</p>
            </div>

            <form class="horizontal-filter flex" autocomplete="off">
                <div class="form-group horizontal-filter-item">
                    <label for="classSearch">ID (PLXID / Source Id / DOI)</label>
                    <div class="input-group">
                        <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                        <input type="search" id="classSearch" name="classSearch" class="pl-4 white-background" style="width: 300px" placeholder="PLXID / Source Id / DOI" aria-label="Classification Search Term expects ID of type PLXID or Source Id or DOI" v-model='searchReq.term' v-on:keyup.enter="onClassificationSearch" />
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="dateCreatedFrom">Created From</label>
                    <div class="input-group">
                        <input type="date" id="dateCreatedFrom" name="dateCreatedFrom" class="white-background" placeholder="Date From" style="width: 150px;" v-model='searchReq.createdFrom' v-on:change="onCreatedFromDateChanged">
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="dateCreatedTo">Created To</label>
                    <div class="input-group">
                        <input type="date" id="dateCreatedTo" name="dateCreatedTo" class="white-background" placeholder="Date To" style="width: 150px;" v-model='searchReq.createdTo' v-on:change="onCreatedToDateChanged">
                    </div>
                </div>

                <div class="form-group horizontal-filter-item" v-if="displayLastUpdatedDateFilter">
                    <label for="dateFrom">Last Updated From</label>
                    <div class="input-group">
                        <input type="date" id="dateFrom" name="dateFrom" class="white-background" placeholder="Date From" style="width: 150px;" v-model='searchReq.lastUpdatedFrom' v-on:change="onFromDateChanged">
                    </div>
                </div>

                <div class="form-group horizontal-filter-item" v-if="displayLastUpdatedDateFilter">
                    <label for="dateTo">Last Updated To</label>
                    <div class="input-group">
                        <input type="date" id="dateTo" name="dateTo" class="white-background" placeholder="Date To" style="width: 150px;" v-model='searchReq.lastUpdatedTo' v-on:change="onToDateChanged">
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="substance">Substance</label>
                    <div class="input-group">
                        <multi-select :item-list="substances" :placeholder="'Select Substances'" style="width: 250px;"
                                      :selections="searchReq.substances"
                                      :enable-filter="true">
                        </multi-select>
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="classificationCategory">Classification Category</label>
                    <div class="input-group">
                        <multi-select :item-list="classificationCategories" :placeholder="'Select Categories'" style="width: 200px;"
                                      :selections="searchReq.classificationCategories">
                        </multi-select>
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="psur">PSUR Relevant?</label>
                    <div class="input-group custom-select">
                        <select id="psur" name="psur" class="white-background" style="width: 150px" v-model='searchReq.psur'>
                            <option value=''>-- Select PSUR --</option>
                            <option v-for='opt in psurLookup' :value='opt'>{{ opt }}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group horizontal-filter-item" v-if="companies.length > 0">
                    <label for="company">Company</label>
                    <div class="input-group custom-select">
                        <select id="company" name="company" class="white-background" style="width: 250px" v-model='searchReq.companyId'>
                            <option value='0'>-- Select Company --</option>
                            <option v-for='company in companies' :value='company.id' :class="[{'inactive':!company.isActive}]">{{ company.name }}</option>
                        </select>
                    </div>
                </div>

                <div style="flex-basis: 100%; height: 0;"></div>

                <div class="form-group horizontal-filter-item">
                    <label for="titleSearch">Title</label>
                    <div class="input-group inline-flex">
                        <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                        <input type="search" id="titleSearch" name="titleSearch" class="pl-4 white-background" style="width: 300px" placeholder="Title" aria-label="Free text search of Titles" v-model='searchReq.title' v-on:keyup.enter="onClassificationSearch" />
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="meshTermSearch">MeSH Term</label>
                    <div class="input-group inline-flex">
                        <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                        <input type="search" id="meshTermSearch" name="meshTermSearch" class="pl-4 white-background" style="width: 300px" placeholder="MeSH Term" aria-label="Free text search of MeSH Terms" v-model='searchReq.meshTerm' v-on:keyup.enter="onClassificationSearch" />
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="meshTermWithOrSearch" style="width: 75px" title="Only applies to MeSH Terms, everything else is AND">OR Search</label>
                    <div class="input-group inline-flex">
                        <div style="padding-top:10px">
                            <input type="checkbox" id="meshTermWithOrSearch" name="meshTermWithOrSearch" class="white-background" aria-label="Do multiple term free text searches with OR instead of AND" v-model='searchReq.searchMeshTermWithOr' />
                        </div>
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="keywordSearch">Keyword</label>
                    <div class="input-group inline-flex">
                        <i class="m-icon pl-1 z-index-1" style="position: absolute;left: 0;top: 30px;">search</i>
                        <input type="search" id="keywordSearch" name="keywordSearch" class="pl-4 white-background" style="width: 300px" placeholder="Keyword" aria-label="Free text search of Keywords" v-model='searchReq.keyword' v-on:keyup.enter="onClassificationSearch" />
                    </div>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="keywordWithOrSearch" style="width: 75px" title="Only applies to Keywords, everything else is AND">OR Search</label>
                    <div class="input-group inline-flex">
                        <div style="padding-top:10px">
                            <input type="checkbox" id="keywordWithOrSearch" name="keywordWithOrSearch" class="white-background" aria-label="Do multiple term free text searches with OR instead of AND" v-model='searchReq.searchKeywordWithOr' />
                        </div>
                    </div>
                </div>
                <div class="form-group horizontal-filter-item">
                    <label for="btnClassSearch" id="btnLabelSearch">
                        &nbsp;
                        <button type="button" id="btnClassSearch" name="btnClassSearch" class="btn btn-spinner" @@click="onClassificationSearch">
                            <span class="btn-label">Search</span>
                            <span class="spinner"></span>
                        </button>
                    </label>
                </div>

                <div class="form-group horizontal-filter-item">
                    <label for="resetButton">&nbsp;</label>
                    <a class="button reset-button" @@click="onClassificationReset" id="resetButton">Reset</a>
                </div>
            </form>
            <hr />
            <div v-if="classificationSearchResults">
                <filtered-table :items="classificationSearchResults" :columns="classificationSearchResultColumns" :filters="filters" :link="classificationSearchLink"></filtered-table>
            </div>
        </div>

    </section>
</div>


@section Scripts {
    <script type="text/javascript">

        let model = @Html.Raw(AntiXss.ToJson(Model));

        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

        var pageConfig = {
            appElement: "#search",
            data: function () {
                return {
                    activeTab: "classificationSearchTab",
                    referenceSearchLink: '/References/',
                    referenceSearchResults: null,
                    classificationSearchLink: '/References/Classifications/',
                    classificationSearchResults: null,
                    classificationSearchFullTextErrorMessage: null,
                    substances: model.substances,
                    classificationCategories: model.classificationCategories,
                    companies: model.companies,
                    displayLastUpdatedDateFilter: model.displayLastUpdatedDateFilter,
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'sourceId',
                                sortKey: 'sourceId',
                                header: 'Source Id',
                                edit: {},
                                type: 'number',
                                style: 'width: 150px;'
                            },
                            {
                                dataKey: 'doi',
                                sortKey: 'doi',
                                header: 'DOI',
                                edit: {},
                                type: 'text',
                                style: 'width: 300px;'
                            },
                            {
                                dataKey: 'title',
                                sortKey: 'title',
                                header: 'Title',
                                edit: {},
                                type: 'html'
                            }
                        ]
                    },
                    classificationSearchResultColumns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'sourceId',
                                sortKey: 'sourceId',
                                header: 'Source Id',
                                edit: {},
                                type: 'number',
                                style: 'width: 150px;'
                            },
                            {
                                dataKey: 'doi',
                                sortKey: 'doi',
                                header: 'DOI',
                                edit: {},
                                type: 'text',
                                style: 'width: 190px;'
                            },
                            {
                                dataKey: 'id',
                                sortKey: 'id',
                                header: 'PLX ID',
                                edit: {},
                                type: 'number',
                                style: 'width: 150px;'
                            },
                            {
                                dataKey: 'title',
                                sortKey: 'title',
                                header: 'Title',
                                edit: {},
                                type: 'html'
                            },
                            {
                                dataKey: 'substance',
                                sortKey: 'substance',
                                header: 'Substance',
                                edit: {},
                                type: 'text',
                                style: 'width: 190px;'
                            },
                            {
                                dataKey: 'classificationCategory',
                                sortKey: 'classificationCategory',
                                header: 'Classification Category',
                                edit: {},
                                type: 'text',
                                style: 'width: 190px;'
                            },
                            {
                                dataKey: 'minimalCriteria',
                                sortKey: 'minimalCriteria',
                                header: 'Minimal Criteria',
                                edit: {},
                                type: 'text',
                                style: 'width: 80px;'
                            },
                            {
                                dataKey: 'createdDate',
                                sortKey: 'createdDate',
                                header: 'Created On',
                                edit: {
                                    convert: e => moment(e).format('DD MMM YYYY')
                                },
                                type: 'date',
                                style: 'width: 120px;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'substance',
                            options: [],
                            type: 'search',
                            header: 'Search Substance',
                            fn: v => p => p.substance.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    referenceSearchString: '',
                    psurLookup: ["Yes", "No", "N/A"],
                    refreshSearchReq: 0,
                    searchReq: {
                        term: '',
                        substances: { selectedIds: [] },
                        classificationCategories: { selectedIds: [] },
                        psur: '',
                        createdFrom: null,
                        createdTo: null,
                        lastUpdatedFrom: null,
                        lastUpdatedTo: null,
                        companyId: 0,
                        title: '',
                        meshTerm: '',
                        searchMeshTermWithOr: false,
                        keyword: '',
                        searchKeywordWithOr: false
                    }
                };
            },
            computed: {
                buttonsDisabled() {
                    // Property to force recalc when local storage is updated
                    this.refreshSearchReq;

                    var prevClassificationSearchReq = null;
                    // Despite using the storage validator, since this is a computed property, it accesses this before it's fixed.
                    if (localStorage.prevClassificationSearchReq != null) {
                        try {
                            prevClassificationSearchReq = JSON.parse(localStorage.prevClassificationSearchReq);
                        } catch(e) {}
                    }

                    let enabled = this.searchReq != null
                        && prevClassificationSearchReq != null
                        && this.searchReq.term == prevClassificationSearchReq.term
                        && (prevClassificationSearchReq.substances && this.arraysAreEqual(this.searchReq.substances.selectedIds, prevClassificationSearchReq.substances.selectedIds))
                        && (prevClassificationSearchReq.classificationCategories && this.arraysAreEqual(this.searchReq.classificationCategories.selectedIds, prevClassificationSearchReq.classificationCategories.selectedIds))
                        && this.searchReq.psur == prevClassificationSearchReq.psur
                        && this.searchReq.createdFrom == prevClassificationSearchReq.createdFrom
                        && this.searchReq.createdTo == prevClassificationSearchReq.createdTo
                        && this.searchReq.lastUpdatedFrom == prevClassificationSearchReq.lastUpdatedFrom
                        && this.searchReq.lastUpdatedTo == prevClassificationSearchReq.lastUpdatedTo
                        && this.searchReq.companyId == prevClassificationSearchReq.companyId
                        && this.searchReq.title == prevClassificationSearchReq.title
                        && this.searchReq.meshTerm == prevClassificationSearchReq.meshTerm
                        && this.searchReq.searchMeshTermWithOr == prevClassificationSearchReq.searchMeshTermWithOr
                        && this.searchReq.keyword == prevClassificationSearchReq.keyword
                        && this.searchReq.searchKeywordWithOr == prevClassificationSearchReq.searchKeywordWithOr
                        && this.classificationSearchFullTextErrorMessage === null;

                    return !enabled;
                },
                printPreviewRequest() {
                    return JSON.stringify(this.searchReq);
                }
            },
            methods: {
                loadReferenceSearch: function () {
                    this.activeTab = 'referenceSearchTab';
                },
                loadClassificationSearch: function () {
                    this.activeTab = 'classificationSearchTab';
                },
                onReferenceSearch: function () {
                    this.referenceSearchResults = null;

                    fetch(`/Search/ReferenceSearch?term=${this.referenceSearchString}`, {
                        method: "GET"
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.referenceSearchResults = data;
                    }).catch(error => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                onClassificationSearch: function ($event) {
                    this.classificationSearchResults = null;
                    this.classificationSearchFullTextErrorMessage = null;
                    $("#btnClassSearch").attr("data-loading", "");

                    // If the last updated date filter is not visible, clear values (set when logged in as user with filter visible).
                    if (!this.displayLastUpdatedDateFilter) {
                        this.searchReq.lastUpdatedFrom = null;
                        this.searchReq.lastUpdatedTo = null;
                    }

                    // Remember search term(s) to disable export button if search is dirty
                    localStorage.prevClassificationSearchReq = JSON.stringify(this.searchReq);

                    // Property to force recalc when local storage is updated
                    this.refreshSearchReq++;

                    let url = '/Search/ClassificationSearch';

                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(this.searchReq),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.classificationSearchResults = data.classifications;
                        this.classificationSearchFullTextErrorMessage = data.fullTextErrorMessage;
                        $("#btnClassSearch").removeAttr('data-loading');
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        $("#btnClassSearch").removeAttr('data-loading');
                    });
                },
                onClassificationReset: function() {
                    localStorage.removeItem('prevClassificationSearchReq');

                    this.searchReq.term = '';
                    this.searchReq.substances.selectedIds = [];
                    this.searchReq.classificationCategories.selectedIds = [];
                    this.searchReq.psur = '';
                    this.searchReq.createdFrom = null;
                    this.searchReq.createdTo = null;
                    this.searchReq.lastUpdatedFrom = null;
                    this.searchReq.lastUpdatedTo = null;
                    this.searchReq.companyId = 0;
                    this.searchReq.title = '';
                    this.searchReq.meshTerm = '';
                    this.searchReq.searchMeshTermWithOr = false;
                    this.searchReq.keyword = '';
                    this.searchReq.searchKeywordWithOr = false;

                    this.refreshComponents++;
                    window.location.reload();
                },
                onExport: function () {
                    let term = this.searchReq.term === null ? '' : this.searchReq.term;
                    let exportUrl = `/Search/Export`;
                    $("#btnExport").attr('data-loading', '');

                    var onFailure = function() {
                        plx.toast.show('Unable to export, please try again', 2, 'failed', null, 2500);
                    }
                    var onComplete = function() {
                        $("#btnExport").removeAttr('data-loading');
                    }

                    DownloadFile.fromUrl(exportUrl, JSON.stringify(this.searchReq), null, onFailure, onComplete);
                },
                onCreatedFromDateChanged: function (e) {
                    // Revert date to null when clearing date input control which in turn binds an empty string value to our model
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdFrom == '') {
                        this.searchReq.createdFrom = null;
                    }
                },
                onCreatedToDateChanged: function (e) {
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdTo == '') {
                        this.searchReq.createdTo = null;
                    }
                },
                onCreatedFromDateChanged: function (e) {
                    // Revert date to null when clearing date input control which in turn binds an empty string value to our model
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdFrom == '') {
                        this.searchReq.createdFrom = null;
                    }
                },
                onCreatedToDateChanged: function (e) {
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.createdTo == '') {
                        this.searchReq.createdTo = null;
                    }
                },
                onFromDateChanged: function (e) {
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.lastUpdatedFrom == '') {
                        this.searchReq.lastUpdatedFrom = null;
                    }
                },
                onToDateChanged: function (e) {
                    let input = new Date(e.target.value);
                    if (!(input instanceof Date && !isNaN(input)) || this.searchReq.lastUpdatedTo == '') {
                        this.searchReq.lastUpdatedTo = null;
                    }
                },
                arraysAreEqual: function(array1, array2) {
                    return array1.filter(x => !array2.includes(x)).length == 0
                        && array2.filter(x => !array1.includes(x)).length == 0;
                }
            },
            errorCaptured: function (err) {
                plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                console.log(err);
                return false; //stop propagation, error spilling into dotnetCore Error.cshtml
            },
            mounted() {
                window.addEventListener('keyup', event => {
                    if (event.keyCode === 13) {
                        if (event.target.name == 'classSearch') {
                            this.onClassificationSearch()
                        }
                        if (event.target.name == 'refSearch') {
                            this.onReferenceSearch()
                        }
                    }
                });

                window.addEventListener('pageshow', event => {
                    if (localStorage.prevClassificationSearchReq != null) {
                        LocalStorageValidator.clearJsonItemIfInvalid('prevClassificationSearchReq', this.searchReq);
                        if (localStorage.prevClassificationSearchReq) {
                            this.searchReq = JSON.parse(localStorage.prevClassificationSearchReq);
                        }
                        if (this.activeTab == 'classificationSearchTab') {
                            this.onClassificationSearch(null);
                        }
                    }
                });
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/MultiSelect" />
    <partial name="Components/Vue3/FilteredTable" />
}

