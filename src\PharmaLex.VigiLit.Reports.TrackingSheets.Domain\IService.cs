﻿using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public interface IService
{
    /// <summary>
    /// Gets the tracking sheets page model.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <returns>
    /// The tracking sheets page model.
    /// </returns>
    Task<PageModel> GetTrackingSheetsPageModel(User user);

    /// <summary>
    /// Gets the cards for the tracking sheets page.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <param name="companyId">The company identifier. External users don't have the option to select a company, so their assigned company is used instead.</param>
    /// <param name="year">The year.</param>
    /// <returns>
    /// <para>An empty list when an internal user has not yet selected a company.</para>
    /// <para>Otherwise, 52 or 53 cards representing the weeks of the year.</para>
    /// </returns>
    Task<IEnumerable<CardModel>> GetCards(User user, int companyId, int year);

    /// <summary>
    /// Download a tracking sheet.
    /// </summary>
    /// <param name="trackingSheetId">The tracking sheet identifier.</param>
    /// <returns>
    /// Tracking sheet downloadable file.
    /// </returns>
    Task<DownloadFile> Download(int trackingSheetId);

    /// <summary>
    /// Create a tracking sheet.
    /// </summary>
    /// <param name="request">The request.</param>
    Task Create(CreateRequest request);

    /// <summary>
    /// Creates the tracking sheets for last week.
    /// </summary>
    Task CreateTrackingSheetsForLastWeek();

    Task<bool> CanUserDownloadTrackingSheet(int userContextUserId, int trackingSheetId);

}
