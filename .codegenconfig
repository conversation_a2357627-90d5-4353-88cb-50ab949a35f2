{"Entities": [{"Name": "Company", "Properties": [{"Name": "Name", "Type": "string"}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "string"}, {"Name": "Address", "Type": "string"}, {"Name": "VAT", "Type": "string"}, {"Name": "MedDRA", "Type": "bool"}]}], "Projects": [{"Name": "VigiLit.Domain", "Templates": [{"TemplateName": "Entity", "FileNameSuffix": "", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": [], "StaticFileName": ""}]}, {"Name": "VigiLit.Application", "Templates": [{"TemplateName": "IService", "FileNameSuffix": "Service", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": true, "Dependencies": ["VigiLit.Domain", "VigiLit.Messages"], "StaticFileName": ""}, {"TemplateName": "Service", "FileNameSuffix": "Service", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": ["VigiLit.Domain", "VigiLit.Messages"], "StaticFileName": ""}, {"TemplateName": "MappingProfile", "FileNameSuffix": "MappingProfile", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": ["VigiLit.Domain", "VigiLit.Messages"], "StaticFileName": ""}]}, {"Name": "VigiLit.Infrastructure.Data", "Templates": [{"TemplateName": "EntityConfiguration", "FileNameSuffix": "Configuration", "FileExtension": "cs", "Folder": "Configuration", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": ["VigiLit.Domain", "VigiLit.Application"], "StaticFileName": ""}]}, {"Name": "VigiLit.Messages", "Templates": [{"TemplateName": "Model", "FileNameSuffix": "Model", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": [], "StaticFileName": ""}, {"TemplateName": "HistoryModel", "FileNameSuffix": "HistoryModel", "FileExtension": "cs", "Folder": "", "PlurizeEntityName": false, "IsInterface": false, "Dependencies": [], "StaticFileName": ""}]}, {"Name": "VigiLit.Web", "Templates": [{"TemplateName": "Controller", "FileNameSuffix": "Controller", "FileExtension": "cs", "Folder": "Controllers", "PlurizeEntityName": true, "IsInterface": false, "Dependencies": ["VigiLit.Application", "VigiLit.Messages", "VigiLit.Domain"], "StaticFileName": ""}, {"TemplateName": "Index", "FileNameSuffix": "", "FileExtension": "cshtml", "Folder": "Views", "PlurizeEntityName": true, "IsInterface": false, "Dependencies": ["VigiLit.Messages"], "StaticFileName": "Index"}, {"TemplateName": "Edit", "FileNameSuffix": "", "FileExtension": "cshtml", "Folder": "Views", "PlurizeEntityName": true, "IsInterface": false, "Dependencies": ["VigiLit.Messages"], "StaticFileName": "Edit"}, {"TemplateName": "History", "FileNameSuffix": "", "FileExtension": "cshtml", "Folder": "Views", "PlurizeEntityName": true, "IsInterface": false, "Dependencies": ["VigiLit.Messages"], "StaticFileName": "History"}]}]}