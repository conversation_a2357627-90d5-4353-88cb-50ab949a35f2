﻿using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement.Unit.Tests.Fake;

public class FakeUser : EntityBase, IUserEntity
{
    private readonly IReadOnlyCollection<IClaimEntity> _claims = new List<IClaimEntity>();
    public string Email { get; set; } = default!;
    public string GivenName { get; set; } = default!;
    public string FamilyName { get; set; } = default!;

    public IReadOnlyCollection<IClaimEntity> GetClaims() => _claims;

    public bool HasActiveCompany()
    {
        throw new NotImplementedException();
    }

    public bool IsCompanyUser()
    {
        throw new NotImplementedException();
    }

    public int GetActiveCompanyId()
    {
        throw new NotImplementedException();
    }
}
