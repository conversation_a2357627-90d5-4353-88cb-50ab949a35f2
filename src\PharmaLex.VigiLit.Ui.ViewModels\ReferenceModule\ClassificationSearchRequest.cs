﻿namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ClassificationSearchRequest
{
    private string _psurValue;
    private DateTime? _createdFrom;
    private DateTime? _createdTo;
    private DateTime? _lastUpdatedFrom;
    private DateTime? _lastUpdatedTo;

    public string Term { get; set; }
    public MultiSelectFilterRequest Substances { get; set; }
    public MultiSelectFilterRequest ClassificationCategories { get; set; }

    public string PSUR
    {
        get 
        { 
            return _psurValue;  
        }

        set
        {
            if (!string.IsNullOrWhiteSpace(value) && string.Equals(value, "N/A", StringComparison.InvariantCultureIgnoreCase))
            {
                _psurValue = "NA";
            }
            else
            {
                _psurValue = value;
            }
        }
    }

    public DateTime? CreatedFrom
    {
        get
        {
            return _createdFrom;
        }

        set
        {
            if (value != null)
            {
                _createdFrom = new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc);
            }
            else
            {
                _createdFrom = value;
            }
        }
    }

    public DateTime? CreatedTo
    {
        get
        {
            return _createdTo;
        }

        set
        {
            if (value != null)
            {
                _createdTo = new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc);
            }
            else
            {
                _createdTo = value;
            }
        }
    }

    public DateTime? LastUpdatedFrom 
    { 
        get
        {
            return _lastUpdatedFrom;
        }

        set
        {
            if (value != null)
            {
                _lastUpdatedFrom = new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc);
            }
            else
            {
                _lastUpdatedFrom = value;
            }
        }
    }

    public DateTime? LastUpdatedTo
    {
        get
        {
            return _lastUpdatedTo;
        }

        set
        {
            if (value != null)
            {
                _lastUpdatedTo = new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc);
            }
            else
            {
                _lastUpdatedTo = value;
            }
        }
    }

    public int CompanyId { get; set; }

    public string Title {  get; set; }

    public string MeshTerm { get; set; }

    public bool SearchMeshTermWithOr { get; set; }

    public string Keyword { get; set; }

    public bool SearchKeywordWithOr { get; set; }
}