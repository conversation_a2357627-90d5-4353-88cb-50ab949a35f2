﻿using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Merz.Domain;

public class MerzClientReportResult : EntityBase
{
    public required int? PlxId { get; set; }
    public required string Substance { get; set; }
    public required string Authors { get; set; }
    public required string Title { get; set; }
    public required string Citation { get; set; }
    public required string ImportDate { get; set; }
    public required string Source { get; set; }
    public required int? ContractVersion { get; set; }
}
