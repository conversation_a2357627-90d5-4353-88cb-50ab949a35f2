﻿using Microsoft.AspNetCore.Http;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Web.Authentication;

/// <summary>
/// PharmaLex User Context.
/// </summary>
/// <remarks>
/// This used to be in the PharmaLex.Shared.Web project. It is most likely to be redundant however or perhaps the VigiLitUserContext is redundant. Either way
/// this is being kept for now so nothing breaks. 
/// </remarks>
/// <seealso cref="PharmaLex.DataAccess.IUserContext" />
public class PlxUserContext : IUserContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public PlxUserContext(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string User => _httpContextAccessor.HttpContext?.User.GetEmail() ?? "<EMAIL>" ;
}