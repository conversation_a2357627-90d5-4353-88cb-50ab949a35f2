﻿using Xunit.Abstractions;
using Xunit.Sdk;

namespace PharmaLex.VigiLit.Test.Framework.Orderers;

public class FollowsTestOrderer : ITestCaseOrderer
{
    public IEnumerable<TTestCase> OrderTestCases<TTestCase>(IEnumerable<TTestCase> testCases)
        where TTestCase : ITestCase
    {
        var assemblyQualifiedName = typeof(FollowsTestAttribute).AssemblyQualifiedName!;
        var sortedMethods = new List<TTestCase>();
        var testCaseByMethodName = testCases.ToDictionary(tc => tc.TestMethod.Method.Name);

        foreach (var testCase in testCases)
        {
            var orderAttribute = testCase.TestMethod.Method
                .GetCustomAttributes(assemblyQualifiedName)
                .SingleOrDefault();

            if (orderAttribute != null)
            {
                var previousTestMethodName = orderAttribute.GetConstructorArguments().FirstOrDefault() as string;
                if (!string.IsNullOrEmpty(previousTestMethodName) && testCaseByMethodName.TryGetValue(previousTestMethodName, out var previousTestCase))
                {
                    sortedMethods.Insert(sortedMethods.IndexOf(previousTestCase) + 1, testCase);
                    continue;
                }
            }

            sortedMethods.Add(testCase);
        }

        return sortedMethods;
    }
}