using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class GeneralNote
{
    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(GeneralNoteOwner.NLM)]
    public GeneralNoteOwner Owner { get; set; } = GeneralNoteOwner.NLM;

    [XmlText()]
    public string Value { get; set; }
}