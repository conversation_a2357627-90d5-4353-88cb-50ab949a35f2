﻿using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Logging;
public static partial class LogSanitizer
{
    [GeneratedRegex(@"[\u0000-\u001F]", RegexOptions.IgnoreCase)]
    private static partial Regex ControlCharactersRegex();

    /// <summary>
    /// Sanitizes the specified input.
    /// </summary>
    /// <param name="input">The input.</param>
    /// <remarks>
    /// Removes newlines to prevent log injection.
    /// Replace tabs with spaces (prevents spacing issues)
    /// Remove other control characters (ASCII &lt; 32 except space).
    /// </remarks>
    /// <returns></returns>
    public static string Sanitize(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return string.Empty;
        }

        input = input.Replace("\r", "").Replace("\n", "").Replace("\t", "");
        input = ControlCharactersRegex().Replace(input, "");

        return input;
    }
}