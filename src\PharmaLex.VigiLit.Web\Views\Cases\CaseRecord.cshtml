﻿@using Microsoft.AspNetCore.Authorization
@using PharmaLex.VigiLit.Domain.Enums;
@using PharmaLex.VigiLit.Domain.UserManagement

@inject IAuthorizationService AuthorizationService

@model PharmaLex.VigiLit.Ui.ViewModels.CaseManagement.CaseDetailedResponse

@{
    ViewData["Title"] = "Published Cases | " + @Model.CaseId;
}

@Html.AntiForgeryToken()

<div id="caseView" data-case-id="@Model.CaseId">

    <div class="sub-header">
        <h2>Published Cases | @Model.CaseId</h2>
    </div>
    <section class="flex flex-wrap flex-gap card-container">
        <div class="section-card expand">
            <h2>Overview</h2>
            <div class="details-grid">
                <label>Case ID</label>
                <span>@Model.CaseId</span>
                <label>PLX ID</label>
                <span>@Model.PlxId</span>
                <label>Substance</label>
                <Span>@Model.SubstanceName</span>
                @if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalUser)).Succeeded)
                {
                    <label>Company</label>
                    {
                        var companies = Model.Companies
                        .Where(company => Model.SelectedCompanyIds.Contains(company.Id))
                        .Select(company => company.Name)
                        .OrderBy(name => name);
                        var companyValue = string.Join(", ", companies);
                        <span>@companyValue</span>
                    }
                }
                <label>PSUR</label>
                @{
                    var spanClassPSUR = Model.PSUR.ToString() != null ? "badge-" + Model.PSUR.ToString().ToLower() : string.Empty;
                    <span class=@spanClassPSUR>@Model.PSUR</span>
                }
                <label>MLM</label>
                @{
                    var shouldHideMlm = Model.MLM.ToString() == null || Model.MLM == CaseMLMDuplicate.NA;
                    var spanClassMLM = shouldHideMlm ? string.Empty : "badge-" + Model.MLM.ToString().ToLower();
                    var spanContents = shouldHideMlm ? string.Empty : Model.MLM.ToString();
                    <span class=@spanClassMLM>@spanContents</span>
                }
                <label>PV Safety Database ID</label>
                <span>@Model.PvSafetyDatabaseId</span>
                <label>Comment</label>
                <span>@Model.Comment</span>
            </div>
            <hr class="full-width-rule mb-2 mt-2"/>
            <div class="details-grid">
                <label>Created On</label>
                <span>@Model.CreatedDate</span>
                @if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalUser)).Succeeded)
                {
                    <label>Modified By</label>
                    var toDisplay = string.IsNullOrEmpty(Model.LastUpdatedByFullName) ? Model.LastUpdatedBy : Model.LastUpdatedByFullName;
                    <span>@toDisplay</span>
                }
            </div>
        </div>
        <div class="section-card expand">
            <h2>Files</h2>
            <div class="case-upload-file-list">
                <ul id="uploadedFilenames">
                    @if (Model.UploadedFiles != null)
                    {
                        foreach (var uploadedFile in Model.UploadedFiles)
                        {
                            var fileSizeText = uploadedFile.FileSize == 0 ? string.Empty : $"({Math.Round(uploadedFile.FileSize / 1024.0 / 1024.0, 2)} MB)";
                            <li class="case-upload-file-item">
                                <div>@uploadedFile.FileName @fileSizeText</div>
                                <div class="case-upload-file-options" data-case-file-id="@uploadedFile.CaseFileId" data-case-file-name="@uploadedFile.FileName">
                                    <div class="case-upload-file-download">download</div>
                                </div>
                            </li>
                        }
                    }
                </ul>
            </div>
        </div>
    </section>
</div>


<script type="text/javascript">
    $(document).ready(function () {

        function downloadFile() {
            var caseFileId = $(this).parent().attr('data-case-file-id');
            var caseFileName = $(this).parent().attr('data-case-file-name');
            if (caseFileId !== undefined) {
                var caseId = $("#caseView").attr('data-case-id');
                download(`/Cases/File?caseId=${caseId}&caseFileId=${caseFileId}`, this, caseFileName);
            }
            else {
                var uploadId = $("#case-upload-form").attr('data-upload-id');
                download(`/Cases/File/Temporary?uploadId=${uploadId}&caseFileName=${caseFileName}`, this, caseFileName);
            }
        }

        $('#caseView .case-upload-file-options div.case-upload-file-download').click(downloadFile);

        function download(downloadUrl, button, fileName) {
            $(button).text('downloading');
            plx.toast.show(`Started downloading ${fileName}, please wait...`, 2, 'confirm', null, 2500);

            var onFailure = function () {
                plx.toast.show('Unable to reach the file, please try again', 2, 'failed', null, 2500);
            }
            var onComplete = function () {
                $(button).text('download');
            }
            DownloadFile.fromUrl(downloadUrl, null, null, onFailure, onComplete);
        }
    });
</script>