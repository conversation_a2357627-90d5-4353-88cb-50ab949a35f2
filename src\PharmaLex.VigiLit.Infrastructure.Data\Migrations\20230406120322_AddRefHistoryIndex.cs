﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddRefHistoryIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] ([Id],[PeriodEnd],[PeriodStart])");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] WITH ( ONLINE = OFF )");
        }
    }
}
