/*!*****************************************************!*\
  !*** ./src/js/utilities/local-storage-validator.js ***!
  \*****************************************************/
LocalStorageValidator={validateJsonProperties:function(e=null,r=null){if(!e||!r)return!1;var t,n=localStorage.getItem(e);if(!n)return!0;try{if(null==(t=JSON.parse(n)))return!1}catch(e){return!1}for(const e of Object.getOwnPropertyNames(r))if(!Object.hasOwn(t,e))return!1;return!0},clearJsonItemIfInvalid:function(e,r){this.validateJsonProperties(e,r)||localStorage.removeItem(e)}};
//# sourceMappingURL=localStorageValidator.js.map