﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Services;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using System.Reflection;

namespace PharmaLex.VigiLit.Scraping.Service;

public static class ConfigureServices
{
    public static void RegisterScrapingServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IEnumerable<AzureStorageDocumentOptions>>(sp =>
        {
            return new List<AzureStorageDocumentOptions>
            {
                sp.GetRequiredService<IOptions<AzureStorageImportScrapeDocumentOptions>>().Value
            };
        });
        services.Configure<AzureStorageImportScrapeDocumentOptions>(configuration.GetSection(AzureStorageImportScrapeDocumentOptions.ConfigurationKey));
        services.AddScoped<IDownloadBlobStorage, DownloadBlobStorage>();
        services.AddApify(configuration);
        services.AddScoped<IApifyNotification, ApifyNotification>();

        services.AddSingleton(sp =>
        {
            return new List<ApifyWebhookSettings>
            {
                sp.GetRequiredService<IOptions<ApifyWebhookSettings>>().Value
            };
        });
        //  services.Configure<ApifyWebhookSettings>(configuration.GetSection(ApifyConfiguration.ConfigKey));


        // var assembly = typeof(ConfigureServices).Assembly;
        //services.AddAutoMapper(assembly);

        //services.AddControllers().AddApplicationPart(assembly);
        var assembly = typeof(Phlex.Core.Apify.Webhook.Controllers.ApifyWebhookController).Assembly;

        //var assembly = Assembly.Load("Phlex.Core.Apify.Webhook");
        services.AddControllers(options =>
        {
            // Configure global filters for webhook controllers
            // The external ApifyWebhookController should allow anonymous access
            // and ignore antiforgery tokens since external webhooks can't provide them
        })
        .AddApplicationPart(assembly); // Register the external assembly
    }
}