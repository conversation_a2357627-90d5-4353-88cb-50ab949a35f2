﻿using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;

public class ViewReferenceClassificationPermissionContext : IAccessControlContext
{
    /// <summary>
    /// Context for checking permissions for a user to view a reference classification.
    /// </summary>
    /// <seealso cref="PharmaLex.VigiLit.AccessControl.IAccessControlContext" />
    public ViewReferenceClassificationPermissionContext(int userId, int referenceClassificationId)
    {
        UserId = userId;
        ReferenceClassificationId = referenceClassificationId;
    }

    public int UserId { get; }

    public int ReferenceClassificationId { get; }
}
