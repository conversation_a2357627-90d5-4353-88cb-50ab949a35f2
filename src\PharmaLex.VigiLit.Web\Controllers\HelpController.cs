﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using System;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize]
[Route("[controller]")]
public class HelpController: BaseController
{
    public HelpController(IUserSessionService userSessionService,
                                IConfiguration configuration) : base(userSessionService, configuration)

    {
    }

    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    [HttpGet("[action]")]
    public FileStreamResult ViewUserManual()
    {
        string path = AppDomain.CurrentDomain.BaseDirectory + "Views\\Help\\Files\\";
        string fileName = "VigiLit User Manual V1.pdf";
        string contentType = "application/pdf";

        var stream = System.IO.File.OpenRead(path + fileName);

        return new FileStreamResult(stream, contentType);
    }

    [HttpGet("[action]")]
    public FileStreamResult DownloadUserManual()
    {
        string path = AppDomain.CurrentDomain.BaseDirectory + "Views\\Help\\Files\\";
        string fileName = "VigiLit User Manual V1.pdf";
        string contentType = "application/pdf";

        var stream = System.IO.File.OpenRead(path + fileName);

        var result = new FileStreamResult(stream, contentType)
        {
            FileDownloadName = fileName
        };

        return result;
    }
}