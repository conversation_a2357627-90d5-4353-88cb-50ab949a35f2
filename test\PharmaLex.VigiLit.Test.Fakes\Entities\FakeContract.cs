﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.Fakes.Entities;
public class FakeContract : Contract
{
    public FakeContract(int id, Substance substance, Project project, DateTime contractStartDate, List<ContractVersion> contractVersions)
    {
        Id = id;
        Substance = substance;
        Project = project;
        ContractVersionsInternal = contractVersions;
        ContractStartDate = contractStartDate;
    }
}
