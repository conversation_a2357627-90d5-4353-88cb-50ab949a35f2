﻿using Moq;
using PharmaLex.VigiLit.ContractManagement.Access;
using PharmaLex.VigiLit.ContractManagement.Security;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ContractManagement.Unit.Tests.Access;
public class ViewContractEvaluatorTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new();
    private readonly Mock<IContractRepository> _mockContractRepository = new();

    private const int UserId = 42;
    private const int ContractId = 43;


    [Fact]
    public async Task ViewContract_NullUser_Returns_False()
    {
        // Arrange
        FakeUser? user = null;

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewContractEvaluator = new ViewContractEvaluator(_mockUserRepository.Object, _mockContractRepository.Object);
        var viewContractPermissionContext = new ViewContractPermissionContext(UserId, ContractId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewContractEvaluator.HasPermissions(viewContractPermissionContext);
        });
    }

    [Fact]
    public async Task ViewContract_InternalUser_Returns_True()
    {
        // Arrange
        var user = GetInternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewContractEvaluator = new ViewContractEvaluator(_mockUserRepository.Object, _mockContractRepository.Object);
        var viewContractPermissionContext = new ViewContractPermissionContext(UserId, ContractId);


        // Act
        var result = await viewContractEvaluator.HasPermissions(viewContractPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewContract_ExternalUser_Returns_False_If_User_HasNoActiveCompany()
    {
        // Arrange
        var user = GetExternalUser();
        user.CompanyUser.Active = false;

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        var viewContractEvaluator = new ViewContractEvaluator(_mockUserRepository.Object, _mockContractRepository.Object);
        var viewContractPermissionContext = new ViewContractPermissionContext(UserId, ContractId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewContractEvaluator.HasPermissions(viewContractPermissionContext);
        });
    }

    [Fact]
    public async Task ViewContract_ExternalUser_Returns_True_If_contract_belongs_to_company()
    {
        // Arrange
        var user = GetExternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        _mockContractRepository
            .Setup(x => x.DoesContractBelongToCompany(43, 43))
            .ReturnsAsync(true);

        var viewContractEvaluator = new ViewContractEvaluator(_mockUserRepository.Object, _mockContractRepository.Object);
        var viewContractPermissionContext = new ViewContractPermissionContext(UserId, ContractId);

        // Act
        var result = await viewContractEvaluator.HasPermissions(viewContractPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewContract_ExternalUser_Returns_False_If_contract_does_not_belong_to_company()
    {
        // Arrange
        var user = GetExternalUser();

        _mockUserRepository
            .Setup(x => x.GetForSecurity(42))
            .ReturnsAsync(user);

        _mockContractRepository
            .Setup(x => x.DoesContractBelongToCompany(43, 43))
            .ReturnsAsync(false);

        var viewContractEvaluator = new ViewContractEvaluator(_mockUserRepository.Object, _mockContractRepository.Object);
        var viewContractPermissionContext = new ViewContractPermissionContext(UserId, ContractId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewContractEvaluator.HasPermissions(viewContractPermissionContext);
        });
    }

    private static FakeUser GetInternalUser()
    {
        return new FakeUser("FirstName", "LastName", "EmailAddress", 42, DateTime.Now, DateTime.Now);
    }

    private static FakeUser GetExternalUser()
    {
        var user = new FakeUser("FirstName", "LastName", "EmailAddress", 42, DateTime.Now, DateTime.Now);
        user.AddClaim(new FakeClaim(6, "ClientResearcher"));
        user.CompanyUser = new CompanyUser() { Active = true, CompanyId = 43 };
        return user;
    }

}
