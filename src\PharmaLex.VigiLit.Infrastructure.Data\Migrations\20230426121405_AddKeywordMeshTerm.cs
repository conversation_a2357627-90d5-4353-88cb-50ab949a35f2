﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddKeywordMeshTerm : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Keywords",
                table: "ReferenceUpdates");

            migrationBuilder.DropColumn(
                name: "MeshHeadings",
                table: "ReferenceUpdates");

            migrationBuilder.DropColumn(
                name: "Keywords",
                table: "References")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferencesHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);

            migrationBuilder.DropColumn(
                name: "MeshHeadings",
                table: "References")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferencesHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);

            migrationBuilder.CreateTable(
                name: "Keywords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Keywords", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KeywordUpdates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    ReferenceUpdateId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KeywordUpdates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KeywordUpdates_ReferenceUpdates_ReferenceUpdateId",
                        column: x => x.ReferenceUpdateId,
                        principalTable: "ReferenceUpdates",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "MeshTerms",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeshTerms", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MeshTermUpdates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    ReferenceUpdateId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeshTermUpdates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MeshTermUpdates_ReferenceUpdates_ReferenceUpdateId",
                        column: x => x.ReferenceUpdateId,
                        principalTable: "ReferenceUpdates",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "KeywordReference",
                columns: table => new
                {
                    KeywordsInternalId = table.Column<int>(type: "int", nullable: false),
                    ReferencesId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KeywordReference", x => new { x.KeywordsInternalId, x.ReferencesId });
                    table.ForeignKey(
                        name: "FK_KeywordReference_Keywords_KeywordsInternalId",
                        column: x => x.KeywordsInternalId,
                        principalTable: "Keywords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_KeywordReference_References_ReferencesId",
                        column: x => x.ReferencesId,
                        principalTable: "References",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MeshTermReference",
                columns: table => new
                {
                    MeshTermsInternalId = table.Column<int>(type: "int", nullable: false),
                    ReferencesId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeshTermReference", x => new { x.MeshTermsInternalId, x.ReferencesId });
                    table.ForeignKey(
                        name: "FK_MeshTermReference_MeshTerms_MeshTermsInternalId",
                        column: x => x.MeshTermsInternalId,
                        principalTable: "MeshTerms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MeshTermReference_References_ReferencesId",
                        column: x => x.ReferencesId,
                        principalTable: "References",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_KeywordReference_ReferencesId",
                table: "KeywordReference",
                column: "ReferencesId");

            migrationBuilder.CreateIndex(
                name: "IX_Keywords_Name",
                table: "Keywords",
                column: "Name",
                unique: true,
                filter: "[Name] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_KeywordUpdates_ReferenceUpdateId",
                table: "KeywordUpdates",
                column: "ReferenceUpdateId");

            migrationBuilder.CreateIndex(
                name: "IX_MeshTermReference_ReferencesId",
                table: "MeshTermReference",
                column: "ReferencesId");

            migrationBuilder.CreateIndex(
                name: "IX_MeshTerms_Name",
                table: "MeshTerms",
                column: "Name",
                unique: true,
                filter: "[Name] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_MeshTermUpdates_ReferenceUpdateId",
                table: "MeshTermUpdates",
                column: "ReferenceUpdateId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "KeywordReference");

            migrationBuilder.DropTable(
                name: "KeywordUpdates");

            migrationBuilder.DropTable(
                name: "MeshTermReference");

            migrationBuilder.DropTable(
                name: "MeshTermUpdates");

            migrationBuilder.DropTable(
                name: "Keywords");

            migrationBuilder.DropTable(
                name: "MeshTerms");

            migrationBuilder.AddColumn<string>(
                name: "Keywords",
                table: "ReferenceUpdates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MeshHeadings",
                table: "ReferenceUpdates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Keywords",
                table: "References",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MeshHeadings",
                table: "References",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
