﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>88ab51b5-c175-4979-9645-780e13e2f12c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Views\Reports\TrackingSheets\Index.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Views\Reports\TrackingSheets\Index.cshtml" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
		<PackageReference Include="Telerik.Documents.Flow.FormatProviders.Pdf" Version="2024.1.124" />
		<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Company\PharmaLex.VigiLit.Company.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Ui\PharmaLex.VigiLit.Reporting.Ui.csproj" />
	</ItemGroup>
</Project>
