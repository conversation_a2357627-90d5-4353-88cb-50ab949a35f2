﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests;

public class ImportServiceTests
{
    private readonly IMapper _mapper;
    private readonly IImportService _importService;

    private readonly Mock<IImportRepository> _importRepository = new();
    private readonly Mock<IImportContractRepository> _importContractRepository = new();
    private readonly Mock<IAdHocImportRepository> _adHocImportRepository = new();
    private readonly Mock<IAdHocImportContractRepository> _adHocImportContractRepository = new();

    public ImportServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportReferenceMappingProfile());
            mc.AddProfile(new ImportFileMappingProfile());
            mc.AddProfile(new ManualEntryMappingProfile());
            mc.AddProfile(new FailedImportMappingProfile());
        });

        _mapper = mapperConfig.CreateMapper();

        _importService = new ImportService(
            _importRepository.Object,
            _importContractRepository.Object,
            _adHocImportContractRepository.Object);
    }

}

