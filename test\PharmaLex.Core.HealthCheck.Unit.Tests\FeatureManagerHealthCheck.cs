using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.FeatureManagement;
using Moq;

namespace PharmaLex.Core.HealthCheck.Unit.Tests;

public class FeatureManagerHealthCheckTests
{
    private readonly FeatureManagerHealthCheck _sutFeatureManagerHealthCheck;
    private readonly Mock<IFeatureManager> _mockFeatureManager = new();
    private const string DisplayAiSuggestions = "DisplayAiSuggestions";
    private readonly HealthCheckContext? _healthContext = null;

    public FeatureManagerHealthCheckTests()
    {
        _sutFeatureManagerHealthCheck = new FeatureManagerHealthCheck(DisplayAiSuggestions, new NullLoggerFactory(),
            _mockFeatureManager.Object);
    }


    [Fact]
    public async Task GetHealthStatus_WhenFeatureFlagEnabled_ReturnsHealthy()
    {
        // Arrange
        _mockFeatureManager.Setup(x => x.IsEnabledAsync(DisplayAiSuggestions)).ReturnsAsync(true);

        //Act
        var result = await _sutFeatureManagerHealthCheck.CheckHealthAsync(_healthContext);


        //Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
    }

    [Fact]
    public async Task GetHealthStatus_WhenFeatureFlagDisabled_ReturnsUnHealthy()
    {
        // Arrange
        _mockFeatureManager.Setup(x => x.IsEnabledAsync(DisplayAiSuggestions)).ReturnsAsync(false);

        //Act
        var result = await _sutFeatureManagerHealthCheck.CheckHealthAsync(_healthContext);

        //Assert
        Assert.Equal(HealthStatus.Unhealthy, result.Status);
    }


    [Fact]
    public async void GetHealthStatus_ExceptionScenario_ReturnsUnHealthyAndThrowsException()
    {
        // Arrange
        _mockFeatureManager.Setup(x => x.IsEnabledAsync(DisplayAiSuggestions)).Throws<Exception>();

        // Act
        var result = await _sutFeatureManagerHealthCheck.CheckHealthAsync(_healthContext);

        //Assert
        Assert.Equal(HealthStatus.Unhealthy, result.Status);
    }
}