﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Views\Reports\AccovionClientReport\Index.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Views\Reports\AccovionClientReport\Index.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="HtmlSanitizer" Version="9.0.884" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.262" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Ui\PharmaLex.VigiLit.Reporting.Ui.csproj" />
  </ItemGroup>

  <ItemGroup>
	  <InternalsVisibleTo Include="PharmaLex.VigiLit.Reports.Accovion.Domain.Unit.Tests"></InternalsVisibleTo>
	  <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>

</Project>
