﻿using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests;

public class ReferenceBuilderTests
{
    private readonly IReferenceBuilder _referenceBuilder;

    public ReferenceBuilderTests()
    {
        _referenceBuilder = new ReferenceBuilder();
    }

    [Fact]
    public void Build_EmptyArticle_As_Control_For_Other_Tests()
    {
        // Arrange
        var article = GetEmptyArticle();

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.NotNull(reference);
    }

    [Fact]
    public void Build_Sets_Pmid()
    {
        // Arrange
        var article = GetEmptyArticle();

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("123", reference.SourceId);
    }

    [Fact]
    public void Build_Sets_Doi()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.PubmedData = new()
        {
            ArticleIdList = new()
            {
                new(){ IdType = ArticleIdIdType.doi, Value = "my doi" }
            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my doi", reference.Doi);
    }

    [Fact]
    public void Build_Sets_DateRevised()
    {
        // Arrange
        var article = GetEmptyArticle();

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal(2000, reference.DateRevised.Year);
        Assert.Equal(1, reference.DateRevised.Month);
        Assert.Equal(13, reference.DateRevised.Day);
    }

    [Fact]
    public void Build_Sets_Title()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.ArticleTitle = new() { Text = new[] { "my title" } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my title", reference.Title);
    }

    [Fact]
    public void Build_Sets_Abstract_From_Single_AbstractText()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Abstract = new() { AbstractText = new() { new() { Text = new[] { "single abstract text" } } } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("single abstract text", reference.Abstract);
    }

    [Fact]
    public void Build_Sets_Abstract_From_Multiple_AbstractTexts()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Abstract = new()
        {
            AbstractText = new()
            {
                new() { Label = "label 1", Text = new[] { "single abstract text" } },
                new() { Label = "label 2", Text = new[] { "another abstract text" } }
            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("label 1: single abstract text<br>" + Environment.NewLine + "label 2: another abstract text<br>", reference.Abstract);
    }

    [Fact]
    public void Build_Sets_Language()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Language = new List<string> { "eng" };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("eng", reference.Language);
    }

    [Fact]
    public void Build_Sets_Pagination()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Pagination = new() { MedlinePgn = "2-3" };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("2-3", reference.FullPagination);
    }

    [Fact]
    public void Build_Sets_PublicationType()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.PublicationTypeList = new()
        {
            new() { Value = "Journal Article" },
            new() { Value = "Review" }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("Journal Article; Review", reference.PublicationType);
    }

    [Fact]
    public void Build_Sets_Issn()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { ISSN = new() { Value = "my issn" } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my issn", reference.Issn);
    }

    [Fact]
    public void Build_Sets_VolumeAbbreviation_From_MedlineTA()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { ISOAbbreviation = "my iso abbr" };
        article.MedlineCitation.MedlineJournalInfo = new() { MedlineTA = "my medline ta" };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my medline ta", reference.VolumeAbbreviation);
    }

    [Fact]
    public void Build_Sets_VolumeAbbreviation_From_ISOAbbreviation()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { ISOAbbreviation = "my iso abbr" };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my iso abbr", reference.VolumeAbbreviation);
    }

    [Fact]
    public void Build_Sets_Issue()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { JournalIssue = new() { Issue = "my issue" } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my issue", reference.Issue);
    }

    [Fact]
    public void Build_Sets_PublicationYear()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { JournalIssue = new() { PubDate = new() { Year = 2021 } } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal((ushort)2021, reference.PublicationYear);
    }

    [Fact]
    public void Build_Sets_Volume()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { JournalIssue = new() { Volume = "my volume" } };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my volume", reference.Volume);
    }

    [Fact]
    public void Build_Sets_JournalTitle()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.Journal = new() { Title = "my journal title" };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my journal title", reference.JournalTitle);
    }

    [Fact]
    public void Build_Sets_Authors_With_Few_Values()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.AuthorList = new()
        {
            Author = new()
            {
                new() { LastName = "Smith", Initials = "A" },
                new() { LastName = "Bloggs", Initials = "J" }
            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("Smith A, Bloggs J", reference.Authors);
    }

    [Fact]
    public void Build_Sets_Authors_With_Many_Values()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.AuthorList = new()
        {
            Author = new()
            {
                new() { LastName = "Smith", Initials = "A" },
                new() { LastName = "Bloggs", Initials = "J" },
                new() { LastName = "Cameron", Initials = "D" },
                new() { LastName = "May", Initials = "T" },
                new() { LastName = "Johnson", Initials = "B" },
                new() { LastName = "Truss", Initials = "L" },
                new() { LastName = "Sunak", Initials = "R" },
                new() { LastName = "Starmer", Initials = "K" }

            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("Smith A, Bloggs J, Cameron D, May T, Johnson B, Truss L, et al", reference.Authors);
    }

    [Fact]
    public void Build_Sets_AffiliationTextFirstAuthor()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.AuthorList = new()
        {
            Author = new()
            {
                new() { AffiliationInfo = new(){ new() { Affiliation = new() { Text = "my affiliation text first author" } } } }
            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("my affiliation text first author", reference.AffiliationTextFirstAuthor);
    }

    [Fact]
    public void Build_Sets_CountryOfOccurrence()
    {
        // Arrange
        var article = GetEmptyArticle();

        article.MedlineCitation.Article.AuthorList = new()
        {
            Author = new()
            {
                new() { AffiliationInfo = new(){ new() { Affiliation = new() { Text = "my affiliation text first author, India." } } } }
            }
        };

        // Act
        var reference = _referenceBuilder.Build(article);

        // Assert
        Assert.Equal("India", reference.CountryOfOccurrence);
    }

    private static PubmedArticle GetEmptyArticle()
    {
        return new()
        {
            MedlineCitation = new MedlineCitation
            {
                DateRevised = new DateRevised { Day = 13, Month = "1", Year = 2000 },
                Article = new Article { },
                PMID = new PMID { Value = 123 }
            }
        };
    }
}
