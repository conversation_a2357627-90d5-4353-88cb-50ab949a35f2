﻿using PharmaLex.BlobStorage.Descriptors;

namespace PharmaLex.VigiLit.DataExtraction.Service;

public interface IExtractDataFileDownload
{
    Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName);
    Task Delete<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<bool> Exists<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> CopyBack(
            ImportFileDescriptor importFileDescriptor,
            ImportFileUploadDescriptor importFileUploadDescriptor,
            CancellationToken cancellationToken = default);
}