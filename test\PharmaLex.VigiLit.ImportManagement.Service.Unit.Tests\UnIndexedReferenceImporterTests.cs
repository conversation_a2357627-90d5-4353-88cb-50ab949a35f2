﻿using AutoMapper;
using Microsoft.Extensions.Time.Testing;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Service.Services;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using IReferenceClassificationRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceClassificationRepository;
using IReferenceHistoryActionRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceHistoryActionRepository;
using IReferenceRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceRepository;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class UnIndexedReferenceImporterTests
{
    private readonly IMapper _mapper;
    private readonly IUnIndexedReferenceImporter _unIndexedReferenceImporter;
    private readonly Mock<IImportReferenceRepository> _mockImportReferenceRepository = new();
    private readonly Mock<IContractMatcherService> _mockContractMatcherService = new();

    private readonly Mock<IImportingImportRepository> _mockImportRepository = new();
    private readonly Mock<IImportingImportContractRepository> _mockImportContractRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceHistoryActionRepository> _mockReferenceHistoryActionRepository = new();
    private readonly Mock<IImportingImportContractReferenceClassificationRepository> _mockImportContractReferenceClassificationRepository = new();

    private readonly FakeTimeProvider _fakeTimeProvider = new();

    public UnIndexedReferenceImporterTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportReferenceMappingProfile());
        });

        _mapper = mapperConfig.CreateMapper();

        _unIndexedReferenceImporter = new UnIndexedReferenceImporter(
            _mockImportRepository.Object,
            _mockImportContractRepository.Object,
            _mockImportReferenceRepository.Object,
            _mockReferenceRepository.Object,
            _mockReferenceClassificationRepository.Object,
            _mockReferenceHistoryActionRepository.Object,
            _mockImportContractReferenceClassificationRepository.Object,
            _mockContractMatcherService.Object,
            _mapper,
            _fakeTimeProvider);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_DoesNot_SaveRecord_When_ReferenceNotMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).Returns(Task.FromResult(default(ImportReference?)));

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_SavesRecord_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<IReference>()))
            .ReturnsAsync(GetTestContractVersions());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_Saves_Import()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued())
                                                        .ReturnsAsync(GetTestImportReference());

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<IReference>()))
            .ReturnsAsync(GetTestContractVersions());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_SavesImportStatusAsCompleted_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<IReference>()))
            .ReturnsAsync(GetTestContractVersions());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportRepository.Verify(x => x.Add(It.Is<Import>(i => i.ImportStatusType == ImportStatusType.Completed)));
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_SavesImportContractStatusAsCompleted_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<IReference>()))
            .ReturnsAsync(GetTestContractVersions());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportContractRepository.Verify(x => x.Add(It.Is<ImportContract>(i => i.ImportContractStatusType == ImportContractStatusType.Completed)));
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_ImportCreatedCorrectly_When_ReferenceMatched()
    {

        // Arrange
        var dateTime = new DateTime(2004, 1, 28);

        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<IReference>()))
            .ReturnsAsync(GetTestContractVersions());

        _fakeTimeProvider.SetUtcNow(new DateTimeOffset(dateTime));

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportRepository.Verify(x => x.Add(It.Is<Import>(i =>
            i.ImportStatusType == ImportStatusType.Completed)));

        _mockImportRepository.Verify(x => x.Add(It.Is<Import>(i =>
            i.ImportDashboardStatusType == ImportDashboardStatusType.Imported)));

        _mockImportRepository.Verify(x => x.Add(It.Is<Import>(i =>
            i.StartDate.HasValue &&
            i.StartDate.Value == dateTime)));

        _mockImportRepository.Verify(x => x.Add(It.Is<Import>(i =>
            i.EndDate.HasValue &&
            i.EndDate.Value == dateTime)));

        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }


    private static ImportReference GetTestImportReference()
    {
        return new FakeImportReference(100);
    }

    private static List<ContractVersion> GetTestContractVersions()
    {
        return new List<ContractVersion>
        {
            new ContractVersion( GetTestContract(), "some substance" )
        };
    }

    private static Contract GetTestContract()
    {
        return new FakeContract(200, GetTestSubstance(), new Project(), DateTime.Now, new List<ContractVersion>());
    }

    private static Substance GetTestSubstance()
    {
        return new FakeSubstance(300, "Test substance", "ch");
    }
}