﻿using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Test.Framework.Orderers;
using SubstanceRepository = PharmaLex.VigiLit.Data.Repositories.SubstanceRepository;

namespace PharmaLex.VigiLit.ImportManagement.Service.Integration.Tests;

[TestCaseOrderer(
    ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
    ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
public class ContractRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, ContractRepositoryTests>>
{
    private readonly IImportingContractRepository _importingContractRepository;
    private readonly UserHelper _userHelper;
    private readonly CompanyHelper _companyHelper;
    private readonly ContractHelper _contractHelper;
    private readonly CountryHelper _countryHelper;
    private readonly JournalHelper _journalHelper;
    private readonly ProjectHelper _projectHelper;
    private readonly SubstanceHelper _substanceHelper;

    public ContractRepositoryTests(DatabaseFixture<VigiLitDbContext, ContractRepositoryTests> fixture)
    {
        _importingContractRepository = RepositoryFactory.GetRepositoryInstance<Contract, ImportingContractRepository>(fixture.Context);
        var userRepository = RepositoryFactory.GetRepositoryInstance<User, UserRepository, UserMappingProfile>(fixture.Context);
        var companyRepository = RepositoryFactory.GetRepositoryInstance<Company, CompanyRepository, CompanyMappingProfile>(fixture.Context);
        var projectRepository = RepositoryFactory.GetRepositoryInstance<Project, ProjectRepository, ProjectMappingProfile>(fixture.Context);
        var substanceRepository = RepositoryFactory.GetRepositoryInstance<Substance, SubstanceRepository, SubstanceMappingProfile>(fixture.Context);
        var contractRepository = RepositoryFactory.GetRepositoryInstance<Contract, ContractRepository>(fixture.Context);
        var journalRepository = RepositoryFactory.GetRepositoryInstance<Journal, JournalRepository>(fixture.Context);
        var countryRepository = RepositoryFactory.GetRepositoryInstance<Country, CountryRepository>(fixture.Context);

        _userHelper = new UserHelper(userRepository);
        _companyHelper = new CompanyHelper(companyRepository);
        _projectHelper = new ProjectHelper(projectRepository);
        _substanceHelper = new SubstanceHelper(substanceRepository);
        _contractHelper = new ContractHelper(contractRepository);
        _journalHelper = new JournalHelper(journalRepository);
        _countryHelper = new CountryHelper(countryRepository);
    }


    [Fact, TestPriority(1)]
    public async Task GetContractsToMatchOn_Returns_Zero_LatestVersions_For_Global_ScreeningType()
    {
        //Arrange
        await SetUpTestRecord(1, false, true, 1, 2);

        //Act
        var contractVersions = await _importingContractRepository.GetContractsToMatchOn();

        //Assert
        Assert.Empty(contractVersions);
    }

    [Fact, TestPriority(2)]
    public async Task GetContractsToMatchOn_Returns_One_LatestVersions_For_Global_ScreeningType()
    {
        //Arrange
        await SetUpTestRecord(2, true, true, 1, 2);

        //Act
        var contractVersions = await _importingContractRepository.GetContractsToMatchOn();

        //Assert
        Assert.NotNull(contractVersions);
        var collection = contractVersions.ToList();
        Assert.Single(collection);
        Assert.True(collection[0].IsActive);
        Assert.Equal(ContractVersionStatus.Approved, collection[0].ContractVersionStatus);
    }

    [Fact, TestPriority(3)]
    public async Task GetContractsToMatchOn_Returns_Two_LatestVersions_For_Global_ScreeningType()
    {
        //Arrange
        await SetUpTestRecord(3, true, true, 2, 2);

        //Act
        var contractVersions = await _importingContractRepository.GetContractsToMatchOn();

        //Assert
        Assert.NotNull(contractVersions);
        var collection = contractVersions.ToList();
        Assert.Equal(2, collection.Count);
        Assert.True(collection[0].IsActive);
        Assert.Equal(ContractVersionStatus.Approved, collection[0].ContractVersionStatus);
        Assert.True(collection[1].IsActive);
        Assert.Equal(ContractVersionStatus.Approved, collection[1].ContractVersionStatus);
    }

    [Fact, TestPriority(4)]
    public async Task GetContractsToMatchOn_Returns_ContractVersion_with_EnabledJournal_For_LocalScreeningType()
    {
        //Arrange
        var contract = await SetUpTestRecord(4, true, true, 2, 1);
        await AddJournals(contract, new List<(string, bool)>
        {
            ("Journal A", true),
            ("Journal B", false)
        });
        //Act
        var contractVersions = await _importingContractRepository.GetContractsToMatchOn();

        //Assert
        Assert.NotNull(contractVersions);
        var contractVersion = contractVersions.ToList().Last();
        var journals = contractVersion.ContractVersionJournals;

        Assert.Equal(2, journals.Count);
    }

    private async Task<Contract> SetUpTestRecord(int seqNo, bool activeCompany, bool activeProject, int version, int screeningType)
    {

        var user = await _userHelper.AddUser($"User{seqNo}", $"Surname{seqNo}", $"user{seqNo}@email.com");

        var substance = await _substanceHelper.AddSubstance($"Substance {seqNo}", "");

        var company = await _companyHelper.AddCompany($"Company {seqNo}", "", "", activeCompany);

        var project = await _projectHelper.AddProject($"Project {seqNo}", company);

        var contract = await _contractHelper.AddContract(user, substance, project, activeProject, version, screeningType);

        return await _contractHelper.SaveContract(contract);
    }

    private async Task AddJournals(Contract contract, List<(string Name, bool Enabled)> journals)
    {
        var country = await _countryHelper.AddCountry("United Kingdom", "UK");

        var contractVersion = contract.ContractVersions.Last();

        foreach (var (name, enabled) in journals)
        {
            var journal = await _journalHelper.AddJournal(name, country, enabled);

            var contractVersionJournal = new ContractVersionJournal()
            {
                ContractVersion = contractVersion,
                Journal = journal,
            };

            contractVersion.ContractVersionJournals.Add(contractVersionJournal);
        }

        await _contractHelper.SaveContract(contract);
    }
}
