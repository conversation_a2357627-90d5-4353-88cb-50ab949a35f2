using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class PubmedData
{
    [XmlArrayItem("PubMedPubDate", IsNullable = false)]
    public List<PubMedPubDate> History { get; set; } = new List<PubMedPubDate>();

    public string PublicationStatus { get; set; }

    [XmlArrayItem("ArticleId", IsNullable = false)]
    public List<ArticleId> ArticleIdList { get; set; } = new List<ArticleId>();

    [XmlArrayItem("Object", IsNullable = false)]
    public List<PubMedDataObject> ObjectList { get; set; } = new List<PubMedDataObject>();

    [XmlElement("ReferenceList")]
    public List<ReferenceList> ReferenceList { get; set; } = new List<ReferenceList>();
}