﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class SubstanceHelper
{
    private readonly ISubstanceRepository _substanceRepository;

    public SubstanceHelper(ISubstanceRepository substanceRepository)
    {
        _substanceRepository = substanceRepository;
    }

    public async Task<Substance> AddSubstance(string name, string type)
    {
        var substance = new Substance(name, type);
        _substanceRepository.Add(substance);
        await _substanceRepository.SaveChangesAsync();
        return substance;
    }
}
