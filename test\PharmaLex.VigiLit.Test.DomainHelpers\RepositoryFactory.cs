﻿using AutoMapper;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public static class RepositoryFactory
{
#pragma warning disable  CS8625
    public static TRepository GetRepositoryInstance<T, TRepository, TProfile>(VigiLitDbContext context)
        where T : class, IEntity, new()
        where TProfile : Profile, new()
        where TRepository : IRepository<T>
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new TProfile());
        });

        var mapper = mapperConfig.CreateMapper();
        var userContext = new UserContext();

        if (typeof(TRepository) == typeof(SubstanceRepository))
        {
            var args = new object[] { context, null, mapper, userContext };
            var instance = Activator.CreateInstance(typeof(TRepository), args);

            return CheckInstance<T, TRepository>(instance);
        }

        if (typeof(TRepository) == typeof(ProjectRepository))
        {
            var args = new object[] { context, userContext };
            var instance = Activator.CreateInstance(typeof(TRepository), args);

            return CheckInstance<T, TRepository>(instance);
        }
        else
        {
            var args = new object[] { context, mapper, userContext };
            var instance = Activator.CreateInstance(typeof(TRepository), args);

            return CheckInstance<T, TRepository>(instance);
        }
    }

    public static TRepository GetRepositoryInstance<T, TRepository>(VigiLitDbContext context)
        where T : class, IEntity, new()
        where TRepository : IRepository<T>
    {
        var userContext = new UserContext();

        try
        {
            var args = new object[] { context, userContext };
            var instance = Activator.CreateInstance(typeof(TRepository), args);
            return CheckInstance<T, TRepository>(instance);
        }
        catch
        {
            throw new MissingMethodException("Developer: You may need to use the overloaded GetRepositoryInstance that requires a mapper");
        }
    }

    private static TRepository CheckInstance<T, TRepository>(object? instance)
        where T : class, IEntity, new()
        where TRepository : IRepository<T>
    {
        if (instance != null)
        {
            return (TRepository)instance;
        }
        else
        {
            throw new InvalidOperationException();
        }
    }
#pragma warning restore  CS8625
}
