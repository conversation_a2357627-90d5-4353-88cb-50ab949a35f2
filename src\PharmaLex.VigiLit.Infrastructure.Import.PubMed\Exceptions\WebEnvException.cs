﻿using System.Runtime.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Exceptions;

[Serializable]
public class WebEnvException : Exception
{
    public WebEnvException()
    {
    }

    public WebEnvException(string message) : base(message)
    {
    }

    public WebEnvException(string message, Exception innerException) : base(message, innerException)
    {
    }

    protected WebEnvException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }
}