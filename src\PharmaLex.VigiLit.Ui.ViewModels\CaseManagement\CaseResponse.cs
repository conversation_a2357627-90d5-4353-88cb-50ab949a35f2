﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

public record CaseResponse(int? CaseId, int? PlxId, string SubstanceName, IReadOnlyCollection<CompanyModel> Companies, PSURRelevanceAbstract PSUR, 
    CaseMLMDuplicate MLM, string PvSafetyDatabaseId, string Comment, string UploadId, IReadOnlyCollection<CaseResponseFile> UploadedFiles, IReadOnlyCollection<int> SelectedCompanyIds, string CreatedDate, string LastUpdatedBy)
{
    public static CaseResponse Empty(string uploadId)
        => new(null, null, null, Array.Empty<CompanyModel>(), PSURRelevanceAbstract.NA, CaseMLMDuplicate.NA, null, null, uploadId, Array.Empty<CaseResponseFile>(), Array.Empty<int>(), null, null);
}
