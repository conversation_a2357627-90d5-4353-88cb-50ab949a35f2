﻿namespace PharmaLex.VigiLit.ReferenceManagement;

#pragma warning disable S1133 // Obsolete enums might be used later

public enum ReferenceState
{
    New = 0,
    Preclassified = 1,
    Approved = 2,
    Signed = 3,
    [Obsolete("Updated is not currently in use but might be used in future", true)]
    Updated = 4,
    [Obsolete("Duplicate is not currently in use but might be used in future.", true)]
    Duplicate = 5,
    Reclassified = 6,
    Split = 7,

    Inactive = 70,
}
#pragma warning restore S1133