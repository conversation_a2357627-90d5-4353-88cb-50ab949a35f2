﻿Naming convention: <MigrationName>-<ScriptName>.sql

Script execution happens in the migration "Up" method:

        migrationBuilder.SqlFileExec("Initial-CreateUsers.sql");
        migrationBuilder.SqlFileExec("Initial-CreateClaims.sql");
        migrationBuilder.SqlFileExec("Initial-CreateUserClaims.sql");
        migrationBuilder.SqlFileExec("Initial-CreateSubstances.sql");
        migrationBuilder.SqlFileExec("Initial-CreateSourceProviders.sql");
        migrationBuilder.SqlFileExec("Initial-CreateClassificationCategories.sql");
        migrationBuilder.SqlFileExec("Initial-CreateCountries.sql");
        migrationBuilder.SqlFileExec("Initial-CreateEmailPreferences.sql");

Add them in the end of the method on each respective migration.