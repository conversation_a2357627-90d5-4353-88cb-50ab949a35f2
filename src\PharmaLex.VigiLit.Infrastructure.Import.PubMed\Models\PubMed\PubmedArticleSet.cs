﻿using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

[XmlRoot("PubmedArticleSet")]
public class PubmedArticleSet
{
    [XmlElement("PubmedArticle", typeof(PubmedArticle))]
    public List<PubmedArticle> PubMedArticles { get; set; } = new List<PubmedArticle>();

    [XmlElement("PubmedBookArticle", typeof(PubmedBookArticle))]
    public List<PubmedBookArticle> PubMedBookArticles { get; set; } = new List<PubmedBookArticle>();

    [XmlArrayItem("PMID", IsNullable = false)]
    public List<PMID> DeleteCitation { get; set; } = new List<PMID>();

    public void AddRange(PubmedArticleSet set)
    {
        if (set != null)
        {
            PubMedArticles.AddRange(set.PubMedArticles);
            PubMedBookArticles.AddRange(set.PubMedBookArticles);
            DeleteCitation.AddRange(set.DeleteCitation);
        }
    }
}
