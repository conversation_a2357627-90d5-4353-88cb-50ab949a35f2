﻿using Moq;
using PharmaLex.VigiLit.Application.Tests.Fakes;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;
using PharmaLex.VigiLit.ReferenceManagement.Service.Access;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests.Access;

public class ViewReferenceEvaluatorTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new Mock<IUserRepository>();
    private readonly Mock<ICompanyInterestRepository> _mockCompanyInterestRepository = new Mock<ICompanyInterestRepository>();

    private const int UserId = 42;
    private const int ReferenceId = 43;
    private const int CompanyId = 44;
    private readonly User _user = new FakeUser(UserId);


    [Fact]
    public async Task ViewReferenceEvaluator_When_user_can_view_reference_Returns_true()
    {
        // Arrange
        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(_user);
        _mockCompanyInterestRepository.Setup(x => x.CompanyHasInterestInReference(CompanyId, ReferenceId)).ReturnsAsync(true);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);

        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, ReferenceId);

        // Act
        var result = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewReferenceEvaluator_When_user_can_not_view_reference_as_company_has_no_interest_Throws_UnauthorisationAccessException()
    {
        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = false;
        stubbedUser.CompanyUser.CompanyId = CompanyId;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        _mockCompanyInterestRepository.Setup(x => x.CompanyHasInterestInReference(CompanyId, ReferenceId)).ReturnsAsync(false);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);

        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, ReferenceId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);
        });
    }

    // --------------------------

    [Fact]
    public async Task ViewReferenceEvaluator_When_user_does_not_exists_Throws_UnauthorisationAccessException()
    {
        // Arrange
        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync((User?)null);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);
        });
    }

    [Fact]
    public async Task ViewReferenceEvaluator_When_user_is_not_a_company_user_is_an_internal_user_Returns_true()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "NOT ClientResearcher"));

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, CompanyId);

        // Act
        var result = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewReferenceEvaluator_When_user_is_an_external_user_but_user_has_no_active_company_Throws_UnauthorisationAccessException()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = false;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);
        });
    }

    [Fact]
    public async Task ViewReferenceEvaluator_When_user_is_an_external_user_and_company_is_active_but_BelongsToDifferentCompany_Throws_UnauthorisationAccessException()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = true;
        stubbedUser.CompanyUser.CompanyId = 44;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var viewReferenceEvaluator = new ViewReferenceEvaluator(_mockUserRepository.Object, _mockCompanyInterestRepository.Object);
        var viewReferencePermissionContext = new ViewReferencePermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReferenceEvaluator.HasPermissions(viewReferencePermissionContext);
        });
    }
}
