﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class UpdateIndexReferenceStatePreAssessorId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceState",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications",
                columns: new[] { "ReferenceState", "PreAssessorId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceState",
                table: "ReferenceClassifications",
                column: "ReferenceState");
        }
    }
}
