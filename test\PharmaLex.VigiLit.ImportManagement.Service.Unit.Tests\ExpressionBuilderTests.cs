﻿using PharmaLex.VigiLit.ImportManagement.Service.Matching;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;
public class ExpressionBuilderTests
{

    private readonly ExpressionBuilder _expressionBuilder = new ExpressionBuilder();

    [Theory]
    // Straight single word
    [InlineData("Amoxicillin", "IsWordInAbstract(\"Amoxicillin\")")]
    // Word with an ending wildcard
    [InlineData("Amox*", "IsWordInAbstract(\"Amox*\")")]
    // Word in a quotes
    [InlineData(@"""Amoxicillin""", @"IsWordInAbstract(""Amoxicillin"")")]
    // Word with wildcard in quotes
    [InlineData(@"""Amox*""", "IsWordInAbstract(\"Amox*\")")]
    // Multiple words
    [InlineData("Piperacillin Oxytocin", "IsWordInAbstract(\"Piperacillin Oxytocin\")")]
    // Multiple words with a wildcard
    [InlineData("Piperacillin Oxyt*", "IsWordInAbstract(\"Piperacillin Oxyt*\")")]
    // Multiple words in quotes
    [InlineData(@"""Piperacillin Oxytocin""", "IsWordInAbstract(\"Piperacillin Oxytocin\")")]
    // Multiple words in quotes with wildcard
    [InlineData(@"""Piperacillin Oxyt*""", "IsWordInAbstract(\"Piperacillin Oxyt*\")")]
    // Single word terms with an "AND"
    [InlineData("Piperacillin AND Amoxicillin", "IsWordInAbstract(\"Piperacillin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    // Single word terms with an "NOT"
    //[InlineData("(Piperacillin) NOT Amoxicillin", "(IsWordInAbstract(\"Piperacillin\")) &&! IsWordInAbstract(\"Amoxicillin\")")]
    // Single word terms with an "OR"
    [InlineData("Piperacillin OR Amoxicillin", "IsWordInAbstract(\"Piperacillin\")||IsWordInAbstract(\"Amoxicillin\")")]
    // Single word terms with an "AND" and first word in quotes
    [InlineData(@"""Piperacillin"" AND Amoxicillin", "IsWordInAbstract(\"Piperacillin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    // Single word terms with an "OR" and first word in quotes
    [InlineData(@"""Piperacillin"" OR Amoxicillin", "IsWordInAbstract(\"Piperacillin\")||IsWordInAbstract(\"Amoxicillin\")")]
    // Multiple word term in quotes with an "AND" and single word term
    [InlineData(@"""Piperacillin Oxytocin"" AND Amoxicillin", "IsWordInAbstract(\"Piperacillin Oxytocin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    // Multiple word term without quotes with an "AND" and single word term
    [InlineData(@"Piperacillin Oxytocin AND Amoxicillin", "IsWordInAbstract(\"Piperacillin Oxytocin\")&&IsWordInAbstract(\"Amoxicillin\")")]
    // Single word term in brackets
    [InlineData(@"(Piperacillin)", "(IsWordInAbstract(\"Piperacillin\"))")]
    // Single word term in brackets with "AND" and a single word term
    [InlineData(@"(Piperacillin) AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin""))&&IsWordInAbstract(""Amoxicillin"")")]
    // Two single word term in brackets with "AND"
    [InlineData(@"(Piperacillin AND Amoxicillin)", @"(IsWordInAbstract(""Piperacillin"")&&IsWordInAbstract(""Amoxicillin""))")]
    // Multiple word term in brackets with an "AND" and single word term
    [InlineData(@"(Piperacillin Oxytocin) AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin Oxytocin""))&&IsWordInAbstract(""Amoxicillin"")")]
    // Multiple word term in quotes and brackets with an "AND" and single word term
    [InlineData(@"(""Piperacillin Oxytocin"") AND Amoxicillin", @"(IsWordInAbstract(""Piperacillin Oxytocin""))&&IsWordInAbstract(""Amoxicillin"")")]
    // Single word terms with an "NOT"
    [InlineData("(Piperacillin) NOT Amoxicillin", "(IsWordInAbstract(\"Piperacillin\"))&&!IsWordInAbstract(\"Amoxicillin\")")]
    public void ExpressionBuilder_CreatesExpressionCorrectly(string searchTerm, string code)
    {

        // Act
        var convertedExpression = _expressionBuilder.Build(searchTerm);

        // Assert
        Assert.Equal(code, convertedExpression);
    }
}
