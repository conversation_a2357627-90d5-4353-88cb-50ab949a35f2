using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class Author
{
    [XmlElement("CollectiveName", typeof(CollectiveName))]
    public CollectiveName CollectiveName { get; set; }

    [XmlElement("ForeName", typeof(string))]
    public string ForeName { get; set; }

    [XmlElement("Initials", typeof(string))]
    public string Initials { get; set; }

    [XmlElement("LastName", typeof(string))]
    public string LastName { get; set; }

    [XmlElement("Suffix", typeof(Suffix))]
    public Suffix Suffix { get; set; }

    // TODO: Is this used at all?
    [XmlElement("Identifier")]
    public List<Identifier> Identifier { get; set; } = new List<Identifier>();

    [XmlElement("AffiliationInfo")]
    public List<AffiliationInfo> AffiliationInfo { get; set; } = new List<AffiliationInfo>();

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(EnumYN.Y)]
    public EnumYN ValidYN { get; set; } = EnumYN.Y;

    [XmlAttribute()]
    public AuthorEqualContrib EqualContrib { get; set; }

    [XmlIgnore()]
    public bool EqualContribSpecified { get; set; }
}