/*!********************************************!*\
  !*** ./src/js/vue/v3/filtered-table-v3.js ***!
  \********************************************/
vueApp.component("collection-cell",{template:"#collection-cell-template",props:{val:{required:!0,type:Array}},computed:{tipContent(){return`<ul>\n                ${this.val.slice(1,this.val.length).map((e=>`<li>${e}</li>`)).join(" ")}</ul>`}}}),vueApp.component("text-cell",{template:"#text-cell-template",props:{val:String,isYesNo:Boolean}}),vueApp.component("text-edit-select-cell",{template:"#text-edit-select-cell-template",data(){return{invalid:this.data.invalid}},props:{data:Object},methods:{onChange(e){this.invalid&&e.target.reportValidity()},onInput(e){this.data.invalid=this.invalid=this.data.required&&!e.target.checkValidity()}}}),vueApp.component("text-edit-plain-cell",{template:"#text-edit-plain-cell-template",data(){return{invalid:this.data.invalid}},props:{data:Object,inputType:{type:String,default:"text"}},methods:{onChange(e){this.invalid&&e.target.reportValidity()},onInput(e){this.data.invalid=this.invalid=this.data.required&&!e.target.checkValidity()}}}),vueApp.component("link-edit-plain-cell",{template:"#text-edit-plain-cell-template",data(){return{invalid:this.data.invalid}},props:{data:Object,inputType:{type:String,default:"url"}},methods:{onChange(e){this.invalid&&e.target.reportValidity()},onInput(e){this.data.invalid=this.invalid=this.data.required&&!e.target.checkValidity()}}}),vueApp.component("date-edit-plain-cell",{template:"#text-edit-plain-cell-template",data(){return{invalid:this.data.invalid}},props:{data:Object,inputType:{type:String,default:"date"}},methods:{onChange(e){this.invalid&&e.target.reportValidity()},onInput(e){this.data.invalid=this.invalid=this.data.required&&!e.target.checkValidity()}}}),vueApp.component("link-cell",{template:'<td><a class="action-link" :href="href" target="_blank" v-on:click.stop>{{val}}</a></td>',props:["val","href"]}),vueApp.component("multiline-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("html-cell",{template:'<td v-html="val"></td>',props:["val"]}),vueApp.component("number-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("email-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("url-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("date-cell",{template:"<td>{{convert ? convert(val) : val}}</td>",props:["val","convert"]}),vueApp.component("picklist-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("relationship-cell",{template:"<td>{{val}}</td>",props:["val"]}),vueApp.component("bool-cell",{template:"#bool-cell-template",props:{val:Boolean}}),vueApp.component("select-filter",{template:"#select-filter-template",data:function(){return{isOpen:!1,textField:this.config.display,dataField:this.config.dataKey,strings:{...this.resources}}},emits:["update:value","filter"],props:{value:{required:!0,default:""},config:{type:Object,required:!0},filtered:{type:Array,required:!0},resources:Object},computed:{availableOptions:function(){return[...new Set(this.filtered.map((e=>e[this.config.filterCollection])).flat())].map((e=>{if(this.textField===this.dataField)return{key:null===e?"-":e,value:null===e?this.strings.noValue:e};const t=this.config.options.find((t=>t[this.dataField]===e));return{key:null===e?"-":e,value:t?t[this.textField]:this.strings.noValue}})).sort(((e,t)=>e.value.localeCompare(t.value,"en",{sensitivity:"base"})))}},methods:{open(){this.isOpen=!this.isOpen,this.$nextTick((function(){this.$refs.select.focus()}))},change(e){this.isOpen=!1,this.$emit("update:value",e),this.$emit("filter",{key:this.config.key})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1)},close(){this.isOpen=!1}},mounted:function(){document.addEventListener("click",this.handleClickOutside),window.addEventListener("resize",this.close)},destroyed:function(){document.removeEventListener("click",this.handleClickOutside),window.removeEventListener("resize",this.close)}}),vueApp.component("data-table-pager",{template:"#data-table-pager-template",data(){return{strings:{...this.resources}}},emits:["page-size-change","page-index-change"],props:{isPaged:Boolean,itemCount:Number,pageIndex:Number,pageSize:Number,location:{type:String,default:"bottom"},totalItems:{type:Number,required:!0},resources:Object},computed:{startIndex:function(){return this.pageSize*this.pageIndex},endIndex:function(){return Math.min(parseInt(this.startIndex)+parseInt(this.pageSize),this.itemCount)},pageCount:function(){let e=this.itemCount%this.pageSize?1:0;return Math.floor(this.itemCount/this.pageSize)+e}},methods:{interpolate(e,...t){const i=e.split("{}");let s=i[0];return t.forEach(((e,t)=>{s+=e,i.length>t+1&&(s+=i[t+1])})),s},pageSizeChange(e){this.$emit("page-size-change",+e)}}}),vueApp.component("search-filter",{template:"#search-filter-template",data:()=>({isOpen:!1}),emits:["update:value","filter"],props:{value:{required:!0,default:""},config:{type:Object,required:!0},filtered:{type:Array,required:!0}},methods:{open(){this.isOpen=!this.isOpen,this.isOpen&&this.$nextTick((function(){this.$refs.search.focus()}))},change(e){this.$emit("update:value",e),this.$emit("filter",{key:this.config.key})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1)},close(){this.isOpen=!1},clear(){this.change(""),this.close()}},mounted:function(){document.addEventListener("click",this.handleClickOutside),window.addEventListener("resize",this.close)},destroyed:function(){document.removeEventListener("click",this.handleClickOutside),window.removeEventListener("resize",this.close)}}),vueApp.component("filtered-table",{template:"#filtered-table-template",data:function(){return{data:{count:this.items?.length||0,items:this.items?.length?this.items.map((e=>({...e}))):[],paged:!this.items},size:this.pageSize,pageIndex:0,filterModel:this.filters.reduce(((e,t)=>(e[t.key]="",e)),{}),selectedItems:[],sortMode:this.columns.sortMode||"single",sortModel:this.columns.config.reduce(((e,t)=>(t.sortKey&&(e[t.sortKey]=t.sortDirection||0),e)),{}),internalFilters:this.filters.map((e=>{let t={...e},i=t.options[0]?.hasOwnProperty("id")?"id":"Id",s=t.options[0]?.hasOwnProperty("name")?"name":"Name";return t.dataKey=t.dataKey?t.dataKey:i,t.display=t.display?t.display:s,t.convert=t.convert||(e=>e),t.fn=t.fn||(e=>i=>(null===i[t.dataKey]?this.strings.noValue:i[t.dataKey]).toLowerCase().includes(e.toLowerCase())),t})),storageKey:`${window.location.href}(${this.$attrs.id||""})`,editItem:null,working:!1,strings:{noRecordsMessage:"No matching records found",sortByFormat:"Sort by {}",filterByFormat:"Filter by {}",searchInFormat:"Search in {}",clearFilters:"Clear filters",addItem:"Add item",edit:"Edit",remove:"Delete",save:"Save",cancel:"Cancel",noValue:"-- No Value",pager:{showingFormat:"Showing {} to {} of {} entries",showingFilteredFormat:"(filtered from {})",pageSize:"Page size",first:"First",previous:"Previous",next:"Next",last:"Last"},...this.resources}}},emits:["filter","on-selection-change"],props:{items:Array,filters:{type:Array,default:()=>[]},pageSize:{type:Number,default:25},pagerLocation:{type:String,default:"bottom"},columns:Object,styling:String,link:String,itemsUrl:{type:String,default:window.location.href+"/paged"},behavior:String,addurl:String,resources:{type:Object,default:()=>({})},selectable:{type:Boolean,default:!1}},computed:{appliedFilters:function(){return Object.keys(this.filterModel).reduce(((e,t)=>{let i=this.filterModel[t];if(i||!1===i||0===i){let s=this.getFilterByKey(t);e[t]=s.fn(s.convert(i))}return e}),{})},appliedSorts:function(){return Object.keys(this.sortModel).reduce(((e,t)=>{let i=this.columns.config.find((e=>e.sortKey===t)),s=this.data.items[0]?typeof this.data.items[0][i.sortKey]:null,l=i?i.sortComparer||s:null;return"object"===l&&(l="string"),e.push({key:t,comparer:this.getComparer(t,this.sortModel[t],l)}),e}),[])},itemCount:function(){return this.data.paged?this.data.count:this.filterItems.length},filterItems:function(){return this.data.paged?this.data.items:(this.pageIndex=0,Object.keys(this.appliedFilters).reduce(((e,t)=>e.filter(this.appliedFilters[t])),this.data.items))},sortItems:function(){return this.data.paged?this.filterItems.slice():this.appliedSorts.reduce(((e,t)=>e.sort(t.comparer)),this.filterItems.slice())},pageItems:function(){return this.data.paged?this.sortItems.slice():this.sortItems.slice(this.size*this.pageIndex,this.size*(this.pageIndex+1))},pageItemsAdd(){return this.editItem&&!this.editItem.id?[this.editItem,...this.pageItems]:this.pageItems},isFiltered(){return!!Object.keys(this.appliedFilters).length},allSelected(){return this.pageItemsAdd.length&&this.pageItemsAdd.filter((e=>this.selectedItems.includes(e.id))).length==this.pageItemsAdd.length}},methods:{interpolate(e,...t){const i=e.split("{}");let s=i[0];return t.forEach(((e,t)=>{s+=e,i.length>t+1&&(s+=i[t+1])})),s},loadState(){try{let e=localStorage.getItem(this.storageKey),t=e?JSON.parse(e):{date:new Date};!function(e){let t=new Date,i=new Date(e.date);return t.getDay()===i.getDay()&&t.getMonth()===i.getMonth()&&t.getYear()===i.getYear()}(t)?localStorage.removeItem(this.storageKey):(t.filterModel&&(this.filterModel=t.filterModel,Object.keys(this.filterModel).forEach((e=>{this.filterModel[e]&&this.filter({key:e})}))),t.sortModel&&t.sortModel.forEach((e=>{let[t,i]=e;this.sortModel[t]=i})),this.size=t.pageSize||this.size,this.data.paged?this.pageIndex=t.hasOwnProperty("pageIndex")?t.pageIndex:this.pageIndex:this.$nextTick((function(){this.pageIndex=t.hasOwnProperty("pageIndex")?t.pageIndex:this.pageIndex})))}catch{localStorage.removeItem(this.storageKey)}},updateState(e){let t=localStorage.getItem(this.storageKey),i=t?JSON.parse(t):{};e={...e,date:new Date},localStorage.setItem(this.storageKey,JSON.stringify(Object.assign(i,e)))},getCellType(e,t){return this.editItem?.id===e[this.columns.idKey]&&t.edit?`${t.type}-edit-${t.edit.type}-cell`:`${t.type}-cell`},getHref(e,t){if("link"===t?.type)return e[t.dataKey];if(!this.link)return"/";let[i,s]=this.link.match(/\{([a-z0-9]+)\}/i)||[];return s?this.link.replace(i,e[s]):this.link+e[this.columns.idKey]},rowClicked(e){if(!this.editItem)if(this.link?.length>0){let t=this.getHref(e);if("newtab"===this.behavior){let e=document.createElement("a");e.href=t,e.setAttribute("target","_blank"),e.click()}else window.location.href=t}else this.$emit("row-clicked",e)},getComparer:(e,t=0,i="string")=>"number"===i?(i,s)=>{let l=i[e]||0,a=s[e]||0;return l===a?0:l>a?1*t:-1*t}:"string"===i?(i,s)=>{let l=i[e]||"",a=s[e]||"";return t*l.localeCompare(a,"en",{sensitivity:"base"})}:"boolean"===i?(i,s)=>{let l=i[e]?1:0,a=s[e]?1:0;return t*(l-a)}:"date"===i?(i,s)=>{let l=i[e]||"1970-1-1",a=s[e]||"1970-1-1",n=new Date(l),r=new Date(a);return l===a?0:n>r?1*t:-1*t}:()=>0,sort(e){if(e.sortKey){let t=0,i=this.sortModel[e.sortKey];i?1===i&&(t=-1):t=1,"single"===this.sortMode&&Object.keys(this.sortModel).forEach((e=>this.sortModel[e]=0)),this.sortModel[e.sortKey]=t,this.updateState({sortModel:Object.entries(this.sortModel)}),this.loadItems()}},filter({key:e}){let t=this.filterModel[e];if(this.$emit("filter",{key:e,val:t}),this.updateState({filterModel:this.filterModel,pageIndex:0}),this.selectable){const e=this.selectedItems.length;this.selectedItems=this.selectedItems.filter((e=>this.filterItems.map((e=>e.id)).includes(e))),e!==this.selectedItems.length&&this.$emit("on-selection-change",this.selectedItems)}},getFilterByKey(e){return this.internalFilters.find((t=>t.key===e))},select(e){let t=this.selectedItems.indexOf(e);t>=0?this.selectedItems.splice(t,1):this.selectedItems.push(e),this.$emit("on-selection-change",this.selectedItems)},toggleAll(e){this.selectedItems=e?[...new Set([...this.selectedItems,...this.pageItemsAdd.map((e=>e.id))])]:this.selectedItems.filter((e=>!this.pageItemsAdd.map((e=>e.id)).includes(e))),this.$emit("on-selection-change",this.selectedItems)},getColumnStyle(e){let t="";return"bool"===e.type&&(t+=""),(e.style||"")+t},clear(){this.internalFilters.forEach(((e,t)=>{this.filterModel[e.key]="",this.updateState({filterModel:this.filterModel})})),this.$emit("filter")},changePage(e){this.pageIndex=+e,this.updateState({pageIndex:this.pageIndex}),this.loadItems()},changePageSize(e){this.changePage(0),this.size=+e,this.updateState({pageSize:this.size}),this.loadItems()},tippify(){tippy(".tipified",{interactive:!0,placement:"auto-end",animateFill:!1})},saveItemClicked(e){if(!this.working&&(this.working=!0,this.editItem)){if(this.columns.config.find((({dataKey:e})=>this.editItem[e].invalid)))return void(this.working=!1);const t=t=>{if(this.editItem=null,e.id){const t=this.data.items.findIndex((t=>t[this.columns.idKey]===e.id));this.data.items.splice(t,1,e),this.$emit("edit",e)}else e.id=t.id,t.path&&(e.path=t.path),this.$emit("add",e),this.data.items.push(e);this.working=!1},i=(e=this.columns.config.reduce(((e,t)=>{const i=t.dataKey,s=this.internalFilters.find((e=>e.key===i)),l=s?.filterCollection??i,a=t.edit?null:s?.options[0];return e[l]=this.editItem[i].selected||a?.id||null,l!==i&&(e[i]=e[l]?s?.options.find((t=>t.id===e[l])).name:a?.name),e}),{...e})).id?this.columns.edit:this.columns.add;!i?.enabled||i.canEditProp&&!e[i.canEditProp]||(i.claim?i.claim(e).then(t,(()=>{this.working=!1})):t())}},editItemClicked(e={}){this.editItem=this.columns.config.reduce(((t,i)=>{const s=i.dataKey,l=this.internalFilters.find((e=>e.key===s)),a=l?.filterCollection??s,n=`-- ${i.header||s} --`;let r=!1;return!t.id&&i.edit?.required&&(r=!e[a]&&!i.edit?.value),t[s]={selected:e[a]||i.edit?.value||"",selectedText:e[s]||i.edit?.value||l?.options[i.edit?.default??0]?.name,options:[...l?.options||[]],columnId:a,placeholder:n,invalid:r,...i.edit||{}},t[s].options.splice(0,0,{id:"",name:n}),t}),{id:e[this.columns.idKey]})},removeItemClicked(e){const t=()=>{this.data.items.splice(this.data.items.indexOf(e),1),this.$emit("remove",e)};!this.columns.remove?.enabled||this.columns.remove.canRemoveProp&&!e[this.columns.remove.canRemoveProp]||(this.columns.remove.claim?this.columns.remove.claim(e).then(t,(()=>{})):t())},addItemClicked(){if(this.addurl)return window.location.href=this.addurl,window.location.href;this.editItemClicked()},loadItems(){if(this.itemsUrl&&this.data.paged){Object.keys(this.filterModel).reduce(((e,t)=>(e[0]=t,e[1]=this.filterModel[t],e)),[[],[]]);let e=Object.keys(this.sortModel).find((e=>this.sortModel[e]))||Object.keys(this.sortModel)[0],t=this.sortModel[e]<0?"desc":"",i=this.sortModel[e]>0?"asc":t,s={page:this.pageIndex,pageSize:this.size,sortBy:e,sortDirection:i,filterKeys:["type"],filterValues:["ch"]};fetch(`${this.itemsUrl}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).then((e=>e.json())).then((e=>{this.data={...this.data,...e}}))}}},updated(){this.$nextTick((function(){this.tippify()}))},mounted(){this.tippify(),this.loadState(),this.loadItems()}});
//# sourceMappingURL=filteredTable.js.map