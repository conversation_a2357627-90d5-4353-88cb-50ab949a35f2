using Moq;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using System.Globalization;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain.Unit.Tests;

public class ServiceTests
{
    private readonly IAccovionClientReportService _accovionClientReportService;
    private readonly Mock<IRepository> _mockRepository = new();
    private readonly Mock<IExportService> _mockExportService = new();

    private readonly Dictionary<int, string> _expectedMonths
            = Enumerable.Range(1, 12)
                        .ToDictionary(
                            monthNumber => monthNumber,
                            monthNumber => CultureInfo.InvariantCulture.DateTimeFormat.GetMonthName(monthNumber)
                        );

    public ServiceTests()
    {
        _accovionClientReportService = new Service(_mockRepository.Object, _mockExportService.Object);
    }

    [Fact]
    public void Given_AccovionClientReportService_When_GetPageModel_is_called_Then_PageModel_returned()
    {
        // Arrange

        // Act
        var result = _accovionClientReportService.GetPageModel();

        // Assert
        Assert.NotNull(result);

        var actualMonths = result.Cards.ToDictionary(o => o.Month, o => o.MonthName);

        // Assert: Check if each expected month number has the correct name in actualMonths
        foreach (var expectedMonth in _expectedMonths)
        {
            Assert.True(actualMonths.TryGetValue(expectedMonth.Key, out var actualMonthName),
                $"Month number {expectedMonth.Key} is missing.");
            Assert.Equal(expectedMonth.Value, actualMonthName);
        }
    }

    [Fact]
    public void Given_AccovionClientReportService_When_GetCards_is_called_Then_CardModels_returned()
    {
        // Arrange

        // Act
        var result = _accovionClientReportService.GetCards(2024);

        // Assert
        Assert.NotNull(result);

        var actualMonths = result.ToDictionary(o => o.Month, o => o.MonthName);

        // Assert: Check if each expected month number has the correct name in actualMonths
        foreach (var expectedMonth in _expectedMonths)
        {
            Assert.True(actualMonths.TryGetValue(expectedMonth.Key, out var actualMonthName),
                $"Month number {expectedMonth.Key} is missing.");
            Assert.Equal(expectedMonth.Value, actualMonthName);
        }
    }

    [Fact]
    public async Task Given_AccovionClientReportService_When_Download_is_called_with_negative_month_Then_Exception_is_thrown()
    {
        // Act
        // & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _accovionClientReportService.Download(2024, -42));
    }

    [Fact]
    public async Task Given_AccovionClientReportService_When_Download_is_called_with_month_more_than_12_Then_Exception_is_thrown()
    {
        // Act
        // & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _accovionClientReportService.Download(2024, 13));
    }

    [Fact]
    public async Task Given_AccovionClientReportService_When_Download_is_called_Then_list_of_results_returned()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetStoredProcResults(2024, 1)).ReturnsAsync(new List<Result>());

        _mockExportService.Setup(x =>
            x.Export<Result, ResultClassMap>("AccovionClientReport", It.IsAny<List<Result>>(),
                It.IsAny<ExportConfiguration>())).Returns(
            new ExportModel("FileName", "ContentType", Array.Empty<byte>()));

        // Act
        var result = await _accovionClientReportService.Download(2024, 1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Accovion Cases-data_2024_1.csv", result.FileName);
        Assert.Equal("ContentType", result.ContentType);
        Assert.Equal(Array.Empty<byte>(), result.Bytes);
    }
}
