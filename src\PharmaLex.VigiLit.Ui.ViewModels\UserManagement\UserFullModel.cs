﻿using PharmaLex.Core.UserManagement.Claims;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

public class UserFullModel : UserModel
{
    public UserFullModel()
    {
        ClaimIds = new List<int>();
        Claims = new List<ClaimModel>();
    }

    public string Name { get; set; }
    public string Value { get; set; }
    public bool Active { get; set; } = true;

    public bool Initialised { get; set; }


    public string DisplayClaimsText => string.Join(", ", Claims.OrderBy(c => c.Id).Select(x => x.DisplayName));
    public string DisplayNameAndEmail => this.Id > 0 ? $"{this.GivenName} {this.FamilyName} ({this.Email})" : "";
}