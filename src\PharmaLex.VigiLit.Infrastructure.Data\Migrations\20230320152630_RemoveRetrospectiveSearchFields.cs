﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class RemoveRetrospectiveSearchFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsRetrospectiveSearchEnabled",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "RetrospectiveStartDate",
                table: "Contracts");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsRetrospectiveSearchEnabled",
                table: "Contracts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "RetrospectiveStartDate",
                table: "Contracts",
                type: "datetime2",
                nullable: true);
        }
    }
}
