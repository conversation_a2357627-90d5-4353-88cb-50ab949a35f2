﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;


namespace PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests
{
    public class JournalServiceTests
    {
        private readonly IJournalService _journalService;
        private readonly Mock<IJournalRepository> _journalRepository = new();
        private readonly IMapper _mapper;

        public JournalServiceTests()
        {

            var mapperConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new JournalMappingProfile());

            });

            _mapper = mapperConfig.CreateMapper();

            _journalService = new JournalService(
                _journalRepository.Object,
                _mapper);


        }
        [Fact]
        public async Task GetAllAsync_ShouldReturnJournalModels()
        {
            var countryName = "Country";
            var url = "Url";
            var countryId = 99;

            // Arrange
            var journalEntities = new List<FakeJournal>
            {
                new FakeJournal(1) { Name = "Journal 1", Url = url, CountryId = countryId, Enabled = true },
                new FakeJournal(2) { Name = "Journal 2",Url = url, CountryId = countryId, Issn = "1476-4687", Enabled = false }
            };

            var expectedJournalModels = new List<JournalViewModel>
            {
                new JournalViewModel { Id = 1, Name = "Journal 1", CountryName = countryName, Url = url, CountryId = countryId, Enabled = true},
                new JournalViewModel { Id = 2, Name = "Journal 2" , CountryName = countryName, Url = url, CountryId = countryId, Enabled = false}
            };

            _journalRepository
                .Setup(repo => repo.GetAll())
                .ReturnsAsync(journalEntities);

            // Act
            var result = await _journalService.GetAll();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedJournalModels.Count, result.Count());
            Assert.Collection(result,
                    item =>
                    {
                        Assert.Equal(1, item.Id);
                        Assert.Equal("Journal 1", item.Name);
                        Assert.Equal(99, item.CountryId);
                        Assert.Equal("N/A", item.Issn);
                        Assert.True(item.Enabled);
                    },
                    item =>
                    {
                        Assert.Equal(2, item.Id);
                        Assert.Equal("Journal 2", item.Name);
                        Assert.Equal(99, item.CountryId);
                        Assert.Equal("1476-4687", item.Issn);
                        Assert.False(item.Enabled);
                    }
                );
            _journalRepository.Verify(repo => repo.GetAll(), Times.Once);
        }
        [Fact]
        public async Task GetJournalsForCountry_ShouldReturnJournalModelsForCountry()
        {
            // Arrange
            int countryId = 1;
            var countryName = "Country";
            var url = "Url";

            var journalEntities = new List<FakeJournal>
        {
            new FakeJournal(1) { Name = "Journal 1", CountryId = countryId  },
            new FakeJournal(2) { Name = "Journal 2" , CountryId = countryId },
        };

            var expectedJournalModels = new List<JournalViewModel>
        {
            new JournalViewModel { Id = 1, Name = "Journal 1", CountryId = countryId, CountryName = countryName, Url = url },
            new JournalViewModel { Id = 2, Name = "Journal 2", CountryId = countryId, CountryName = countryName, Url = url }
        };

            _journalRepository
                .Setup(repo => repo.GetForCountry(countryId))
                .ReturnsAsync(journalEntities);

            // Act
            var result = await _journalService.GetJournalsForCountry(countryId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedJournalModels.Count, result.Count());
            Assert.All(result, journal => Assert.Equal(countryId, journal.CountryId));

            _journalRepository.Verify(repo => repo.GetForCountry(countryId), Times.Once);
        }

        [Fact]
        public async Task GetJournal_ReturnsViewModel_WhenJournalExists()
        {
            // Arrange
            var journalId = 1;
            var journalEntity = new FakeJournal(journalId) { Name = "Test Journal", CountryId = 100, Url = "https://example.com", Enabled = true };
            var expectedViewModel = new JournalViewModel { Id = journalId, Name = "Test Journal", CountryId = 100, CountryName = "", Url = "https://example.com", Enabled = true };

            _journalRepository
                .Setup(repo => repo.GetById(journalId))
                .ReturnsAsync(journalEntity);

            // Act
            var result = await _journalService.GetJournal(journalId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedViewModel.Id, result.Id);
            Assert.Equal(expectedViewModel.Name, result.Name);
            Assert.Equal(expectedViewModel.CountryId, result.CountryId);
            Assert.Equal(expectedViewModel.Url, result.Url);
            Assert.Equal(expectedViewModel.Enabled, result.Enabled);
            _journalRepository.Verify(repo => repo.GetById(journalId), Times.Once);

        }
        [Fact]
        public async Task UpdateAsync_ShouldUpdateAndSaveJournal()
        {
            // Arrange
            var model = new JournalModel
            {
                Id = 1,
                Name = "Updated Journal",
                Url = "https://example.com",
                CountryId = 100,
                Enabled = true
            };

            var existingJournal = new FakeJournal(1) { Name = "Journal" };

            _journalRepository
                .Setup(repo => repo.GetById(model.Id.Value))
                .ReturnsAsync(existingJournal);

            _journalRepository
                .Setup(repo => repo.SaveChangesAsync());

            // Act
            await _journalService.UpdateAsync(model);

            // Assert
            _journalRepository.Verify(repo => repo.GetById(model.Id.Value), Times.Once);
            _journalRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithoutId_ShouldThrowArgumentException()
        {
            // Arrange
            var model = new JournalModel
            {
                Id = null,
                Name = "Updated Journal",
                Url = "https://example.com",
                CountryId = 100,
                Enabled = true
            };

            // Act & Assert
            var ex = await Assert.ThrowsAsync<ArgumentException>(() => _journalService.UpdateAsync(model));
            Assert.Equal("Id is required to update a journal", ex.Message);
        }

        [Fact]
        public async Task CountContractsWithEnabledJournal_ReturnsExpectedCount()
        {
            // Arrange
            int journalId = 1;
            int expectedCount = 5;

            _journalRepository
                .Setup(repo => repo.GetContractsWithEnabledJournalById(journalId))
                .ReturnsAsync(expectedCount);

            // Act
            var actualCount = await _journalService.CountContractsWithEnabledJournal(journalId);

            // Assert
            Assert.Equal(expectedCount, actualCount);
            _journalRepository.Verify(repo => repo.GetContractsWithEnabledJournalById(journalId), Times.Once);
        }
    }
}
