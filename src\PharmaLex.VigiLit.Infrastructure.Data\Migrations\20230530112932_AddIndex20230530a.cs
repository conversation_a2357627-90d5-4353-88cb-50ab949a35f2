﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndex20230530a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_ReferenceClassificationsHistory_ReferenceId_SubstanceId_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] WITH ( ONLINE = OFF )");

            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferenceClassificationsHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] ([Id],[PeriodEnd],[PeriodStart])");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_ReferenceClassificationsHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] WITH ( ONLINE = OFF )");

            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferenceClassificationsHistory_ReferenceId_SubstanceId_PeriodEnd_PeriodStart] ON [dbo].[ReferenceClassificationsHistory] ([ReferenceId],[SubstanceId],[PeriodEnd],[PeriodStart])");
        }
    }
}
