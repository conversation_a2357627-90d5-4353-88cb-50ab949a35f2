﻿using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Helpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Repositories;

public class SubstanceRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, SubstanceRepositoryTests>>
{
    private readonly RepositoryHelper _repoHelper;

    public SubstanceRepositoryTests(DatabaseFixture<VigiLitDbContext, SubstanceRepositoryTests> fixture)
    {
        _repoHelper = new RepositoryHelper(fixture);
    }


    [Fact]
    public async Task GetSubstancesForActiveCompanyContractsAsync_Returns_SubstanceForActiveContracts()
    {
        // Arrange
        await AddSubstanceWithActiveContractToRepository();
        await AddSubstanceWithNoActiveContractToRepository();

        // Act
        var substances = await _repoHelper.GetSubstanceRepository().GetActiveSubstancesWithNoClassificationsForReference(1);

        // Assert
        Assert.NotNull(substances);
        Assert.Single(substances);
        Assert.True(substances.All(s => s.Name == "Substance 1"));
    }

    [Fact]
    public async Task GetSubstancesForActiveCompanyContractsAsync_Returns_NoSubstanceForClassifiedReference()
    {
        // Arrange
        var referenceId = await AddSubstanceWithClassificationToRepository();

        // Act
        var substances = await _repoHelper.GetSubstanceRepository().GetActiveSubstancesWithNoClassificationsForReference(referenceId);

        // Assert        
        Assert.DoesNotContain(substances, s => s.Name == "Substance 3");
    }

    private async Task AddSubstanceWithActiveContractToRepository()
    {
        var user = await _repoHelper.AddUser("User1", "Surname1", "<EMAIL>");

        var substance = await _repoHelper.AddSubstance("Substance 1", "");

        var company = await _repoHelper.AddCompany("Company 1", "", "", true);

        var project = await _repoHelper.AddProject("Project 1", company);

        var contract = await _repoHelper.AddContract(user, substance, project, true, 1);
    }

    private async Task AddSubstanceWithNoActiveContractToRepository()
    {
        var user = await _repoHelper.AddUser("User2", "Surname2", "<EMAIL>");

        var substance = await _repoHelper.AddSubstance("Substance 2", "");

        var company = await _repoHelper.AddCompany("Company 2", "", "", true);

        var project = await _repoHelper.AddProject("Project 2", company);

        var contract = await _repoHelper.AddContract(user, substance, project, false, 1);
    }

    private async Task<int> AddSubstanceWithClassificationToRepository()
    {
        var user = await _repoHelper.AddUser("User3", "Surname3", "<EMAIL>");

        var substance = await _repoHelper.AddSubstance("Substance 3", "");

        var company = await _repoHelper.AddCompany("Company 3", "", "", true);

        var project = await _repoHelper.AddProject("Project 3", company);

        var contract = await _repoHelper.AddContract(user, substance, project, true, 1);

        var reference = await _repoHelper.AddReference("Ref3");

        var classification = await _repoHelper.AddClassification(reference, substance);

        return classification.Reference.Id;
    }
}
