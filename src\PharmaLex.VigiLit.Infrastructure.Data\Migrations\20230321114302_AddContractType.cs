﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddContractType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ContractType",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            // Existing contracts are of type Daily.
            migrationBuilder.Sql("exec('UPDATE [Contracts] SET [ContractType] = 10')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContractType",
                table: "Contracts");
        }
    }
}
