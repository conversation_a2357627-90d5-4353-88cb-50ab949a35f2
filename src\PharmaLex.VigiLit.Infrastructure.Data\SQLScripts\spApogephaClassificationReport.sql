﻿CREATE OR ALTER PROC dbo.spApogephaClassificationReport
/*---------------------------------------------------------------------------------------------------------------------
--
-- Summary:		Returns data for Apogepha Client Report
--
-- Parameters:	@Company   = company name
--				@StartDate = report start date in any date format, e.g '20230701', '1 Jul 2023', etc.
--				@EndDate   = report end date
--
-- Notes:		Used by company 'APOGEPHA Arzneimittel GmbH'
--
-- Usage:		exec spApogephaClassificationReport 'APOGEPHA Arzneimittel GmbH', '2023-07-01', '2023-08-31'
--
---------------------------------------------------------------------------------------------------------------------*/

    @CompanyName varchar(100), 
    @StartDate datetime,
    @EndDate datetime
AS

    SELECT
    	FORMAT(GETUTCDATE(), 'dd/MM/yyyy') as 'DateOfQuery',
    	s.[Name] as 'Substance', 
    	CAST(rc.Id AS VARCHAR) as 'YesId', 
    	FORMAT(rc.CreatedDate, 'dd/MM/yyyy') as 'EntryDate', 
    	ISNULL(r.Title, '') as 'Title', 
    	ISNULL(r.Authors, '') as 'Authors', 
    	ISNULL(r.Abstract, '') as 'Abstract', 
    	CASE WHEN NULLIF(r.Volume, '') IS NOT NULL
    	THEN
    		CASE WHEN NULLIF(r.FullPagination, '') IS NOT NULL
    		THEN
    			CONCAT(r.VolumeAbbreviation, ' ', r.PublicationYear, '; ', r.Volume, ' : ', r.FullPagination)
    		ELSE
    			CONCAT(r.VolumeAbbreviation, ' ', r.PublicationYear, '; ', r.Volume)
    		END
    	ELSE
    		CONCAT(r.VolumeAbbreviation, ' ', r.PublicationYear, '; ')
    	END as 'Source',
    	ISNULL(CAST(rc.ClassificationCategoryId AS VARCHAR), '') as 'Category',  
        GETUTCDATE() As [CreatedDate],   
        GETUTCDATE() As [LastUpdatedDate],   
        '' As [CreatedBy],   
        '' As [LastUpdatedBy] 
    FROM [ReferenceClassifications] rc 
    INNER JOIN [References] r ON rc.ReferenceId = r.Id
    INNER JOIN Substances s ON rc.SubstanceId = s.id
    INNER JOIN CompanyInterests ci ON ci.ReferenceId = r.Id AND ci.ReferenceClassificationId = rc.Id
    INNER JOIN Companies c ON ci.CompanyId = c.Id
    WHERE rc.CreatedDate BETWEEN @StartDate and @EndDate
    	AND c.[Name] = @CompanyName
    ORDER BY 4
GO