﻿@model PharmaLex.VigiLit.Web.ViewModels.ErrorViewModel

@{
    ViewData["Title"] = "Error";
}

<div class="manage-container">

    <h2>Error</h2>
    
    <p>Something unexpected happened and we were unable to complete your request. The error has been logged but a system administrator has not been notified.</p>

    @if (!String.IsNullOrEmpty(Model.NotifyEmail))
    {
        <p>
            If you repeatedly experience the same issue please
            <a id="letUsKnow" class="button icon-mail" style="margin:0;" href="mailto:@(Model.NotifyEmail)?subject=@Model.AppName Error@(Model.ShowRequestId ? $"&body=Request ID: {Model.RequestId}" : "")">let us know</a>
            providing as much detail as possible about the error e.g. data being managed, steps to reproduce etc.
        </p>
    }

    <p><a href="/" class="button secondary icon-button-back">Go to Home page</a></p>

</div>
