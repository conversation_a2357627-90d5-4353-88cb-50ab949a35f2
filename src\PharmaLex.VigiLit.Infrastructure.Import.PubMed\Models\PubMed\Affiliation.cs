using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

//[XmlType(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[XmlRoot(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public class Affiliation
{
    //[XmlElement("b", typeof(b))]
    //public b b { get; set; }

    //[XmlElement("i", typeof(i))]
    //public i i { get; set; }

    //[XmlElement("sub", typeof(sub))]
    //public sub sub { get; set; }

    //[XmlElement("sup", typeof(sup))]
    //public sup sup { get; set; }

    //[XmlElement("u", typeof(u))]
    //public u u { get; set; }

    ////public object[] Items {
    ////    get {
    ////        return this.itemsField;
    ////    }
    ////    set {
    ////        this.itemsField = value;
    ////    }
    ////}


    [XmlText()]
    public string Text { get; set; }
}