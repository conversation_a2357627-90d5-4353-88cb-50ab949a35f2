﻿namespace PharmaLex.VigiLit.Test.Framework.Orderers;

/// <summary>
/// Attribute for specifying the order in which test methods will be executed.
/// </summary>
/// <remarks>
/// To run chained tests apply this attribute to the test method specifying the priority or order number in which the tests should be executed.
/// <example>
/// The XUnit runner in the example below will execute "Test_with_setup" before "Chained_test"
/// <code>
/// [TestCaseOrderer(
///     ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
///     ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
/// public class MyTestsByPriorityOrder
/// {
/// 
///     [Fact, TestPriority(10)]
///     public void Chained_test()
///     {
///     }
/// 
///     [Fact, TestPriority(5)]
///     public void Test_with_setup()
///     {
///     }
/// }
/// </code>
/// </example>
/// source: <a href="https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit">https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit</a>
/// </remarks>
/// <seealso cref="System.Attribute" />
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class TestPriorityAttribute : Attribute
{
    /// <summary>
    /// The priority order for the attributed test method.
    /// </summary>
    /// <value>
    /// The priority.
    /// </value>
    public int Priority { get; private set; }

    public TestPriorityAttribute(int priority) => Priority = priority;
}