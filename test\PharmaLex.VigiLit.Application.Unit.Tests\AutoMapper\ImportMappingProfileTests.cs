﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.AutoMapper;

public class ImportMappingProfileTests
{
    readonly IMapper _mapper;

    public ImportMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<ImportMappingProfile>());
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_Import_To_ImportModel()
    {
        // Arrange
        Import import = new()
        {
            ImportType = ImportType.Scheduled,
            ImportDate = new DateTime(2023, 10, 13, 0, 0, 0, DateTimeKind.Utc),
            StartDate = new DateTime(2023, 10, 11, 23, 1, 2, DateTimeKind.Utc),
            EndDate = new DateTime(2023, 10, 12, 23, 2, 3, DateTimeKind.Utc),
            ImportStatusType = ImportStatusType.Completed,
            ImportContracts = new List<ImportContract>()
            {
                new()
                {
                    ContractId = 1,
                    ReferencesCount = 15,
                    NewReferencesCount = 4,
                    UpdatesCount = 5,
                    SilentUpdatesCount = 6
                },
                new()
                {
                    ContractId = 2,
                    ReferencesCount = 35,
                    NewReferencesCount = 14,
                    UpdatesCount = 15,
                    SilentUpdatesCount = 16
                }
            }
        };

        // Act
        var model = _mapper.Map<ImportModel>(import);

        // Assert
        Assert.Equal("Scheduled", model.ImportType);
        Assert.Equal("13 Oct 2023", model.ImportDate);
        Assert.Equal("11 Oct 2023 23:01:02", model.StartDate);
        Assert.Equal("12 Oct 2023 23:02:03", model.EndDate);
        Assert.Equal("00:01:01", model.Duration);
        Assert.Equal("Completed", model.ImportStatusType);
        Assert.Equal(2, model.ContractsCount);
        Assert.Equal(50, model.ReferencesCount);
        Assert.Equal(18, model.NewReferencesCount);
        Assert.Equal(20, model.UpdatesCount);
        Assert.Equal(22, model.SilentUpdatesCount);
    }

    [Fact]
    public void Maps_Import_To_ImportModel_HandlesNullDates()
    {
        // Arrange
        Import import = new()
        {
            ImportDate = null,
            StartDate = null,
            EndDate = null
        };

        // Act
        var model = _mapper.Map<ImportModel>(import);

        // Assert
        Assert.Equal("", model.ImportDate);
        Assert.Equal("", model.StartDate);
        Assert.Equal("", model.EndDate);
        Assert.Equal("", model.Duration);
    }

    [Fact]
    public void Maps_Import_To_ImportDashboardModel()
    {
        // Arrange
        Import import = new()
        {
            ImportType = ImportType.Scheduled,
            ImportDate = new DateTime(2023, 10, 13, 0, 0, 0, DateTimeKind.Utc)
        };

        // Act
        var model = _mapper.Map<ImportDashboardModel>(import);

        // Assert
        Assert.Equal("Scheduled", model.ImportType);
        Assert.Equal("13 Oct 2023", model.ImportDate);
        Assert.Equal("Scheduled: 13 Oct 2023", model.Name);
    }

    [Fact]
    public void Maps_Import_To_ImportDashboardModel_HandlesNullDates()
    {
        // Arrange
        Import import = new()
        {
            ImportType = ImportType.Scheduled,
            ImportDate = null
        };

        // Act
        var model = _mapper.Map<ImportDashboardModel>(import);

        // Assert
        Assert.Equal("Scheduled", model.ImportType);
        Assert.Equal("", model.ImportDate);
        Assert.Equal("Scheduled: ", model.Name);
    }
}