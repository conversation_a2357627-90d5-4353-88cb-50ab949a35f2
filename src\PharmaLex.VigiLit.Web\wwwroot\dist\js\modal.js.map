{"version": 3, "file": "js/modal.js", "mappings": ";;;;;;;;;;;;AAUA,GAAsB,oBAAXA,OACP,MAAM,IAAIC,MAAM,2CAEnB,SAAUC,GACP,aACA,IAAIC,EAINH,OAJkBI,GAAGC,OAAOC,MAAM,KAAK,GAAGA,MAAM,KAC9C,GAAKH,EAAQ,GAAK,GAAKA,EAAQ,GAAK,GAAqB,GAAdA,EAAQ,IAAyB,GAAdA,EAAQ,IAAWA,EAAQ,GAAK,GAAOA,EAAQ,GAAK,EAC9G,MAAM,IAAIF,MAAM,2FAExB,CANC,GAiBA,SAAUC,GACP,aAKA,IAAIK,EAAQ,SAAUC,EAASC,GAC3BC,KAAKD,QAAUA,EACfC,KAAKC,MAAQT,EAAEU,SAASC,MACxBH,KAAKI,SAAWZ,EAAEM,GAClBE,KAAKK,QAAUL,KAAKI,SAASE,KAAK,iBAClCN,KAAKO,UAAY,KACjBP,KAAKQ,QAAU,KACfR,KAAKS,gBAAkB,KACvBT,KAAKU,eAAiB,EACtBV,KAAKW,qBAAsB,EAC3BX,KAAKY,aAAe,0CAEhBZ,KAAKD,QAAQc,QACbb,KAAKI,SACAE,KAAK,kBACLQ,KAAKd,KAAKD,QAAQc,OAAQrB,EAAEuB,OAAM,WAC/Bf,KAAKI,SAASY,QAAQ,kBAC1B,GAAGhB,MAEf,EAiRA,SAASiB,EAAOC,EAAQC,GACpB,OAAOnB,KAAKoB,MAAK,WACb,IAAIC,EAAQ7B,EAAEQ,MACVsB,EAAOD,EAAMC,KAAK,YAClBvB,EAAUP,EAAE+B,OAAO,CAAC,EAAG1B,EAAM2B,SAAUH,EAAMC,OAAyB,iBAAVJ,GAAsBA,GAEjFI,GAAMD,EAAMC,KAAK,WAAaA,EAAO,IAAIzB,EAAMG,KAAMD,IACrC,iBAAVmB,EAAoBI,EAAKJ,GAAQC,GACnCpB,EAAQ0B,MAAMH,EAAKG,KAAKN,EACrC,GACJ,CAzRAtB,EAAM6B,QAAU,QAEhB7B,EAAM8B,oBAAsB,IAC5B9B,EAAM+B,6BAA+B,IAErC/B,EAAM2B,SAAW,CACbK,UAAU,EACVC,UAAU,EACVL,MAAM,GAGV5B,EAAMkC,UAAUC,OAAS,SAAUb,GAC/B,OAAOnB,KAAKQ,QAAUR,KAAKiC,OAASjC,KAAKyB,KAAKN,EAClD,EAEAtB,EAAMkC,UAAUN,KAAO,SAAUN,GAC7B,IAAIe,EAAOlC,KACPmC,EAAI3C,EAAE4C,MAAM,gBAAiB,CAAEC,cAAelB,IAElDnB,KAAKI,SAASY,QAAQmB,GAElBnC,KAAKQ,SAAW2B,EAAEG,uBAEtBtC,KAAKQ,SAAU,EAEfR,KAAKuC,iBACLvC,KAAKwC,eACLxC,KAAKC,MAAMwC,SAAS,cAEpBzC,KAAK0C,SACL1C,KAAK2C,SAEL3C,KAAKI,SAASwC,GAAG,yBAA0B,yBAA0BpD,EAAEuB,MAAMf,KAAKiC,KAAMjC,OAExFA,KAAKK,QAAQuC,GAAG,8BAA8B,WAC1CV,EAAK9B,SAASyC,IAAI,4BAA4B,SAAUV,GAChD3C,EAAE2C,EAAEW,QAAQC,GAAGb,EAAK9B,YAAW8B,EAAKvB,qBAAsB,EAClE,GACJ,IAEAX,KAAK6B,UAAS,WACV,IAAImB,EAAaxD,EAAEyD,QAAQD,YAAcd,EAAK9B,SAAS8C,SAAS,QAE3DhB,EAAK9B,SAAS+C,SAASC,QACxBlB,EAAK9B,SAASiD,SAASnB,EAAKjC,OAGhCiC,EAAK9B,SACAqB,OACA6B,UAAU,GAEfpB,EAAKqB,eAEDP,GACAd,EAAK9B,SAAS,GAAGoD,YAGrBtB,EAAK9B,SAASqC,SAAS,MAEvBP,EAAKuB,eAEL,IAAItB,EAAI3C,EAAE4C,MAAM,iBAAkB,CAAEC,cAAelB,IAEnD6B,EACId,EAAK7B,QACAwC,IAAI,mBAAmB,WACpBX,EAAK9B,SAASY,QAAQ,SAASA,QAAQmB,EAC3C,IACCuB,qBAAqB7D,EAAM8B,qBAChCO,EAAK9B,SAASY,QAAQ,SAASA,QAAQmB,EAC/C,IACJ,EAEAtC,EAAMkC,UAAUE,KAAO,SAAUE,GACzBA,GAAGA,EAAEwB,iBAETxB,EAAI3C,EAAE4C,MAAM,iBAEZpC,KAAKI,SAASY,QAAQmB,GAEjBnC,KAAKQ,UAAW2B,EAAEG,uBAEvBtC,KAAKQ,SAAU,EAEfR,KAAK0C,SACL1C,KAAK2C,SAELnD,EAAEU,UAAU0D,IAAI,oBAEhB5D,KAAKI,SACAyD,YAAY,MACZD,IAAI,0BACJA,IAAI,4BAET5D,KAAKK,QAAQuD,IAAI,8BAEjBpE,EAAEyD,QAAQD,YAAchD,KAAKI,SAAS8C,SAAS,QAC3ClD,KAAKI,SACAyC,IAAI,kBAAmBrD,EAAEuB,MAAMf,KAAK8D,UAAW9D,OAC/C0D,qBAAqB7D,EAAM8B,qBAChC3B,KAAK8D,YACb,EAEAjE,EAAMkC,UAAU0B,aAAe,WAC3BjE,EAAEU,UACG0D,IAAI,oBACJhB,GAAG,mBAAoBpD,EAAEuB,OAAM,SAAUoB,GAClCjC,WAAaiC,EAAEW,QACf9C,KAAKI,SAAS,KAAO+B,EAAEW,QACtB9C,KAAKI,SAAS2D,IAAI5B,EAAEW,QAAQM,QAC7BpD,KAAKI,SAASY,QAAQ,QAE9B,GAAGhB,MACX,EAEAH,EAAMkC,UAAUW,OAAS,WACjB1C,KAAKQ,SAAWR,KAAKD,QAAQ+B,SAC7B9B,KAAKI,SAASwC,GAAG,2BAA4BpD,EAAEuB,OAAM,SAAUoB,GAChD,IAAXA,EAAE6B,OAAehE,KAAKiC,MAC1B,GAAGjC,OACKA,KAAKQ,SACbR,KAAKI,SAASwD,IAAI,2BAE1B,EAEA/D,EAAMkC,UAAUY,OAAS,WACjB3C,KAAKQ,QACLhB,EAAEyE,QAAQrB,GAAG,kBAAmBpD,EAAEuB,MAAMf,KAAKkE,aAAclE,OAE3DR,EAAEyE,QAAQL,IAAI,kBAEtB,EAEA/D,EAAMkC,UAAU+B,UAAY,WACxB,IAAI5B,EAAOlC,KACXA,KAAKI,SAAS6B,OACdjC,KAAK6B,UAAS,WACVK,EAAKjC,MAAM4D,YAAY,cACvB3B,EAAKiC,mBACLjC,EAAKkC,iBACLlC,EAAK9B,SAASY,QAAQ,kBAC1B,GACJ,EAEAnB,EAAMkC,UAAUsC,eAAiB,WAC7BrE,KAAKO,WAAaP,KAAKO,UAAU+D,SACjCtE,KAAKO,UAAY,IACrB,EAEAV,EAAMkC,UAAUF,SAAW,SAAU0C,GACjC,IAAIrC,EAAOlC,KACPwE,EAAUxE,KAAKI,SAAS8C,SAAS,QAAU,OAAS,GAExD,GAAIlD,KAAKQ,SAAWR,KAAKD,QAAQ8B,SAAU,CACvC,IAAI4C,EAAYjF,EAAEyD,QAAQD,YAAcwB,EAqBxC,GAnBAxE,KAAKO,UAAYf,EAAEU,SAASwE,cAAc,QACrCjC,SAAS,kBAAoB+B,GAC7BnB,SAASrD,KAAKC,OAEnBD,KAAKI,SAASwC,GAAG,yBAA0BpD,EAAEuB,OAAM,SAAUoB,GACrDnC,KAAKW,oBACLX,KAAKW,qBAAsB,EAG3BwB,EAAEW,SAAWX,EAAEwC,gBACM,UAAzB3E,KAAKD,QAAQ8B,SACP7B,KAAKI,SAAS,GAAGwE,QACjB5E,KAAKiC,OACf,GAAGjC,OAECyE,GAAWzE,KAAKO,UAAU,GAAGiD,YAEjCxD,KAAKO,UAAUkC,SAAS,OAEnB8B,EAAU,OAEfE,EACIzE,KAAKO,UACAsC,IAAI,kBAAmB0B,GACvBb,qBAAqB7D,EAAM+B,8BAChC2C,GAER,MAAO,IAAKvE,KAAKQ,SAAWR,KAAKO,UAAW,CACxCP,KAAKO,UAAUsD,YAAY,MAE3B,IAAIgB,EAAiB,WACjB3C,EAAKmC,iBACLE,GAAYA,GAChB,EACA/E,EAAEyD,QAAQD,YAAchD,KAAKI,SAAS8C,SAAS,QAC3ClD,KAAKO,UACAsC,IAAI,kBAAmBgC,GACvBnB,qBAAqB7D,EAAM+B,8BAChCiD,GAER,MAAWN,GACPA,GAER,EAIA1E,EAAMkC,UAAUmC,aAAe,WAC3BlE,KAAKuD,cACT,EAEA1D,EAAMkC,UAAUwB,aAAe,WAC3B,IAAIuB,EAAqB9E,KAAKI,SAAS,GAAG2E,aAAe7E,SAAS8E,gBAAgBC,aAElFjF,KAAKI,SAAS8E,IAAI,CACdC,aAAcnF,KAAKoF,mBAAqBN,EAAqB9E,KAAKU,eAAiB,GACnF2E,aAAcrF,KAAKoF,oBAAsBN,EAAqB9E,KAAKU,eAAiB,IAE5F,EAEAb,EAAMkC,UAAUoC,iBAAmB,WAC/BnE,KAAKI,SAAS8E,IAAI,CACdC,YAAa,GACbE,aAAc,IAEtB,EAEAxF,EAAMkC,UAAUQ,eAAiB,WAC7B,IAAI+C,EAAkBrB,OAAOsB,WAC7B,IAAKD,EAAiB,CAClB,IAAIE,EAAsBtF,SAAS8E,gBAAgBS,wBACnDH,EAAkBE,EAAoBE,MAAQC,KAAKC,IAAIJ,EAAoBK,KAC/E,CACA7F,KAAKoF,kBAAoBlF,SAASC,KAAK2F,YAAcR,EACrDtF,KAAKU,eAAiBV,KAAK+F,kBAC/B,EAEAlG,EAAMkC,UAAUS,aAAe,WAC3B,IAAIwD,EAAUC,SAAUjG,KAAKC,MAAMiF,IAAI,kBAAoB,EAAI,IAC/DlF,KAAKS,gBAAkBP,SAASC,KAAK+F,MAAMb,cAAgB,GAC3D,IAAI3E,EAAiBV,KAAKU,eACtBV,KAAKoF,oBACLpF,KAAKC,MAAMiF,IAAI,gBAAiBc,EAAUtF,GAC1ClB,EAAEQ,KAAKY,cAAcQ,MAAK,SAAU+E,EAAOrG,GACvC,IAAIsG,EAAgBtG,EAAQoG,MAAMb,aAC9BgB,EAAoB7G,EAAEM,GAASoF,IAAI,iBACvC1F,EAAEM,GACGwB,KAAK,gBAAiB8E,GACtBlB,IAAI,gBAAiBoB,WAAWD,GAAqB3F,EAAiB,KAC/E,IAER,EAEAb,EAAMkC,UAAUqC,eAAiB,WAC7BpE,KAAKC,MAAMiF,IAAI,gBAAiBlF,KAAKS,iBACrCjB,EAAEQ,KAAKY,cAAcQ,MAAK,SAAU+E,EAAOrG,GACvC,IAAIyG,EAAU/G,EAAEM,GAASwB,KAAK,iBAC9B9B,EAAEM,GAAS0G,WAAW,iBACtB1G,EAAQoG,MAAMb,aAAekB,GAAoB,EACrD,GACJ,EAEA1G,EAAMkC,UAAUgE,iBAAmB,WAC/B,IAAIU,EAAYvG,SAASwE,cAAc,OACvC+B,EAAUC,UAAY,0BACtB1G,KAAKC,MAAM0G,OAAOF,GAClB,IAAI/F,EAAiB+F,EAAUjD,YAAciD,EAAUX,YAEvD,OADA9F,KAAKC,MAAM,GAAG2G,YAAYH,GACnB/F,CACX,EAkBA,IAAImG,EAAMrH,EAAEE,GAAGoH,MAEftH,EAAEE,GAAGoH,MAAQ7F,EACbzB,EAAEE,GAAGoH,MAAMC,YAAclH,EAMzBL,EAAEE,GAAGoH,MAAME,WAAa,WAEpB,OADAxH,EAAEE,GAAGoH,MAAQD,EACN7G,IACX,EAMAR,EAAEU,UAAU0C,GAAG,0BAA2B,yBAAyB,SAAUT,GACzE,IAAId,EAAQ7B,EAAEQ,MACViH,EAAO5F,EAAM6F,KAAK,QAClBpE,EAASzB,EAAM6F,KAAK,gBACnBD,GAAQA,EAAKE,QAAQ,iBAAkB,IAExCC,EAAU5H,EAAEU,UAAUI,KAAKwC,GAC3B5B,EAASkG,EAAQ9F,KAAK,YAAc,SAAW9B,EAAE+B,OAAO,CAAEV,QAAS,IAAIwG,KAAKJ,IAASA,GAAQG,EAAQ9F,OAAQD,EAAMC,QAEnHD,EAAM0B,GAAG,MAAMZ,EAAEwB,iBAErByD,EAAQvE,IAAI,iBAAiB,SAAUyE,GAC/BA,EAAUhF,sBACd8E,EAAQvE,IAAI,mBAAmB,WAC3BxB,EAAM0B,GAAG,aAAe1B,EAAML,QAAQ,QAC1C,GACJ,IACAC,EAAOsG,KAAKH,EAASlG,EAAQlB,KACjC,GAEJ,CA5VC,CA4VCV,O", "sources": ["webpack://PharmaLex.VigiLit.v2/./src/js/modal/modal.js"], "sourcesContent": ["/*!\r\n * Generated using the Bootstrap Customizer (https://getbootstrap.com/docs/3.4/customize/)\r\n */\r\n\r\n/*!\r\n * Bootstrap v3.4.1 (https://getbootstrap.com/)\r\n * Copyright 2011-2023 Twitter, Inc.\r\n * Licensed under the MIT license\r\n */\r\n\r\nif (typeof jQuery === 'undefined') {\r\n    throw new Error('Bootstrap\\'s JavaScript requires jQuery')\r\n}\r\n+function ($) {\r\n    'use strict';\r\n    var version = $.fn.jquery.split(' ')[0].split('.')\r\n    if ((version[0] < 2 && version[1] < 9) || (version[0] == 1 && version[1] == 9 && version[2] < 1) || (version[0] > 3)) {\r\n        throw new Error('Bootstrap\\'s JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4')\r\n    }\r\n}(jQuery);\r\n\r\n/* ========================================================================\r\n * Bootstrap: modal.js v3.4.1\r\n * https://getbootstrap.com/docs/3.4/javascript/#modals\r\n * ========================================================================\r\n * Copyright 2011-2019 Twitter, Inc.\r\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\r\n * ======================================================================== */\r\n\r\n\r\n+function ($) {\r\n    'use strict';\r\n\r\n    // MODAL CLASS DEFINITION\r\n    // ======================\r\n\r\n    var Modal = function (element, options) {\r\n        this.options = options\r\n        this.$body = $(document.body)\r\n        this.$element = $(element)\r\n        this.$dialog = this.$element.find('.modal-dialog')\r\n        this.$backdrop = null\r\n        this.isShown = null\r\n        this.originalBodyPad = null\r\n        this.scrollbarWidth = 0\r\n        this.ignoreBackdropClick = false\r\n        this.fixedContent = '.navbar-fixed-top, .navbar-fixed-bottom'\r\n\r\n        if (this.options.remote) {\r\n            this.$element\r\n                .find('.modal-content')\r\n                .load(this.options.remote, $.proxy(function () {\r\n                    this.$element.trigger('loaded.bs.modal')\r\n                }, this))\r\n        }\r\n    }\r\n\r\n    Modal.VERSION = '3.4.1'\r\n\r\n    Modal.TRANSITION_DURATION = 300\r\n    Modal.BACKDROP_TRANSITION_DURATION = 150\r\n\r\n    Modal.DEFAULTS = {\r\n        backdrop: true,\r\n        keyboard: true,\r\n        show: true\r\n    }\r\n\r\n    Modal.prototype.toggle = function (_relatedTarget) {\r\n        return this.isShown ? this.hide() : this.show(_relatedTarget)\r\n    }\r\n\r\n    Modal.prototype.show = function (_relatedTarget) {\r\n        var that = this\r\n        var e = $.Event('show.bs.modal', { relatedTarget: _relatedTarget })\r\n\r\n        this.$element.trigger(e)\r\n\r\n        if (this.isShown || e.isDefaultPrevented()) return\r\n\r\n        this.isShown = true\r\n\r\n        this.checkScrollbar()\r\n        this.setScrollbar()\r\n        this.$body.addClass('modal-open')\r\n\r\n        this.escape()\r\n        this.resize()\r\n\r\n        this.$element.on('click.dismiss.bs.modal', '[data-dismiss=\"modal\"]', $.proxy(this.hide, this))\r\n\r\n        this.$dialog.on('mousedown.dismiss.bs.modal', function () {\r\n            that.$element.one('mouseup.dismiss.bs.modal', function (e) {\r\n                if ($(e.target).is(that.$element)) that.ignoreBackdropClick = true\r\n            })\r\n        })\r\n\r\n        this.backdrop(function () {\r\n            var transition = $.support.transition && that.$element.hasClass('fade')\r\n\r\n            if (!that.$element.parent().length) {\r\n                that.$element.appendTo(that.$body) // don't move modals dom position\r\n            }\r\n\r\n            that.$element\r\n                .show()\r\n                .scrollTop(0)\r\n\r\n            that.adjustDialog()\r\n\r\n            if (transition) {\r\n                that.$element[0].offsetWidth // force reflow\r\n            }\r\n\r\n            that.$element.addClass('in')\r\n\r\n            that.enforceFocus()\r\n\r\n            var e = $.Event('shown.bs.modal', { relatedTarget: _relatedTarget })\r\n\r\n            transition ?\r\n                that.$dialog // wait for modal to slide in\r\n                    .one('bsTransitionEnd', function () {\r\n                        that.$element.trigger('focus').trigger(e)\r\n                    })\r\n                    .emulateTransitionEnd(Modal.TRANSITION_DURATION) :\r\n                that.$element.trigger('focus').trigger(e)\r\n        })\r\n    }\r\n\r\n    Modal.prototype.hide = function (e) {\r\n        if (e) e.preventDefault()\r\n\r\n        e = $.Event('hide.bs.modal')\r\n\r\n        this.$element.trigger(e)\r\n\r\n        if (!this.isShown || e.isDefaultPrevented()) return\r\n\r\n        this.isShown = false\r\n\r\n        this.escape()\r\n        this.resize()\r\n\r\n        $(document).off('focusin.bs.modal')\r\n\r\n        this.$element\r\n            .removeClass('in')\r\n            .off('click.dismiss.bs.modal')\r\n            .off('mouseup.dismiss.bs.modal')\r\n\r\n        this.$dialog.off('mousedown.dismiss.bs.modal')\r\n\r\n        $.support.transition && this.$element.hasClass('fade') ?\r\n            this.$element\r\n                .one('bsTransitionEnd', $.proxy(this.hideModal, this))\r\n                .emulateTransitionEnd(Modal.TRANSITION_DURATION) :\r\n            this.hideModal()\r\n    }\r\n\r\n    Modal.prototype.enforceFocus = function () {\r\n        $(document)\r\n            .off('focusin.bs.modal') // guard against infinite focus loop\r\n            .on('focusin.bs.modal', $.proxy(function (e) {\r\n                if (document !== e.target &&\r\n                    this.$element[0] !== e.target &&\r\n                    !this.$element.has(e.target).length) {\r\n                    this.$element.trigger('focus')\r\n                }\r\n            }, this))\r\n    }\r\n\r\n    Modal.prototype.escape = function () {\r\n        if (this.isShown && this.options.keyboard) {\r\n            this.$element.on('keydown.dismiss.bs.modal', $.proxy(function (e) {\r\n                e.which == 27 && this.hide()\r\n            }, this))\r\n        } else if (!this.isShown) {\r\n            this.$element.off('keydown.dismiss.bs.modal')\r\n        }\r\n    }\r\n\r\n    Modal.prototype.resize = function () {\r\n        if (this.isShown) {\r\n            $(window).on('resize.bs.modal', $.proxy(this.handleUpdate, this))\r\n        } else {\r\n            $(window).off('resize.bs.modal')\r\n        }\r\n    }\r\n\r\n    Modal.prototype.hideModal = function () {\r\n        var that = this\r\n        this.$element.hide()\r\n        this.backdrop(function () {\r\n            that.$body.removeClass('modal-open')\r\n            that.resetAdjustments()\r\n            that.resetScrollbar()\r\n            that.$element.trigger('hidden.bs.modal')\r\n        })\r\n    }\r\n\r\n    Modal.prototype.removeBackdrop = function () {\r\n        this.$backdrop && this.$backdrop.remove()\r\n        this.$backdrop = null\r\n    }\r\n\r\n    Modal.prototype.backdrop = function (callback) {\r\n        var that = this\r\n        var animate = this.$element.hasClass('fade') ? 'fade' : ''\r\n\r\n        if (this.isShown && this.options.backdrop) {\r\n            var doAnimate = $.support.transition && animate\r\n\r\n            this.$backdrop = $(document.createElement('div'))\r\n                .addClass('modal-backdrop ' + animate)\r\n                .appendTo(this.$body)\r\n\r\n            this.$element.on('click.dismiss.bs.modal', $.proxy(function (e) {\r\n                if (this.ignoreBackdropClick) {\r\n                    this.ignoreBackdropClick = false\r\n                    return\r\n                }\r\n                if (e.target !== e.currentTarget) return\r\n                this.options.backdrop == 'static'\r\n                    ? this.$element[0].focus()\r\n                    : this.hide()\r\n            }, this))\r\n\r\n            if (doAnimate) this.$backdrop[0].offsetWidth // force reflow\r\n\r\n            this.$backdrop.addClass('in')\r\n\r\n            if (!callback) return\r\n\r\n            doAnimate ?\r\n                this.$backdrop\r\n                    .one('bsTransitionEnd', callback)\r\n                    .emulateTransitionEnd(Modal.BACKDROP_TRANSITION_DURATION) :\r\n                callback()\r\n\r\n        } else if (!this.isShown && this.$backdrop) {\r\n            this.$backdrop.removeClass('in')\r\n\r\n            var callbackRemove = function () {\r\n                that.removeBackdrop()\r\n                callback && callback()\r\n            }\r\n            $.support.transition && this.$element.hasClass('fade') ?\r\n                this.$backdrop\r\n                    .one('bsTransitionEnd', callbackRemove)\r\n                    .emulateTransitionEnd(Modal.BACKDROP_TRANSITION_DURATION) :\r\n                callbackRemove()\r\n\r\n        } else if (callback) {\r\n            callback()\r\n        }\r\n    }\r\n\r\n    // these following methods are used to handle overflowing modals\r\n\r\n    Modal.prototype.handleUpdate = function () {\r\n        this.adjustDialog()\r\n    }\r\n\r\n    Modal.prototype.adjustDialog = function () {\r\n        var modalIsOverflowing = this.$element[0].scrollHeight > document.documentElement.clientHeight\r\n\r\n        this.$element.css({\r\n            paddingLeft: !this.bodyIsOverflowing && modalIsOverflowing ? this.scrollbarWidth : '',\r\n            paddingRight: this.bodyIsOverflowing && !modalIsOverflowing ? this.scrollbarWidth : ''\r\n        })\r\n    }\r\n\r\n    Modal.prototype.resetAdjustments = function () {\r\n        this.$element.css({\r\n            paddingLeft: '',\r\n            paddingRight: ''\r\n        })\r\n    }\r\n\r\n    Modal.prototype.checkScrollbar = function () {\r\n        var fullWindowWidth = window.innerWidth\r\n        if (!fullWindowWidth) { // workaround for missing window.innerWidth in IE8\r\n            var documentElementRect = document.documentElement.getBoundingClientRect()\r\n            fullWindowWidth = documentElementRect.right - Math.abs(documentElementRect.left)\r\n        }\r\n        this.bodyIsOverflowing = document.body.clientWidth < fullWindowWidth\r\n        this.scrollbarWidth = this.measureScrollbar()\r\n    }\r\n\r\n    Modal.prototype.setScrollbar = function () {\r\n        var bodyPad = parseInt((this.$body.css('padding-right') || 0), 10)\r\n        this.originalBodyPad = document.body.style.paddingRight || ''\r\n        var scrollbarWidth = this.scrollbarWidth\r\n        if (this.bodyIsOverflowing) {\r\n            this.$body.css('padding-right', bodyPad + scrollbarWidth)\r\n            $(this.fixedContent).each(function (index, element) {\r\n                var actualPadding = element.style.paddingRight\r\n                var calculatedPadding = $(element).css('padding-right')\r\n                $(element)\r\n                    .data('padding-right', actualPadding)\r\n                    .css('padding-right', parseFloat(calculatedPadding) + scrollbarWidth + 'px')\r\n            })\r\n        }\r\n    }\r\n\r\n    Modal.prototype.resetScrollbar = function () {\r\n        this.$body.css('padding-right', this.originalBodyPad)\r\n        $(this.fixedContent).each(function (index, element) {\r\n            var padding = $(element).data('padding-right')\r\n            $(element).removeData('padding-right')\r\n            element.style.paddingRight = padding ? padding : ''\r\n        })\r\n    }\r\n\r\n    Modal.prototype.measureScrollbar = function () { // thx walsh\r\n        var scrollDiv = document.createElement('div')\r\n        scrollDiv.className = 'modal-scrollbar-measure'\r\n        this.$body.append(scrollDiv)\r\n        var scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth\r\n        this.$body[0].removeChild(scrollDiv)\r\n        return scrollbarWidth\r\n    }\r\n\r\n\r\n    // MODAL PLUGIN DEFINITION\r\n    // =======================\r\n\r\n    function Plugin(option, _relatedTarget) {\r\n        return this.each(function () {\r\n            var $this = $(this)\r\n            var data = $this.data('bs.modal')\r\n            var options = $.extend({}, Modal.DEFAULTS, $this.data(), typeof option == 'object' && option)\r\n\r\n            if (!data) $this.data('bs.modal', (data = new Modal(this, options)))\r\n            if (typeof option == 'string') data[option](_relatedTarget)\r\n            else if (options.show) data.show(_relatedTarget)\r\n        })\r\n    }\r\n\r\n    var old = $.fn.modal\r\n\r\n    $.fn.modal = Plugin\r\n    $.fn.modal.Constructor = Modal\r\n\r\n\r\n    // MODAL NO CONFLICT\r\n    // =================\r\n\r\n    $.fn.modal.noConflict = function () {\r\n        $.fn.modal = old\r\n        return this\r\n    }\r\n\r\n\r\n    // MODAL DATA-API\r\n    // ==============\r\n\r\n    $(document).on('click.bs.modal.data-api', '[data-toggle=\"modal\"]', function (e) {\r\n        var $this = $(this)\r\n        var href = $this.attr('href')\r\n        var target = $this.attr('data-target') ||\r\n            (href && href.replace(/.*(?=#[^\\s]+$)/, '')) // strip for ie7\r\n\r\n        var $target = $(document).find(target)\r\n        var option = $target.data('bs.modal') ? 'toggle' : $.extend({ remote: !/#/.test(href) && href }, $target.data(), $this.data())\r\n\r\n        if ($this.is('a')) e.preventDefault()\r\n\r\n        $target.one('show.bs.modal', function (showEvent) {\r\n            if (showEvent.isDefaultPrevented()) return // only register focus restorer if modal will actually get shown\r\n            $target.one('hidden.bs.modal', function () {\r\n                $this.is(':visible') && $this.trigger('focus')\r\n            })\r\n        })\r\n        Plugin.call($target, option, this)\r\n    })\r\n\r\n}(jQuery);\r\n"], "names": ["j<PERSON><PERSON><PERSON>", "Error", "$", "version", "fn", "j<PERSON>y", "split", "Modal", "element", "options", "this", "$body", "document", "body", "$element", "$dialog", "find", "$backdrop", "isShown", "originalBodyPad", "scrollbarWidth", "ignoreBackdropClick", "fixedContent", "remote", "load", "proxy", "trigger", "Plugin", "option", "_relatedTarget", "each", "$this", "data", "extend", "DEFAULTS", "show", "VERSION", "TRANSITION_DURATION", "BACKDROP_TRANSITION_DURATION", "backdrop", "keyboard", "prototype", "toggle", "hide", "that", "e", "Event", "relatedTarget", "isDefaultPrevented", "checkScrollbar", "setScrollbar", "addClass", "escape", "resize", "on", "one", "target", "is", "transition", "support", "hasClass", "parent", "length", "appendTo", "scrollTop", "adjustDialog", "offsetWidth", "enforceFocus", "emulateTransitionEnd", "preventDefault", "off", "removeClass", "hideModal", "has", "which", "window", "handleUpdate", "resetAdjustments", "resetScrollbar", "removeBackdrop", "remove", "callback", "animate", "doAnimate", "createElement", "currentTarget", "focus", "callback<PERSON><PERSON><PERSON>", "modalIsOverflowing", "scrollHeight", "documentElement", "clientHeight", "css", "paddingLeft", "bodyIsOverflowing", "paddingRight", "fullWindowWidth", "innerWidth", "documentElementRect", "getBoundingClientRect", "right", "Math", "abs", "left", "clientWidth", "measureScrollbar", "bodyPad", "parseInt", "style", "index", "actualPadding", "calculatedPadding", "parseFloat", "padding", "removeData", "scrollDiv", "className", "append", "<PERSON><PERSON><PERSON><PERSON>", "old", "modal", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "href", "attr", "replace", "$target", "test", "showEvent", "call"], "sourceRoot": ""}