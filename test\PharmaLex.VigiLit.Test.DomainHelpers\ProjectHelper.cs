﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class ProjectHelper
{
    private readonly IProjectRepository _projectRepository;

    public ProjectHelper(IProjectRepository projectRepository)
    {
        _projectRepository = projectRepository;
    }

    public async Task<Project> AddProject(string name, Company company)
    {
        var project = new Project(name, company.Id);
        _projectRepository.Add(project);
        await _projectRepository.SaveChangesAsync();
        return project;
    }
}
