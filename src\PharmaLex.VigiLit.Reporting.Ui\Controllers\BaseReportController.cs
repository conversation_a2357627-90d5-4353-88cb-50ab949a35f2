﻿using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;

namespace PharmaLex.VigiLit.Reporting.Ui.Controllers;

public abstract class BaseReportController : BaseController
{
    protected BaseReportController(IUserSessionService userSessionService, IConfiguration configuration) : base(userSessionService, configuration)
    {
    }

    protected string ControllerName => ControllerContext.RouteData.Values["controller"]?.ToString() ?? string.Empty;
}