﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.262" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserManagement\PharmaLex.Core.UserManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj" />
  </ItemGroup>

</Project>
