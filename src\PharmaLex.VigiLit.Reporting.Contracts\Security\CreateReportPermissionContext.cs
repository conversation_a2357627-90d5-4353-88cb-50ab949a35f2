﻿using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.Reporting.Contracts.Security;

public class CreateReportPermissionContext : IAccessControlContext
{
    /// <summary>
    /// Context for checking permissions for a user to create a report.
    /// </summary>
    /// <seealso cref="PharmaLex.VigiLit.AccessControl.IAccessControlContext" />
    public CreateReportPermissionContext(int userId, int companyId)
    {
        UserId = userId;
        CompanyId = companyId;
    }

    /// <summary>
    /// Gets the user identifier of the user who is requesting to create a report.
    /// </summary>
    /// <value>
    /// The user identifier.
    /// </value>
    public int UserId { get; }

    /// <summary>
    /// Gets the company identifier for the report that is requested.
    /// </summary>
    /// <value>
    /// The company identifier.
    /// </value>
    public int CompanyId { get; }
}
