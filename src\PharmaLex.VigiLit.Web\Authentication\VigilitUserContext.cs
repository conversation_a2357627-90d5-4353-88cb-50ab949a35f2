﻿using System;
using Microsoft.AspNetCore.Http;
using PharmaLex.Authentication.B2C;
using PharmaLex.VigiLit.Domain;

namespace PharmaLex.VigiLit.Web.Authentication;

public class VigilitUserContext : PlxUserContext, IVigiLitUserContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public int UserId
    {
        get
        {
            var userId = _httpContextAccessor.HttpContext?.User.GetClaimValue("plx:userid");
            if (string.IsNullOrEmpty(userId))
                throw new InvalidOperationException("UserId could not be found in user claims");

            return int.Parse(userId);
        }
    }

    public VigilitUserContext(IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }
}
