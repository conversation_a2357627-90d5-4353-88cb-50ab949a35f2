using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Contracts.Repositories;
using PharmaLex.VigiLit.Scraping.Contracts.Services;
using PharmaLex.VigiLit.Scraping.Entities;
using PharmaLex.VigiLit.Scraping.Entities.Enums;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyRunCompletionService : IApifyRunCompletionService
{
    private readonly IApifyRunRepository _apifyRunRepository;
    private readonly IApifyRunFileRepository _apifyRunFileRepository;
    private readonly IDataExtractionClient _dataExtractionClient;
    private readonly ILogger<ApifyRunCompletionService> _logger;

    public ApifyRunCompletionService(
        IApifyRunRepository apifyRunRepository,
        IApifyRunFileRepository apifyRunFileRepository,
        IDataExtractionClient dataExtractionClient,
        ILogger<ApifyRunCompletionService> logger)
    {
        _apifyRunRepository = apifyRunRepository;
        _apifyRunFileRepository = apifyRunFileRepository;
        _dataExtractionClient = dataExtractionClient;
        _logger = logger;
    }

    public async Task ProcessCompletedRunsAsync()
    {
        _logger.LogInformation("Starting to process completed Apify runs for metadata extraction");

        try
        {
            var runsReadyForExtraction = await _apifyRunRepository.GetRunsReadyForExtractionAsync();
            
            _logger.LogInformation("Found {Count} Apify runs ready for metadata extraction", runsReadyForExtraction.Count());

            foreach (var run in runsReadyForExtraction)
            {
                await ProcessRunForExtractionAsync(run);
            }

            _logger.LogInformation("Completed processing Apify runs for metadata extraction");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing completed Apify runs");
            throw;
        }
    }

    public async Task ProcessRunAsync(string apifyRunId)
    {
        _logger.LogInformation("Processing specific Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));

        try
        {
            var run = await _apifyRunRepository.GetWithFilesByApifyRunIdAsync(apifyRunId);
            if (run == null)
            {
                _logger.LogWarning("Apify run not found: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
                return;
            }

            if (!run.IsReadyForExtraction())
            {
                _logger.LogInformation("Apify run {ApifyRunId} is not ready for extraction. Status: {Status}, FilesTransferred: {FilesTransferred}, ExtractionInitiated: {ExtractionInitiated}",
                    LogSanitizer.Sanitize(apifyRunId), run.Status, run.FilesTransferredAt.HasValue, run.ExtractionInitiatedAt.HasValue);
                return;
            }

            await ProcessRunForExtractionAsync(run);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
            throw;
        }
    }

    public async Task UpdateRunStatusesAsync()
    {
        _logger.LogInformation("Starting to update Apify run statuses");

        try
        {
            var runsNeedingUpdate = await _apifyRunRepository.GetRunsNeedingStatusUpdateAsync();
            
            _logger.LogInformation("Found {Count} Apify runs needing status update", runsNeedingUpdate.Count());

            foreach (var run in runsNeedingUpdate)
            {
                // TODO: Integrate with Phlex.Core.Apify to check run status
                // For now, we'll just update the LastStatusCheck timestamp
                run.LastStatusCheck = DateTime.UtcNow;
            }

            await _apifyRunRepository.SaveChangesAsync();
            _logger.LogInformation("Completed updating Apify run statuses");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating Apify run statuses");
            throw;
        }
    }

    public async Task<ApifyRun> RegisterRunAsync(string apifyRunId, string actorName)
    {
        _logger.LogInformation("Registering new Apify run: {ApifyRunId}, Actor: {ActorName}", 
            LogSanitizer.Sanitize(apifyRunId), LogSanitizer.Sanitize(actorName));

        try
        {
            // Check if run already exists
            var existingRun = await _apifyRunRepository.GetByApifyRunIdAsync(apifyRunId);
            if (existingRun != null)
            {
                _logger.LogInformation("Apify run already exists: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
                return existingRun;
            }

            var newRun = new ApifyRun(apifyRunId, actorName);
            _apifyRunRepository.Add(newRun);
            await _apifyRunRepository.SaveChangesAsync();

            _logger.LogInformation("Successfully registered Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
            return newRun;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
            throw;
        }
    }

    public async Task MarkFilesTransferredAsync(string apifyRunId)
    {
        _logger.LogInformation("Marking files as transferred for Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));

        try
        {
            var run = await _apifyRunRepository.GetByApifyRunIdAsync(apifyRunId);
            if (run == null)
            {
                _logger.LogWarning("Apify run not found when marking files transferred: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
                return;
            }

            run.MarkFilesTransferred();
            await _apifyRunRepository.SaveChangesAsync();

            _logger.LogInformation("Successfully marked files as transferred for Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark files as transferred for Apify run: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
            throw;
        }
    }

    public async Task MarkRunFailedAsync(string apifyRunId)
    {
        _logger.LogInformation("Marking Apify run as failed: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));

        try
        {
            var run = await _apifyRunRepository.GetByApifyRunIdAsync(apifyRunId);
            if (run == null)
            {
                _logger.LogWarning("Apify run not found when marking as failed: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
                return;
            }

            run.UpdateStatus(ApifyRunStatus.Failed);
            await _apifyRunRepository.SaveChangesAsync();

            _logger.LogInformation("Successfully marked Apify run as failed: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark Apify run as failed: {ApifyRunId}", LogSanitizer.Sanitize(apifyRunId));
            throw;
        }
    }

    private async Task ProcessRunForExtractionAsync(ApifyRun run)
    {
        _logger.LogInformation("Processing Apify run {ApifyRunId} for metadata extraction", LogSanitizer.Sanitize(run.ApifyRunId));

        try
        {
            var pdfsForExtraction = run.GetPdfsForExtraction().ToList();
            _logger.LogInformation("Found {PdfCount} PDFs ready for extraction in run {ApifyRunId}", 
                pdfsForExtraction.Count, LogSanitizer.Sanitize(run.ApifyRunId));

            foreach (var pdfFile in pdfsForExtraction)
            {
                await SendExtractionCommandAsync(run, pdfFile);
            }

            // Mark extraction as initiated
            run.MarkExtractionInitiated();
            await _apifyRunRepository.SaveChangesAsync();

            _logger.LogInformation("Successfully initiated metadata extraction for {PdfCount} PDFs from Apify run {ApifyRunId}", 
                pdfsForExtraction.Count, LogSanitizer.Sanitize(run.ApifyRunId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process Apify run {ApifyRunId} for extraction", LogSanitizer.Sanitize(run.ApifyRunId));
            throw;
        }
    }

    private async Task SendExtractionCommandAsync(ApifyRun run, ApifyRunFile pdfFile)
    {
        try
        {
            var correlationId = Guid.NewGuid();
            var command = new ExtractDataCommand
            {
                BatchId = run.ApifyRunId,
                FileName = pdfFile.FileName,
                CorrelationId = correlationId,
                Source = Source.Apify
            };

            await _dataExtractionClient.Send(command);

            // Update the file to mark extraction as queued
            pdfFile.MarkExtractionQueued(correlationId);

            _logger.LogInformation("Successfully sent ExtractDataCommand for file {FileName} from Apify run {ApifyRunId} with correlation ID {CorrelationId}",
                LogSanitizer.Sanitize(pdfFile.FileName), LogSanitizer.Sanitize(run.ApifyRunId), correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send ExtractDataCommand for file {FileName} from Apify run {ApifyRunId}",
                LogSanitizer.Sanitize(pdfFile.FileName), LogSanitizer.Sanitize(run.ApifyRunId));
            throw;
        }
    }
}
