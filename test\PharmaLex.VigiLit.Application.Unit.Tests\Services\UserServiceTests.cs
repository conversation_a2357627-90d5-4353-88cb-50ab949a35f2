﻿using AutoMapper;
using Moq;
using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.Application.UserManagement.Claims;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System.Security.Claims;
using Xunit;
using Claim = PharmaLex.VigiLit.Domain.UserManagement.Claim;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class UserServiceTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new();
    private readonly Mock<IClaimRepository> _mockClaimRepository = new();
    private readonly Mock<IUserSessionService> _mockUserSessionService = new();
    private readonly IUserService _userService;
    readonly IMapper _mapper;

    public UserServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ClaimProfile());
            mc.AddProfile(new UserMappingProfile());
        });

        _mapper = mapperConfig.CreateMapper();

        _userService = new UserService(_mockUserRepository.Object,
                                        _mockClaimRepository.Object,
                                        _mockUserSessionService.Object,
                                        _mapper);
    }

    [Fact]
    public async Task GetAllAsync_Returns_collection_of_users()
    {
        // Arrange
        var userFullModel = new UserFullModel()
        {
            GivenName = "Given Name",
            Claims = new List<ClaimModel>()
            {
                new() { Id = 3, DisplayName = "Claim #3" },
                new() { Id = 4, DisplayName = "Claim #4" },
            },
            ClaimIds = new List<int> { 3, 4 },
        };

        IEnumerable<UserFullModel> users = new List<UserFullModel>
        {
            userFullModel
        };

        _mockUserRepository.Setup(x => x.GetAllAsync(true)).ReturnsAsync(users);

        IEnumerable<Claim> claims = new List<Claim>()
        {
            new FakeClaim(1,"SuperAdmin") { DisplayName = "Super User", ClaimType="admin" },
            new FakeClaim(2,"InternalSupport") { DisplayName = "Internal Support", ClaimType="admin" },
            new FakeClaim(3,"PreAssessor") { DisplayName = "Pre Assessor", ClaimType="operator" },
            new FakeClaim(4,"MasterAssessor") { DisplayName = "Master Assessor", ClaimType="operator" },
            new FakeClaim(6,"ClientResearcher") { DisplayName = "Client Researcher", ClaimType="company" },
            new FakeClaim(7,"PharmaLexResearcher") { DisplayName = "PharmaLex Researcher", ClaimType="operator" },
            new FakeClaim(8,"PharmaLexClientResearcher") { DisplayName = "PharmaLex Client Researcher", ClaimType="operator" },
            new FakeClaim(9,"CaseFileOperator") { DisplayName = "Case File Operator", ClaimType="operator" },
        };

        _mockClaimRepository.Setup(x => x.GetAllAsync()).ReturnsAsync(claims);

        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity());

        // Act
        var result = await _userService.GetAllAsync(claimsPrincipal);

        // Assert
        var singleResult = result.Single();
        Assert.Equal("Given Name", singleResult.GivenName);
        Assert.Equal("Claim #3", singleResult.Claims[0].DisplayName);
        Assert.Equal("Claim #4", singleResult.Claims[1].DisplayName);
    }


    [Fact]
    public async Task UpdateAssessor_throws_exception_for_invalid_qualityCheckPercentage()
    {
        //Arrange
        var user = GetUser();
        _mockUserRepository.Setup(x => x.GetById(user.Id)).ReturnsAsync(user);
        var substanceIds = new[] { 1, 34, 45 };
        var invalidQualityCheckPercentage = -10;
        var expectedMessage = "Invalid quality check percentage";

        //Act
        var exception = await Record.ExceptionAsync(() => _userService.UpdateAssessor(user.Id, substanceIds, invalidQualityCheckPercentage));

        //Assert
        Assert.NotNull(exception);
        Assert.IsType<ArgumentException>(exception);
        Assert.Equal(expectedMessage, exception.Message);
    }
    [Fact]
    public async Task UpdateAssessor_throws_no_exception_for_valid_qualityCheckPercentage()
    {
        //Arrange
        var user = GetUser();
        _mockUserRepository.Setup(x => x.GetById(user.Id)).ReturnsAsync(user);
        var substanceIds = new[] { 1, 34, 45 };
        var validQualityCheckPercentage = 20;

        //Act
        var exception = await Record.ExceptionAsync(() => _userService.UpdateAssessor(user.Id, substanceIds, validQualityCheckPercentage));

        //Assert
        Assert.Null(exception);
    }

    [Fact]
    public async Task Update_updates_user_and_calls_SaveChanges()
    {
        //Arrange
        var user = GetUser();
        var userModel = new UserModel();
        _mapper.Map(userModel, user);

        _mockUserRepository.Setup(x => x.GetById(user.Id)).ReturnsAsync(user);
        _mockUserRepository.Setup(x => x.SaveChangesAsync());

        //Act
        await _userService.Update(userModel);

        //Assert
        _mockUserRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    private static FakeUser GetUser()
    {
        var lastLoginDate = new DateTime(2024, 1, 24, 00, 00, 00, DateTimeKind.Utc);
        var activationExpiryDate = new DateTime(2024, 1, 30, 00, 00, 00, DateTimeKind.Utc);

        var fakeUser = new FakeUser("Given Name", "Family Name", "<EMAIL>", 1, lastLoginDate, activationExpiryDate)
        {
            UserSubstances = new List<UserSubstance>() { new UserSubstance(1, 34) }
        };
        return fakeUser;
    }
}