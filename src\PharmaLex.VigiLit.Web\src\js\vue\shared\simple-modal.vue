﻿<template>
    <transition name="modal">
        <div class="modal-mask">
            <div class="modal-wrapper">
                <div class="modal-container">

                    <div class="modal-header flex flex-nowrap justify-space-between">
                        <h3><slot name="header"></slot></h3>
                        <i class="m-icon" @click.prevent="$emit('close')">close</i>
                    </div>

                    <div class="modal-body">
                        <p><slot name="body"></slot></p>
                    </div>

                    <div class="modal-footer buttons">
                            <button class="button secondary" @click.prevent="$emit('close')">
                                No
                            </button>
                            <button class="button" @click.prevent="callback">
                                Yes
                            </button>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
    export default {
        methods: {
            callback() {
                this.$emit("onCallback")
            }
        }
    }
</script>

