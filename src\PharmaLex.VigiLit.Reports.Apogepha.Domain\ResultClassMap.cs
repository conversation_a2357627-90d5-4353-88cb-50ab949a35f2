﻿using PharmaLex.VigiLit.Reporting.Contracts.ClassMap;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

public sealed class ResultClassMap : CamelCaseClassMap<ApogephaClientReportResult>
{
    public ResultClassMap()
    {
        Map(m => m.DateOfQuery).Name("date of query");
        Map(m => m.Substance).Name("substance");
        Map(m => m.YesId).Name("yes-id");
        Map(m => m.EntryDate).Name("pm-bps_entry date");
        Map(m => m.Title).Name("title");
        Map(m => m.Authors).Name("authors");
        Map(m => m.Abstract).Name("abstract");
        Map(m => m.Source).Name("source");
        Map(m => m.Category).Name("category");

        Map(m => m.CreatedDate).Ignore();
        Map(m => m.CreatedBy).Ignore();
        Map(m => m.LastUpdatedDate).Ignore();
        Map(m => m.LastUpdatedBy).Ignore();
    }
}