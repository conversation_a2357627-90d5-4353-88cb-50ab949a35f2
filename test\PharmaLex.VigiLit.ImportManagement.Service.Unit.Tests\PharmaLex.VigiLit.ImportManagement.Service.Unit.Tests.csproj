﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.2.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj" />
	</ItemGroup>

</Project>
