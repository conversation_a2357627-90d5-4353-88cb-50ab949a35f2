﻿using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.AccessControl.Unit.Tests;

[ExcludeFromCodeCoverage(Justification = "Test Object")]
internal class TestEvaluator1 : PermissionEvaluator<TestContext1>
{
#pragma warning disable CS1998 // Test Object - ignore
    public override async Task<bool> HasPermissions(TestContext1 context)
    {
        if (context.X == context.Y)
        {
            return true;
        }
        throw new UnauthorizedAccessException();
    }
#pragma warning restore CS1998
}
