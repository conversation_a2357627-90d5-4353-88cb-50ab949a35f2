﻿using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests;

public class DashboardServiceTests
{
    private readonly IDashboardService _dashboardService;

    private readonly Mock<IImportRepository> _mockImportRepository = new();
    private readonly Mock<IImportContractReferenceClassificationRepository> _mockImportContractReferenceClassificationRepository = new();
    private readonly Mock<IImportSelectionRepository> _mockImportSelectionRepository = new();
    private readonly Mock<IReferenceClassificationLockRepository> _mockReferenceClassificationLockRepository = new();
    private readonly Mock<IVigiLitUserContext> _mockUserContext = new();
    private readonly Mock<ICompanyRepository> _mockCompanyRepository = new();

    public DashboardServiceTests()
    {
        _dashboardService = new DashboardService(
            new NullLoggerFactory(),
            _mockImportRepository.Object,
            _mockImportContractReferenceClassificationRepository.Object,
            _mockImportSelectionRepository.Object,
            _mockReferenceClassificationLockRepository.Object,
            _mockCompanyRepository.Object,
            _mockUserContext.Object);
    }

    [Fact]
    public async Task GetImportDashboardDetailsExport_ReturnsResults()
    {
        // Arrange
        var models = new List<DashboardDetailsRowModel>
        {
            new()
            {
                SourceId="1",
                CompanyName = "Company 1",
                ProjectName = "Project 1",
                ClassificationStatus = "Status 1",
                SubstanceName = "Substance 1",
                SourceModificationDate = "28/01/1970",
                ICRCType = "Type1"
            },
            new()
            {
                SourceId="2",
                CompanyName = "Company 2",
                ProjectName = "Project 2",
                ClassificationStatus = "Status 2",
                SubstanceName = "Substance 2",
                SourceModificationDate = "28/02/1970",
                ICRCType = "Type2"
            },
        };

        _mockImportContractReferenceClassificationRepository.Setup(x => x.GetImportDashboardDetailsExport(1)).ReturnsAsync(models);

        // Act
        var results = await _dashboardService.GetImportDashboardDetailsExport(1);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(2, results.Count());
        Assert.Equal("1", results.ToList()[0].SourceId);
        Assert.Equal("2", results.ToList()[1].SourceId);
    }

    [Fact]
    public async Task GetImportDashboardDetails_Page_is_less_than_one_throws_exception()
    {
        // Arrange
        var importId = 42;
        var page = 0;
        var pageSize = 10;

        // Act
        // Assert
        var exception = await Assert.ThrowsAsync<ArgumentOutOfRangeException>(async () => await _dashboardService.GetImportDashboardDetails(importId, page, pageSize));
        Assert.Equal("Page must be 1 or greater. (Parameter 'page')", exception.Message);
    }

    [Fact]
    public async Task GetImportDashboardDetails_Page_size_is_less_than_25_throws_exception()
    {
        // Arrange
        var importId = 42;
        var page = 1;
        var pageSize = 24;

        // Act
        // Assert
        var exception = await Assert.ThrowsAsync<ArgumentOutOfRangeException>(async () => await _dashboardService.GetImportDashboardDetails(importId, page, pageSize));
        Assert.Equal("Page size must be between 25 and 100. (Parameter 'pageSize')", exception.Message);
    }

    [Fact]
    public async Task GetImportDashboardDetails_Page_size_is_more_than_100_throws_exception()
    {
        // Arrange
        var importId = 42;
        var page = 1;
        var pageSize = 101;

        // Act
        // Assert
        var exception = await Assert.ThrowsAsync<ArgumentOutOfRangeException>(async () => await _dashboardService.GetImportDashboardDetails(importId, page, pageSize));
        Assert.Equal("Page size must be between 25 and 100. (Parameter 'pageSize')", exception.Message);
    }

    [Fact]
    public async Task GetImportDashboardDetails_returns()
    {
        // Arrange
        var importId = 42;
        var page = 1;
        var pageSize = 50;

        var importDashboardDetailsResult = new DashboardDetailsResult()
        {
            Rows = new List<DashboardDetailsRowModel>(),
            TotalRowCount = 43,
        };

        var importDashboardModel = new ImportDashboardModel()
        {
            Name = "Dashboard Model",
            ImportType = "Import Type",
            ImportDate = "28/01/2025",
            ImportDashboardStatusType = "Status",
        };

        _mockImportContractReferenceClassificationRepository.Setup(x => x.GetImportDashboardDetailsRows(importId, page, pageSize))
            .ReturnsAsync(importDashboardDetailsResult);

        _mockImportRepository.Setup(x => x.GetImportForDashboardDetails(42)).ReturnsAsync(importDashboardModel);

        // Act
        var result = await _dashboardService.GetImportDashboardDetails(importId, page, pageSize);

        // Assert
        Assert.Equal(importDashboardModel, result.Import);
        Assert.Equal(1, result.Page);
        Assert.Equal(50, result.PageSize);
        Assert.Equal(43, result.TotalRowCount);
    }

    [Fact]
    public async Task GetImportsForDashboard_ReturnsResults()
    {
        // Arrange
        var models = new List<ImportDashboardModel>
        {
            new()
            {
                Id=1,
                Name = "Dashboard Model #1",
                ImportType = "Import Type #1",
                ImportDate = "28/01/2025",
                ImportDashboardStatusType = "Status",
            },
            new()
            {
                Id=2,
                Name = "Dashboard Model #2",
                ImportType = "Import Type #2",
                ImportDate = "28/01/2025",
                ImportDashboardStatusType = "Status",
            },
        };

        _mockImportRepository.Setup(x => x.GetImportsForDashboard()).ReturnsAsync(models);

        // Act
        var results = await _dashboardService.GetImportsForDashboard();

        // Assert
        Assert.NotNull(results);
        Assert.Equal(2, results.Count());
        Assert.Equal(1, results.ToList()[0].Id);
        Assert.Equal(2, results.ToList()[1].Id);
    }

    [Fact]
    public async Task Archive_Calls_Repos()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Signed
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        await _dashboardService.Archive(1);

        // Assert
        _mockImportRepository.Verify(x => x.GetById(1), Times.Once);
        _mockImportRepository.Verify(x => x.Archive(1), Times.Once);
        _mockImportSelectionRepository.Verify(x => x.ClearSelections(1), Times.Once);
    }

    [Fact]
    public async Task Archive_Throws_ArgumentException_When_Import_Is_Null()
    {
        // Arrange
        Import? import = null;

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _dashboardService.Archive(1));
    }

    [Fact]
    public async Task Archive_Throws_InvalidOperationException_When_Import_Is_Not_Signed()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Imported
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () => await _dashboardService.Archive(1));
    }

    [Fact]
    public async Task Select_Calls_Repos()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Imported
        };

        var lockingResult = new LockingResult()
        {
            IsSuccessful = true,
            ClassificationIds = []
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(It.IsAny<int>())).ReturnsAsync(lockingResult);

        // Act
        await _dashboardService.Select(1);

        // Assert
        _mockImportRepository.Verify(x => x.GetById(1), Times.Once);
        _mockImportSelectionRepository.Verify(x => x.Select(It.IsAny<int>(), 1), Times.Once);
    }

    [Fact]
    public async Task Select_Throws_ArgumentException_When_Import_Is_Null()
    {
        // Arrange
        Import? import = null;

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _dashboardService.Select(1));
    }

    [Fact]
    public async Task Select_Throws_InvalidOperationException_When_Import_Is_Not_Active()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Archived
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () => await _dashboardService.Select(1));
    }

    [Fact]
    public async Task Deselect_Calls_Repos()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Imported
        };

        var lockingResult = new LockingResult()
        {
            IsSuccessful = true,
            ClassificationIds = []
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(It.IsAny<int>())).ReturnsAsync(lockingResult);

        // Act
        await _dashboardService.Deselect(1);

        // Assert
        _mockImportRepository.Verify(x => x.GetById(1), Times.Once);
        _mockImportSelectionRepository.Verify(x => x.Deselect(It.IsAny<int>(), 1), Times.Once);
    }

    [Fact]
    public async Task Deselect_Throws_ArgumentException_When_Import_Is_Null()
    {
        // Arrange
        Import? import = null;

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _dashboardService.Deselect(1));
    }

    [Fact]
    public async Task Deselect_Throws_InvalidOperationException_When_Import_Is_Not_Active()
    {
        // Arrange
        var import = new FakeImport(1)
        {
            ImportDashboardStatusType = ImportDashboardStatusType.Archived
        };

        _mockImportRepository.Setup(x => x.GetById(1)).ReturnsAsync(import);

        // Act
        // Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () => await _dashboardService.Deselect(1));
    }

    [Fact]
    public async Task GetSelectedImport_ReturnsResults()
    {
        // Arrange
        var importSelectionModel = new ImportSelectionModel() { ImportId = 1 };

        _mockImportSelectionRepository.Setup(x => x.GetSelectedImport(It.IsAny<int>())).ReturnsAsync(importSelectionModel);

        // Act
        var result = await _dashboardService.GetSelectedImport();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ImportId);
    }
}