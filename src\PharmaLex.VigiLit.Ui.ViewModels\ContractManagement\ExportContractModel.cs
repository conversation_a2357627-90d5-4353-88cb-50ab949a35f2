﻿namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class ExportContractModel
{
    // Cannot inherit from SimpleContractModel, otherwise the CSV column order will map incorrectly.
    public int Id { get; set; }
    public string CompanyName { get; set; }
    public string ProjectName { get; set; }
    public string SubstanceName { get; set; }
    public string ContractType { get; set; }
    public string ContractWeekday { get; set; }
    public string SearchPeriod { get; set; }
    public DateTime ContractStartDate { get; set; }
    public int SearchVersion { get; set; }
    public bool IsActive { get; set; }
    public string SearchString { get; set; }
}
