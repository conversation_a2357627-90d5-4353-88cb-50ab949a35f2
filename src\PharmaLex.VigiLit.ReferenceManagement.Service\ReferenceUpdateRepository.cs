﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class ReferenceUpdateRepository : TrackingGenericRepository<ReferenceUpdate>, IReferenceUpdateRepository, IImportingReferenceUpdateRepository
{
    private readonly IMapper _mapper;

    public ReferenceUpdateRepository(PlxDbContext context, IUserContext userContext, IMapper mapper)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<ReferenceUpdate?> GetById(int id)
    {
        return await context.Set<ReferenceUpdate>()
            .Where(e => e.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<ReferenceUpdateModel?> GetForClassification(int referenceId, int substanceId)
    {
        var query = context.Set<ReferenceUpdate>()
            .Where(e => e.ReferenceId == referenceId && e.SubstanceId == substanceId)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceUpdateModel>(query).FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<ReferenceUpdate>> GetUpdatesByReferenceId(int referenceId)
    {
        // Used during Import to update-the-updates
        return await context.Set<ReferenceUpdate>()
            .Where(e => e.ReferenceId == referenceId)
            .ToListAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}
