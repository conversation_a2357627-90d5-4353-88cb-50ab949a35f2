﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Entities;
using PharmaLex.VigiLit.DataExtraction.Entities.Enums;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Client;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;

public class CallbackHandlerTests
{
    private readonly Mock<IMdeQueueItemRepository> _mockRepository = new();
    private readonly Mock<ILogger<CallbackHandler>> _mockLogger = new();
    private readonly Mock<IImportManagementClient> _mockImportManagementClient = new();
    private readonly Mock<IQualityControlChecker> _mockQualityControlChecker = new();
    private readonly Mock<IExtractDataFileDownload> _mockExtractDataFileDownload = new();

    private readonly ICallbackHandler _callbackHandler;

    public CallbackHandlerTests()
    {
        _callbackHandler = new CallbackHandler(_mockLogger.Object, _mockImportManagementClient.Object, _mockQualityControlChecker.Object, _mockExtractDataFileDownload.Object, _mockRepository.Object);
    }

    [Fact]
    public async Task Given_GetDocumentStream_Returns_Stream_When_CorrelationId_is_found()
    {
        // Arrange
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();

        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>()))
                .Returns(Task.FromResult(queueItem));

        _mockExtractDataFileDownload.Setup(x => x.DownloadImportedFile(It.IsAny<Guid>(), It.IsAny<string>()))
                .Returns(Task.FromResult(GetDownLoadFile()));

        // Act
        var result = await _callbackHandler.GetDocumentStream(Guid.Empty);

        // Assert
        _mockExtractDataFileDownload.Verify(x => x.DownloadImportedFile(
            It.Is<Guid>(g => queueItem != null && g.Equals(queueItem.BatchId)), It.Is<string>(s => queueItem != null && s == queueItem.Filename)), Times.Once());

        Assert.True(result.Length > 0);
    }

    [Fact]
    public async Task Given_GetDocumentStream_When_CorrelationId_is_not_found_Then_exception_is_thrown()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult((MdeQueueItem?)null));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
        {
            await _callbackHandler.GetDocumentStream(Guid.Empty);
        });
    }

    [Fact]
    public async Task Given_success_When_CorrelationId_is_found_Then_status_is_set_to_completed()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);

        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        Assert.Equal(MdeQueueItemStatus.Completed, queueItem?.Status);
        _mockRepository.Verify(x => x.SaveChangesAsync());
    }

    [Fact]
    public async Task Given_success_When_CorrelationId_is_not_found_Then_exception_is_thrown()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult((MdeQueueItem?)null));

        // Act & Assert

        await Assert.ThrowsAsync<ArgumentException>(async () =>
        {
            await _callbackHandler.Success(Guid.Empty, extractedMetaData);
        });
    }

    [Fact]
    public async Task Given_success_When_QC_passes_then_reference_is_sent()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));
    }

    [Fact]
    public async Task Given_success_When_QC_fails_then_failed_extraction_is_sent()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(false);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<ManualCorrectionCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));
    }

    [Fact]
    public async Task Given_success_for_two_extractions_When_QC_passes_on_both_then_two_references_are_sent()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithTwoReferences();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));

        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 2" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));
    }

    [Fact]
    public async Task Given_success_for_two_extractions_When_QC_fails_on_both_then_two_failed_extractions_are_sent()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithTwoReferences();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(false);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<ManualCorrectionCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));

        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<ManualCorrectionCommand>(
                c => c.Reference.Title == "TITLE 2" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));
    }

    [Fact]
    public async Task Given_success_for_two_extractions_When_QC_passes_on_one_only_then_one_references_and_one_fail_extraction_is_sent()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithTwoReferences();

        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(
            It.Is<ExtractedReference>(x => x.Title.Value == "TITLE 1"))).Returns(true);

        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(
            It.Is<ExtractedReference>(x => x.Title.Value == "TITLE 2"))).Returns(false);

        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));

        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<ManualCorrectionCommand>(
                c => c.Reference.Title == "TITLE 2" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024)));
    }

    [Fact]
    public async Task Given_error_When_raised_by_PhlexVision_then_error_is_logged()
    {
        // Arrange
        var correlationId = new Guid("9ad62d98-d69b-41fd-b9d3-afbb3770de63");
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult(queueItem));

        // Act
        await _callbackHandler.Error(correlationId);

        // Assert
        Assert.Equal(MdeQueueItemStatus.Errored, queueItem?.Status);
        _mockRepository.Verify(x => x.SaveChangesAsync());
    }

    [Fact]
    public async Task Given_error_When_raised_by_PhlexVision_and_no_correlation_match_then_exception_is_thrown()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult((MdeQueueItem?)null));

        // Act & Assert

        await Assert.ThrowsAsync<ArgumentException>(async () =>
        {
            await _callbackHandler.Error(Guid.Empty);
        });
    }

    [Fact]
    public async Task Given_success_on_file_source_When_QC_passes_then_reference_is_sent_with_file_as_source()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        queueItem!.Source = Source.File;

        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult((MdeQueueItem?)queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024 &&
                     c.Reference.SourceSystem == (int)SourceSystem.File)));
    }


    [Fact]
    public async Task Given_success_on_web_source_When_QC_passes_then_reference_is_sent_with_web_as_source()
    {
        // Arrange
        var extractedMetaData = CallbackHandlerTestsHelper.GetMetadataWithOneReference();
        _mockQualityControlChecker.Setup(x => x.DoesPassQualityControl(It.IsAny<ExtractedReference>())).Returns(true);
        var queueItem = CallbackHandlerTestsHelper.GetMdeQueueItem();
        queueItem!.Source = Source.Web;

        _mockRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).Returns(Task.FromResult((MdeQueueItem?)queueItem));

        // Act
        await _callbackHandler.Success(Guid.Empty, extractedMetaData);

        // Assert
        _mockImportManagementClient.Verify(x =>
            x.Send(It.Is<EnqueueReferenceCommand>(
                c => c.Reference.Title == "TITLE 1" &&
                     c.Reference.Doi == "10.24950/rspmi.2566" &&
                     c.Reference.FullPagination == "145-148" &&
                     c.Reference.PublicationYear == 2024 &&
                     c.Reference.SourceSystem == (int)SourceSystem.Web)));
    }

    private static DownloadFile GetDownLoadFile()
    {
        return new DownloadFile
        {
            FileName = "filename.pdf",
            ContentType = "application/json",
            Bytes = "Test stream"u8.ToArray()
        };
    }
}
