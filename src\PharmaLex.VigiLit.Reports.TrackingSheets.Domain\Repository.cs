using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;
using System.Globalization;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class Repository : TrackingGenericRepository<TrackingSheet>, IRepository
{
    public Repository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public Task<TrackingSheet> GetById(int id)
    {
        return context.Set<TrackingSheet>()
            .Where(ts => ts.Id == id)
            .FirstAsync();
    }

    public async Task<IEnumerable<TrackingSheet>> GetByCompanyAndYear(int companyId, int year)
    {
        return await context.Set<TrackingSheet>()
            .Where(ts => ts.CompanyId == companyId && ts.Year == year)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<TrackingSheetPdfModel> GetTrackingSheetPdfModel(Company company, int year, int week)
    {
        return new TrackingSheetPdfModel()
        {
            CompanyName = company.Name,
            Week = week,
            Year = year,
            Imports = await GetImports(company, year, week)
        };
    }

    public async Task<bool> Exists(int companyId, int year, int week)
    {
        return await context.Set<TrackingSheet>()
            .AnyAsync(ts => ts.CompanyId == companyId && ts.Year == year && ts.Week == week);
    }

    /// <summary>
    /// Gets the imports for the tracking sheet pdf content.
    /// </summary>
    private async Task<List<TrackingSheetPdfImportModel>> GetImports(Company company, int year, int week)
    {
        List<TrackingSheetPdfImportModel> imports = new();

        var results = await GetStoredProcResults(company, year, week);

        ProcessResults(imports, results);

        return imports;
    }

    private async Task<List<TrackingSheetStoredProcResult>> GetStoredProcResults(Company company, int year, int week)
    {
        var startDate = ISOWeek.ToDateTime(year, week, DayOfWeek.Monday);
        var endDate = ISOWeek.ToDateTime(year, week, DayOfWeek.Sunday);

        var results = await context.Set<TrackingSheetStoredProcResult>()
            .FromSqlRaw("exec spTrackingSheetReport @CompanyId, @StartDate, @EndDate",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate))
            .ToListAsync();

        return results;
    }

    /// <summary>
    /// Processes the results from the tracking sheet stored proc.
    /// The results can include multiple imports.
    /// </summary>
    private static void ProcessResults(List<TrackingSheetPdfImportModel> imports, List<TrackingSheetStoredProcResult> results)
    {
        int processingImportId = 0;

        foreach (var result in results)
        {
            if (result.ImportId != processingImportId)
            {
                imports.Add(new TrackingSheetPdfImportModel()
                {
                    Name = string.Format("{0}: {1}", ((ImportType)result.ImportType).GetDescription(), result.ImportDate.ToString("d MMM yyyy")),
                    Rows = new List<TrackingSheetPdfImportRowModel>()
                });

                processingImportId = result.ImportId;
            }

            imports[^1].Rows.Add(new()
            {
                SubstanceName = result.SubstanceName,
                ContractVersion = result.ContractVersion,
                SearchInterval = result.SearchInterval,
                Hits = result.Hits
            });
        }
    }
}
