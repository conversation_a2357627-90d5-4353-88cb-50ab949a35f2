﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;

namespace PharmaLex.VigiLit.Domain.Unit.Tests.Models;
public class ContractTests
{
    private readonly Substance _stubbedSubstance = new FakeSubstance(1, "Substance", "type");
    private readonly Project _stubbedProject = new Project("Project", 42);

    [Fact]
    public void Given_contract_version_with_journals_When_GetContractTypeDescription_is_called_Then_NA_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>
                    {
                        new ()
                        {
                            Journal = new Journal()
                        }
                    }
                }
            ]);


        // Act
        var result = contract.GetContractTypeDescription();

        // Assert
        Assert.Equal("n/a", result);
    }

    [Fact]
    public void Given_adhoc_contract_version_without_journals_When_GetContractTypeDescription_is_called_Then_AdHoc_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>(),
                }
            ]);

        // Act
        var result = contract.GetContractTypeDescription();

        // Assert
        Assert.Equal("Ad-Hoc Only", result);
    }

    [Fact]
    public void Given_scheduled_contract_version_without_journals_When_GetContractTypeDescription_is_called_Then_Scheduled_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.Scheduled, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>(),
                }
            ]);

        // Act
        var result = contract.GetContractTypeDescription();

        // Assert
        Assert.Equal("Scheduled", result);
    }

    [Fact]
    public void Given_contract_version_with_journals_When_GetSearchPeriodDescription_is_called_Then_NA_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>
                    {
                        new ()
                        {
                            Journal = new Journal()
                        }
                    }
                }
            ]);


        // Act
        var result = contract.GetSearchPeriodDescription();

        // Assert
        Assert.Equal("n/a", result);
    }

    [Fact]
    public void Given_Unlimited_contract_version_without_journals_When_GetSearchPeriodDescription_is_called_Then_Unlimited_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>(),
                }
            ]);

        // Act
        var result = contract.GetSearchPeriodDescription();

        // Assert
        Assert.Equal("Unlimited", result);
    }

    [Fact]
    public void Given_contract_version_without_journals_with_42_days_search_period_When_GetSearchPeriodDescription_is_called_Then_42_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.Scheduled, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>(),
                    SearchPeriodDays = 42,
                }
            ]);

        // Act
        var result = contract.GetSearchPeriodDescription();

        // Assert
        Assert.Equal("42 days", result);
    }

    [Fact]
    public void Given_contract_version_with_journals_When_GetWeekDayDescription_is_called_Then_NA_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>
                    {
                        new ()
                        {
                            Journal = new Journal()
                        }
                    },
                    ContractWeekday = ContractWeekday.Friday,
                }
            ]);


        // Act
        var result = contract.GetWeekDayDescription();

        // Assert
        Assert.Equal("n/a", result);
    }

    [Fact]
    public void Given_friday_contract_version_without_journals_When_GetWeekDayDescription_is_called_Then_Friday_is_returned()
    {
        // Arrange
        var contract = new FakeContract(1, _stubbedSubstance, _stubbedProject,
            DateTime.Now,
            [
                new FakeContractVersion(1, ContractType.AdHocOnly, ContractVersionStatus.Approved)
                {
                    ContractVersionJournals = new List<ContractVersionJournal>(),
                    ContractWeekday = ContractWeekday.Friday,
                }
            ]);

        // Act
        var result = contract.GetWeekDayDescription();

        // Assert
        Assert.Equal("Friday", result);
    }
}
