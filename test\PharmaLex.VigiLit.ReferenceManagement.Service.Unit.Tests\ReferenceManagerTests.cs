﻿using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests;

public class ReferenceManagerTests
{
    private readonly ReferenceManager _referenceManager;
    private readonly Mock<IImportingReferenceUpdateRepository> _mockImportReferenceUpdateRepository = new();

    public ReferenceManagerTests()
    {
        _referenceManager = new ReferenceManager(new NullLoggerFactory(), _mockImportReferenceUpdateRepository.Object);
    }

    [Fact]
    public void UpdateImportContractWithReference_ImportContract_Contracts_Updated()
    {
        // Arrange
        var reference = new Reference();
        var importContract = new ImportContract
        {
            Contract = new Contract(99, 100)
        };

        // Act
        _referenceManager.UpdateImportContractWithReference(importContract, reference);

        // Assert
        Assert.Single(importContract.ImportContractReferenceClassifications);
        Assert.Equal(ICRCType.New, importContract.ImportContractReferenceClassifications.First().ICRCType);
        Assert.Equal(1, importContract.NewReferencesCount);
    }

    [Fact]
    public void UpdateImportContractWithReference_Adds_New_Contract_To_Contracts()
    {
        // Arrange
        var reference = new Reference();
        var importContract = new ImportContract
        {
            Contract = new Contract(99, 100)
        };
        importContract.ImportContractReferenceClassifications.Add(new ImportContractReferenceClassification());

        // Act
        _referenceManager.UpdateImportContractWithReference(importContract, reference);

        // Assert
        Assert.Equal(2, importContract.ImportContractReferenceClassifications.Count);
    }

    [Fact]
    public void AddReferenceUpdate_Adds_UpdateReference_To_Repository()
    {
        // Arrange
        var substanceId = 10;
        var referenceId = 200;
        var importContractId = 3000;
        var reference = new Reference();
        var importContract = new FakeImportContract(importContractId)
        {
            Contract = new Contract(substanceId, 100),
        };
        var referenceIdentifiers = new ReferenceIdentifiers(referenceId, "Test", DateTime.Now);

        // Act
        _referenceManager.AddReferenceUpdate(importContract, referenceIdentifiers, reference);

        // Assert
        _mockImportReferenceUpdateRepository.Verify(x => x.Add(
            It.Is<ReferenceUpdate>(y => y.ReferenceId == referenceId && y.SubstanceId == substanceId && y.ImportContractId == importContractId)));
        Assert.Equal(1, importContract.UpdatesCount);
    }
}