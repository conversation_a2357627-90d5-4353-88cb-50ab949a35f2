using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Apogepha.Domain;

public class ApogephaClientReportStoredProcResultConfiguration : EntityBaseMap<ApogephaClientReportResult>
{
    public override void Configure(EntityTypeBuilder<ApogephaClientReportResult> builder)
    {
        builder.HasNoKey().ToView(null);
    }
}