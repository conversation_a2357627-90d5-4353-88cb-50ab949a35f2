
<!--
     File isotech.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     Entity names in this file are derived from files carrying the
     following notice:

     (C) International Organization for Standardization 1991
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.

-->

<!ENTITY acd              "&#x0223F;" ><!--ac current -->
<!ENTITY aleph            "&#x02135;" ><!--/aleph aleph, Hebrew -->
<!ENTITY And              "&#x02A53;" ><!--dbl logical and -->
<!ENTITY and              "&#x02227;" ><!--/wedge /land B: logical and -->
<!ENTITY andand           "&#x02A55;" ><!--two logical and -->
<!ENTITY andd             "&#x02A5C;" ><!--and, horizontal dash -->
<!ENTITY andslope         "&#x02A58;" ><!--sloping large and -->
<!ENTITY andv             "&#x02A5A;" ><!--and with middle stem -->
<!ENTITY angrt            "&#x0221F;" ><!--right (90 degree) angle -->
<!ENTITY angsph           "&#x02222;" ><!--/sphericalangle angle-spherical -->
<!ENTITY angst            "&#x0212B;" ><!--Angstrom capital A, ring -->
<!ENTITY ap               "&#x02248;" ><!--/approx R: approximate -->
<!ENTITY apacir           "&#x02A6F;" ><!--approximate, circumflex accent -->
<!ENTITY awconint         "&#x02233;" ><!--contour integral, anti-clockwise -->
<!ENTITY awint            "&#x02A11;" ><!--anti clock-wise integration -->
<!ENTITY becaus           "&#x02235;" ><!--/because R: because -->
<!ENTITY bernou           "&#x0212C;" ><!--Bernoulli function (script capital B)  -->
<!ENTITY bne              "&#x0003D;&#x020E5;" ><!--reverse not equal -->
<!ENTITY bnequiv          "&#x02261;&#x020E5;" ><!--reverse not equivalent -->
<!ENTITY bNot             "&#x02AED;" ><!--reverse not with two horizontal strokes -->
<!ENTITY bnot             "&#x02310;" ><!--reverse not -->
<!ENTITY bottom           "&#x022A5;" ><!--/bot bottom -->
<!ENTITY cap              "&#x02229;" ><!--/cap B: intersection -->
<!ENTITY Cconint          "&#x02230;" ><!--triple contour integral operator -->
<!ENTITY cirfnint         "&#x02A10;" ><!--circulation function -->
<!ENTITY compfn           "&#x02218;" ><!--/circ B: composite function (small circle) -->
<!ENTITY cong             "&#x02245;" ><!--/cong R: congruent with -->
<!ENTITY Conint           "&#x0222F;" ><!--double contour integral operator -->
<!ENTITY conint           "&#x0222E;" ><!--/oint L: contour integral operator -->
<!ENTITY ctdot            "&#x022EF;" ><!--/cdots, three dots, centered -->
<!ENTITY cup              "&#x0222A;" ><!--/cup B: union or logical sum -->
<!ENTITY cwconint         "&#x02232;" ><!--contour integral, clockwise -->
<!ENTITY cwint            "&#x02231;" ><!--clockwise integral -->
<!ENTITY cylcty           "&#x0232D;" ><!--cylindricity -->
<!ENTITY disin            "&#x022F2;" ><!--set membership, long horizontal stroke -->
<!ENTITY Dot              "&#x000A8;" ><!--dieresis or umlaut mark -->
<!ENTITY DotDot           "&#x020DC;" ><!--four dots above -->
<!ENTITY dsol             "&#x029F6;" ><!--solidus, bar above -->
<!ENTITY dtdot            "&#x022F1;" ><!--/ddots, three dots, descending -->
<!ENTITY dwangle          "&#x029A6;" ><!--large downward pointing angle -->
<!ENTITY elinters         "&#x0FFFD;" ><!--electrical intersection -->
<!ENTITY epar             "&#x022D5;" ><!--parallel, equal; equal or parallel -->
<!ENTITY eparsl           "&#x029E3;" ><!--parallel, slanted, equal; homothetically congruent to -->
<!ENTITY equiv            "&#x02261;" ><!--/equiv R: identical with -->
<!ENTITY eqvparsl         "&#x029E5;" ><!--equivalent, equal; congruent and parallel -->
<!ENTITY exist            "&#x02203;" ><!--/exists at least one exists -->
<!ENTITY fltns            "&#x025B1;" ><!--flatness -->
<!ENTITY fnof             "&#x00192;" ><!--function of (italic small f) -->
<!ENTITY forall           "&#x02200;" ><!--/forall for all -->
<!ENTITY fpartint         "&#x02A0D;" ><!--finite part integral -->
<!ENTITY ge               "&#x02265;" ><!--/geq /ge R: greater-than-or-equal -->
<!ENTITY hamilt           "&#x0210B;" ><!--Hamiltonian (script capital H)  -->
<!ENTITY iff              "&#x021D4;" ><!--/iff if and only if  -->
<!ENTITY iinfin           "&#x029DC;" ><!--infinity sign, incomplete -->
<!ENTITY imped            "&#x001B5;" ><!--impedance -->
<!ENTITY infin            "&#x0221E;" ><!--/infty infinity -->
<!ENTITY infintie         "&#x029DD;" ><!--tie, infinity -->
<!ENTITY Int              "&#x0222C;" ><!--double integral operator -->
<!ENTITY int              "&#x0222B;" ><!--/int L: integral operator -->
<!ENTITY intlarhk         "&#x02A17;" ><!--integral, left arrow with hook -->
<!ENTITY isin             "&#x02208;" ><!--/in R: set membership  -->
<!ENTITY isindot          "&#x022F5;" ><!--set membership, dot above -->
<!ENTITY isinE            "&#x022F9;" ><!--set membership, two horizontal strokes -->
<!ENTITY isins            "&#x022F4;" ><!--set membership, vertical bar on horizontal stroke -->
<!ENTITY isinsv           "&#x022F3;" ><!--large set membership, vertical bar on horizontal stroke -->
<!ENTITY isinv            "&#x02208;" ><!--set membership, variant -->
<!ENTITY lagran           "&#x02112;" ><!--Lagrangian (script capital L)  -->
<!ENTITY Lang             "&#x0300A;" ><!--left angle bracket, double -->
<!ENTITY lang             "&#x02329;" ><!--/langle O: left angle bracket -->
<!ENTITY lArr             "&#x021D0;" ><!--/Leftarrow A: is implied by -->
<!ENTITY lbbrk            "&#x03014;" ><!--left broken bracket -->
<!ENTITY le               "&#x02264;" ><!--/leq /le R: less-than-or-equal -->
<!ENTITY loang            "&#x03018;" ><!--left open angular bracket -->
<!ENTITY lobrk            "&#x0301A;" ><!--left open bracket -->
<!ENTITY lopar            "&#x02985;" ><!--left open parenthesis -->
<!ENTITY lowast           "&#x02217;" ><!--low asterisk -->
<!ENTITY minus            "&#x02212;" ><!--B: minus sign -->
<!ENTITY mnplus           "&#x02213;" ><!--/mp B: minus-or-plus sign -->
<!ENTITY nabla            "&#x02207;" ><!--/nabla del, Hamilton operator -->
<!ENTITY ne               "&#x02260;" ><!--/ne /neq R: not equal -->
<!ENTITY nedot            "&#x02250;&#x00338;" ><!--not equal, dot -->
<!ENTITY nhpar            "&#x02AF2;" ><!--not, horizontal, parallel -->
<!ENTITY ni               "&#x0220B;" ><!--/ni /owns R: contains -->
<!ENTITY nis              "&#x022FC;" ><!--contains, vertical bar on horizontal stroke -->
<!ENTITY nisd             "&#x022FA;" ><!--contains, long horizontal stroke -->
<!ENTITY niv              "&#x0220B;" ><!--contains, variant -->
<!ENTITY Not              "&#x02AEC;" ><!--not with two horizontal strokes -->
<!ENTITY notin            "&#x02209;" ><!--/notin N: negated set membership -->
<!ENTITY notindot         "&#x022F5;&#x00338;" ><!--negated set membership, dot above -->
<!ENTITY notinE           "&#x022F9;&#x00338;" ><!--negated set membership, two horizontal strokes -->
<!ENTITY notinva          "&#x02209;" ><!--negated set membership, variant -->
<!ENTITY notinvb          "&#x022F7;" ><!--negated set membership, variant -->
<!ENTITY notinvc          "&#x022F6;" ><!--negated set membership, variant -->
<!ENTITY notni            "&#x0220C;" ><!--negated contains -->
<!ENTITY notniva          "&#x0220C;" ><!--negated contains, variant -->
<!ENTITY notnivb          "&#x022FE;" ><!--contains, variant -->
<!ENTITY notnivc          "&#x022FD;" ><!--contains, variant -->
<!ENTITY nparsl           "&#x02AFD;&#x020E5;" ><!--not parallel, slanted -->
<!ENTITY npart            "&#x02202;&#x00338;" ><!--not partial differential -->
<!ENTITY npolint          "&#x02A14;" ><!--line integration, not including the pole -->
<!ENTITY nvinfin          "&#x029DE;" ><!--not, vert, infinity -->
<!ENTITY olcross          "&#x029BB;" ><!--circle, cross -->
<!ENTITY Or               "&#x02A54;" ><!--dbl logical or -->
<!ENTITY or               "&#x02228;" ><!--/vee /lor B: logical or -->
<!ENTITY ord              "&#x02A5D;" ><!--or, horizontal dash -->
<!ENTITY order            "&#x02134;" ><!--order of (script small o)  -->
<!ENTITY oror             "&#x02A56;" ><!--two logical or -->
<!ENTITY orslope          "&#x02A57;" ><!--sloping large or -->
<!ENTITY orv              "&#x02A5B;" ><!--or with middle stem -->
<!ENTITY par              "&#x02225;" ><!--/parallel R: parallel -->
<!ENTITY parsl            "&#x02AFD;" ><!--parallel, slanted -->
<!ENTITY part             "&#x02202;" ><!--/partial partial differential -->
<!ENTITY permil           "&#x02030;" ><!--per thousand -->
<!ENTITY perp             "&#x022A5;" ><!--/perp R: perpendicular -->
<!ENTITY pertenk          "&#x02031;" ><!--per 10 thousand -->
<!ENTITY phmmat           "&#x02133;" ><!--physics M-matrix (script capital M)  -->
<!ENTITY pointint         "&#x02A15;" ><!--integral around a point operator -->
<!ENTITY Prime            "&#x02033;" ><!--double prime or second -->
<!ENTITY prime            "&#x02032;" ><!--/prime prime or minute -->
<!ENTITY profalar         "&#x0232E;" ><!--all-around profile -->
<!ENTITY profline         "&#x02312;" ><!--profile of a line -->
<!ENTITY profsurf         "&#x02313;" ><!--profile of a surface -->
<!ENTITY prop             "&#x0221D;" ><!--/propto R: is proportional to -->
<!ENTITY qint             "&#x02A0C;" ><!--/iiiint quadruple integral operator -->
<!ENTITY qprime           "&#x02057;" ><!--quadruple prime -->
<!ENTITY quatint          "&#x02A16;" ><!--quaternion integral operator -->
<!ENTITY radic            "&#x0221A;" ><!--/surd radical -->
<!ENTITY Rang             "&#x0300B;" ><!--right angle bracket, double -->
<!ENTITY rang             "&#x0232A;" ><!--/rangle C: right angle bracket -->
<!ENTITY rArr             "&#x021D2;" ><!--/Rightarrow A: implies -->
<!ENTITY rbbrk            "&#x03015;" ><!--right broken bracket -->
<!ENTITY roang            "&#x03019;" ><!--right open angular bracket -->
<!ENTITY robrk            "&#x0301B;" ><!--right open bracket -->
<!ENTITY ropar            "&#x02986;" ><!--right open parenthesis -->
<!ENTITY rppolint         "&#x02A12;" ><!--line integration, rectangular path around pole -->
<!ENTITY scpolint         "&#x02A13;" ><!--line integration, semi-circular path around pole -->
<!ENTITY sim              "&#x0223C;" ><!--/sim R: similar -->
<!ENTITY simdot           "&#x02A6A;" ><!--similar, dot -->
<!ENTITY sime             "&#x02243;" ><!--/simeq R: similar, equals -->
<!ENTITY smeparsl         "&#x029E4;" ><!--similar, parallel, slanted, equal -->
<!ENTITY square           "&#x025A1;" ><!--/square, square -->
<!ENTITY squarf           "&#x025AA;" ><!--/blacksquare, square, filled  -->
<!ENTITY strns            "&#x000AF;" ><!--straightness -->
<!ENTITY sub              "&#x02282;" ><!--/subset R: subset or is implied by -->
<!ENTITY sube             "&#x02286;" ><!--/subseteq R: subset, equals -->
<!ENTITY sup              "&#x02283;" ><!--/supset R: superset or implies -->
<!ENTITY supe             "&#x02287;" ><!--/supseteq R: superset, equals -->
<!ENTITY tdot             "&#x020DB;" ><!--three dots above -->
<!ENTITY there4           "&#x02234;" ><!--/therefore R: therefore -->
<!ENTITY tint             "&#x0222D;" ><!--/iiint triple integral operator -->
<!ENTITY top              "&#x022A4;" ><!--/top top -->
<!ENTITY topbot           "&#x02336;" ><!--top and bottom -->
<!ENTITY topcir           "&#x02AF1;" ><!--top, circle below -->
<!ENTITY tprime           "&#x02034;" ><!--triple prime -->
<!ENTITY utdot            "&#x022F0;" ><!--three dots, ascending -->
<!ENTITY uwangle          "&#x029A7;" ><!--large upward pointing angle -->
<!ENTITY vangrt           "&#x0299C;" ><!--right angle, variant -->
<!ENTITY veeeq            "&#x0225A;" ><!--logical or, equals -->
<!ENTITY Verbar           "&#x02016;" ><!--/Vert dbl vertical bar -->
<!ENTITY wedgeq           "&#x02259;" ><!--/wedgeq R: corresponds to (wedge, equals) -->
<!ENTITY xnis             "&#x022FB;" ><!--large contains, vertical bar on horizontal stroke -->
