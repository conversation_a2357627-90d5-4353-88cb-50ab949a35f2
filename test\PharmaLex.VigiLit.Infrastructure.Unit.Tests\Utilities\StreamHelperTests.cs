﻿using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Test.Framework;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Utilities;

public class StreamHelperTests
{
    public readonly IStreamHelper _streamHelper;

    public StreamHelperTests()
    {
        _streamHelper = new StreamHelper();
    }

    [Fact]
    public void GetBytesFromFileStreams_When_No_Data_Returns_Zero_Length_Array()
    {
        // Arrange
        var originalStreams = new Dictionary<string, Stream>();

        // Act
        var zippedStreams = _streamHelper.GetZipOfBytesFromFileStreams(originalStreams);

        // Assert
        Assert.Empty(zippedStreams);
    }

    [Fact]
    public void GetBytesFromFileStreams_When_1_Pdf_Zipped_Data_Matches_Original()
    {
        // Arrange        
        var originalStreams = new Dictionary<string, Stream>()
        {
            { "filename.pdf", ResourceUtility.GetPdfStream() }
        };

        // Act
        var zippedStreams = _streamHelper.GetZipOfBytesFromFileStreams(originalStreams);

        // Assert
        var unzippedStreams = _streamHelper.UnzipBytesToDictionaryOfStreams(zippedStreams);

        Assert.True(_streamHelper.AreDictionariesTheSame(originalStreams, unzippedStreams));
    }

    [Fact]
    public void GetBytesFromFileStreams_When_3_Files_Zipped_Data_Matches_Originals()
    {
        // Arrange
        var streamHelper = new StreamHelper();
        var originalStreams = new Dictionary<string, Stream>()
        {
            { "filename.pdf", ResourceUtility.GetPdfStream() },
            { "filename.xls", ResourceUtility.GetXlsStream() },
            { "filename.docx", ResourceUtility.GetDocXStream() }
        };

        // Act
        var zippedStreams = streamHelper.GetZipOfBytesFromFileStreams(originalStreams);

        // Assert
        var unzippedStreams = _streamHelper.UnzipBytesToDictionaryOfStreams(zippedStreams);

        Assert.True(_streamHelper.AreDictionariesTheSame(originalStreams, unzippedStreams));
    }

    [Fact]
    public void DictionariesAreTheSame_Returns_True_When_Dictionaries_Are_Same()
    {
        // Arrange
        var dictionary1 = new Dictionary<string, Stream>
        {
            { "filename.pdf", ResourceUtility.GetPdfStream() }
        };
        var memoryStream = new MemoryStream();
        ResourceUtility.GetPdfStream().CopyTo(memoryStream);

        var dictionary2 = new Dictionary<string, MemoryStream>
        {
            { "filename.pdf", memoryStream }
        };

        // Assert
        Assert.True(_streamHelper.AreDictionariesTheSame(dictionary1, dictionary2));
    }

    [Fact]
    public void DictionariesAreTheSame_Returns_False_When_Dictionaries_Are_Different()
    {
        // Arrange
        var dictionary1 = new Dictionary<string, Stream>
        {
            { "filename.pdf", ResourceUtility.GetPdfStream() }
        };
        var memoryStream = new MemoryStream();
        ResourceUtility.GetDocXStream().CopyTo(memoryStream);

        var dictionary2 = new Dictionary<string, MemoryStream>
        {
            { "filename.docx", memoryStream }
        };

        // Assert
        Assert.False(_streamHelper.AreDictionariesTheSame(dictionary1, dictionary2));
    }

    [Fact]
    public void DictionariesAreTheSame_Returns_True_When_Dictionaries_Are_Empty()
    {
        // Arrange
        var dictionary1 = new Dictionary<string, Stream>();
        var dictionary2 = new Dictionary<string, MemoryStream>();

        // Assert
        Assert.True(_streamHelper.AreDictionariesTheSame(dictionary1, dictionary2));
    }

    [Fact]
    public void UnzipBytesToDictionaryOfStreams_ReturnsDictionaryOfStreams()
    {
        // Arrange        
        var originalStreams = new Dictionary<string, Stream>()
        {
            { "filename.pdf", ResourceUtility.GetPdfStream() }
        };

        // Act
        var zippedStreams = _streamHelper.GetZipOfBytesFromFileStreams(originalStreams);
        var unZippedStreams = _streamHelper.UnzipBytesToDictionaryOfStreams(zippedStreams);
        unZippedStreams.TryGetValue("filename.pdf", out var value);

        // Assert
        Assert.True(unZippedStreams.ContainsKey("filename.pdf"));
        Assert.NotNull(value);
        Assert.True(_streamHelper.AreStreamsIdentical(ResourceUtility.GetPdfStream(), value));
    }
}
