﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexesPhase4 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UserSubstances_UserId",
                table: "UserSubstances");

            migrationBuilder.DropIndex(
                name: "IX_References_Doi",
                table: "References");

            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ContractId",
                table: "ImportContracts");

            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ImportId_ContractId_PubMedModificationDate",
                table: "ImportContracts");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_EmailQueued_EmailSent",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_UserSubstances_UserId_SubstanceId",
                table: "UserSubstances",
                columns: new[] { "UserId", "SubstanceId" });

            migrationBuilder.CreateIndex(
                name: "IX_References_Doi_Id",
                table: "References",
                columns: new[] { "Doi", "Id" });

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_Id_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications",
                columns: new[] { "Id", "ReferenceState", "PreAssessorId" });

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_ReferenceId_SubstanceId_Id_ClassifierId",
                table: "ReferenceClassifications",
                columns: new[] { "ReferenceId", "SubstanceId", "Id", "ClassifierId" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ContractId_PubMedModificationDate",
                table: "ImportContracts",
                columns: new[] { "ContractId", "PubMedModificationDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts",
                column: "ImportId");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_EmailSent_EmailReason_EmailQueued",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "EmailSent", "EmailReason", "EmailQueued" });

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive",
                table: "Contracts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Companies_IsActive",
                table: "Companies",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ClassificationCategories_Id_PushServiceRelevant",
                table: "ClassificationCategories",
                columns: new[] { "Id", "PushServiceRelevant" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UserSubstances_UserId_SubstanceId",
                table: "UserSubstances");

            migrationBuilder.DropIndex(
                name: "IX_References_Doi_Id",
                table: "References");

            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_Id_ReferenceState_PreAssessorId",
                table: "ReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_ReferenceId_SubstanceId_Id_ClassifierId",
                table: "ReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ContractId_PubMedModificationDate",
                table: "ImportContracts");

            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_EmailSent_EmailReason_EmailQueued",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive",
                table: "Contracts");

            migrationBuilder.DropIndex(
                name: "IX_Companies_IsActive",
                table: "Companies");

            migrationBuilder.DropIndex(
                name: "IX_ClassificationCategories_Id_PushServiceRelevant",
                table: "ClassificationCategories");

            migrationBuilder.CreateIndex(
                name: "IX_UserSubstances_UserId",
                table: "UserSubstances",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_References_Doi",
                table: "References",
                column: "Doi");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ContractId",
                table: "ImportContracts",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ImportId_ContractId_PubMedModificationDate",
                table: "ImportContracts",
                columns: new[] { "ImportId", "ContractId", "PubMedModificationDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_EmailQueued_EmailSent",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "EmailQueued", "EmailSent" });
        }
    }
}
