﻿BEGIN TRAN

	RAISERROR (N'Begin Turning System Versioning OFF for All Temporal Tables ...', 10, 1) WITH NOWAIT;

	DECLARE @schema sysname = N'dbo'
		, @sql nvarchar(max) = N'';

	SELECT @sql += N'ALTER TABLE ' + src + N' SET (SYSTEM_VERSIONING = OFF);'
	FROM (	SELECT src = QUOTENAME(SCHEMA_NAME(t.schema_id)) + N'.' + QUOTENAME(t.name)
			FROM sys.tables AS t
			INNER JOIN sys.tables AS h ON t.history_table_id = h.[object_id]
			WHERE t.temporal_type = 2
				AND t.[schema_id] = SCHEMA_ID(@schema)
			) AS x;

	EXEC sys.sp_executesql @sql;

	RAISERROR (N'Turning System Versioning OFF for All Temporal Tables ... DONE!', 10, 1) WITH NOWAIT;

	SET @sql = N'';

	RAISERROR (N'Begin Dropping All Foreign Keys Constraints ...', 10, 1) WITH NOWAIT;

	SELECT @sql += N'ALTER TABLE ' + [s].[TableName] + N' DROP CONSTRAINT ' + [ConstraintName] + N';'
	FROM (	SELECT [TableName] = QUOTENAME([t].[TABLE_SCHEMA]) + N'.' + QUOTENAME([t].[TABLE_NAME]) 
				, [ConstraintName] = [t].[CONSTRAINT_NAME]
			FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS [t]
			WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
				AND [t].[TABLE_SCHEMA] = @schema
		) as [s]

	EXEC sys.sp_executesql @sql;

	RAISERROR (N'Dropping All Foreign Keys Constraints ... DONE!', 10, 1) WITH NOWAIT;

	SET @sql = N'';

	RAISERROR (N'Begin Dropping All Tables for schema [%s] ...', 10, 1, @schema) WITH NOWAIT;

	SELECT @sql += N'DROP TABLE ' + [s].[TableName] + N';'
	FROM (	SELECT [TableName] = QUOTENAME([t].[TABLE_SCHEMA]) + N'.' + QUOTENAME([t].[TABLE_NAME]) 
			FROM INFORMATION_SCHEMA.TABLES [t]
			WHERE [t].[TABLE_SCHEMA] = @schema
		) as [s]

	EXEC sys.sp_executesql @sql;
	
	RAISERROR (N'Begin Dropping All Tables for schema [%s] ... DONE!', 10, 1, @schema) WITH NOWAIT;

COMMIT TRAN