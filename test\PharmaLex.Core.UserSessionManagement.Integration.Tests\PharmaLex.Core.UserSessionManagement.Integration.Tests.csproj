<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
	  <PackageReference Include="Moq" Version="4.20.72" />
	  <PackageReference Include="xunit" Version="2.9.3" />
	  <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests\PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.DomainHelpers\PharmaLex.VigiLit.Test.DomainHelpers.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
