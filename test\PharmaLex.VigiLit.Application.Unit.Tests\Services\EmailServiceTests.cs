﻿using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using SendGrid;
using System.Net;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class EmailServiceTests
{
    private readonly IEmailService _emailService;

    private readonly Mock<ICompanyRepository> _mockCompanyRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<ICompanyInterestRepository> _mockCompanyInterestRepository = new();
    private readonly Mock<IEmailRepository> _mockEmailRepository = new();
    private readonly Mock<IEmailLogService> _mockEmailLogService = new();
    private readonly Mock<IEmailSender> _mockEmailSender = new();
    private readonly Mock<ILogger<EmailService>> _mockLogger = new();
    private readonly Mock<IUrlRenderHelper> _urlRenderHelper = new();

    private readonly int _companyId = 100;
    private readonly int _referenceId = 200;
    private readonly int _referenceClassificationId = 300;
    private readonly int _emailId = 400;
    private readonly string _referenceSourceId = "12345";
    private readonly string _substanceName = "substance";
    private readonly string _countryName = "country";
    private readonly string _classificationCategoryName = "categoryName";
    private readonly string _minimalCriteriaY = "Y";
    private readonly string _referenceTitle = "Reference title";
    private readonly string _referenceAbstract = "Reference abstract";
    private readonly string _abstractText = "Reference abstract <a href=\"https://pubmed.ncbi.nlm.nih.gov/12345\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ";

    private static string MakeFamilyName(int id) => $"Familyname{id}";
    private static string MakeGivenName(int id) => $"Givenname{id}";
    private static string MakeUserEmail(int id) => $"email{id}@email.com";
    private static string MakeSubjectDate() => $"({DateTime.Now:d MMM yyyy})";

    public EmailServiceTests()
    {
        var fakeFactory = new FakeLoggerFactory<EmailService>(_mockLogger);

        _emailService = new EmailService(
                fakeFactory,
                _mockEmailSender.Object,
                _mockCompanyRepository.Object,
                _mockEmailLogService.Object,
                _mockCompanyInterestRepository.Object,
                _mockReferenceRepository.Object,
                _mockReferenceClassificationRepository.Object,
                _mockEmailRepository.Object,
                _urlRenderHelper.Object
        );
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_NewPotentialCaseEmail_To_Interested_Company()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompany(_referenceId, _referenceClassificationId, _companyId, EmailRelevantEventEmailStatusType.New));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(_abstractText);

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expected = SendGridTemplateModel(EmailReason.NewPotentialCase, _companyId, _referenceId, _referenceClassificationId, _substanceName, _countryName, _classificationCategoryName, _minimalCriteriaY, _referenceSourceId, _referenceTitle, _referenceAbstract);


        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_EmptyEmailSection_If_Company_Has_No_Interests()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(NoCompanyInterestsForCompany());

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expected = SendGridTemplateModelForNoClassifications(_companyId);

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_EmptyEmailSection_If_Email_Has_Been_Sent_Already()
    {
        // Arrange
        const int companyId = 100;
        const int referenceId = 200;
        const int referenceClassificationId = 300;
        const int emailId = 400;

        const string substanceName = "substance";
        const string countryName = "country";
        const string classificationCategoryName = "categoryName";

        const string referenceSourceId = "12345";
        const string referenceTitle = "Reference title";
        const string referenceAbstract = "Reference abstract";
        const string minimalCriteriaY = "Y";

        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(companyId))
            .ReturnsAsync(CompanyInterestsForCompany(referenceId, referenceClassificationId, companyId, EmailRelevantEventEmailStatusType.Sent));

        _mockReferenceRepository.Setup(x => x.GetForEmail(referenceId))
            .ReturnsAsync(GetSendGridTemplate(referenceAbstract, referenceId, referenceSourceId, referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(substanceName, countryName, classificationCategoryName, referenceClassificationId, minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(emailId))
            .ReturnsAsync(GetDailyReferenceEmail(emailId));

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expected = SendGridTemplateModelForNoClassifications(companyId);

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_NewPotentialCaseEmail_To_Interested_Company_For_SplitReference()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompanyIsSplitReference(_referenceId, _referenceClassificationId, _companyId));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(
            _abstractText);

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expected = SendGridTemplateModel(EmailReason.NewPotentialCase, _companyId, _referenceId, _referenceClassificationId, _substanceName, _countryName, _classificationCategoryName, _minimalCriteriaY, _referenceSourceId, _referenceTitle, _referenceAbstract);

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_New_Potential_Case_EmailSection_When_Existing_Classification_Changes_From_Non_Potential_Case_To_Potential_Case()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompanyNonPcToPotentialCase(_referenceId, _referenceClassificationId, _companyId));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(
            _abstractText);

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert       
        var expected = SendGridTemplateModel(EmailReason.NewPotentialCase, _companyId, _referenceId, _referenceClassificationId, _substanceName, _countryName, _classificationCategoryName, _minimalCriteriaY, _referenceSourceId, _referenceTitle, _referenceAbstract);

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_New_NonPotential_Case_EmailSection_When_Existing_Classification_Changes_From_Potential_Case_To_Non_Potential_Case()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompanyPotentialCaseChangeToNonPC(_referenceId, _referenceClassificationId, _companyId));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(
            _abstractText);

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expected = new DailyClassificationEmailSendGridTemplateModel
        {
            Subject = "1 VigiLit Classification " + MakeSubjectDate(),
            RecipientEmail = MakeUserEmail(_companyId),
            RecipientName = $"{MakeGivenName(_companyId)} {MakeFamilyName(_companyId)}",
            ChangedFromPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>
            {
                new DailyClassificationEmailSendGridTemplateModelRow(
                     0,
                     EmailReason.ChangedFromPotentialCase,
                     new ReferenceSendGridTemplateModel{
                        Id = _referenceId,
                        SourceId = _referenceSourceId,
                        Title = _referenceTitle,
                        Abstract = AppendPubmedUrlToAbstract(_referenceAbstract,_referenceSourceId),
                     },
                     new ReferenceClassificationSendGridTemplateModel
                     {
                        Id = _referenceClassificationId,
                        ClassificationCategoryName = _classificationCategoryName,
                        CountryOfOccurrence = _countryName,
                        MinimalCriteria = _minimalCriteriaY,
                        SubstanceName = _substanceName
                     }
                )},
            NewPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>()
        };

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_Email_To_Multiple_Customers()
    {
        // Arrange
        List<DailyClassificationEmailSendGridTemplateModel> emailsCreated = new();

        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompaniesWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompany(_referenceId, _referenceClassificationId, _companyId, EmailRelevantEventEmailStatusType.New));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>()))
                .Callback((DailyClassificationEmailSendGridTemplateModel template) =>
                {
                    emailsCreated.Add(template);
                }).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(
            _abstractText);


        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expectedPC = SendGridTemplateModel(EmailReason.NewPotentialCase, _companyId, _referenceId, _referenceClassificationId, _substanceName, _countryName, _classificationCategoryName, _minimalCriteriaY, _referenceSourceId, _referenceTitle, _referenceAbstract);

        Assert.Equal(3, emailsCreated.Count);
        Assert.True(SimpleObjectComparer(emailsCreated.First(x => x.NewPotentialCases.Count > 0), expectedPC));
        Assert.Equal(2, emailsCreated.Count(x => x.ChangedFromPotentialCases.Count == 0 && x.NewPotentialCases.Count == 0));
    }

    [Fact]
    /// <summary>Test that only the latest email relevant event is considered. This simulates when a non potential case email has been sent for the classification
    /// then the classification is changed to potential case and then back to non potential case, and no email has yet been sent out after the initial one. 
    /// This can happen when a classification is changed, then changed back on the same day. The customer should not receive an email.
    /// </summary>
    public async Task SendDailyReferenceClassificationEmails_Sends_EmptyEmailSection_If_NonPcClassification_Sent_then_Changed_To_Pc_Then_NonPc()
    {
        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompanySentNonPcToUnsentPcToUnsentNonPc(_referenceId, _referenceClassificationId, _companyId));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert       
        var expected = SendGridTemplateModelForNoClassifications(_companyId);

        VerifyResultIs(expected);
    }

    [Fact]
    /// <summary>Test that only the latest email relevant event is considered. This simulates when a potential case email has been sent for the classification
    /// then the classification is changed to non potential case and then back to potential case, and no email has yet been sent out after the initial one. 
    /// This can happen when a classification is changed, then changed back on the same day. The customer should not receive an email.
    /// </summary>
    public async Task SendDailyReferenceClassificationEmails_Sends_EmptyEmailSection_If_PcClassification_Sent_then_Changed_To_NonPc_Then_Pc()
    {

        // sends latest email relevant event when pc -> non pc -> pc

        // Arrange
        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompanyWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompanySentPcToUnsentNonPcToUnsentPc(_referenceId, _referenceClassificationId, _companyId));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>())).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert       
        var expected = SendGridTemplateModelForNoClassifications(_companyId);

        VerifyResultIs(expected);
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmails_Sends_Correct_EmailSections_When_Different_Customers_Have_Been_Informed_Of_Different_Events_For_Same_Reference()
    {
        // Arrange
        List<DailyClassificationEmailSendGridTemplateModel> emailsCreated = new();

        _mockEmailLogService.Setup(x => x.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, EmailTriggerType.Manual))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithUsers())
            .ReturnsAsync(GetCompaniesWithActiveUsers(_companyId));

        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId))
            .ReturnsAsync(CompanyInterestsForCompaniesWithDifferenctEmailRelevantEventsSameClassification(_referenceId, _referenceClassificationId, _companyId, EmailRelevantEventEmailStatusType.New));
        _mockCompanyInterestRepository.Setup(x => x.GetForSendingDailyEmail(_companyId + 1))
            .ReturnsAsync(CompanyInterestsForCompaniesWithDifferenctEmailRelevantEventsSameClassification(_referenceId, _referenceClassificationId, _companyId + 1, EmailRelevantEventEmailStatusType.Sent));

        _mockReferenceRepository.Setup(x => x.GetForEmail(_referenceId))
            .ReturnsAsync(GetSendGridTemplate(_referenceAbstract, _referenceId, _referenceSourceId, _referenceTitle));

        _mockReferenceClassificationRepository.Setup(x => x.GetForEmail(_referenceClassificationId))
            .ReturnsAsync(GetReferenceClassificationSendGridTemplateModel(_substanceName, _countryName, _classificationCategoryName, _referenceClassificationId, _minimalCriteriaY));

        _mockEmailSender.Setup(x => x.SendDailyReferenceClassificationEmail(
            It.IsAny<DailyClassificationEmailSendGridTemplateModel>()))
                .Callback((DailyClassificationEmailSendGridTemplateModel template) =>
                {
                    emailsCreated.Add(template);
                }).ReturnsAsync(SendGridSuccessResponse());

        _mockEmailRepository.Setup(x => x.GetEmail(_emailId))
            .ReturnsAsync(GetDailyReferenceEmail(_emailId));

        _urlRenderHelper.Setup(x => x.GetUrlLinkBasedOnSourceSystem(
            It.IsAny<DailyClassificationEmailSendGridTemplateModelRow>())).Returns(
            _abstractText);

        // Act
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        // Assert
        var expectedPC = SendGridTemplateModel(EmailReason.NewPotentialCase, _companyId, _referenceId, _referenceClassificationId, _substanceName, _countryName, _classificationCategoryName, _minimalCriteriaY, _referenceSourceId, _referenceTitle, _referenceAbstract);

        Assert.Equal(3, emailsCreated.Count);
        Assert.True(SimpleObjectComparer(emailsCreated.Single(x => x.RecipientEmail == $"email{_companyId}@email.com"), expectedPC));
        Assert.Equal(2, emailsCreated.Count(x => x.ChangedFromPotentialCases.Count == 0 && x.NewPotentialCases.Count == 0));

    }

    private static string AppendPubmedUrlToAbstract(string referenceAbstract, string sourceId)
    {
        return $"{referenceAbstract} <a href=\"https://pubmed.ncbi.nlm.nih.gov/{sourceId}\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ";
    }

    private static Response SendGridSuccessResponse()
    {
        var content = new FakeHttpContent();
        var message = new HttpResponseMessage();
        var headers = message.Headers;
        headers.Add("HeaderKey", "HeaderValue");

        return new SendGrid.Response(HttpStatusCode.OK, content, headers);
    }

    private static FakeEmail GetDailyReferenceEmail(int id)
    {
        return new FakeEmail(id)
        {
            EmailType = EmailType.DailyReferenceClassificationEmail,
            EmailTriggerType = EmailTriggerType.Auto,
            StartDate = DateTime.UtcNow,
            EmailStatusType = EmailStatusType.Started
        };
    }

    private static ReferenceSendGridTemplateModel GetSendGridTemplate(string abstractText, int id, string sourceId, string title)
    {
        return new ReferenceSendGridTemplateModel
        {
            Abstract = abstractText,
            Id = id,
            SourceId = sourceId,
            Title = title,
        };
    }

    private static ReferenceClassificationSendGridTemplateModel GetReferenceClassificationSendGridTemplateModel(string substanceName, string country, string classificationCategoryName, int id, string minimalCriteria)
    {
        return new ReferenceClassificationSendGridTemplateModel()
        {
            Id = id,
            ClassificationCategoryName = classificationCategoryName,
            CountryOfOccurrence = country,
            SubstanceName = substanceName,
            MinimalCriteria = minimalCriteria
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompany(int referenceId, int referenceClassificationId, int companyId, EmailRelevantEventEmailStatusType emailStatus)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryPC = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, emailStatus)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPC,
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompanyNonPcToPotentialCase(int referenceId, int referenceClassificationId, int companyId)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryNonPC = new FakeClassificationCategory("Safety relevant information", false);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, EmailRelevantEventEmailStatusType.Sent)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.SafetyRelevantInformation,
            ClassificationCategory = fakeClassificationCategoryNonPC,
        });

        var fakeClassificationCategoryPC = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(2, EmailRelevantEventEmailStatusType.New)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPC
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompanyPotentialCaseChangeToNonPC(int referenceId, int referenceClassificationId, int companyId)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryPC = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, EmailRelevantEventEmailStatusType.Sent)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPC
        });

        var fakeClassificationCategoryNonPC = new FakeClassificationCategory("Safety relevant information", false);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(2, EmailRelevantEventEmailStatusType.New)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.SafetyRelevantInformation,
            ClassificationCategory = fakeClassificationCategoryNonPC
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompanyIsSplitReference(int referenceId, int referenceClassificationId, int companyId)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryPC = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, EmailRelevantEventEmailStatusType.New)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.SplitReference,
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPC
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompanySentNonPcToUnsentPcToUnsentNonPc(int referenceId, int referenceClassificationId, int companyId)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryNonPCEdit1 = new FakeClassificationCategory("Safety relevant information", false);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, EmailRelevantEventEmailStatusType.Sent)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.SafetyRelevantInformation,
            ClassificationCategory = fakeClassificationCategoryNonPCEdit1,
        });

        var fakeClassificationCategoryPCEdit2 = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(2, EmailRelevantEventEmailStatusType.New)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPCEdit2
        });

        var fakeClassificationCategoryNonPCEdit3 = new FakeClassificationCategory("Efficacy Information", false);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(3, EmailRelevantEventEmailStatusType.New)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.EfficacyInformation,
            ClassificationCategory = fakeClassificationCategoryNonPCEdit3,
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompanySentPcToUnsentNonPcToUnsentPc(int referenceId, int referenceClassificationId, int companyId)
    {
        var companyInterest = new CompanyInterest();

        var emailRelevantEvents = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryPCEdit1 = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(1, EmailRelevantEventEmailStatusType.Sent)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPCEdit1
        });

        var fakeClassificationCategoryNonPCEdit2 = new FakeClassificationCategory("Safety relevant information", false);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(2, EmailRelevantEventEmailStatusType.New)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.SafetyRelevantInformation,
            ClassificationCategory = fakeClassificationCategoryNonPCEdit2
        });


        var fakeClassificationCategoryPCEdit3 = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents.Add(new FakeEmailRelevantEvent(3, EmailRelevantEventEmailStatusType.New)
        {
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPCEdit3
        });

        companyInterest.EmailRelevantEvents = emailRelevantEvents.ToArray();

        companyInterest.CompanyId = companyId;
        companyInterest.ReferenceId = referenceId;
        companyInterest.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest
        };
    }

    private static IEnumerable<CompanyInterest> CompanyInterestsForCompaniesWithDifferenctEmailRelevantEventsSameClassification(int referenceId, int referenceClassificationId, int companyId, EmailRelevantEventEmailStatusType emailStatus)
    {
        var companyInterest1 = new CompanyInterest();

        var emailRelevantEvents1 = new List<FakeEmailRelevantEvent>();

        var fakeClassificationCategoryPC1 = new FakeClassificationCategory("Potential Case", true);
        emailRelevantEvents1.Add(new FakeEmailRelevantEvent(1, emailStatus)
        {
            ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
            ClassificationCategory = fakeClassificationCategoryPC1,
            EmailRelevantEventActionType = EmailRelevantEventActionType.Signed,
        });

        companyInterest1.EmailRelevantEvents = emailRelevantEvents1.ToArray();

        companyInterest1.CompanyId = companyId;
        companyInterest1.ReferenceId = referenceId;
        companyInterest1.ReferenceClassificationId = referenceClassificationId;

        return new CompanyInterest[] {
            companyInterest1
        };
    }

    private static IEnumerable<CompanyInterest> NoCompanyInterestsForCompany()
    {
        return new CompanyInterest[] {
            new CompanyInterest()
        };
    }

    private static IEnumerable<CompanyEmailModel> GetCompanyWithActiveUsers(int companyId)
    {
        return new CompanyEmailModel[] {
            new CompanyEmailModel { Id = companyId, Name = "Company 1", CompanyUsers = GetCompanyUsers(companyId) },
        };
    }

    private static IEnumerable<CompanyEmailModel> GetCompaniesWithActiveUsers(int companyId)
    {
        return new CompanyEmailModel[] {
            new CompanyEmailModel{ Id = companyId,   Name = "Company 1", CompanyUsers = GetCompanyUsers(companyId) },
            new CompanyEmailModel{ Id = companyId+1, Name = "Company 2", CompanyUsers = GetCompanyUsers(companyId + 1) },
            new CompanyEmailModel{ Id = companyId+2, Name = "Company 3", CompanyUsers = GetCompanyUsers(companyId + 2) }
        };
    }

    private static DailyClassificationEmailSendGridTemplateModel SendGridTemplateModelForNoClassifications(int companyId)
    {
        return new DailyClassificationEmailSendGridTemplateModel
        {
            Subject = "0 VigiLit Classifications " + MakeSubjectDate(),
            RecipientEmail = MakeUserEmail(companyId),
            RecipientName = $"{MakeGivenName(companyId)} {MakeFamilyName(companyId)}",
            NewPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>(),
            ChangedFromPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>()
        };
    }

    private static List<CompanyUserModel> GetCompanyUsers(int companyId)
    {
        return new List<CompanyUserModel> { new CompanyUserModel
        {
            CompanyId = companyId,
            FamilyName = MakeFamilyName(companyId),
            GivenName = MakeGivenName(companyId),
            Email = MakeUserEmail(companyId),
            Active = true,
            EmailPreferenceIds = new List<int> { (int)EmailType.DailyReferenceClassificationEmail }
        }};
    }

    private static DailyClassificationEmailSendGridTemplateModel SendGridTemplateModel(EmailReason emailReason, int companyId, int referenceId, int referenceClassificationId, string substanceName, string countryName, string classificationCategoryName, string minimalCriteriaY, string referenceSourceId, string referenceTitle, string referenceAbstract)
    {
        return new DailyClassificationEmailSendGridTemplateModel
        {
            Subject = "1 VigiLit Classification " + MakeSubjectDate(),
            RecipientEmail = MakeUserEmail(companyId),
            RecipientName = $"{MakeGivenName(companyId)} {MakeFamilyName(companyId)}",
            NewPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>
            {
                new DailyClassificationEmailSendGridTemplateModelRow(
                     0,
                     emailReason,
                     new ReferenceSendGridTemplateModel{
                        Id = referenceId,
                        SourceId = referenceSourceId,
                        Title = referenceTitle,
                        Abstract = AppendPubmedUrlToAbstract(referenceAbstract, referenceSourceId),
                     },
                     new ReferenceClassificationSendGridTemplateModel
                     {
                        Id = referenceClassificationId,
                        ClassificationCategoryName = classificationCategoryName,
                        CountryOfOccurrence = countryName,
                        MinimalCriteria = minimalCriteriaY,
                        SubstanceName = substanceName
                     }
                )},
            ChangedFromPotentialCases = new List<DailyClassificationEmailSendGridTemplateModelRow>()
        };
    }

    private void VerifyResultIs(DailyClassificationEmailSendGridTemplateModel expected)
    {
        _mockEmailSender.Verify(x => x.SendDailyReferenceClassificationEmail(It.Is<DailyClassificationEmailSendGridTemplateModel>(dce =>
            dce.RecipientEmail == expected.RecipientEmail &&
            dce.Subject == expected.Subject &&
            dce.RecipientName == expected.RecipientName &&
            SimpleObjectComparer(dce.NewPotentialCases, expected.NewPotentialCases) &&
            SimpleObjectComparer(dce.ChangedFromPotentialCases, expected.ChangedFromPotentialCases)
         )), Times.Once);
    }

    private static bool SimpleObjectComparer(ICollection<DailyClassificationEmailSendGridTemplateModelRow> compare1, ICollection<DailyClassificationEmailSendGridTemplateModelRow> compare2)
    {
        var json1 = JsonConvert.SerializeObject(compare1);
        var json2 = JsonConvert.SerializeObject(compare2);
        return json1.Equals(json2);
    }

    private static bool SimpleObjectComparer(DailyClassificationEmailSendGridTemplateModel compare1, DailyClassificationEmailSendGridTemplateModel compare2)
    {
        var json1 = JsonConvert.SerializeObject(compare1);
        var json2 = JsonConvert.SerializeObject(compare2);
        return json1.Equals(json2);
    }
}
