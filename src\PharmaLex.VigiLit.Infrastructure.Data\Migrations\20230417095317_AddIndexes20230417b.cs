﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230417b : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts",
                column: "ImportId")
                .Annotation("SqlServer:Include", new[] { "ContractId", "PubMedModificationDate", "StartDate", "EndDate", "ImportContractStatusType", "ReferencesCount", "NewReferencesCount", "UpdatesCount", "SilentUpdatesCount" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ImportId",
                table: "ImportContracts",
                column: "ImportId");
        }
    }
}
