@model List<PharmaLex.VigiLit.Ui.ViewModels.EmailService.EmailModel>
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Email Log";
}

<div id="emails">
    <div class="sub-header">
        <h2>Email Log</h2>
        <div class="controls">
        </div>
    </div>
    <section>
        <filtered-table :items="emails" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

<script type="text/javascript">

        var pageConfig = {
            appElement: "#emails",
            data: function () {
                return {
                    link: '/Emails/',
                    emails: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'emailType',
                                sortKey: 'emailType',
                                header: 'Type',
                                type: 'text',
                                style: 'width: 17%;'
                            },
                            {
                                dataKey: 'emailTriggerType',
                                sortKey: 'emailTriggerType',
                                header: 'Trigger',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'startDate',
                                sortKey: 'startDate',
                                header: 'Start Date',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'endDate',
                                sortKey: 'endDate',
                                header: 'End Date',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'duration',
                                sortKey: 'duration',
                                header: 'Duration',
                                type: 'text',
                                style: 'width: 11.5%;'
                            },
                            {
                                dataKey: 'emailStatusType',
                                sortKey: 'emailStatusType',
                                header: 'Status',
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'sentEmailsCount',
                                sortKey: 'sentEmailsCount',
                                header: 'Emails Sent',
                                type: 'number',
                                style: 'width: 11.5%;'
                            },
                            {
                                dataKey: 'failedEmailsCount',
                                sortKey: 'failedEmailsCount',
                                header: 'Emails Failed',
                                type: 'number',
                                style: 'width: 11.5%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'emailType',
                            options: [],
                            type: 'search',
                            header: 'Search Type',
                            fn: v => p => p.emailType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'emailTriggerType',
                            options: [],
                            type: 'search',
                            header: 'Search Trigger',
                            fn: v => p => p.emailTriggerType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'emailStatusType',
                            options: [],
                            type: 'search',
                            header: 'Search Status',
                            fn: v => p => p.emailStatusType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}