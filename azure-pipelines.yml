trigger:
  - master
  - develop

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  Major: '1'
  Minor: '0'
  Patch:  $[counter('', 820)]

name: VigiLit $(Major).$(Minor).$(Patch).$(Build.SourceBranchName)


steps:
  - checkout: self
    submodules: true
    fetchDepth: 1
  - task: UseDotNet@2
    displayName: 'Install Dotnet Core 8'
    inputs:
      packageType: 'sdk'
      version: '8.0.x'

  - task: DotNetCoreCLI@2
    displayName: 'dotnet restore'
    inputs:
      command: 'restore'
      projects: '**/*.csproj'
      feedsToUse: 'select'
      vstsFeed: '780c2041-bb89-4053-a6e6-13d4c16ae672'

  - task: DotNetCoreCLI@2
    displayName: 'dotnet build'
    inputs:
      command: 'build'
      projects: '**/*.csproj'
      arguments: '--configuration $(BuildConfiguration) --no-restore'

  - task: DotNetCoreCLI@2
    displayName: 'dotnet test'
    inputs:
      command: 'test'
      projects: '**/*.Tests.csproj'
      arguments: '--configuration $(BuildConfiguration) --no-build'

  - task: CmdLine@2
    displayName: yarn install
    inputs:
      script: yarn install
      workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'

  - task: CmdLine@2
    displayName: yarn build
    inputs:
      script: yarn build-prod
      workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'

  - task: DotNetCoreCLI@2
    displayName: Install EF Tool
    inputs:
      command: custom
      custom: 'tool'
      arguments: 'install --global dotnet-ef --version 8.0.2'

  - task: DotNetCoreCLI@2
    displayName: Create SQL Scripts
    inputs:
      command: custom
      custom: 'ef '
      arguments: migrations script --project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" --startup-project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web\PharmaLex.VigiLit.Web.csproj" --output $(Build.artifactstagingdirectory)\Migrations\migration.sql --idempotent --verbose --no-build --configuration $(BuildConfiguration)

  - task: DotNetCoreCLI@2
    displayName: 'dotnet publish web'
    inputs:
      command: 'publish'
      publishWebProjects: true
      arguments: '--configuration $(BuildConfiguration) --output $(build.artifactstagingdirectory)'

  - task: DotNetCoreCLI@2
    displayName: 'dotnet publish import function'
    inputs:
      command: 'publish'
      publishWebProjects: false
      projects: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.ImportApp\PharmaLex.VigiLit.ImportApp.csproj'
      arguments: '--configuration Release --output $(build.artifactstagingdirectory)\import'
      zipAfterPublish: false
      modifyOutputPath: false

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\import'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)\VigiLitImportApp.zip'
      replaceExistingArchive: true

  - task: PublishBuildArtifacts@1
    displayName: 'publish artifacts drop'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      ArtifactName: 'drop'
      publishLocation: 'Container'
