﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Models.Document.TrackingSheet;


namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

internal class TrackingSheetDocumentService : ITrackingSheetDocumentService
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageTrackingSheetDocumentOptions _trackingSheetDocumentOptions;

    public TrackingSheetDocumentService(
        IDocumentService documentService,
        IOptions<AzureStorageTrackingSheetDocumentOptions> trackingSheetDocumentOptions)
    {
        _documentService = documentService;
        _trackingSheetDocumentOptions = trackingSheetDocumentOptions.Value;
    }

    public async Task Create(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, Stream stream, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(trackingSheetDocumentDescriptor);
        await _documentService.Create(documentDescriptorUpload, stream, cancellationToken);
    }

    public async Task<bool> Exists(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(trackingSheetDocumentDescriptor);
        var exists = await _documentService.Exists(documentDescriptor, cancellationToken);
        return exists;
    }

    public async Task<Stream> OpenRead(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(trackingSheetDocumentDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    public async Task Delete(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(trackingSheetDocumentDescriptor);
        await _documentService.Delete(documentDescriptor, cancellationToken);
    }

    private DocumentDescriptor GetDocumentDescriptor(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor)
    {
        var blobName = trackingSheetDocumentDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_trackingSheetDocumentOptions.AccountName, _trackingSheetDocumentOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}