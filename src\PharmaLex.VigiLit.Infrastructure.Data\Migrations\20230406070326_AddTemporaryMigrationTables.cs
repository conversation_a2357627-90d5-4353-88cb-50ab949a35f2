﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddTemporaryMigrationTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MIGRATION_EmailRecipients",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MySqlIdUser = table.Column<long>(type: "bigint", nullable: false),
                    MySqlIdCompany = table.Column<long>(type: "bigint", nullable: false),
                    VigiLitIdUser = table.Column<int>(type: "int", nullable: true),
                    VigiLitIdCompany = table.Column<int>(type: "int", nullable: true),
                    IncludeDailyReference = table.Column<bool>(type: "bit", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FirstName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Surname = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sent = table.Column<bool>(type: "bit", nullable: false),
                    IsLiveEmail = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_EmailRecipients", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressClassification",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressClassification", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressCompanies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressCompanies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressContract",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressContract", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressInternalUser",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressInternalUser", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressProject",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressProject", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressReference",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressReference", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressUser",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressUser", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MIGRATION_Summary",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TableName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProcessStartTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RecordsRemaining = table.Column<int>(type: "int", nullable: false),
                    RecordsProcessed = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_Summary", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MIGRATION_EmailRecipients");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressClassification");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressCompanies");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressContract");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressInternalUser");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressProject");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressReference");

            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressUser");

            migrationBuilder.DropTable(
                name: "MIGRATION_Summary");
        }
    }
}
