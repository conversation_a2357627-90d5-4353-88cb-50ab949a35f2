using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class CompanyModel
{
    public int Id { get; set; }
    [Required]
    [MaxLength(250)]
    [RegularExpression("^\\S[\\S\\s]*$", ErrorMessage = "Invalid Name")]
    [Remote("ValidateCompanyName", "Companies", AdditionalFields = "Id", ErrorMessage = "Company name already exists")]
    public string Name { get; set; }
    [MaxLength(250)]
    public string ContactPersonName { get; set; }
    [MaxLength(256)]
    [EmailAddress(ErrorMessage = "Invalid Email Address.")]
    public string ContactPersonEmail { get; set; }
    public bool IsActive { get; set; }
}
