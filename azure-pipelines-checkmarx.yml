trigger:
  - master
  - develop

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  Major: '1'
  Minor: '0'
  Patch:  $[counter('', 820)]
  RunCheckmarx: true

name: VigiLit $(Major).$(Minor).$(Patch).$(Build.SourceBranchName)

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git
  containers:
    - container: mssql
      image: mcr.microsoft.com/mssql/server:2022-latest
      ports: 
        - 1433:1433
      env:
        ACCEPT_EULA: Y
        SA_PASSWORD: <YourStrong!Passw0rd>
        MSSQL_PID: Express

    - container: rabbit
      image: rabbitmq:4.0.9-management-alpine
      env:
        RABBITMQ_ERLANG_COOKIE: "${RABBITMQ_ERLANG_COOKIE}"
        RABBITMQ_DEFAULT_USER: "test"
        RABBITMQ_DEFAULT_PASS: "test"
        RABBITMQ_DEFAULT_VHOST: "/"
      ports:
        - 5672:5672
stages:
- stage: BuildAndTest
  jobs:
    - job: BuildTest
      variables:
        - group: Nuget
        - name: NuGet_Source
          value: $[variables.Source]
        - name: LongLivedBranch
          ${{ if or(eq(variables['Build.SourceBranchName'], 'develop'), eq(variables['Build.SourceBranchName'], 'master')) }}:
            value : true
          ${{ else }}:
            value: false
        - name: TestProjects
          value : '**/PharmaLex.VigiLit.Ignore.Unit.Tests.csproj'
             

      services:
        rabbit: rabbit
        mssql: mssql
      displayName: "Build And Test"
      pool:
        vmImage: 'ubuntu-latest'
      steps:
        - template: Build/dotnet/build-test-analyse.yml@templates
          parameters:
            NugetSource: '$(NuGet_Source)'
            SourceBranch: "variables['Source_Branch']"
            VersionNumber: "$(Major).$(Minor).$(Patch)"
            SonarProjectKey: "phlexglobal_pharmalex-vigilit"
            SonarProjectName: "PharmaLex.Vigilit"
            SolutionName: '**/VigiLit.sln'
            TestProjects: ${{ variables.TestProjects }}
            AnalysePackages: true
            LongLivedBranch: ${{ variables.LongLivedBranch }}
            CheckmarxEnabled: ${{ variables.RunCheckmarx }}
            CheckmarxTeam: 'PV'

        
