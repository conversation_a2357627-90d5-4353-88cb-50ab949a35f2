﻿using System.ComponentModel;

namespace PharmaLex.VigiLit.ReferenceManagement;

public enum ReferenceHistoryActionType
{

    [Description("")]
    None = 0,

    [Description("New")]
    New,

    [Description("Update")]
    Update,

    [Description("Silent")]
    Silent,

    [Description("Inactive")]
    Inactive,

    [Description("Classified")]
    Classified,

    [Description("Unchanged")]
    Unchanged,

    [Description("Corrected")]
    Corrected,

    [Description("Approved")]
    Approved,

    [Description("Signed")]
    Signed,

    [Description("Reclassified")]
    ReClassified,

    [Description("Migrated")]
    Migrated,

    [Description("Case Upload")]
    CaseUpload,

    [Description("Case Edit")]
    CaseEdit,

    [Description("Split Reference")]
    SplitReference
}