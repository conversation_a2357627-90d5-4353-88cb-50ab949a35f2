﻿html, body, p, select, input, th, td, label,
.pager, .no-records-container, button, a.button, .core-content ul li,
.autocomplete .autocomplete-items li {
    font-weight: normal;
}

input[type=text], input[type=url], input[type=email], input[type=search],
input[type=number], input[type=tel], input[type=date], input[type=password],
textarea, select, .fake-input, .fake-textarea {
    font: 13px $main-font;
    font-weight: normal;
}

span.preClassifierName {
    font-size: 17px;
    color: $brand;
}

ul.tabs li span.tab-badge {
    font-size: 12px;
}

th, label, .chip {
    font-weight: bold;
}

.page-header, .sub-header {
    font-family: $cencora-regular;
}

.sub-header h2 { 
    font-size: .875rem;
    font-weight: 500; 
}

h2.dashboard {
    font-size: 1.25rem;
    font-weight: 600;
}

.core-container > header a.app-title {
    min-width: 125px;
}

.nav-menu > li {
    font-size: 17px;
    a {
        color: #1F394A;
    }
}

.nav-menu > li .flyout li {
    font-size: 17px;
}

sub {
    vertical-align: sub !important;
    font-size: smaller !important;
}

sup {
    vertical-align: super !important;
    font-size: smaller !important;
}
