﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexTrackingSheet : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ImportContracts_ContractVersionId_ContractId_ImportId",
                table: "ImportContracts",
                columns: new[] { "ContractVersionId", "ContractId", "ImportId" })
                .Annotation("SqlServer:Include", new[] { "ReferencesCount", "ImportDate" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContracts_ContractVersionId_ContractId_ImportId",
                table: "ImportContracts");
        }
    }
}
