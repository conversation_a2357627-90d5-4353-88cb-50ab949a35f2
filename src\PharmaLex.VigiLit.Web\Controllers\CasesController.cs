﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Case;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize]
public class CasesController : BaseController
{
    private readonly ICaseService _caseService;
    private readonly ICaseDocumentUploadService _caseDocumentUploadService;
    private readonly IClassificationService _classificationService;
    private readonly ICompanyService _companyService;
    private readonly IUserRepository<User> _userRepository;
    private readonly ISubstanceService _substanceService;
    private readonly IVigiLitUserContext _vigiLitUserContext;
    private readonly IUserService _userService;
    private readonly ICaseFileDocumentTypeService _caseFileDocumentTypeService;
    private readonly IAccessControlService _accessControlService;
    private readonly ILogger<CasesController> _logger;

    public CasesController(
        ICaseService caseService,
        ICaseDocumentUploadService caseDocumentUploadService,
        IClassificationService classificationService,
        ICompanyService companyService,
        IUserRepository<User> userRepository,
        ISubstanceService substanceService,
        IVigiLitUserContext vigiLitUserContext,
        IUserService userService,
        ICaseFileDocumentTypeService caseFileDocumentTypeService,
        IAccessControlService accessControlService,
        IConfiguration configuration,
        IUserSessionService userSessionService,
        ILogger<CasesController> logger) : base(userSessionService, configuration)
    {
        _caseService = caseService;
        _caseDocumentUploadService = caseDocumentUploadService;
        _classificationService = classificationService;
        _companyService = companyService;
        _userRepository = userRepository;
        _substanceService = substanceService;
        _vigiLitUserContext = vigiLitUserContext;
        _userService = userService;
        _caseFileDocumentTypeService = caseFileDocumentTypeService;
        _accessControlService = accessControlService;
        _logger = logger;
    }

    [HttpGet("Pending")]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<IActionResult> IndexPending()
    {
        var searchRequest = new CaseSearchRequest
        {
            CaseStatus = CaseStatus.Pending
        };
        var items = await _caseService.SearchAsync(searchRequest);
        return View("IndexPending", items);
    }

    [HttpGet("Published")]
    [Authorize(Policy = Policies.InternalUser)]
    public async Task<IActionResult> IndexPublishedInternal()
    {
        var user = await _userRepository.GetForSecurity(CurrentUserId);
        if (user == null)
        {
            return Unauthorized();
        }

        var model = new CaseSearchPageModel
        {
            Substances = await _substanceService.GetForSearch(user)
        };
        return View("IndexPublishedInternal", model);
    }

    [HttpGet]
    [Authorize(Policy = Policies.CaseExternal)]
    public async Task<IActionResult> IndexPublishedExternal()
    {
        var user = await _userRepository.GetForSecurity(CurrentUserId);
        if (user?.CompanyUser == null || !user.CompanyUser.Active)
        {
            return Unauthorized();
        }

        var model = new CaseSearchPageModel
        {
            Substances = await _substanceService.GetForSearch(user)
        };
        return View("IndexPublishedExternal", model);
    }

    [HttpGet("{caseId:int}")]
    [Authorize]
    public async Task<IActionResult> Case(int caseId)
    {
        try
        {
            var caseModel = await _caseService.GetByIdAsync(caseId);
            if (caseModel is not { Status: CaseStatus.Published })
            {
                return NotFound();
            }

            var casesContext = new CaseAccessContext(User, caseModel);
            await _accessControlService.HasPermission(casesContext);

            var caseDetailedResponse = await GetCaseDetailed(caseModel, string.Empty);

            return View("CaseRecord", caseDetailedResponse);

        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
    }

    [HttpPost("Search")]
    [Authorize(Policy = Policies.CaseExternal)]
    public async Task<IActionResult> Search([FromBody] CaseSearchRequest searchRequest = null)
    {
        var user = await _userRepository.GetForSecurity(CurrentUserId);
        if (user?.CompanyUser == null || !user.CompanyUser.Active)
            return Unauthorized();

        searchRequest ??= new CaseSearchRequest();
        searchRequest.CaseStatus = CaseStatus.Published;
        searchRequest.CompanyId = user.CompanyUser.CompanyId;

        var items = await _caseService.SearchAsync(searchRequest);
        return Ok(items);
    }

    [HttpPost("PublishedSearch")]
    [Authorize(Policy = Policies.InternalUser)]
    public async Task<ActionResult> SearchInternal([FromBody] CaseSearchRequest searchRequest = null)
    {
        searchRequest ??= new CaseSearchRequest();
        searchRequest.CaseStatus = CaseStatus.Published;
        var items = await _caseService.SearchAsync(searchRequest);
        return Json(items);
    }

    [HttpGet("Pending/CaseTable")]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<ActionResult> GetCaseTableJson()
    {
        var searchRequest = new CaseSearchRequest
        {
            CaseStatus = CaseStatus.Pending
        };
        var items = await _caseService.SearchAsync(searchRequest);
        return Json(items);
    }

    [HttpGet("CaseRecord/{id:int?}")]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<IActionResult> GetCaseRecord(int? id = null)
    {
        var uploadId = $"{DateTime.UtcNow:yyyyMMdd.HHmmss}.{_vigiLitUserContext.UserId}";

        if (!id.HasValue)
        {
            return PartialView("CaseRecordModal", CaseResponse.Empty(uploadId));
        }

        var caseRecord = await _caseService.GetByIdAsync(id.Value);
        if (caseRecord is not { Status: CaseStatus.Pending })
        {
            return NotFound();
        }

        var caseResponse = await GetCase(caseRecord, uploadId);

        return PartialView("CaseRecordModal", caseResponse);
    }

    [HttpDelete("CaseRecord/{id:int}")]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<IActionResult> DeleteCaseRecord(int id)
    {
        try
        {
            await _caseService.DeleteAsync(id);
        }
        catch(KeyNotFoundException ex)
        {
            _logger.LogError(ex, "Could not find a case with ID : '{id}'", id);
            return NotFound("Something went wrong, please try again"); 
        }
        catch (InvalidOperationException)
        {
            return BadRequest("Can only delete pending cases.");
        }

        return Ok("Successfully deleted Case record.");
    }

    [HttpGet("Classification")]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<ActionResult> GetClassification(int plxId)
    {
        if (plxId <= 0)
        {
            return BadRequest("Please enter a valid PLX ID.");
        }

        var classification = await _classificationService.GetByIdAsync(plxId);
        if (classification == null)
        {
            return NotFound("Classification was not found for PLX ID.");
        }

        var companies = (await _companyService.GetCompaniesForSubstanceAsync(classification.SubstanceId)).ToList();

        var caseRecord = new CaseClassificationResponse(classification.Substance?.Name, companies, classification.PSURRelevanceAbstract, classification.PvSafetyDatabaseId);

        return Ok(caseRecord);
    }

    [HttpPost("Case")]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<ActionResult> SaveCase(SaveCaseRequest saveCaseRequest)
    {
        var caseValidationResult = _caseService.Validate(saveCaseRequest);
        if (!caseValidationResult.IsSuccess)
        {
            return BadRequest(caseValidationResult.Error);
        }

        if (!saveCaseRequest.CaseId.HasValue)
        {
            await _caseService.AddAsync(saveCaseRequest);
        }
        else
        {
            try
            {
                await _caseService.UpdateAsync(saveCaseRequest);
            }
            catch (ArgumentException ex)
            {
                var caseId = saveCaseRequest.CaseId.GetValueOrDefault();
                // MEND Suppression: Safe due to structured logging
                _logger.LogError(ex, "Argument Exception for case Id: '{CaseId}'", caseId);
                return BadRequest($"Invalid request for save case Id: '{caseId}'");
            }
        }

        return RedirectToAction(nameof(IndexPending));
    }

    [HttpPost("File/Temporary")]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<ActionResult> UploadTemporaryFile(string uploadId, int plxId, [FromForm(Name = "caseFile")] IFormFile caseFile)
    {
        await using var stream = caseFile?.OpenReadStream();

        var caseValidationResult = await _caseService.Validate(caseFile?.FileName, stream, plxId, uploadId);
        if (!caseValidationResult.IsSuccess)
        {
            return BadRequest(caseValidationResult.Error);
        }

        var caseDocumentUploadDescriptor = new CaseDocumentUploadDescriptor(uploadId, caseFile!.FileName);
        var exists = await _caseDocumentUploadService.Exists(caseDocumentUploadDescriptor);
        if (exists)
        {
            await _caseDocumentUploadService.Delete(caseDocumentUploadDescriptor);
        }
        await _caseDocumentUploadService.Create(caseDocumentUploadDescriptor, stream);

        return Ok("File uploaded successfully!");
    }

    [HttpGet("File")]
    [ValidateAntiForgeryToken]
    [Authorize]
    public async Task<ActionResult> DownloadFile(int caseId, int caseFileId)
    {
        try
        {
            var caseModel = await GetCaseModel(caseId);

            var casesContext = new CaseAccessContext(User, caseModel);
            await _accessControlService.HasPermission(casesContext);

            var (stream, filename) = await _caseService.GetDocumentStream(caseModel, caseFileId);
            return File(stream, "application/octet-stream", filename);
        }
        catch (NotFoundException ex)
        {
            _logger.LogError(ex, "Could not download file caseId '{caseId}', caseFileId '{caseFileId}'", caseId, caseFileId);
            return NotFound("Something went wrong, please try again.");
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized(); 
        }
    }

    private async Task<CaseModel> GetCaseModel(int caseId)
    {
        var caseModel = await _caseService.GetByIdAsync(caseId);
        if (caseModel == null)
        {
            throw new NotFoundException("Something went wrong, please try again.");
        }

        return caseModel;
    }

    [HttpGet("File/Temporary")]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<ActionResult> DownloadTemporaryFile(string uploadId, string caseFileName)
    {
        var sanitizedUploadId = Path.GetFileName(uploadId);
        if (sanitizedUploadId != uploadId)
        {
            return BadRequest("Upload ID is invalid.");
        }

        var sanitizedFilename = Path.GetFileName(caseFileName);
        if (sanitizedFilename != caseFileName)
        {
            return BadRequest("Case File name is invalid.");
        }
            
        var caseDocumentUploadDescriptor = new CaseDocumentUploadDescriptor(uploadId, caseFileName);
        var exists = await _caseDocumentUploadService.Exists(caseDocumentUploadDescriptor);
        if (!exists)
        {
            return NotFound("Case File does not exist.");
        }

        var stream = await _caseDocumentUploadService.OpenRead(caseDocumentUploadDescriptor);

        return File(stream, "application/octet-stream", caseDocumentUploadDescriptor.FileName);
    }

    [HttpGet("File/Types")]
    [Authorize(Policy = Policies.CaseManagement)]
    public async Task<IActionResult> GetFileTypes()
    {
        return Json(await _caseFileDocumentTypeService.GetAllAsync());
    }

    private async Task<CaseResponse> GetCase(CaseModel caseRecord, string uploadId)
    {
        var classification = await _classificationService.GetByIdAsync(caseRecord.PlxId);

        var companies = classification != null
            ? (await _companyService.GetCompaniesForSubstanceAsync(classification.SubstanceId)).ToList()
            : new List<CompanyModel>();

        Enum.TryParse(caseRecord.MLM, out CaseMLMDuplicate mlm);

        var uploadedFiles = caseRecord.CaseFiles
            .Select(caseFile => new CaseResponseFile(caseFile.Id, caseFile.FileName, caseFile.FileSize))
            .ToList();

        return new CaseResponse(caseRecord.Id, caseRecord.PlxId, classification?.Substance?.Name, companies, classification?.PSURRelevanceAbstract ?? PSURRelevanceAbstract.NA,
            mlm, caseRecord.PvSafetyDatabaseId, caseRecord.Comment, uploadId, uploadedFiles, caseRecord.CompanyIds, caseRecord.CreatedDate, caseRecord.LastUpdatedBy);
    }

    private async Task<CaseDetailedResponse> GetCaseDetailed(CaseModel caseRecord, string uploadId)
    {
        var caseResponse = await GetCase(caseRecord, uploadId);
        var lastModifiedByUser = await _userService.GetUser(caseResponse.LastUpdatedBy);

        return new CaseDetailedResponse(caseResponse, lastModifiedByUser?.DisplayFullName);
    }
}