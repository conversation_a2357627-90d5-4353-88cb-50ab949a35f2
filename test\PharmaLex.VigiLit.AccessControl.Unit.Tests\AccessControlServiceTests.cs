namespace PharmaLex.VigiLit.AccessControl.Unit.Tests;

public class AccessControlServiceTests
{
    [Fact]
    public async Task AccessControlService_evaluator_not_found_exception_thrown()
    {
        // Arrange
        var context = new TestContext1(1, 1);
        var accessControlService = new AccessControlService(new List<IPermissionEvaluator>());

        // Act
        // & Assert
        await Assert.ThrowsAsync<NotSupportedException>(async () => await accessControlService.HasPermission(context));
    }

    [Fact]
    public async Task AccessControlService_context_passes_return_true()
    {
        // Arrange
        var context = new TestContext1(1, 1);
        var accessControlService = new AccessControlService(
            new List<IPermissionEvaluator>()
            {
                new TestEvaluator1()
            }
        );


        // Act
        var result = await accessControlService.HasPermission(context);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task AccessControlService_more_than_one_right_context_is_called_passes_return_true()
    {
        // Arrange
        var context = new TestContext2("YES");
        var accessControlService = new AccessControlService(
            new List<IPermissionEvaluator>()
            {
                new TestEvaluator1(),
                new TestEvaluator2()
            }
        );


        // Act
        var result = await accessControlService.HasPermission(context);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task AccessControlService_context_fails_exception_thrown()
    {
        // Arrange
        var context = new TestContext1(1, 2);
        var accessControlService = new AccessControlService(
            new List<IPermissionEvaluator>()
            {
                new TestEvaluator1()
            }
        );

        // Act
        // & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await accessControlService.HasPermission(context));
    }
}
