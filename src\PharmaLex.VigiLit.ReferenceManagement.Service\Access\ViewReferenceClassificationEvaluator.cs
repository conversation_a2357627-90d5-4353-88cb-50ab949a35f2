﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Access;
internal class ViewReferenceClassificationEvaluator : PermissionEvaluator<ViewReferenceClassificationPermissionContext>
{
    private readonly IUserRepository _userRepository;
    private readonly ICompanyInterestRepository _companyInterestRepository;

    public ViewReferenceClassificationEvaluator(IUserRepository userRepository, ICompanyInterestRepository companyInterestRepository)
    {
        _userRepository = userRepository;
        _companyInterestRepository = companyInterestRepository;
    }

    private async Task<IUserEntity?> GetSecurityUser(int userId)
    {
        return await _userRepository.GetForSecurity(userId);
    }

    private async Task<bool> CanUserViewClassification(int userId, int classificationId)
    {
        var user = await GetSecurityUser(userId);

        if (TryDetermineAccessByUserType(user, out bool accessResult))
        {
            return accessResult;
        }

        return await _companyInterestRepository.CompanyHasInterestInClassification(user.GetActiveCompanyId(), classificationId);
    }

    public override async Task<bool> HasPermissions(ViewReferenceClassificationPermissionContext context)
    {
        if (await CanUserViewClassification(context.UserId, context.ReferenceClassificationId))
        {
            return true;
        }

        throw new UnauthorizedAccessException();
    }

    /// <summary>
    /// Tries to evaluate access based on their user type.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <param name="accessResult">if returns <c>true</c> [access result].</param>
    /// <returns>
    /// Returns True if can evaluate what the access should be and returns that access result. 
    /// Returns False if further processing is necessary for the operation. 
    /// </returns>
    
    private static bool TryDetermineAccessByUserType([NotNullWhen(false)] IUserEntity? user, out bool accessResult)
    {
        accessResult = false;

        if (user == null)
        {
            accessResult = false;
            return true;
        }

        if (!user.IsCompanyUser())
        {
            accessResult = true;
            return true;
        }

        if (!user.HasActiveCompany())
        {
            accessResult = false;
            return true;
        }

        return false;
    }
}
