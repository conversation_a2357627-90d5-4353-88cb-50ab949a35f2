LocalStorageValidator = {
    validateJsonProperties: function(storageName = null, itemToModel = null) {
        if (!storageName || !itemToModel) {
            // Return false to clear storage in case of reference issues.
            return false;
        }
        var itemJson = localStorage.getItem(storageName);
        if (!itemJson) {
            // Return true since no action is needed if storage item value is undefined.
            return true;
        }
        
        var item;
        try {
            item = JSON.parse(itemJson);
            if (item == null) {
                return false;
            }
        } catch(error) {
            return false;
        }
        
        for (const propertyName of Object.getOwnPropertyNames(itemToModel)) {
            if (!Object.hasOwn(item, propertyName)) {
                return false;
            }
        }

        return true;
    },
    clearJsonItemIfInvalid: function(storageName, itemToModel) {
        var isValid = this.validateJsonProperties(storageName, itemToModel);
        if (!isValid) {
            localStorage.removeItem(storageName);
        }
    }
};