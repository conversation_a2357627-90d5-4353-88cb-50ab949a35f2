﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using System;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.Admin)]
[Route("[controller]")]
public class EmailsController: BaseController
{
    private readonly IEmailLogService _emailLogService;

    public EmailsController(IEmailLogService emailLogService,
                                IUserSessionService userSessionService,
                                IConfiguration configuration) : base(userSessionService, configuration)
    {
        _emailLogService = emailLogService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var data = await _emailLogService.GetEmailLog();
        return View(data);
    }

    [HttpGet("{emailId}")]
    public async Task<IActionResult> EmailMessages(int emailId)
    {
        var data = await _emailLogService.GetEmailMessagesLog(emailId);
        return View(data);
    }

    [HttpGet("[action]/{emailMessageId}")]
    public async Task<IActionResult> EmailMessageDetails(int emailMessageId)
    {
        var emailMessage = await _emailLogService.GetEmailMessage(emailMessageId);

        switch (emailMessage.Email.EmailType)
        {
            case EmailType.DailyReferenceClassificationEmail:
                {
                    var data = await _emailLogService.GetEmailMessageClassifications(emailMessageId);
                    return View("EmailMessageClassifications", data);
                }
            case EmailType.DailyCaseFilesEmail:
                {
                    // TODO: Implement a details page for case file email messages.
                    AddNotification("Case file message details page not implemented.", UserNotificationType.Info);
                    return RedirectToAction("EmailMessages", new { emailId = emailMessage.EmailId });
                }
        }

        throw new NotSupportedException();
    }
}

