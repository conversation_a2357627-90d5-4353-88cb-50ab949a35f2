using CsvHelper.Configuration;
using PharmaLex.VigiLit.Reporting.Contracts.Models;

namespace PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;

public interface IExportService
{
    ExportModel Export<TData, TClassMap>(string name, IEnumerable<TData> data)
        where TData : class
        where TClassMap : ClassMap<TData>;

    ExportModel Export<TData, TClassMap>(string name, IEnumerable<TData> data, ExportConfiguration configuration)
        where TData : class
        where TClassMap : ClassMap<TData>;
}