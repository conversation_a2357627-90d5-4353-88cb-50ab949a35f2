
services:
  vigilit-ai:
    image: vigilit-ai:${VERSION_NO}
    build:
      context: .
      dockerfile: "./Dockerfile"
      target: "final"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
    networks:
      - overlay
      
  vigilit-ai-tests:
    image: vigilit-ai-tests:${VERSION_NO}
    build:
      context: .
      dockerfile: "./Dockerfile"
      target: "testrunner"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
    # depends_on:
    #   - sql
    networks:
      - overlay
networks:
  overlay:
