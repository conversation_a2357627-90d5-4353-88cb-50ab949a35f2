﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class ReferenceHelper
{
    private readonly IReferenceRepository _referenceRepository;

    public ReferenceHelper(IReferenceRepository referenceRepository)
    {
        _referenceRepository = referenceRepository;
    }

    public async Task<Reference> AddReference(string title)
    {
        var reference = new Reference { Title = title };
        _referenceRepository.Add(reference);
        await _referenceRepository.SaveChangesAsync();
        return reference;
    }
}
