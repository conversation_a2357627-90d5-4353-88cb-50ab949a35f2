﻿using System.Collections.Generic;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class PrintPreviewFiltersModel
{
    public string Term { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public DateTime? LastUpdatedFrom { get; set; }
    public DateTime? LastUpdatedTo { get; set; }
    public List<string> Substances { get; set; }
    public List<string> Categories { get; set; }
    public string PSUR { get; set; }
    public string Company { get; set; }
    public string Title { get; set; }
    public string MeshTerms { get; set; }
    public bool MeshTermsOr { get; set; }
    public string Keywords { get; set; }
    public bool KeywordsOr { get; set; }
}