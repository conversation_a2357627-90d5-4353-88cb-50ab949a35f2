@using Microsoft.AspNetCore.Authorization;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.Enums
@using PharmaLex.VigiLit.Web.Helpers;
@using PharmaLex.VigiLit.Domain.UserManagement;

@inject IAuthorizationService AuthorizationService

@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.ReferenceDetailsPageModel

@{
    ViewData["Title"] = "Reference";
}

<div id="app" v-cloak>
    <div class="sub-header">
        <h2>Reference</h2>
        <h2>Source Id: {{reference.sourceId}}</h2>
        <div class="controls">
            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.CaseFileOperator)).Succeeded)
            {
                <a class="button" :href="'/References/Split/'+ reference.id">Split Reference</a>
            }
            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalSupport)).Succeeded)
            {
                <a class="button" href="/Search">Back to Search</a>
            }
        </div>
    </div>
    <div v-cloak id="reference-details-container" class="flex gapped">
        @Html.AntiForgeryToken()
        <div id="reference-substance-section" class="flex-item tile flex-20-percent">
            <h3>Substances</h3>
            <ul>
                <li id="substance-list" v-for="referenceClassification in referenceClassifications" @@click="classificationClicked(referenceClassification)">
                    <span class="substance-name">{{referenceClassification.substance.name}}</span><br />
                    <span class="plxid">PLX ID: {{referenceClassification.id}}</span>
                </li>
            </ul>
        </div>

        <div id="" class="flex-item tile flex-80-percent reference-details-section">
            <div id="reference-details-info">

                <label>Source Id:</label>
                <span v-if="pubmedSource"><a :href="`https://pubmed.ncbi.nlm.nih.gov/${reference.sourceId}`" target="_blank" rel="noopener">{{reference.sourceId}}</a></span>
                <span v-else>{{reference.sourceId}}</span>

                <label>DOI:</label>
                <span v-if="reference.doi">{{reference.doi}}</span>
                <span v-else>N/A</span>

                <label>Modified:</label>
                <span v-if="pubmedSource">{{getDateFormat(reference.dateRevised)}} <abbr title="Eastern Time">ET</abbr></span>
                <span v-else>N/A</span>
            </div>
            <div id="reference-details-content">
                <reference :reference="reference"> </reference>
            </div>

        </div>

    </div>

</div>

@section Scripts {
    <script type="text/javascript">

        let model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: "#app",
            data: function () {
                return {
                    reference: model.reference,
                    referenceClassifications: model.referenceClassifications
                };
            },
            methods: {
                getDateFormat: function (date) {
                    return moment.utc(date).format('DD MMM YYYY');
                },
                classificationClicked(referenceClassification) {
                    window.location.href = `/References/Classifications/${referenceClassification.id}`
                }
            },
            computed: {
                pubmedSource() {
                    return this.reference && this.reference.sourceSystem == '@((Int16)SourceSystem.PubMed)';
                }
            },
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/ReferenceComponent" />
}

