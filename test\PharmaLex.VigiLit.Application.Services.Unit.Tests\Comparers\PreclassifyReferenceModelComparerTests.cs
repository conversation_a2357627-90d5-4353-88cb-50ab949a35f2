﻿using PharmaLex.VigiLit.Application.Services.Comparers;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Services.Unit.Tests.Comparers
{
    public class PreclassifyReferenceModelComparerTests
    {
        private readonly DateTime _someDate = DateTime.Parse("2000-01-01");
        private readonly DateTime _laterDate = DateTime.Parse("2010-01-01");

        private readonly PreclassifyReferenceModelComparer _comparer;

        public PreclassifyReferenceModelComparerTests()
        {
            _comparer = new PreclassifyReferenceModelComparer();
        }

        [Fact]
        public void Compare_TwoNewModelsModel1IsNull_ReturnsNegative()
        {
            // Arrange
            PreclassifyReferenceModel model1 = null!;

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_TwoNewModelsModel2IsNull_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            PreclassifyReferenceModel model2 = null!;

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void Compare_TwoNewModelsModelBothNull_ReturnsZero()
        {
            // Arrange
            PreclassifyReferenceModel model1 = null!;
            PreclassifyReferenceModel model2 = null!;

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void Compare_TwoNewModelsWithSameDate_ReturnsZero()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void Compare_NewModelsWithModel1HavingLaterDate_ReturnsNegative()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _laterDate }
                },
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_NewModelsWithModel2HavingLaterDate_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                    Reference = new ReferenceDetailedModel { DateRevised = _laterDate }
                },
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void Compare_NewModelAndNonNewModel_ReturnsNegative()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                },
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_NonNewModelAndNewModel_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.New,
                },
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithModel1HavingLaterDate_ReturnsNegative()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _laterDate }
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithModel2HavingLaterDate_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _laterDate }
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithModel1HavingUpdateAndModel2NoUpdate_ReturnsNegative()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = null,
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithModel1HavingNoUpdateAndModel2Update_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = null,
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                },
                ReferenceUpdate = new ReferenceUpdateModel { DateRevised = _someDate }
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithBothHavingNoUpdateAndReferenceDateRevisedAreSame_ReturnsZero()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
                ReferenceUpdate = null,
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
                ReferenceUpdate = null,
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void Compare_NonNewModelsWithBothHavingNoUpdateAndModel1HasLaterDateRevised_ReturnsNegative()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _laterDate }
                },
                ReferenceUpdate = null,
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
                ReferenceUpdate = null,
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result < 0);
        }

        [Fact]
        public void Compare_NonNewModelsWithBothHavingNoUpdateAndModel2HasLaterDateRevised_ReturnsPositive()
        {
            // Arrange
            var model1 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _someDate }
                },
                ReferenceUpdate = null,
            };

            var model2 = new PreclassifyReferenceModel
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    ReferenceState = (int)ReferenceState.Approved,
                    Reference = new ReferenceDetailedModel { DateRevised = _laterDate }
                },
                ReferenceUpdate = null,
            };

            // Act
            var result = _comparer.Compare(model1, model2);

            // Assert
            Assert.True(result > 0);
        }
    }
}