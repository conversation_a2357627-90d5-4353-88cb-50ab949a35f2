﻿using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.ESearch;

public class ErrorList
{
    public ErrorList()
    {
        FieldNotFound = new List<string>();
        PhraseNotFound = new List<string>();
    }

    [XmlElement("PhraseNotFound")]
    public List<string> PhraseNotFound { get; set; }

    [XmlElement("FieldNotFound")]
    public List<string> FieldNotFound { get; set; }
}