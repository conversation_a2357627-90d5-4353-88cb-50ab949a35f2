﻿using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Test.Framework.Orderers;

namespace PharmaLex.Core.UserSessionManagement.Integration.Tests;

[TestCaseOrderer(
    ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
    ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
public class UserSessionRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, UserSessionRepositoryTests>>
{
    private readonly UserSessionRepository _userSessionRepository;
    private readonly IUserRepository _userRepository;

    private readonly UserHelper _userHelper;
    private readonly UserSessionHelper _userSessionHelper;

    public UserSessionRepositoryTests(DatabaseFixture<VigiLitDbContext, UserSessionRepositoryTests> fixture)
    {
        _userSessionRepository = RepositoryFactory.GetRepositoryInstance<UserSession, UserSessionRepository>(fixture.Context);
        _userRepository = RepositoryFactory.GetRepositoryInstance<User, UserRepository, UserMappingProfile>(fixture.Context);

        _userHelper = new UserHelper(_userRepository);
        _userSessionHelper = new UserSessionHelper(_userSessionRepository);
    }

    [Fact, TestPriority(1)]
    public async Task GetAllByUserIdAsync_Returns_Existing_User_Session()
    {
        // Arrange
        int sequenceNo = 1;
        await SetUpTestRecord(sequenceNo);

        // Act     
        var userSessions = await _userSessionRepository.GetAllByUserIdAsync(1);

        // Assert
        Assert.Single(userSessions);
    }

    [Fact, TestPriority(2)]
    public async Task GetAllByUserIdAsync_Returns_No_Session_If_Not_Present()
    {
        // Arrange
        int sequenceNo = 2;
        await SetUpTestRecord(sequenceNo);

        // Act     
        var userSessions = await _userSessionRepository.GetAllByUserIdAsync(999);

        // Assert
        Assert.False(userSessions.Any());
    }

    [Fact, TestPriority(3)]
    public async Task GetSessionsForUsersAsync_Returns_List_Of_Sessions_For_List_Of_User_Ids()
    {
        // Arrange
        var userIdList = new List<int> { 3, 4, 5, 6 };
        await SetUpTestRecord(3);
        await SetUpTestRecord(4);
        await SetUpTestRecord(5);
        await SetUpTestRecord(6);

        // Act     
        var userSessions = await _userSessionRepository.GetSessionsForUsersAsync(userIdList);

        // Assert
        Assert.Equal(4, userSessions.Count());
    }

    [Fact, TestPriority(4)]
    public async Task GetByUserIdAsync_Returns_User_If_Present()
    {
        // Arrange
        int userId = 7;
        await SetUpTestRecord(userId);

        // Act
        var userSession = await _userSessionRepository.GetByUserIdAsync(userId);

        // Assert
        Assert.True(userSession?.UserId == userId);
    }


    [Fact, TestPriority(5)]
    public async Task GetSessionsForUsersAsync_Returns_No_List_Of_Sessions_If_UserId_List_Has_No_Sessions()
    {
        // Arrange
        var userIdList = new List<int> { 30, 40, 50, 60 };
        await SetUpTestRecord(8);
        await SetUpTestRecord(9);
        await SetUpTestRecord(10);
        await SetUpTestRecord(11);

        // Act     
        var userSessions = await _userSessionRepository.GetSessionsForUsersAsync(userIdList);

        // Assert
        Assert.False(userSessions.Any());
    }

    private async Task SetUpTestRecord(int seqNo)
    {
        var user = await _userHelper.AddUser($"User{seqNo}", $"Surname{seqNo}", $"user{seqNo}@email.com");
        await _userSessionHelper.AddUserSession(seqNo, $"1.1.1.{seqNo}", user);
    }
}
