﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>
	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="NLog" Version="5.4.0" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.4.0" />
		<PackageReference Include="Phlex.Core.MessageBus" Version="**********" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.AiAnalysis.Client\PharmaLex.VigiLit.AiAnalysis.Client.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.AiAnalysis.Service\PharmaLex.VigiLit.AiAnalysis.Service.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appSettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Client\" />
		<Folder Include="Fakes\" />
		<Folder Include="Service\" />
	</ItemGroup>

</Project>
