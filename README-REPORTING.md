# Introduction

VigiLit Reporting follows a loosely coupled plug-in style of development.

# Project Structure

```
├── src
│    └── PharmaLex.VigiLit.Reporting
│    └── PharmaLex.VigiLit.Reporting.Contracts
│    └── PharmaLex.VigiLit.Reports.{customer name/report type}.Domain
├── test
│    └── PharmaLex.VigiLit.Reporting.Tests
```


# Adding a new report

All types relating to the new report should be in the new project which is in a ```PharmaLex.VigiLit.Reports.{customer name/report type}.Domain``` namespace. 

Note: The Domain suffix is required so that the assembly is discovered by the PharmaLex.Caching package to cache the entity types. Currently this scans for assemblies with either
the .Domain or .Entities suffix. Using the latter would make an unsatisfactory namespace for non-entity types so .Domain is used. A update to the PharmaLex caching to use assembly attributes instead of a suffix would improve this.

The report project should reference the ```PharmaLex.VigiLit.Reporting.Contracts``` project and any 3rd party packages necessary. It should not reference any of the other VigiLit projects to keep it de-coupled.
Where ever possible all types should be of internal accessibility scope.

# Registering for dependency injection

It is not necessary to register types in the DI container at the application level. Instead a class should be defined which implements IConfigureServices and the project defined types registered there.

For example:

```
internal class ConfigureServices : IConfigureServices
{
    public void RegisterServices(IServiceCollection services)
    {
        services.AddScoped<IRepository, Repository>();
        services.AddScoped<IAccovionClientReportService, Service>();
    }
}
```

In the host application simply reference the ```PharmaLex.VigiLit.Reporting``` project and invoke the ```RegisterReporting``` extension method on the ```IServiceCollecton```. This will register the reporting services
and also call ```RegisterServices()``` for each of the report assemblies that it discovers.

