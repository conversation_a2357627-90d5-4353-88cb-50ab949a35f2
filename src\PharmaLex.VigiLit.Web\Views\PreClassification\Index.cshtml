@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;
@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.PreclassifyModel
@{
    ViewData["Title"] = "Pre-Classification";
}
<div id="preclassification" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2 v-if="selectedImport">Pre-Classification: {{selectedImport.importName}}</h2>
        <h2 v-else>Pre-Classification: No Import Selected</h2>
        <div v-if="activeTab == 'todo'" class="controls">
            <div v-if="displayPickButton">
                <button type="button" @@click="pickNext" :disabled="disablePickButton" class="btn-default">Pick Reference To PreClassify</button>
            </div>
            <div v-if="displayReleaseButton">
                <button type="button" @@click="release" :disabled="disableReleaseButton" class="btn-default">Release</button>
            </div>
            <div v-if="displayClassifyButton">
                <button type="button" @@click="classify" :disabled="disableClassifyButton" class="btn-default">Classify & Pick Next</button>
            </div>
        </div>
    </div>

    <div v-if="pickInProgress" class="in-progress-message"><div class="spinner round"></div> Picking...</div>
    <div v-if="releaseInProgress" class="in-progress-message"><div class="spinner round"></div> Releasing...</div>
    <div v-if="classifyInProgress" class="in-progress-message"><div class="spinner round"></div> Classifying & Picking...</div>

    <div v-if="isLockingErrorState" class="locking-error-message">Error: Lock not held. <a href="/PreClassification">Refresh this page</a>.</div>

    <section class="card-container">

        <ul class="tabs borderless" v-if="selectedImport">
            <li @@click="loadTodo" v-bind:class="{ active: activeTab == 'todo' }">
                <a @@click.prevent href="">To-Do</a>
                <span class="tab-badge">{{todoCount}}</span>
            </li>
            <li class="separator"></li>
            <li @@click="loadCompleted" v-bind:class="{ active: activeTab == 'completed' }">
                <a @@click.prevent href="">Completed</a>
                <span class="tab-badge completed">{{completedCount}}</span>
            </li>
        </ul>

        <div v-if="!selectedImport" class="no-import-selected">
            <i class="m-icon neutral-color">info</i>
            <h2>No Import Selected</h2>
        </div>

        <div v-if="todoCount == 0 && activeTab == 'todo' && importId > 0 " class="preClassificationCompleted">
            <i class="m-icon success-color">done</i>
            <h2>Pre-Classification completed.</h2>
        </div>

        <div v-if="pickReturnedNothing && todoCount > 0 && activeTab == 'todo'" class="pickReturnedNothing">
            <i class="m-icon success-color">done</i>
            <h2>
                Pre-Classification completed.<br />&nbsp;<br />
                <template v-if="todoCount > 1">The {{todoCount}} remaining classifications are locked to other pre-assessors.</template>
                <template v-else>The {{todoCount}} remaining classification is locked to another pre-assessor.</template>
            </h2>
        </div>

        <div v-if="referenceClassificationModels && activeTab == 'todo'">
            <div v-for="referenceClassificationModel in referenceClassificationModels">
                <div class="reference-container-row mb-2">
                    <reference-info :reference-classification="referenceClassificationModel.referenceClassification"
                                    :reference-update="referenceClassificationModel.referenceUpdate">
                    </reference-info>
                    <div class="reference-content">
                        <reference :reference="referenceClassificationModel.referenceClassification.reference"
                                   :reference-update="referenceClassificationModel.referenceUpdate"
                                   :reference-snapshot="referenceClassificationModel.referenceSnapshot"
                                   :substance="referenceClassificationModel.referenceClassification.substance">
                        </reference>
                    </div>
                    <div :class="['reference-classification', { 'ai-expanded': displayAiSuggestions }]">
                        <classification-form v-model:reference-classification="referenceClassificationModel.referenceClassification"
                                             :classification-categories="classificationCategories"
                                             :countries="countries"
                                             :classification-mode="'preclassification'"
                                             v-on:classify="classify"
                                             v-on:retryai="retryai"
                                             header="PreClassification"
                                             :classification-group-size="referenceClassificationModels.length"
                                             :display-ai-suggestions="displayAiSuggestions"
                                             :retry-in-progress="retryInProgress"
                                             v-model:ai-suggested-classification="referenceClassificationModel.aiSuggestedClassification">
                        </classification-form>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="completedClassifications && activeTab == 'completed'">
            <div v-for="completedClassification in completedClassifications" class="reference-container-row mb-2">
                <reference-info :reference-classification="completedClassification"></reference-info>
                <div class="reference-content">
                    <reference :reference="completedClassification.reference"
                               :substance="completedClassification.substance"></reference>
                </div>
                <div class="reference-classification">
                    <classification-form v-model:reference-classification="completedClassification"
                                         :classification-categories="classificationCategories"
                                         :countries="countries"
                                         :classification-mode="'repreclassification'"
                                         v-on:repreclassify="repreclassify"
                                         header="PreClassification">
                    </classification-form>
                </div>
            </div>
        </div>
    </section>

    <modal-dialog v-if="showAiChangedDialog"
                  width="600px"
                  height="150px"
                  title="Warning: AI Display Change"
                  v-on:confirm="confirmSeenAiToggleMessage">
        <p>{{aiSuggestionMessage}}</p>
    </modal-dialog>

</div>

@section VueComponentScripts {
    <partial name="Components/ClassificationDisplay" />
    <partial name="Components/ClassificationForm" />
    <partial name="Components/ReferenceComponent" />
    <partial name="Components/ReferenceInfo" />
    <partial name="Components/ModalDialog" />
}

@section Scripts {
    <script type="text/javascript">

        const aiAnalysisStatusTypes = {
            none: 0,
            awaitingResponse: 1,
            success: 2,
            failed: 3
        };

        let model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var pageConfig = {
            appElement: "#preclassification",
            data: () => {
                return {
                    activeTab: "todo",
                    selectedImport: null,
                    todoCount: 0,
                    completedCount: 0,
                    classificationCategories: [],
                    countries: [],
                    referenceClassificationModels: null,
                    displayAiSuggestions: false,
                    completedClassifications: [],
                    pickReturnedNothing: false,
                    importId: 0,
                    pickInProgress: false,
                    releaseInProgress: false,
                    classifyInProgress: false,
                    isLockingErrorState: false,
                    showAiChangedDialog: false,
                    retryInProgress: false
                };
            },
            computed: {
                inProgress() {
                    return this.pickInProgress || this.releaseInProgress || this.classifyInProgress || this.isLockingErrorState;
                },
                displayPickButton() {
                    return !this.referenceClassificationModels;
                },
                disablePickButton() {
                    if (this.inProgress) return true;
                    return this.todoCount <= 0;
                },
                displayReleaseButton() {
                    return this.referenceClassificationModels;
                },
                disableReleaseButton() {
                    if (this.inProgress) return true;
                    return false;
                },
                displayClassifyButton() {
                    return this.referenceClassificationModels;
                },
                disableClassifyButton() {
                    if (this.inProgress) return true;
                    let atLeastOneActive = false;
                    let allActiveAreValid = true;
                    let allAiDecisionsAreResolved = true;
                    let aiFailed = false;
                    for (i = 0; i < this.referenceClassificationModels.length; i++) {
                        if (this.referenceClassificationModels[i].referenceClassification.isActive) {
                            atLeastOneActive = true;
                            allActiveAreValid = allActiveAreValid && this.isValidClassification(this.referenceClassificationModels[i].referenceClassification);
                            allAiDecisionsAreResolved = allAiDecisionsAreResolved && this.aiDecisionsAreResolved(this.referenceClassificationModels[i]);
                            aiFailed = this.aiFailedOrAwaitingResponse(this.referenceClassificationModels[i]);
                        }
                    }

                    let decisionMadeWithoutAiSuggestion = allActiveAreValid && aiFailed;

                    return !(atLeastOneActive && allActiveAreValid && (allAiDecisionsAreResolved || decisionMadeWithoutAiSuggestion));
                }
            },
            methods: {
                release: function () {
                    this.releaseInProgress = true;
                    fetch(`/PreClassification/Release`, {
                        method: "POST",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(() => {
                            this.referenceClassificationModels = null;
                            plx.toast.show('Locks released.', 2, 'confirm', null, 2000);
                            this.releaseInProgress = false;
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                            this.releaseInProgress = false;
                        });
                },
                classify: function () {
                    this.classifyInProgress = true;
                    fetch(`/PreClassification/PreclassifyAndPickNext`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(this.referenceClassificationModels),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            this.classifyInProgress = false;
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {

                            this.loadSubsequentPageModel(data);

                            if (data && this.displayAiSuggestions != data.displayAiSuggestions) {
                                this.alertUserIfAiSwitchChanged(data);
                            }
                            else if (data && data.preclassifyReferenceModels) {
                                plx.toast.show('Changes saved. Ready for pre-classification.', 2, 'confirm', null, 2000);
                            }
                            else {
                                plx.toast.show('Changes saved.', 2, 'confirm', null, 2000);
                            }
                            this.classifyInProgress = false;
                        })
                        .catch(error => {
                            console.log(error);
                            if (error.status == 400) {
                                this.isLockingErrorState = true;
                            }
                            else {
                                plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                            }

                            this.classifyInProgress = false;
                        });
                },
                pickNext: function () {
                    this.pickInProgress = true;
                    fetch(`/PreClassification/PickNext`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            this.pickInProgress = false;
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {

                            this.loadSubsequentPageModel(data);

                            if (data && this.displayAiSuggestions != data.displayAiSuggestions) {
                                this.alertUserIfAiSwitchChanged(data);
                            }
                            else if (data && data.preclassifyReferenceModels) {
                                plx.toast.show('Ready for pre-classification.', 2, 'confirm', null, 2000);
                            }

                            this.pickInProgress = false;
                        })
                        .catch(error => {
                            console.log(error);
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                            this.pickInProgress = false;
                        });
                },
                loadTodo: function () {
                    this.activeTab = 'todo';
                },
                loadCompleted: function () {
                    this.activeTab = 'completed';
                    fetch('/PreClassification/GetPreclassifiedByCurrentUser', {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(data => data.json())
                        .then(classifications => {
                            this.completedClassifications = classifications;
                            this.completedClassifications.forEach(c => c.buttonDisabled = true);
                        });
                },
                repreclassify: function (classification) {
                    fetch(`/PreClassification/Repreclassify`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(classification),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(() => {
                        const index = this.completedClassifications.findIndex(c => c.id === classification.id);
                        if (index > -1) {
                            this.completedClassifications[index].buttonDisabled = true;
                        }
                        plx.toast.show('Classification updated.', 2, 'confirm', null, 2500);
                    });
                },
                retryai: function (classificationData) {

                    this.setRetryInProgressUi('wait');

                    fetch(`/PreClassification/RetryAiSuggestions`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(classificationData.referenceClassification),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then((data) => {
                            this.searchAiResultOnTimer(classificationData);
                        })
                        .catch(error => {
                            console.log(error);
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                            this.setRetryInProgressUi('cancel');
                        });
                },
                searchAiResultOnTimer: function (data) {

                    let referenceData = data;

                    let aiSuggestion = {};
                    let global = this;

                    let minutesTimeout = 3;
                    let stopTimeout = new Date();
                    stopTimeout.setMinutes(stopTimeout.getMinutes() + minutesTimeout);

                    let pollRepeatTimerSeconds = 10;
                    let exitTimerLoop = false;

                    const interval = setInterval(function (data) {

                        fetch(`/PreClassification/GetSuggestionAiForReferenceAndSubstance?sourceid=${referenceData.referenceClassification.reference.sourceId}&substance=${referenceData.referenceClassification.substance.name}`, {
                            method: "GET",
                            credentials: 'same-origin',
                            headers: {
                                "Content-Type": "application/json",
                                "RequestVerificationToken": token
                            }
                        })
                            .then((data) => {
                                return data.json();
                            })
                            .then((data) => {

                                if (data.status == aiAnalysisStatusTypes.success) {
                                    aiSuggestion = data;
                                    exitTimerLoop = true;
                                }
                            })
                            .catch(error => {
                                console.log(error);
                                global.setRetryInProgressUi('cancel');
                                plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                            });

                        if (stopTimeout < new Date()) {
                            clearInterval(interval);
                            global.setRetryInProgressUi('cancel');
                            plx.toast.show('The operation was unable to find a response in the allocated time. Please try again', 2, 'failed', null, 10000);
                        }

                        if (exitTimerLoop == true) {

                            global.updateUiWithAiSuggestion(aiSuggestion);
                            global.setRetryInProgressUi('cancel');
                            clearInterval(interval);

                        }
                    }, pollRepeatTimerSeconds * 1000);

                },
                setRetryInProgressUi(task) {
                    if (task == 'wait') {
                        document.body.style.cursor = "wait";
                        $("#retryBtn").attr("data-loading", "");
                    }
                    else{
                        document.body.style.cursor = "default";
                        $("#retryBtn").removeAttr('data-loading');
                    }
                },
                loadInitialPageModel: function (data) {
                    this.selectedImport = data.selectedImport;
                    this.todoCount = data.todoCount;
                    this.completedCount = data.completedCount;
                    this.classificationCategories = data.classificationCategories;
                    this.countries = data.countries;
                    this.referenceClassificationModels = data.preclassifyReferenceModels;
                    this.importId = data.selectedImport?.importId;
                    this.displayAiSuggestions = data.displayAiSuggestions;
                },
                loadSubsequentPageModel: function (data) {
                    this.selectedImport = data.selectedImport;
                    this.todoCount = data.todoCount;
                    this.completedCount = data.completedCount;
                    this.referenceClassificationModels = data.preclassifyReferenceModels;
                    this.pickReturnedNothing = !data.preclassifyReferenceModels;
                    this.importId = data.selectedImport?.importId;
                },
                alertUserIfAiSwitchChanged(data) {

                    let message = data.displayAiSuggestions ? 'Ai processing has recently been switched on, so AI suggestions will now be shown.' :
                        'Ai processing has recently been switched off, so AI suggestions will now not be shown.';

                    this.aiSuggestionMessage = message

                    this.displayAiSuggestions = data.displayAiSuggestions;

                    new Promise((claimResolve, claimReject) => {
                        this.showAiChangedDialog = true;

                        var confirm = new Promise((resolve, reject) => {
                            this.confirmResolve = resolve;
                        });

                        confirm.then(() => {
                            this.showAiChangedDialog = false;
                        }, claimReject)
                    });
                },
                confirmSeenAiToggleMessage() {
                    this.showAiChangedDialog = false;
                },
                updateUiWithAiSuggestion: function (data) {

                    let referenceClassification = this.referenceClassificationModels.find(x => x.referenceClassification.substance.name === data.substance);

                    referenceClassification.aiSuggestedClassification.category = data.category;
                    referenceClassification.aiSuggestedClassification.status = data.status;
                    referenceClassification.aiSuggestedClassification.categoryReason = data.categoryReason;
                    referenceClassification.aiSuggestedClassification.dosageForm = data.dosageForm;
                    referenceClassification.aiSuggestedClassification.dosageFormReason = data.dosageFormReason;

                    referenceClassification.aiSuggestedClassification.categoryId = this.getCategoryId(data.category);
                },
                getCategoryId: function (category) {
                    return this.classificationCategories.find(x => x.name.toLowerCase() == category.toLowerCase()).id;
                },
                isValidClassification: function (referenceClassification) {
                    return referenceClassification.classificationCategoryId > 0 && !!referenceClassification.dosageForm;
                },
                aiDecisionsAreResolved: function (model) {
                    return !model.aiSuggestedClassification || (model.aiSuggestedClassification.categoryDecision && model.aiSuggestedClassification.dosageFormDecision);
                },
                aiFailedOrAwaitingResponse: function (model) {
                    return model.aiSuggestedClassification && (model.aiSuggestedClassification.status == aiAnalysisStatusTypes.failed || model.aiSuggestedClassification.status == aiAnalysisStatusTypes.awaitingResponse);
                }
            },
            created() {
                this.loadInitialPageModel(model);
            }
        };
    </script>
}