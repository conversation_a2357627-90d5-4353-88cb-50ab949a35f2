# Introduction

VigiLit is a tool for tracking possible issues for drugs by monitoring medical literature. References are imported from external systems (only PubMed currently) and classified by internal users. Clients can then request full documentation for any cases which interest them. See a more complete introduction on the [VigiLit Wiki](https://phlexglobal.atlassian.net/wiki/spaces/VIG/overview?homepageId=**********).

# Project Structure

NOTE: This list is not exhaustive and under constant change and refactoring. The naming and layout discussion can be found on the [wiki](https://phlexglobal.atlassian.net/wiki/spaces/VIG/pages/**********/Architecture+Design+Principles+and+Patterns)

```
├── codegen
├── deploy
├── docs
├── Powershell
├── scripts
├── src
│   └── PharmaLex.Core.BlobStorage
│   └── PharmaLex.Core.Configuration
│   └── PharmaLex.Core.HealthCheck
│   └── PharmaLex.VigiLit.AccessControl
│   └── PharmaLex.VigiLit.Aggregators.Crossref
│   └── PharmaLex.VigiLit.Aggregators.PubMed
│   └── PharmaLex.VigiLit.AiAnalysis.Client
│   └── PharmaLex.VigiLit.AiAnalysis.Entities
│   └── PharmaLex.VigiLit.AiAnalysis.Service
│   └── PharmaLex.VigiLit.Application
│   └── PharmaLex.VigiLit.Application.Services
│   └── PharmaLex.VigiLit.Case
│   └── PharmaLex.VigiLit.Core.Aggregator
│   └── PharmaLex.VigiLit.Data.Repositories
│   └── PharmaLex.VigiLit.DataAccessLayer
│   └── PharmaLex.VigiLit.Domain
│   └── PharmaLex.VigiLit.Domain.Interfaces
│   └── PharmaLex.VigiLit.ImportApp
│   └── PharmaLex.VigiLit.Infrastructure
│   └── PharmaLex.VigiLit.Infrastructure.Data
│   └── PharmaLex.VigiLit.Infrastructure.Import.PubMed
│   └── PharmaLex.VigiLit.MessageBroker.Contracts
│   └── PharmaLex.VigiLit.Reporting
│   └── PharmaLex.VigiLit.Reporting.Contracts
│   └── PharmaLex.VigiLit.Ui.ViewModels
│   └── PharmaLex.VigiLit.Web
├── test
│   └── PharmaLex.VigiLit.AccessControl.Tests
│   └── PharmaLex.VigiLit.AiAnalysis.Integration.Tests
│   └── PharmaLex.VigiLit.AiAnalysis.Tests
│   └── PharmaLex.VigiLit.Application.Services.Tests
│   └── PharmaLex.VigiLit.Application.Tests
│   └── PharmaLex.VigiLit.Case.Tests
│   └── PharmaLex.VigiLit.Domain.Tests
│   └── PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests
│   └── PharmaLex.VigiLit.Infrastructure.Import.PubMed.Tests
│   └── PharmaLex.VigiLit.Infrastructure.IntegrationTests
│   └── PharmaLex.VigiLit.Infrastructure.Tests
│   └── PharmaLex.VigiLit.Reporting.Tests
│   └── PharmaLex.VigiLit.Test.Fakes
│   └── PharmaLex.VigiLit.Test.Framework
│   └── PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore
│   └── PharmaLex.VigiLit.Ui.ViewModels.Tests
│   └── PharmaLex.VigiLit.Web.Tests
```

# Running the application in local environment

The application requires an instance of PharmaLex.VigiLit.AiAnalysis.Service, Azurite, RabbitMQ and SQL Server running in docker.

# Menu.ps1

In \scripts there is a powershell script called "menu.ps1". This can be used to complete below local site setup tasks.

--- Options while running "menu.ps1" is as below:

# Development Main Menu

S.Local Setup  
└── A. Setup local environment

I.Infrastructure  
└── A.Build SQL Image

M.Generate Migration Script
T.Testing/QA
X.Exit

---

Basic core setup:

- (S) will run all that Yarn on the projects needed to and set the launch settings file up.
- Require I and A to set up SQL Server. 

IMPORTANT: VigiLit uses Full Text Search (FTS). The SQL image build via the menu will include FTS. If using images from other sources they may not include TFS.

NOTE: In previous iterations the infrastructure containers (SQL, Azurite, RabbitMQ etc) were ran independently. These are now included in the ```docker-compose``` project making it simpler to start them all up.

# Database deployment

- Requirements: dotnet sdk installed, dotnet-ef installed.
- Once SQL is running the VigiLitDb database will need to be deployed. From the menu choose "Generate Migration Script". In SMS open up the resultant script file and execute it against a new database.

# Run the website

- Add the PhlexGlobal NuGet feed to your package sources: https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/nuget/v3/index.json
- Configure visual studio to run multiple startup projects. Based on the feature we want to run add that project to start up.
- Run the application in Visual Studio.

- If you get an error regarding credentials for Nuget execute the following Powershell script:

  ```sh
  iex "& { $(irm https://aka.ms/install-artifacts-credprovider.ps1) } -AddNetfx"
  ```

- Run below comand if EF is not installed:

  ```sh
  dotnet tool install --global dotnet-ef 
    ```

# Configuration

Local development secrets are held in User Secrets. The projects that will need these configuring locally are as follows:

- PharmaLex.VigiLit.AiAnalysis.Service
- PharmaLex.VigiLit.ImportApp
- PharmaLex.VigiLit.Web

NOTE: The Ai Analysis Service runs within Docker Desktop and so the `server=` setting in the connection string should be `sqlserver`. For the projects that run outside of Docker it should be set to `local`
At present ImportApp and Web share the same secret settings. This may change.

Right one of the projects listed above and choose "Manage User Secrets". Copy and paste the following:

```
{
  "ConnectionStrings:default": "--- PUT YOUR CONNECTION STRING HERE ---",
  "ConnectionStrings:ServiceApiKey": "PharmaLex.VigiLit.AiAnalysis.Service"

  "MessageBus:TransportType": "RabbitMq",
  "MessageBus:RabbitMq:Username": "test",
  "MessageBus:RabbitMq:Password": "test",
  "MessageBus:ServiceApiKey": "PharmaLex.VigiLit.AiAnalysis.Service"

  "FeatureManagement:AiMessageBusSend","true"
  "FeatureManagement:DisplayAiSuggestions","true"

  "PhlexVision:Secret": "m1Q2e3T4z5A6k7R8b9C0xYwVuTsRq012",
  "PhlexVision:ConsumerKeyHeaderValue": "vigilit",
  "PhlexVision:OpenAiConfigId": "6981a1f6-828d-4928-a1ce-09a65aa0fc39",
  "PhlexVision:Url": "https://phlexvision-integration.phlexglobal.com/api/ocrapi/v1/documents",
  "PhlexVision:CallbackBaseUrl": "-- PUT TUNNEL URL HERE, SEE CREATING A TUNNEL, BELOW --/api/PhlexVisionApi/",
  "AzureStorage:ImportFileDocumentUpload:AccountName": "devstoreaccount1",
  "AzureStorage:ImportFileDocument:AccountName": "devstoreaccount1"
}
```

Note that projects that use User Secrets have the following in their project file so you only need to change one of them:

```<UserSecretsId>PharmaLex.VigiLit</UserSecretsId>```

VigiLit uses the shared CDN. The version of the CDN as with all PharmaLex products is set in the appsettings.json:

```
  "Static": {
    "App": "content",
    "Env": "v1.38.0",
    "Cdn": "https://phcgvcdn-endpoint.azureedge.net"
  }
```

The files in the CDN can be found here: https://phlexglobal.visualstudio.com/Pharmacovigilance%20Shared/_git/PharmaLex.Shared.CDN
The latest version number can be found by looking at the bottom of the "Download blob and get version" build task in the most recent `PharmaLex.Shared.CDN.Deploy` pipeline run.

# Creating a Tunnel
In order to convert and translate text from PDFs, as required for file import, VigiLit communicates with PhlexVision via a Cloudflare tunnel.
For VigiLit, this will use port 5001. To create a tunnel:
- Install cloudflared-windows-amd64.exe and create a console app in the same folder that it is installed.
- On the command line, run these instructions
- dotnet dev-certs https --trust
- cloudflared-windows-amd64.exe tunnel --url https://localhost:5001

A url should be displayed on the console window. The URL should replace "PUT TUNNEL URL HERE" in the secrets.
For example, if the console displays "https://peas-ja-scene-infringement.trycloudflare.com" then the full entry would be:
"PhlexVision:CallbackBaseUrl": "https://peas-ja-scene-infringement.trycloudflare.com/api/PhlexVisionApi/"

# Creating a migration

Currently the entire database is managed with one DbContext and one migration script.

To add a migration run the following command from the ```src\PharmaLex.VigiLit.Web``` folder
``` dotnet ef migrations add <<migration text>> --project ..\PharmaLex.VigiLit.Infrastructure.Data\```

As the codebase moves towards modular monolith this may change per the [tech docs](https://phlexglobal.atlassian.net/wiki/spaces/VIG/pages/4628185089/Module+Design)

# Migration Script Generation

The "Create Sql Scripts" step of the build pipeline has been known to fail. This step is close to the end of the approximately 40min build. To make it easier to test that it will work correctly an item on the Powershell menu can be used to run the EF migration code locally first.

This can be accessed by choosing option "M" from the Main Menu. This will attempt to generate the script in the C:\TEMP\Migrations folder. Running that script in SSMS and checking the schema can be done for verifying the migration.

# Project Structure

The project dependecy diagram can be found [here](./docs/VigilittProjectDependency.png) ![here](./docs/VigilitTestProjectDependency.png)

# Multiple Source Enablement
Note: May want to move this section into a separate README later

Code specific to an aggregator should be placed in the appropriate aggregator project e.g. ```PharmaLex.VigiLit.Aggregators.Crossref``` for Crossref, ```PharmaLex.VigiLit.Aggregators.PubMed``` for PubMed etc.
Where code can be re-used between aggregators this should be placed in ```PharmaLex.VigiLit.Core.Aggregator```. There is an expectation that code should conform to and have the behaviour defined in a contract at this level.
For the moment there is a Contracts folder and namespace however this could be subsequently moved to its own project if needed.
If the code is not specific to aggregators (i.e. used by the new MSE workflow) then that should be placed in a yet to be decided/created project.

Test projects exist for the above and tests should ideally be created for code as it is moved into the aggregator projects.


# Common issues

**"The API version 2024-XX-XX is not supported by Azurite" error when running DocumentServiceTests**

This could do with further investigation. Simplest fix appears to be to delete the executing Azurite container and remove the image. To remove the image:

- Type `docker images` on the command line
- Make a note of the Image Id for the Azurite image
- Type `docker rmi <Image Id>` on a command line substituting the image id
- Hit F5 to re-start docker-compose and start Azurite (it should re-download the image before starting)

**Solution fails to start with an Azure exception at CreateHostBuilder**
AzureVPN has disconnected

**Local dev web pages load without styles**
Ensure you have run `yarn build` in VigiLit\src\VigiLit.Web

**Ad-hoc import is not being processed by ImportApp**
Ensure you have clicked the 'Enqueue' button on the ad-hoc import page

**No references available to pre-classify**
Select an import on the home page

**Need to run scheduled tasks for testing**
Use the /tools page

**User Secrets not working in local development**
User Secrets only work when in development mode hence if you have admin rights set the ENV VAR.If not you have set it in the launchsettings.json of the Project -> Properties
example:

```
{
  "profiles": {
    "PharmaLex.VigiLit.ImportApp": {
      "commandName": "Project",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

**Exception with errorcode "CannotVerifyCopySource" raised while invoking copy blob operation in Azurite**
Update user secrets with below changes:
```
"AzureStorage:ImportFileDocumentUpload:AccountName": "devstoreaccount1",
"AzureStorage:ImportFileDocument:AccountName": "devstoreaccount1"
```
**I have a different or more complex issue**
Have a look at [Troubleshooting](https://phlexglobal.atlassian.net/wiki/spaces/VIG/pages/**********/Troubleshooting), document it there when you figure it out.