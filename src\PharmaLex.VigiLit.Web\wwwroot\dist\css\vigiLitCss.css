/*!********************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-4.use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-4.use[3]!./node_modules/sass-loader/dist/cjs.js!./src/scss/index.scss ***!
  \********************************************************************************************************************************************************************************************************/
/*


    YOU ARE VIEWING THE OUTPUT CSS FROM A CODE-GENERATION (VIA WEBPACK) PROCESS

    PLEASE DO NOT MODIFY THIS FILE AS CHANGES WILL BE OVERWRITTEN


*/
/*Units*/
/*Colours*/
/*Fonts*/
/*Space*/
/*This will apply the quality color to the brand variable, globally (within the context of this application).*/
html, body, p, select, input, th, td, label,
.pager, .no-records-container, button, a.button, .core-content ul li,
.autocomplete .autocomplete-items li {
  font-weight: normal;
}

input[type=text], input[type=url], input[type=email], input[type=search],
input[type=number], input[type=tel], input[type=date], input[type=password],
textarea, select, .fake-input, .fake-textarea {
  font: 13px Arial, Helvetica, Verdana, sans-serif;
  font-weight: normal;
}

span.preClassifierName {
  font-size: 17px;
  color: #461E96;
}

ul.tabs li span.tab-badge {
  font-size: 12px;
}

th, label, .chip {
  font-weight: bold;
}

.page-header, .sub-header {
  font-family: "cencora-gilroy", Arial, Helvetica, Verdana, sans-serif;
}

.sub-header h2 {
  font-size: 0.875rem;
  font-weight: 500;
}

h2.dashboard {
  font-size: 1.25rem;
  font-weight: 600;
}

.core-container > header a.app-title {
  min-width: 125px;
}

.nav-menu > li {
  font-size: 17px;
}
.nav-menu > li a {
  color: #1F394A;
}

.nav-menu > li .flyout li {
  font-size: 17px;
}

sub {
  vertical-align: sub !important;
  font-size: smaller !important;
}

sup {
  vertical-align: super !important;
  font-size: smaller !important;
}

input:disabled, select:disabled {
  opacity: 0.7;
}

select option.inactive {
  background: #DDD;
}

.buttons {
  margin-top: 1rem;
}

table thead {
  background-color: white;
  border-bottom: none;
}

table tr th {
  padding: 0.6rem 0.75rem;
}

table tr.selectable:hover td {
  background-color: #e9e9e9;
}

.pager {
  background-color: white;
  border-radius: 0.6rem;
}
.pager .page-size label {
  margin-bottom: 0;
}

table tr td {
  vertical-align: middle;
}

table {
  border-bottom: solid 2px #f0f7f7;
}

table tr:last-of-type {
  border-bottom: none;
}

table tbody tr:nth-child(odd) {
  background: #F5F5F5;
}

table th,
table td {
  padding: 0.6rem 0.75rem;
}

.data-table-actions {
  text-align: right;
}

.data-table-actions div {
  float: right;
}

.dataTable th {
  overflow: visible;
}

.input-validation-error {
  border: 1px solid #8f0101 !important;
}

span.validation-error {
  padding: 0;
}
span.validation-error span {
  font-size: 1rem;
  color: #8f0101;
}

.page-size {
  -webkit-box-flex: 0 !important;
      -ms-flex: 0 240px !important;
          flex: 0 240px !important;
}

.tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 2px solid #E8E8E8;
  margin-bottom: 1rem;
}
.tabs li {
  margin-bottom: -2px;
  margin-right: 1em;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  color: #3B3B3B;
  padding: 5px 0 10px 0;
}
.tabs li span {
  vertical-align: middle;
}
.tabs li span:first-child {
  margin-right: 7px;
}
.tabs li span:hover {
  color: #461E96;
  -webkit-transition-duration: 0.5s;
          transition-duration: 0.5s;
}
.tabs li a {
  vertical-align: middle;
  margin-right: 7px;
  color: #3B3B3B;
}
.tabs li a:hover {
  color: #461E96;
}
.tabs li.active {
  color: #461E96;
  padding-bottom: -2px;
  border-bottom: 2px solid #461E96;
  cursor: default;
}
.tabs li.active a {
  color: #211359;
}
.tabs li.active a:hover {
  color: #461E96;
}
.tabs li.separator {
  display: inline-block;
  border-right: 2px solid #CACACA;
  cursor: default;
  margin-top: 10px;
  margin-left: 3px;
}
.tabs li:last-of-type {
  margin-bottom: -2px;
}
.tabs .tab-badge {
  display: inline-block;
  line-height: 1rem;
  margin: 1px auto;
  text-align: center;
  border-radius: 15px;
  color: #fff;
  background-color: #461E96;
  padding: 5px 10px 4px;
}
.tabs .tab-badge.completed {
  background-color: #461E96;
}
.tabs .tab-badge:hover {
  color: #fff;
}
.tabs li.nolink {
  cursor: default;
}
.tabs li.nolink span:hover {
  color: #3B3B3B;
}
.tabs li.nolink span.tab-badge {
  color: #fff;
}

.tabs.borderless {
  border: none;
}
.tabs.borderless li.active {
  border: none;
}

.field-validation-error {
  color: #8f0101;
  padding-left: 0.8rem;
  line-height: 1.6rem;
}

.sub-nav {
  height: 35px;
  padding-top: 8px;
  padding-left: 10px;
}

.sub-nav li {
  float: left;
  margin-right: 22px;
}

.sub-nav a {
  color: #727272;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
}
.sub-nav a:hover {
  color: #461E96;
  -webkit-transition-duration: 0.5s;
          transition-duration: 0.5s;
}

.sub-nav a.active {
  color: #1E1E1E;
  border-bottom: 2px solid #461E96;
}

.sub-layout-header-buttons {
  margin-bottom: 1rem;
}

.core-content.has-background {
  background-image: url(/dist/assets/splash.jpg);
}

.z-index-1 {
  z-index: 1;
}

.chip {
  border-radius: 15px;
  background-color: #005C98;
  color: #fff;
  height: 25px;
  display: inline-block;
  font-weight: bold;
  margin: 0 10px 10px 0;
  padding: 0 0 0 10px;
  vertical-align: middle;
}

.chip span {
  position: relative;
  top: 1px;
  padding-right: 2px;
}

.chip i {
  position: relative;
  top: 5px;
  font-size: 1rem;
  margin-right: -10px;
}

.sub-header {
  margin-left: -2rem;
  margin-right: -2rem;
  margin-bottom: 1rem;
  position: sticky;
  top: -1rem;
  z-index: 99;
}

.core-content {
  padding-left: 30px;
  padding-right: 1.875rem;
  padding-top: 0;
}

.core-content section.card-container {
  background: none;
  padding-left: 0;
  padding-right: 0;
}
.core-content section.card-container .section-card {
  border-radius: 4px;
  -ms-flex-preferred-size: 30%;
      flex-basis: 30%;
  background-color: #fff;
  position: relative;
  padding: 1.5rem;
}
@media screen and (max-width: 1200px) {
  .core-content section.card-container .section-card.expand {
    -ms-flex-preferred-size: 40%;
        flex-basis: 40%;
  }
}

.flex-gap {
  gap: 1rem;
}

.flex-align-center label {
  margin-bottom: 0rem;
}

.version-label {
  background: #461E96;
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.full-width-rule {
  margin: 0 -1.5rem;
}

.switch-container.switch-active-inactive {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.switch-container.switch-active-inactive input.switch {
  margin: 0;
}
.switch-container.switch-active-inactive label.switch {
  text-indent: inherit;
  color: #fff;
  font-weight: 400;
  width: 80px;
  text-align: right;
  padding-top: 6px;
  padding-right: 0.5rem;
  margin: 0;
}
.switch-container.switch-active-inactive label.switch:before {
  content: "Inactive";
}
.switch-container.switch-active-inactive input.switch:checked + label:after {
  -webkit-transform: translateX(200%);
          transform: translateX(200%);
}
.switch-container.switch-active-inactive input.switch:checked + label:before {
  content: "Active";
  position: absolute;
  left: 0.75rem;
}

.switch-container.switch-enabled-disabled {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  margin-top: 0.75rem;
}
.switch-container.switch-enabled-disabled input.switch {
  margin: 0;
}
.switch-container.switch-enabled-disabled label.switch {
  text-indent: inherit;
  color: #fff;
  font-weight: 400;
  width: 80px;
  text-align: right;
  padding-top: 6px;
  padding-right: 0.3rem;
  margin: 0;
}
.switch-container.switch-enabled-disabled label.switch:before {
  content: "Disabled";
}
.switch-container.switch-enabled-disabled input.switch:checked + label:after {
  -webkit-transform: translateX(200%);
          transform: translateX(200%);
}
.switch-container.switch-enabled-disabled input.switch:checked + label:before {
  margin-right: 50px;
  content: "Enabled";
  position: absolute;
  left: 0.45rem;
}

/* Status Lozenge */
[class^=state-indicator-],
[class*=state-indicator-] {
  display: inline-block;
  width: 100%;
  line-height: 1.5rem;
  border-radius: 1rem;
  color: #fff;
  text-align: center;
  font-size: 0.75rem;
  min-width: 3.125rem;
  background-color: #461E96;
}

.state-indicator-new {
  background-color: #3B3B3B;
}

.state-indicator-updated {
  background-color: #C2551F;
  margin-bottom: 5px;
}

.state-indicator-preclassified {
  background-color: #761701;
}

.state-indicator-approved {
  background-color: #007F50;
}

.state-indicator-reclassified {
  background-color: #C2551F;
}

.state-indicator-signed {
  background-color: #461E96;
}

.state-indicator-inactive {
  background-color: #0073BE;
}

.support-reference {
  border: 1px solid #CACACA;
  border-radius: 10px;
  padding: 1rem;
}

.support-container {
  padding: 1rem;
  background: #fff;
}
.support-container li a {
  color: #6c767d;
}
.support-container li.active a {
  color: #127f92;
}
.support-container ul li.active {
  color: #461E96;
}

.support-lozenge {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 10px;
  margin-bottom: 1rem;
  cursor: pointer;
}
.support-lozenge.selected {
  border: 2px solid #461E96;
}
.support-lozenge.selectable {
  border: 2px solid #CACACA;
}
.support-lozenge div.lozenge-container {
  padding: 5px 20px 5px 5px;
}
.support-lozenge div.action-container {
  padding: 5px 5px 5px 0;
}
.support-lozenge .version-badge {
  display: inline-block;
  margin: 0.2rem auto;
  width: 2.2rem;
  line-height: 2.2rem;
  text-align: center;
  border-radius: 50%;
  color: #211359;
  background-color: rgba(70, 30, 150, 0.1);
}
.support-lozenge [class^=state-indicator-],
.support-lozenge [class*=state-indicator-] {
  width: 8rem;
  line-height: 30px;
}

#support-history-modal .modal-container {
  border-radius: 5px;
}
#support-history-modal .modal-header {
  border: none;
}
#support-history-modal input {
  height: 10vh;
}
#support-history-modal .dialog-custom-content div:nth-child(2) {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: auto;
}
#support-history-modal a {
  margin: 1.5rem 0 0 0.5rem;
}
#support-history-modal #dialog-closer {
  display: none;
}

.reference-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.reference-container .reference-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1rem;
}
.reference-container .reference-group label {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 12%;
          flex: 1 0 12%;
}
.reference-container .reference-group span,
.reference-container .reference-group a {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 88%;
          flex: 1 0 88%;
}

.reference-container-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  border: 1px solid #CACACA;
  border-radius: 0.6rem;
  background-color: #fff;
}
.reference-container-row > div {
  padding: 1rem;
}
.reference-container-row .reference-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 280px;
          flex: 0 0 280px;
  position: relative;
}
.reference-container-row .reference-info .reference-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  word-break: break-all;
}
.reference-container-row .reference-info .reference-group label {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 25%;
          flex: 1 0 25%;
}
.reference-container-row .reference-info .reference-group span {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 75%;
          flex: 1 0 75%;
}
.reference-container-row .reference-info .positionBottom {
  position: absolute;
  bottom: 1rem;
  display: block;
  width: 90%;
}
.reference-container-row .reference-info .positionBottom label {
  padding-right: 10px;
}
.reference-container-row .reference-content {
  -webkit-box-flex: 5;
      -ms-flex: 5 1 25rem;
          flex: 5 1 25rem;
  border-right: 1px solid #CACACA;
}
.reference-container-row .reference-content .reference-group {
  margin-bottom: 1rem;
}
.reference-container-row .reference-content label {
  display: block;
  margin-bottom: 0;
}

.reference-classification {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 350px;
          flex: 0 0 350px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.reference-classification .form-group:last-of-type {
  margin-bottom: 1.5rem;
}
.reference-classification.ai-expanded {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 370px;
          flex: 0 0 370px;
}
.reference-classification.ai-expanded .form-group.ai-suggestion, .reference-classification.ai-expanded .form-group.ai-status {
  margin-bottom: 10px;
}
.reference-classification .dosage-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.reference-classification .dosage-form div:first-child {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 30%;
          flex: 1 1 30%;
}
.reference-classification .dosage-form .dosage-form-badges {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 70%;
          flex: 1 1 70%;
  margin-left: 1rem;
}
.reference-classification .dosage-form .dosage-form-badges > span {
  display: inline-block;
  min-width: 1.5rem;
  height: 1.5rem;
  background-color: #0073BE;
  text-align: center;
  color: #fff;
  margin: 0 0 0.5rem 0.5rem;
  line-height: 1.5rem;
  padding: 0 1rem;
  border-radius: 0.9rem;
}
.reference-classification .dosage-form .dosage-form-badges > span:hover {
  cursor: pointer;
  opacity: 0.8;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
}
.reference-classification .dosage-form .dosage-form-badges.disabled > span {
  opacity: 0.5;
}
.reference-classification .dosage-form .dosage-form-badges.disabled > span:hover {
  cursor: default;
}
.reference-classification .ai-status i {
  font-size: 30px;
  width: 30px;
  cursor: default;
  opacity: 1;
}
.reference-classification ai-status i:hover {
  opacity: 1;
}
.reference-classification .ai-status div {
  margin-left: 5px;
  padding: 6px 12px 6px 12px;
  border-radius: 0px 8px 8px 8px;
}
.reference-classification .ai-status-awaiting-response div {
  background: #FFF8BA;
}
.reference-classification .ai-status-success div {
  background: #D8FEE7;
}
.reference-classification .ai-status-failed div {
  background: #FCD9D1;
}
.reference-classification .ai-suggestion.category.rejected {
  margin-bottom: 19px;
}
.reference-classification .ai-suggestion.pending > div {
  background: #E7F7FF;
}
.reference-classification .ai-suggestion.accepted > div {
  background: #D8FEE7;
}
.reference-classification .ai-suggestion.rejected > div {
  background: #F9F9F9;
}
.reference-classification .ai-suggestion.pending .header {
  background: #CEEDFC;
}
.reference-classification .ai-suggestion.accepted .header {
  background: #C4F5D8;
}
.reference-classification .ai-suggestion.rejected .header {
  background: #F5F5F5;
}
.reference-classification .ai-suggestion .header {
  width: 100%;
  padding: 10px 20px 10px 20px;
  border-radius: 8px 8px 0px 0px;
  border-bottom: 1px solid white;
}
.reference-classification .ai-suggestion .header .header-text {
  font-weight: bold;
  color: #461E96;
  line-height: 25px;
  width: 240px;
}
.reference-classification .ai-suggestion .header .header-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button {
  display: inline-block;
  width: 25px;
  height: 25px;
  text-align: center;
  margin-left: 5px;
  border: 1px solid #CACACA;
  border-radius: 5px;
  background: #FFF;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button:hover {
  background: #F5F5F5;
  cursor: pointer;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button i {
  font-size: 17px;
  position: relative;
  left: -2px;
  top: 3px;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button i:hover {
  opacity: 1;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled {
  opacity: 0.5;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled:hover {
  opacity: 0.5;
  background: #FFF;
  cursor: default;
}
.reference-classification .ai-suggestion .header .header-buttons .icon-button.disabled i:hover {
  cursor: default;
}
.reference-classification .ai-suggestion .header .header-buttons .undo-button {
  display: inline-block;
  position: relative;
  padding: 5px;
  margin-left: 20px;
  cursor: pointer;
}
.reference-classification .ai-suggestion .suggested-value {
  padding: 15px 20px;
  border-bottom: 1px solid white;
}
.reference-classification .ai-suggestion .suggested-value div:first-child {
  font-weight: bold;
}
.reference-classification .ai-suggestion .suggested-value div:last-child.value-box {
  background: white;
  border-radius: 5px;
  padding: 11px 15px 12px 15px;
  margin-top: 5px;
}
.reference-classification .ai-suggestion .suggested-value div:last-child .dosage-form {
  display: inline-block;
}
.reference-classification .ai-suggestion .suggested-value div:last-child .dosage-form .dosage-form-badges {
  margin-left: 0;
  margin-top: 10px;
}
.reference-classification .ai-suggestion .suggested-value div:last-child select, .reference-classification .ai-suggestion .suggested-value div:last-child input {
  background: white;
  margin-top: 5px;
}
.reference-classification .ai-suggestion .reasoning {
  padding: 15px 20px;
  border-bottom: 1px solid white;
}
.reference-classification .ai-suggestion .reasoning div:first-child {
  font-weight: bold;
}
.reference-classification .ai-suggestion .reasoning div:last-child {
  margin-top: 5px;
}
.reference-classification .ai-suggestion .previous-value {
  padding: 15px 20px;
  border-bottom: 1px solid white;
}
.reference-classification .ai-suggestion > div:last-child {
  border-radius: 0px 0px 8px 8px;
  padding-bottom: 15px;
}
.reference-classification .form-group.actions {
  height: 42px;
  margin-top: auto;
  width: 108%;
  text-align: right;
  border-top: 1px solid #CACACA;
  margin-left: -1rem;
  margin-right: -3rem;
  padding-top: 1rem;
}
.reference-classification .form-group.actions .preClassifierName {
  float: left;
  text-align: left;
  padding-left: 1rem;
  padding-top: 5px;
}
.reference-classification .form-group.actions button, .reference-classification .form-group.actions div {
  margin: 0 1rem;
}
.reference-classification .form-group.actions button .switch-container, .reference-classification .form-group.actions div .switch-container {
  position: relative;
  top: -2px;
  right: -8px;
}
.reference-classification .form-group.actions button .switch-container:focus, .reference-classification .form-group.actions div .switch-container:focus {
  border: 2px solid #111;
}
.reference-classification .separated {
  margin: 0 -1rem;
  padding: 1rem;
  border-top: 1px solid #CACACA;
}

.switch-container input.switch:checked + label {
  color: #fff;
}

.page-header .main-navigation-wrapper {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flyout-header.account {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.horizontal-filter {
  background-color: #EEE;
  padding: 1rem;
}
.horizontal-filter .input-group {
  margin-bottom: 1rem;
}
.horizontal-filter select {
  width: auto;
  background-color: white;
  margin-right: 10px;
}
.horizontal-filter .horizontal-filter-item {
  display: inline-block;
  margin: 0 10px 0 0;
  padding: 0;
  max-width: -webkit-min-content;
  max-width: -moz-min-content;
  max-width: min-content;
}
.horizontal-filter .horizontal-filter-item #btnLabelSearch {
  margin-top: 0.5rem;
}
.horizontal-filter .horizontal-filter-item .reset-button {
  background-color: #737373;
  -webkit-transition-duration: 0.5s;
          transition-duration: 0.5s;
}
.horizontal-filter .horizontal-filter-item .reset-button:hover {
  background-color: #454545;
}
.horizontal-filter .horizontal-filter-item select {
  margin: 0;
}
.horizontal-filter i {
  display: block;
  position: absolute;
  left: 0;
  top: 30px;
}
.horizontal-filter.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

abbr[title] {
  border-bottom: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

#substance #synonyms-column .form-group #addSynonym {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
#substance #synonyms-column .form-group #addBtn {
  margin-left: 10px;
}
#substance #synonyms-column #table-synonyms {
  border-bottom: none;
  max-width: 600px;
  margin-bottom: 1rem;
}
#substance #synonyms-column #table-synonyms tbody tr td .m-icon {
  float: right;
}
#substance #synonyms-column #table-synonyms tbody tr:nth-child(odd) {
  background-color: #F5F5F5;
}

.table-filter-items input[type=search] {
  padding-right: 12px;
}
.table-filter-items .m-icon.close {
  opacity: 1;
}

.emailBounceText {
  height: 280px;
}

.emailSuppressTableText {
  color: #fff;
  margin-left: 30px;
  padding: 5px;
  border-radius: 5px;
}

.email-bounce-background {
  background-color: #E22F00;
}

.email-block-background {
  background-color: #FBC618;
}

.email-spam-background {
  background-color: #3B3B3B;
}

.email-bounce-foreground {
  color: #E22F00;
}

.email-block-foreground {
  color: #FBC618;
}

.email-spam-foreground {
  color: #3B3B3B;
}

#company-user-edit .suppression-reason {
  border: 1px solid #c4c4c4;
  opacity: 0.7;
  border-radius: 0.5rem;
  background: #f3f6f9;
  padding: 0.75rem;
  margin-bottom: 1.5rem;
  height: 175px;
  overflow-y: scroll;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.pointer-events-none {
  pointer-events: none;
}

.disabled {
  opacity: 0.7;
}

#dashboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 25px;
     -moz-column-gap: 25px;
          column-gap: 25px;
  margin: 15px auto;
}
#dashboard #import-dashboard-container {
  margin-bottom: 30px;
}
#dashboard #import-dashboard-container.narrow {
  width: 680px;
}
#dashboard #import-dashboard-container.wide {
  max-width: 1340px;
}
#dashboard #import-dashboard-container.wide.empty {
  min-width: 680px;
}
#dashboard #import-dashboard-container #import-dashboard .content {
  background: white;
  border-radius: 10px;
  padding: 20px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 20px;
     -moz-column-gap: 20px;
          column-gap: 20px;
  row-gap: 10px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card {
  border: 1px solid #C4C4C4;
  border-radius: 10px;
  padding: 0;
  width: 310px;
  line-height: normal;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  width: 100%;
  padding: 16px 20px;
  height: 96px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text {
  width: 36%;
  text-align: left;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type {
  font-size: 17px;
  font-weight: bold;
  text-transform: uppercase;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type.scheduled {
  color: #0073BE;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-type.adhoc {
  color: #461E96;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-date {
  font-size: 14px;
  color: #1F394A;
  padding-top: 5px;
  padding-bottom: 7px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .text .import-status {
  font-size: 12px;
  font-weight: bold;
  color: #000000;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number {
  width: 64%;
  text-align: right;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number .todo-count-label {
  font-size: 12px;
  color: #1F394A;
  padding-top: 2px;
  padding-bottom: 5px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .info .number .todo-count-number {
  font-size: 38px;
  font-weight: bold;
  color: #0391D7;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  border-top: 1px solid #C4C4C4;
  width: 100%;
  background: transparent;
  padding: 10px 20px;
  margin: 0;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons button, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons a.button {
  padding: 8px 16px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons button:focus, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons a.button:focus {
  border: 2px solid black !important;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons button:focus, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons a.button:focus {
  padding: 7px 15px;
  -webkit-transition-duration: 0s;
          transition-duration: 0s;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .left {
  text-align: left;
  width: 30%;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right {
  text-align: right;
  width: 70%;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button {
  margin-right: 5px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button {
  background: white;
  color: black;
  border: 1px solid black;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button:hover, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button:hover {
  background: #1f394a;
  color: white;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .details-button:disabled, #dashboard #import-dashboard-container #import-dashboard .content .cards .card .buttons .right .archive-button:disabled {
  background: white;
  color: black;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected {
  border: 3px solid #007F50;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .info {
  padding: 14px 18px 16px;
  height: 94px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .info .text .import-type {
  color: #028930;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .buttons {
  padding: 10px 18px 8px;
}
#dashboard #import-dashboard-container #import-dashboard .content .cards .card.selected .buttons .deselect-button {
  background: #028930;
}
#dashboard #import-dashboard-container #import-dashboard .content .empty p {
  margin: 0;
}
#dashboard #email-dashboard-container {
  width: 680px;
  margin-bottom: 10px;
}
#dashboard #email-dashboard-container #email-dashboard .content {
  background: white;
  border-radius: 10px;
  padding: 20px;
}
#dashboard #email-dashboard-container #email-dashboard .content .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  -webkit-column-gap: 20px;
     -moz-column-gap: 20px;
          column-gap: 20px;
  margin-bottom: 20px;
}
#dashboard #email-dashboard-container #email-dashboard .content .cards .card {
  border: 1px solid #C4C4C4;
  border-radius: 10px;
  width: 200px;
  text-align: center;
  padding: 40px 30px;
  line-height: normal;
}
#dashboard #email-dashboard-container #email-dashboard .content .cards .card .text {
  font-size: 16px;
  font-weight: bold;
}
#dashboard #email-dashboard-container #email-dashboard .content .cards .card .value {
  font-size: 70px;
  color: #572F8C;
  font-weight: bold;
}
#dashboard #email-dashboard-container #email-dashboard .content #table-container {
  border: 1px solid #C4C4C4;
  border-radius: 10px;
}
#dashboard #email-dashboard-container #email-dashboard .content #table-container table {
  background: transparent;
}
#dashboard #email-dashboard-container #email-dashboard .content #table-container table thead {
  background: transparent;
}
#dashboard #email-dashboard-container #email-dashboard .content #table-container table td, #dashboard #email-dashboard-container #email-dashboard .content #table-container table th {
  padding: 15px 20px 14px;
}
#dashboard #email-dashboard-container #email-dashboard .content #table-container .pager {
  padding-left: 6px;
  padding-right: 6px;
}
#dashboard #email-dashboard-container #build-info {
  margin-top: 30px;
}
#dashboard #email-dashboard-container #build-info .content {
  background: white;
  border-radius: 10px;
  padding: 20px;
}
#dashboard #email-dashboard-container #build-info p:last-of-type {
  margin-bottom: 0;
}

#reference-details-container {
  /* Reference History actions status styling */
}
#reference-details-container #reference-substance-section, #reference-details-container #reference-history-section {
  border: solid 1px #c4c4c4;
  border-radius: 5px;
  padding: 20px;
}
#reference-details-container #reference-substance-section li:hover, #reference-details-container #reference-history-section li:hover {
  color: #461E96;
}
#reference-details-container #substance-list {
  cursor: pointer;
  border: 2px solid #c4c4c4;
  border-radius: 5px;
  padding: 10px;
}
#reference-details-container #substance-list span.substance-name {
  font-weight: bold;
}
#reference-details-container #substance-list span.plxid {
  display: inline-block;
  padding-top: 5px;
  font-size: 11px;
}
#reference-details-container .reference-details-section {
  border: solid 1px #c4c4c4;
  border-radius: 5px;
  padding: 0;
}
#reference-details-container #reference-details-info {
  border-bottom: solid 1px #c4c4c4;
  padding: 10px 10px 0;
}
#reference-details-container #reference-details-info label {
  padding: 10px 6px 10px 10px;
}
#reference-details-container #reference-details-info span {
  margin-right: 16px;
}
#reference-details-container #reference-details-content {
  padding: 20px;
}
#reference-details-container #reference-details-content .form-group-display {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
  margin-bottom: 1rem;
}
#reference-details-container #reference-details-content .form-group-display label {
  -ms-flex-preferred-size: 40%;
      flex-basis: 40%;
}
#reference-details-container #classification-section {
  border-left: solid 1px #c4c4c4;
  padding: 20px;
  min-width: 250px;
}
#reference-details-container #classification-section .tile {
  padding: 0;
}
#reference-details-container #classification-section .form-group-display {
  margin-bottom: 1rem;
}
#reference-details-container #classification-section .reason-for-change {
  display: block;
  width: 200px;
  word-wrap: break-word;
}
#reference-details-container #classification-section .reference-classification {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  width: 350px;
}
#reference-details-container #classification-section .reference-classification label {
  -ms-flex-preferred-size: initial;
      flex-basis: initial;
}
#reference-details-container .state-indicator-update, #reference-details-container .state-indicator-silent {
  background-color: #C2551F;
}

#import-dashboard-details {
  /* Clear floats after the columns */
}
#import-dashboard-details .row {
  border-bottom: 1px solid #EEE;
  margin-bottom: 10px;
}
#import-dashboard-details .column {
  float: left;
  width: 33.33%;
}
#import-dashboard-details .centered {
  text-align: center;
}
#import-dashboard-details .row:after {
  content: "";
  display: table;
  clear: both;
}
#import-dashboard-details .paginator span {
  margin-right: 10px;
}
#import-dashboard-details .show-contract-icon {
  font-family: "MaterialIconsOutlined", sans-serif;
  font-size: 1.6rem;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
}

#locks #locks-dashboard {
  background-color: transparent;
  background-color: initial;
  padding-left: 0;
  padding-right: 0;
}
#locks #locks-dashboard .cards {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-column-gap: 1rem;
  -webkit-column-gap: 1rem;
     -moz-column-gap: 1rem;
          column-gap: 1rem;
  grid-row-gap: 1rem;
  row-gap: 1rem;
}
@media screen and (max-width: 1200px) {
  #locks #locks-dashboard .cards {
    grid-template-columns: repeat(5, 1fr);
  }
}
#locks #locks-dashboard .cards .card {
  padding: 20px 20px 5px 20px;
  border-radius: 4px;
  font-size: 15px;
  width: 100%;
  background: white;
}
#locks #locks-dashboard .cards .card p {
  font-weight: 400;
}
#locks #locks-dashboard .cards .card .flex-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  width: 100%;
  margin-top: 30px;
}
#locks #locks-dashboard .cards .card .flex-container .locksCount {
  width: 50%;
  margin-bottom: 15px;
  font-size: 40px;
}
#locks #locks-dashboard .cards .card .flex-container .release-button {
  width: 50%;
  text-align: right;
  margin-bottom: 15px;
}
#locks #locks-dashboard .cards .card .flex-container .release-button button {
  background-color: #FBC618;
  color: #1E1E1E;
}
#locks #locks-dashboard .cards .card .flex-container .release-button button:hover, #locks #locks-dashboard .cards .card .flex-container .release-button button:active {
  background-color: #3B3B3B;
}
#locks #locks-dashboard p {
  margin-bottom: 0;
}

.no-import-selected {
  padding: 30px 20px 20px;
  margin: 0 auto;
  width: 400px;
}
.no-import-selected i {
  font-size: 30px;
  display: inline-block;
  padding-right: 40px;
  font-weight: bold;
}
.no-import-selected h2 {
  display: inline-block;
  margin: 0;
  position: relative;
  top: -5px;
}

.preClassificationCompleted {
  padding: 30px 20px 20px;
  margin: 0 auto;
  width: 400px;
}
.preClassificationCompleted i {
  font-size: 30px;
  display: inline-block;
  padding-right: 40px;
  font-weight: bold;
}
.preClassificationCompleted h2 {
  display: inline-block;
  margin: 0;
  font-size: 20px;
  position: relative;
  top: -5px;
}

.pickReturnedNothing {
  padding: 30px 20px 20px;
  margin: 0 auto;
  width: 700px;
}
.pickReturnedNothing i {
  font-size: 30px;
  display: inline-block;
  padding-right: 40px;
  font-weight: bold;
  position: relative;
  top: -45px;
}
.pickReturnedNothing h2 {
  display: inline-block;
  margin: 0;
  font-size: 20px;
  position: relative;
  top: -5px;
}

.classification-completed, .classification-signed {
  padding: 30px 20px 20px;
  margin: 0 auto;
  width: 400px;
}
.classification-completed i, .classification-signed i {
  font-size: 30px;
  display: inline-block;
  padding-right: 40px;
  font-weight: bold;
}
.classification-completed h2, .classification-signed h2 {
  display: inline-block;
  margin: 0;
  position: relative;
  top: -5px;
}

.new-company-project-header, .case-upload-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 1rem 2rem 0 2rem;
  border-bottom: 1px solid #E8E8E8;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.new-company-project-header h2, .case-upload-header h2 {
  font-weight: 600;
}
.new-company-project-header .case-status, .case-upload-header .case-status {
  background-color: #026fa8;
  color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-top: 0px;
  font-size: 1rem;
  font-weight: 400;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}
.new-company-project-header .case-status.pending, .case-upload-header .case-status.pending {
  background-color: #bb5c20;
}

/* Case Upload Popup */
.case-upload-form-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  border-right: 1px solid #e5e5e5;
  gap: 1rem;
  width: 40%;
  padding: 1rem 2rem 0 2rem;
}

.case-upload-substance-wrapper .case-upload-label {
  margin-bottom: 0;
}

.case-upload-divider {
  height: 2px;
  border-top: 1px solid #e5e5e5;
  margin: 0.8rem -2rem;
}
.case-upload-divider.first-substance-divider {
  margin-top: 3px;
}
.case-upload-divider.last-substance-divider {
  margin-bottom: 3px;
}

.case-upload-form-right {
  padding: 2rem;
}

.case-upload-content {
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.case-upload-input {
  -ms-flex-item-align: stretch;
      align-self: stretch;
  position: relative;
}

.case-upload-input {
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #c4c4c4;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 8px 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  gap: 10px;
  font-size: var(--body-main-body-size);
}

.case-upload-psur-dropdown-wrapper, .case-upload-mlm-dropdown-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.case-upload-mlm-dropdown-wrapper {
  right: 70%;
  left: 17%;
}

.case-upload-comment-wrapper textarea {
  resize: none;
  margin-bottom: 1rem;
}

.case-upload-company-dropdown-wrapper {
  min-height: 50px;
}

.case-upload-company-dropdown-wrapper #CompanyLabelNoPlxId,
.case-upload-company-dropdown-wrapper #CompanyLabelInvalidPlxId,
.case-upload-company-dropdown-wrapper #CompanyLabelNoCompany {
  display: block;
  color: #AD0000;
  font-weight: normal;
}

.case-upload-dropdown-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 1rem;
  padding-top: 0.5rem;
}

.case-upload-plxid-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  font-family: var(--body-main-body);
  -ms-flex-item-align: stretch;
      align-self: stretch;
  padding-top: 1rem;
}

.case-upload-substance-wrapper {
  font-family: var(--body-main-body);
}

.case-upload-substance-wrapper label:first-of-type {
  margin-right: 61px;
}

.case-upload-substance-break-top, .case-upload-substance-break-bottom {
  position: absolute;
}

.case-upload-substance-break-top {
  height: 0%;
  width: 40.8%;
  top: 21%;
  left: -1.7%;
  border-top: 1px solid #e5e5e5;
}

.case-upload-substance-break-bottom {
  width: 40.8%;
  top: 14%;
  left: -1.7%;
  border-top: 1px solid #e5e5e5;
}

.case-upload-filedrop-wrapper {
  position: absolute;
  height: 24%;
  width: 56%;
  top: 5%;
  right: 2%;
  bottom: 61%;
  left: 42%;
  text-align: center;
}

.case-upload-file-item {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1rem;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: 1px solid #CACACA;
  padding: 0.3rem 0;
  margin: 0;
}
.case-upload-file-item:first-of-type {
  border-top: 1px solid #CACACA;
}

.case-upload-file-options {
  font-family: "MaterialIconsOutlined", sans-serif;
  font-size: 1.75rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 0.5rem;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.case-upload-file-options div {
  padding: 0.25rem;
  cursor: pointer;
}
.case-upload-file-options div.disabled {
  opacity: 0.5;
  pointer-events: none;
}
.case-upload-file-options .case-upload-file-delete {
  padding-bottom: 0.4rem;
  padding-right: 0rem;
}

.flex.flex-nowrap.justify-end i.m-icon {
  margin-left: 3px;
}

.table-row.delete-highlighted {
  background-color: #ffdede;
}

#modal-dialog-case-delete p {
  margin-bottom: 15px;
  font-size: 1rem;
}
#modal-dialog-case-delete #dialog-closer {
  position: absolute;
  right: 5px;
  top: 10px;
}

#caseCommentInput {
  min-height: 82px;
}

/* Button Spinner Animation */
.btn .spinner { /* IE 9 */
  -webkit-transform: scale(0.7, 0.7); /* Safari */
  transform: scale(0.7, 0.7);
  position: absolute;
  width: 32px;
  height: 32px;
  top: 50%;
  margin-top: -16px;
  opacity: 0;
  background-image: url(data:image/gif;base64,R0lGODlhIAAgAPMAAJmZmf///6+vr8nJybW1tcDAwOjo6Nvb26ioqKOjo7Ozs/Ly8vz8/AAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAIAAgAAAE5xDISWlhperN52JLhSSdRgwVo1ICQZRUsiwHpTJT4iowNS8vyW2icCF6k8HMMBkCEDskxTBDAZwuAkkqIfxIQyhBQBFvAQSDITM5VDW6XNE4KagNh6Bgwe60smQUB3d4Rz1ZBApnFASDd0hihh12BkE9kjAJVlycXIg7CQIFA6SlnJ87paqbSKiKoqusnbMdmDC2tXQlkUhziYtyWTxIfy6BE8WJt5YJvpJivxNaGmLHT0VnOgSYf0dZXS7APdpB309RnHOG5gDqXGLDaC457D1zZ/V/nmOM82XiHRLYKhKP1oZmADdEAAAh+QQJCgAAACwAAAAAIAAgAAAE6hDISWlZpOrNp1lGNRSdRpDUolIGw5RUYhhHukqFu8DsrEyqnWThGvAmhVlteBvojpTDDBUEIFwMFBRAmBkSgOrBFZogCASwBDEY/CZSg7GSE0gSCjQBMVG023xWBhklAnoEdhQEfyNqMIcKjhRsjEdnezB+A4k8gTwJhFuiW4dokXiloUepBAp5qaKpp6+Ho7aWW54wl7obvEe0kRuoplCGepwSx2jJvqHEmGt6whJpGpfJCHmOoNHKaHx61WiSR92E4lbFoq+B6QDtuetcaBPnW6+O7wDHpIiK9SaVK5GgV543tzjgGcghAgAh+QQJCgAAACwAAAAAIAAgAAAE7hDISSkxpOrN5zFHNWRdhSiVoVLHspRUMoyUakyEe8PTPCATW9A14E0UvuAKMNAZKYUZCiBMuBakSQKG8G2FzUWox2AUtAQFcBKlVQoLgQReZhQlCIJesQXI5B0CBnUMOxMCenoCfTCEWBsJColTMANldx15BGs8B5wlCZ9Po6OJkwmRpnqkqnuSrayqfKmqpLajoiW5HJq7FL1Gr2mMMcKUMIiJgIemy7xZtJsTmsM4xHiKv5KMCXqfyUCJEonXPN2rAOIAmsfB3uPoAK++G+w48edZPK+M6hLJpQg484enXIdQFSS1u6UhksENEQAAIfkECQoAAAAsAAAAACAAIAAABOcQyEmpGKLqzWcZRVUQnZYg1aBSh2GUVEIQ2aQOE+G+cD4ntpWkZQj1JIiZIogDFFyHI0UxQwFugMSOFIPJftfVAEoZLBbcLEFhlQiqGp1Vd140AUklUN3eCA51C1EWMzMCezCBBmkxVIVHBWd3HHl9JQOIJSdSnJ0TDKChCwUJjoWMPaGqDKannasMo6WnM562R5YluZRwur0wpgqZE7NKUm+FNRPIhjBJxKZteWuIBMN4zRMIVIhffcgojwCF117i4nlLnY5ztRLsnOk+aV+oJY7V7m76PdkS4trKcdg0Zc0tTcKkRAAAIfkECQoAAAAsAAAAACAAIAAABO4QyEkpKqjqzScpRaVkXZWQEximw1BSCUEIlDohrft6cpKCk5xid5MNJTaAIkekKGQkWyKHkvhKsR7ARmitkAYDYRIbUQRQjWBwJRzChi9CRlBcY1UN4g0/VNB0AlcvcAYHRyZPdEQFYV8ccwR5HWxEJ02YmRMLnJ1xCYp0Y5idpQuhopmmC2KgojKasUQDk5BNAwwMOh2RtRq5uQuPZKGIJQIGwAwGf6I0JXMpC8C7kXWDBINFMxS4DKMAWVWAGYsAdNqW5uaRxkSKJOZKaU3tPOBZ4DuK2LATgJhkPJMgTwKCdFjyPHEnKxFCDhEAACH5BAkKAAAALAAAAAAgACAAAATzEMhJaVKp6s2nIkolIJ2WkBShpkVRWqqQrhLSEu9MZJKK9y1ZrqYK9WiClmvoUaF8gIQSNeF1Er4MNFn4SRSDARWroAIETg1iVwuHjYB1kYc1mwruwXKC9gmsJXliGxc+XiUCby9ydh1sOSdMkpMTBpaXBzsfhoc5l58Gm5yToAaZhaOUqjkDgCWNHAULCwOLaTmzswadEqggQwgHuQsHIoZCHQMMQgQGubVEcxOPFAcMDAYUA85eWARmfSRQCdcMe0zeP1AAygwLlJtPNAAL19DARdPzBOWSm1brJBi45soRAWQAAkrQIykShQ9wVhHCwCQCACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiRMDjI0Fd30/iI2UA5GSS5UDj2l6NoqgOgN4gksEBgYFf0FDqKgHnyZ9OX8HrgYHdHpcHQULXAS2qKpENRg7eAMLC7kTBaixUYFkKAzWAAnLC7FLVxLWDBLKCwaKTULgEwbLA4hJtOkSBNqITT3xEgfLpBtzE/jiuL04RGEBgwWhShRgQExHBAAh+QQJCgAAACwAAAAAIAAgAAAE7xDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfZiCqGk5dTESJeaOAlClzsJsqwiJwiqnFrb2nS9kmIcgEsjQydLiIlHehhpejaIjzh9eomSjZR+ipslWIRLAgMDOR2DOqKogTB9pCUJBagDBXR6XB0EBkIIsaRsGGMMAxoDBgYHTKJiUYEGDAzHC9EACcUGkIgFzgwZ0QsSBcXHiQvOwgDdEwfFs0sDzt4S6BK4xYjkDOzn0unFeBzOBijIm1Dgmg5YFQwsCMjp1oJ8LyIAACH5BAkKAAAALAAAAAAgACAAAATwEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GGl6NoiPOH16iZKNlH6KmyWFOggHhEEvAwwMA0N9GBsEC6amhnVcEwavDAazGwIDaH1ipaYLBUTCGgQDA8NdHz0FpqgTBwsLqAbWAAnIA4FWKdMLGdYGEgraigbT0OITBcg5QwPT4xLrROZL6AuQAPUS7bxLpoWidY0JtxLHKhwwMJBTHgPKdEQAACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GAULDJCRiXo1CpGXDJOUjY+Yip9DhToJA4RBLwMLCwVDfRgbBAaqqoZ1XBMHswsHtxtFaH1iqaoGNgAIxRpbFAgfPQSqpbgGBqUD1wBXeCYp1AYZ19JJOYgH1KwA4UBvQwXUBxPqVD9L3sbp2BNk2xvvFPJd+MFCN6HAAIKgNggY0KtEBAAh+QQJCgAAACwAAAAAIAAgAAAE6BDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfYIDMaAFdTESJeaEDAIMxYFqrOUaNW4E4ObYcCXaiBVEgULe0NJaxxtYksjh2NLkZISgDgJhHthkpU4mW6blRiYmZOlh4JWkDqILwUGBnE6TYEbCgevr0N1gH4At7gHiRpFaLNrrq8HNgAJA70AWxQIH1+vsYMDAzZQPC9VCNkDWUhGkuE5PxJNwiUK4UfLzOlD4WvzAHaoG9nxPi5d+jYUqfAhhykOFwJWiAAAIfkECQoAAAAsAAAAACAAIAAABPAQyElpUqnqzaciSoVkXVUMFaFSwlpOCcMYlErAavhOMnNLNo8KsZsMZItJEIDIFSkLGQoQTNhIsFehRww2CQLKF0tYGKYSg+ygsZIuNqJksKgbfgIGepNo2cIUB3V1B3IvNiBYNQaDSTtfhhx0CwVPI0UJe0+bm4g5VgcGoqOcnjmjqDSdnhgEoamcsZuXO1aWQy8KAwOAuTYYGwi7w5h+Kr0SJ8MFihpNbx+4Erq7BYBuzsdiH1jCAzoSfl0rVirNbRXlBBlLX+BP0XJLAPGzTkAuAOqb0WT5AH7OcdCm5B8TgRwSRKIHQtaLCwg1RAAAOwAAAAAAAAAAAA==);
}

.btn,
.btn .spinner,
.btn .btn-label {
  -webkit-transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;
  transition: 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) all;
}

.btn.btn-spinner {
  overflow: hidden;
  position: relative;
}

.btn.btn-spinner span {
  display: inherit;
  margin: inherit;
  color: inherit;
  font-weight: inherit;
  font-size: inherit;
}

.btn.btn-spinner .btn-label {
  position: relative;
  margin: 0;
}

.btn.btn-spinner .spinner {
  left: 50%;
  margin-left: -16px;
  margin-top: 1em;
}

.btn.btn-spinner[data-loading] .btn-label {
  opacity: 0;
  margin: 0;
}

.btn.btn-spinner[data-loading] .spinner {
  opacity: 1;
  margin-top: -16px;
}

.file-drop-loading-wrapper {
  position: absolute;
  left: 178px;
  top: 45px;
}

.file-drop-loading-wrapper div {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  float: left;
  margin: 0 3px;
  background: #E1F5F9;
  -webkit-transform: scale(0);
          transform: scale(0);
}

.file-drop-loading-wrapper .file-drop-loading-ball1 {
  z-index: 1;
  -moz-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation: grow 4.4s infinite ease-in-out;
  opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball2 {
  -moz-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
  opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball3 {
  -moz-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball4 {
  -moz-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation: grow 4.4s infinite ease-in-out;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  opacity: 0.8;
}
@-webkit-keyframes grow {
  0% {
    -webkit-transform: scale(0);
  }
  25% {
    -webkit-transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0);
  }
  75% {
    -webkit-transform: scale(1);
    background: #72c2c2;
  }
  100% {
    -webkit-transform: scale(0);
    background: #72c2c2;
  }
}
.case-upload-file-list {
  margin-top: 19px;
  text-align: left;
}

.case-upload-file-list-item {
  border-bottom: 1px solid #E5E5E5;
  padding: 5px 0px 5px 0px;
}

.file-drop-area {
  border: 2px dashed #C4C4C4;
  border-radius: 0.75rem;
  position: relative;
  height: 117px;
  width: 480px;
  max-width: 100%;
  margin: 0 auto;
  padding: 26px 20px 30px;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.file-drop-area.disabled {
  background: #fbfbfb;
  border: 2px dashed #dbdbdb;
  cursor: not-allowed;
}

.file-drop-area.is-active {
  background-color: #E1F5F9;
  border: 2px dashed #008489;
}

.file-drop-area.dropping {
  border: none;
}

.file-drop-mask {
  top: 0px;
  bottom: 0px;
  position: absolute;
  z-index: 99;
  margin: -2px;
  left: 0px;
  right: 0px;
}

.file-drop-msg {
  font-weight: 400;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 180px);
  vertical-align: middle;
  display: block;
  line-height: 60px;
  left: 121px;
}

.file-drop-msg.disabled {
  color: #343434;
}

.file-drop-msg-sub {
  font-weight: 400;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 180px);
  vertical-align: middle;
  display: block;
  top: -25px;
  left: 163px;
  color: #343434;
}

.file-drop-msg-sub.disabled {
  color: #343434;
}

.file-drop-input {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  opacity: 0;
}

.file-drop-input:focus {
  outline: none;
}

.reference-info-container {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 1rem;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #CACACA;
}
.reference-info-container h3, .reference-info-container label {
  margin: 0;
}
.reference-info-container label {
  margin-right: 0.5rem;
}
.reference-info-container [class*=state-indicator-] {
  width: auto;
  width: initial;
  margin-bottom: 0;
  padding: 0.25rem 0.5rem;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto;
  grid-gap: 1rem;
  gap: 1rem;
}

.badge-yes, .badge-no {
  text-align: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: #fff;
  width: 2.75rem;
}

.badge-yes {
  background-color: #007F50;
}

.badge-no {
  background-color: #9E2305;
}

#edit-contract .sub-header h2 a {
  color: #3B3B3B;
}
#edit-contract .sub-header h2 a:hover {
  color: #461E96;
}

#contractDetails .form-group .contractHistoryLabel {
  font-weight: bold;
  margin-bottom: 0.4rem;
}

#help section {
  border-radius: 5px;
}
#help section .files .file {
  border: 1px solid #CACACA;
  border-radius: 5px;
  padding: 20px 20px 20px;
  width: 290px;
}
#help section .files .file span {
  display: inline-block;
  width: 75px;
  font-weight: bold;
}
#help section .files .file .file-buttons {
  padding-top: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 0.5rem;
}
#help section #build-info p:last-of-type {
  margin-bottom: 0;
}

#preclassification .locking-error-message, #preclassification .in-progress-message {
  position: absolute;
  top: 123px;
  right: 28px;
  background: #FEFEFE;
  border-radius: 5px;
  padding: 5px 10px;
  min-width: 215px;
}
#preclassification .locking-error-message {
  background: #FFE4E1;
  border-left: 2px solid #B22222;
  border-right: 2px solid #B22222;
}
#preclassification .in-progress-message .spinner {
  width: 16px;
  height: 16px;
  display: inline-block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  position: relative;
  left: -3px;
  top: 2px;
}
#preclassification .in-progress-message .spinner.round::before {
  border-radius: 50%;
  content: " ";
  width: 16px;
  height: 16px;
  display: inline-block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-top: solid 3.2px #CACACA;
  border-right: solid 3.2px #CACACA;
  border-bottom: solid 3.2px #CACACA;
  border-left: solid 3.2px #CACACA;
  position: absolute;
  top: 0;
  left: 0;
}
#preclassification .in-progress-message .spinner.round::after {
  border-radius: 50%;
  content: " ";
  width: 16px;
  height: 16px;
  display: inline-block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-top: solid 3.2px #461E96;
  border-right: solid 3.2px transparent;
  border-bottom: solid 3.2px transparent;
  border-left: solid 3.2px transparent;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-animation: round-animate 1s ease-in-out infinite;
          animation: round-animate 1s ease-in-out infinite;
}
@-webkit-keyframes round-animate {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes round-animate {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
#preclassification .modal-wrapper .icon-button-cancel {
  display: none;
}
#preclassification .modal-wrapper .dialog-custom-content {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
#preclassification .modal-wrapper #dialog-closer {
  display: none;
}
#preclassification .modal-wrapper a.button.icon-button-tick {
  font-size: 0;
}
#preclassification .modal-wrapper a.button.icon-button-tick:after {
  content: "OK";
  font-size: medium;
  font-size: initial;
}

#search .classificationSearchFullTextErrorMessage {
  border: 1px solid #8A1A1E;
  border-radius: 6px;
  background: #F9E1E0;
  padding: 15px;
  margin: 13px 0;
}
#search .classificationSearchFullTextErrorMessage p {
  margin-bottom: 0;
}

#reports section {
  border-radius: 5px;
}
#reports section .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 20px;
     -moz-column-gap: 20px;
          column-gap: 20px;
  row-gap: 10px;
}
#reports section .cards .card {
  border: 1px solid #CACACA;
  border-radius: 5px;
  width: 290px;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
#reports section .cards .card .card-info {
  padding: 20px 20px 10px;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
#reports section .cards .card .card-info h2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
#reports section .cards .card .card-buttons {
  border-top: 1px solid #CACACA;
  padding: 10px 20px 10px;
}
#reports section .cards .card .card-buttons .button {
  margin-right: 15px;
}

#importcards section {
  border-radius: 5px;
}
#importcards section .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 20px;
     -moz-column-gap: 20px;
          column-gap: 20px;
  row-gap: 10px;
}
#importcards section .cards .card {
  border: 1px solid #CACACA;
  border-radius: 5px;
  width: 390px;
  height: 150px;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
#importcards section .cards .card .card-info {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
#importcards section .cards .card .card-info h2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
#importcards section .cards .card .card-buttons {
  border-top: 1px solid #CACACA;
  padding: 10px 20px 10px;
}
#importcards section .cards .card .card-buttons .button {
  margin-right: 15px;
}
#importcards section .import-card-type {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 5px;
}
#importcards section .import-card-title-text {
  color: #461E96;
  font-size: 17px;
  font-weight: bold;
  padding: 10px 20px 0px;
}
#importcards section .import-pubmed-card-contract-text {
  font-weight: bold;
  margin-bottom: 10px;
  padding: 0px 20px 0px;
  min-height: 50px;
}
#importcards section .import-card-contract-text {
  font-weight: bold;
  margin-bottom: 10px;
  padding: 0px 20px 0px;
}
#importcards section .import-card-date-title {
  color: #8a8a8a;
  font-size: 12px;
  padding: 10px 12px 0px;
}
#importcards section .import-card-date {
  font-size: 12px;
  padding: 0px 12px 0px;
}
#importcards section .import-card-updatedby-title {
  color: #8a8a8a;
  font-size: 12px;
  padding: 10px 12px 0px;
}
#importcards section .import-card-updatedby {
  font-size: 12px;
  padding: 0px 12px 0px;
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#importcards section .import-card-createdby {
  font-size: 12px;
  padding: 0px 12px 0px;
  width: 170px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#importcards section .import-card-journal-title {
  font-weight: bold;
  padding: 0px 20px 0px;
}
#importcards section .import-card-text {
  font-size: 12px;
  width: 400px;
  padding: 0px 20px 0px;
  word-break: break-word;
  min-height: 40px;
}
#importcards section .import-card-mod {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0px 10px 0px;
}
#importcards section .import-card-line {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-top: 1px solid #CACACA;
  padding: 0px 10px 0px;
}
#importcards section .import-card-mod-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-right: 15px;
}
#importcards section .import-card-title {
  width: 100px;
  height: 30px;
}
#importcards section .import-card-file-import-lozenge {
  background-color: #E7F7FF;
  padding: 2px 10px 2px 10px;
  margin-right: 5px;
  border-radius: 15px;
  border-color: #CFEFFE;
  border-style: solid;
  border-width: 3px;
}

.fileimportbutton {
  height: 40px;
  margin-left: 10px;
  padding-left: 10px;
  line-height: 25px;
  text-align: center;
}

.ellipsis-button {
  border: 2px solid;
  border-color: #e5e5e5;
  border-radius: 4px;
  height: 25px;
  position: relative;
  color: white;
}

.ellipsis-button:hover {
  color: white;
  background-color: #ededed;
}

.ellipsis-button > a > .nav-menu-user-icon {
  margin-right: 0;
  margin-right: initial;
}

.ellipsis-dropdown-content {
  display: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: absolute;
  background-color: white;
  min-width: 90px;
  -webkit-box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
          box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.ellipsis-dropdown-content ul > li {
  margin-bottom: 0;
  margin-bottom: initial;
}

.ellipsis-dropdown-content > ul > li:hover {
  background-color: #ddd;
  cursor: pointer;
}

.ellipsis-container {
  position: relative;
}

.ellipsis-container:hover .ellipsis-dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.ellipsis-dropdown-content > ul > li:nth-child(1) {
  margin-top: 10px;
  padding-left: 15px;
}

.ellipsis-dropdown-content > ul li:nth-child(2) {
  margin-bottom: 10px;
  padding-left: 15px;
}

#tracking-sheets section {
  border-radius: 5px;
}
#tracking-sheets section .filters {
  position: absolute;
  top: 150px;
  right: 80px;
}
#tracking-sheets section .filters select#company {
  width: 250px;
}
#tracking-sheets section .filters select#year {
  width: 90px;
}
#tracking-sheets section .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 10px;
     -moz-column-gap: 10px;
          column-gap: 10px;
  margin: 10px auto;
}
#tracking-sheets section .cards .card {
  width: 140px;
  padding: 0;
  margin: 0 0 10px;
}
#tracking-sheets section .cards .card .card-info {
  padding: 10px 10px 10px;
  background: #E3F2FD;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
#tracking-sheets section .cards .card .card-info h2 {
  color: #1F394A;
  font-size: 24px;
  padding: 5px 0 0;
}
#tracking-sheets section .cards .card .card-info p {
  color: #1F394A;
  font-size: 13px;
  margin: 0;
}
#tracking-sheets section .cards .card .card-buttons {
  padding: 10px 10px 10px;
  background: #CCE6F8;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  font-size: 13px;
}
#tracking-sheets section .cards .card .card-buttons a {
  color: #01699f;
  margin-right: 15px;
}
#tracking-sheets section .cards .card.future .card-info {
  background: #F3F6F9;
}
#tracking-sheets section .cards .card.future .card-info h2, #tracking-sheets section .cards .card.future .card-info p {
  color: #E5E5E5;
}
#tracking-sheets section .cards .card.future .card-buttons {
  background: #E5E5E5;
}
#tracking-sheets section .cards .card.missing .card-info {
  background: #F8E5C4;
}
#tracking-sheets section .cards .card.missing .card-info h2, #tracking-sheets section .cards .card.missing .card-info p {
  color: #1F394A;
}
#tracking-sheets section .cards .card.missing .card-buttons {
  background: #F0DF9D;
}
#tracking-sheets section .cards .card.missing .card-buttons a {
  color: #1F394A;
}

#split-references .company-multi-select {
  height: 60px;
}

#apogepha-client-report section, #merz-client-report section, #accovion-client-report section {
  border-radius: 5px;
}
#apogepha-client-report section .filters, #merz-client-report section .filters, #accovion-client-report section .filters {
  position: absolute;
  top: 150px;
  right: 80px;
}
#apogepha-client-report section .filters select#year, #merz-client-report section .filters select#year, #accovion-client-report section .filters select#year {
  width: 90px;
}
#apogepha-client-report section .cards, #merz-client-report section .cards, #accovion-client-report section .cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-column-gap: 10px;
     -moz-column-gap: 10px;
          column-gap: 10px;
  margin: 10px auto;
}
#apogepha-client-report section .cards .card, #merz-client-report section .cards .card, #accovion-client-report section .cards .card {
  width: 140px;
  padding: 0;
  margin: 0 0 10px;
}
#apogepha-client-report section .cards .card .card-info, #merz-client-report section .cards .card .card-info, #accovion-client-report section .cards .card .card-info {
  padding: 10px 10px 10px;
  background: #E3F2FD;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
#apogepha-client-report section .cards .card .card-info h2, #merz-client-report section .cards .card .card-info h2, #accovion-client-report section .cards .card .card-info h2 {
  color: #1F394A;
  font-size: 18px;
  padding: 5px 0 0;
}
#apogepha-client-report section .cards .card .card-info p, #merz-client-report section .cards .card .card-info p, #accovion-client-report section .cards .card .card-info p {
  color: #1F394A;
  font-size: 13px;
  margin: 0;
}
#apogepha-client-report section .cards .card .card-buttons, #merz-client-report section .cards .card .card-buttons, #accovion-client-report section .cards .card .card-buttons {
  padding: 10px 10px 10px;
  background: #CCE6F8;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  font-size: 13px;
}
#apogepha-client-report section .cards .card .card-buttons a, #merz-client-report section .cards .card .card-buttons a, #accovion-client-report section .cards .card .card-buttons a {
  color: #01699f;
  margin-right: 15px;
}
#apogepha-client-report section .cards .card.future .card-info, #merz-client-report section .cards .card.future .card-info, #accovion-client-report section .cards .card.future .card-info {
  background: #F3F6F9;
}
#apogepha-client-report section .cards .card.future .card-info h2, #apogepha-client-report section .cards .card.future .card-info p, #merz-client-report section .cards .card.future .card-info h2, #merz-client-report section .cards .card.future .card-info p, #accovion-client-report section .cards .card.future .card-info h2, #accovion-client-report section .cards .card.future .card-info p {
  color: #E5E5E5;
}
#apogepha-client-report section .cards .card.future .card-buttons, #merz-client-report section .cards .card.future .card-buttons, #accovion-client-report section .cards .card.future .card-buttons {
  background: #E5E5E5;
}

.custom-select.small:before {
  top: 9px;
}

button.secondary:disabled,
button.secondary.disabled,
a.button.secondary:disabled,
a.button.secondary.disabled {
  border-color: #CACACA;
  color: #CACACA;
}

.nav-menu > li .flyout {
  top: 51px;
  left: 1px;
}

#settings .global-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
#settings .sidebar {
  width: 200px;
  margin-top: 10px;
  margin-right: 50px;
}
#settings .main-content {
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
}
#settings .menu li {
  padding: 5px;
  margin-right: 5px;
}
#settings .menu li:hover {
  background-color: #e7f7ff;
}
#settings .menu li.active {
  background-color: #e7f7ff;
  font-weight: 600;
}
#settings .menu a {
  -webkit-text-decoration: none;
  text-decoration: none;
}
#settings #journals-table .sub-header {
  border: none;
  background: none;
}
#settings #journals-table .sub-header h2 {
  font-weight: 700;
  margin-left: 10px;
  font-size: 1.2rem;
}
#settings #journal-modal .modal-header h2 {
  margin-right: 320px;
  font-weight: 600;
}
#settings #journal-modal .modal-header button {
  font-size: 1.5rem;
  color: black;
  background: none;
}
#settings #journal-modal .modal-body {
  padding: 0 20px 10px;
}
#settings #journal-modal .modal-footer .button {
  margin-right: 5px;
}
#settings .ai-suggestion-toggle-panel {
  background-color: #EEE;
  padding: 1rem;
  max-width: 800px;
}
#settings .ai-suggestion-toggle-panel h2, #settings .ai-suggestion-toggle-panel p {
  margin-left: 15px;
}

.ai-retry button {
  width: 100%;
  margin-bottom: 8rem;
}

#ad-hoc-list select {
  background-color: #0073BE;
  color: #ffffff;
}
#ad-hoc-list select option:disabled {
  background-color: #E8E8E8;
}
#ad-hoc-list select option {
  background-color: #ffffff;
  color: #333;
  padding: 8px;
}

#ad-hoc-manual-entry-form h1 {
  padding: 20px;
  font-weight: 500;
}
#ad-hoc-manual-entry-form #doi-exist-message, #ad-hoc-manual-entry-form #doi-invalid-message {
  display: none;
  padding: 5px;
  background-color: #FCD9D1;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-top: 5px;
}
#ad-hoc-manual-entry-form .error-message {
  display: none;
  padding: 5px;
  color: red;
  margin-top: 5px;
  font-weight: 500;
}
#ad-hoc-manual-entry-form #sources-wrapper {
  margin: 15px;
  border: 1px solid #E8E8E8;
  border-radius: 10px;
}
#ad-hoc-manual-entry-form #sources-wrapper h2 {
  padding-left: 25px;
  margin-top: 20px;
}
#ad-hoc-manual-entry-form #sources-wrapper .form-group {
  padding-left: 20px;
  padding-top: 10px;
}

#file-import-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
#file-import-container .button-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  width: 100%;
  margin-top: 10px;
}
#file-import-container .button {
  margin-right: 10px;
}

/*!
 * Generated using the Bootstrap Customizer (https://getbootstrap.com/docs/3.4/customize/)
 */
/*!
 * Bootstrap v3.4.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
.modal-open {
  overflow: hidden;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}

.modal-body.dropping {
  cursor: wait;
}

.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
}

.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}

.modal-content {
  position: relative;
  background-color: #ffffff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000000;
}

.modal-backdrop.fade {
  filter: alpha(opacity=0);
  opacity: 0;
}

.modal-backdrop.in {
  filter: alpha(opacity=50);
  opacity: 0.5;
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.modal-header .close {
  margin-top: -2px;
}

.modal-title {
  margin: 0;
  line-height: 1.42857143;
}

.modal-body {
  overflow: auto !important;
}

.modal-body {
  position: relative;
  margin: 0px;
}

.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}

.modal-footer .btn + .btn {
  margin-bottom: 0;
  margin-left: 5px;
}

.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.clearfix:before,
.clearfix:after,
.modal-header:before,
.modal-header:after,
.modal-footer:before,
.modal-footer:after {
  display: table;
  content: " ";
}

.clearfix:after,
.modal-header:after,
.modal-footer:after {
  clear: both;
}

.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

.pull-right {
  float: right !important;
}

.pull-left {
  float: left !important;
}

.hide {
  display: none !important;
}

.show {
  display: block !important;
}

.invisible {
  visibility: hidden;
}

.text-hide {
  font-size: 0;
  line-height: 0;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.hidden {
  display: none !important;
}

.affix {
  position: fixed;
}

/* Multi-select */
.multi-select {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: #fff;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: 1px solid #CACACA;
  border-radius: 8px;
  overflow: hidden;
  width: inherit;
}
.multi-select label {
  margin-bottom: 0px;
}
.multi-select .preview-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 6px 10px;
  border-bottom: 1px solid transparent;
}
.multi-select .preview-box .selected-label {
  display: block;
  -ms-flex-item-align: center;
      align-self: center;
  margin-right: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: normal;
  color: #000;
}
.multi-select .preview-box .selected-count {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  justify-self: end;
  margin-left: 10px;
  text-align: center;
  background-color: #461E96;
  color: #fff;
  min-height: calc(1.5rem + 2px);
  min-width: calc(1.5rem + 2px);
  border-radius: 50%;
}
.multi-select .preview-box .filter-multiselect {
  height: 30px;
  margin: 0px;
  border: none;
}
.multi-select .item-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding-bottom: 1px;
  overflow-y: auto;
  max-height: 16em;
}
.multi-select .multi-select-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0px 4px;
}
.multi-select .multi-select-item.disabled-item {
  background-color: #E8E8E8;
  opacity: 0.5;
  pointer-events: none;
}
.multi-select .multi-select-item:hover {
  background-color: #e9e9e9;
}
.multi-select .multi-select-item:hover input {
  background: #fff;
}
.multi-select .multi-select-item input {
  top: 0;
  margin: 0;
  margin-right: 5px;
  border-radius: 3px;
}
.multi-select .multi-select-item .multiSelectItemLabel {
  padding: 6px 0px;
  width: 100%;
  font-weight: normal;
  color: #000;
}
.multi-select .multi-select-item .formatLabel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-item-align: normal;
      align-self: normal;
}
.multi-select .hidden {
  display: none;
}
.multi-select:hover {
  border: 1px solid #8A8A8A;
}
.multi-select.active {
  border: 1px solid #461E96;
}
.multi-select.active .preview-box {
  border-bottom: 1px solid #461E96;
}
.multi-select.disabled {
  pointer-events: none;
  background-color: #f2f4f6;
}
.multi-select.disabled .selected-label {
  color: #949494;
}
.multi-select.disabled .selected-count {
  background-color: #949494;
}

/* Companies Multi-select */
.companies-multi-select {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: #fff;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: 1px solid #CACACA;
  border-radius: 8px;
  overflow: hidden;
  width: calc(40% - 4rem);
}
.companies-multi-select label {
  margin-bottom: 0px;
}
.companies-multi-select .select-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  padding: 6px 10px;
  border-bottom: 1px solid transparent;
}
.companies-multi-select .select-box .selected-label {
  display: block;
  -ms-flex-item-align: center;
      align-self: center;
  margin-right: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.companies-multi-select .select-box .selected-count {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  justify-self: end;
  margin-left: 10px;
  text-align: center;
  background-color: #461E96;
  color: #fff;
  min-height: 20px;
  min-width: 20px;
  border-radius: 50%;
}
.companies-multi-select .check-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding-bottom: 1px;
  overflow-y: auto;
  max-height: 16em;
}
.companies-multi-select .companies-multi-select-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0px 4px;
}
.companies-multi-select .companies-multi-select-item:hover {
  background-color: #e9e9e9;
}
.companies-multi-select .companies-multi-select-item:hover input {
  background: #fff;
}
.companies-multi-select .companies-multi-select-item input {
  top: 0;
  margin: 0;
  margin-right: 5px;
  border-radius: 3px;
}
.companies-multi-select .companies-multi-select-item .multiSelectItemLabel {
  padding: 6px 0px;
  width: 100%;
}
.companies-multi-select .companies-multi-select-item .formatLabel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-item-align: normal;
      align-self: normal;
}
.companies-multi-select .hidden {
  display: none;
}
.companies-multi-select:hover {
  border: 1px solid #8A8A8A;
}
.companies-multi-select.active {
  border: 1px solid #461E96;
}
.companies-multi-select.active .select-box {
  border-bottom: 1px solid #461E96;
}

.switch-wrapper {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 1rem;
  width: 100%;
}

/*# sourceMappingURL=vigiLitCss.css.map*/