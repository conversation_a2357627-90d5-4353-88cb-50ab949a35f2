﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using PharmaLex.VigiLit.Reporting.Contracts;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain;
internal class ConfigureServices : IConfigureServices
{
    public void RegisterServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IRepository, Repository>();
        services.AddScoped<IAccovionClientReportService, Service>();

        var assembly = typeof(ConfigureServices).Assembly;

        services.AddControllersWithViews()
            .AddRazorRuntimeCompilation()
            .AddApplicationPart(assembly);

        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
            { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });
    }
}

