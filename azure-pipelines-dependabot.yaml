trigger: none
pr: none 

schedules:
  - cron: '0 0 1 * *' # runs on midnight UTC 1st day of every month
    displayName: Every month
    branches:
      include:
      #- main
      - develop
    always: true


resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

pool: pv-pool

variables:
- name: projectName
  value: Vigilit

jobs:
  - job: dependabot
    displayName: Dependabot Execution
    timeoutInMinutes: 30
    pool:
      vmImage: ubuntu-latest
    strategy:
      matrix:
       Vigilit_Nuget:
          PROJECT: '$(projectName)'
          REPOSITORY: 'Vigilit'
          DIRECTORY: '/'
          PACKAGE_MANAGER: 'nuget'
          TARGET_BRANCH: 'develop'
          AUTO_COMPLETE: 'false'

       Vigilit_Npm_And_Yarn:
          PROJECT: '$(projectName)'
          REPOSITORY: 'Vigilit'
          DIRECTORY: '/'
          PACKAGE_MANAGER: 'npm_and_yarn'
          TARGET_BRANCH: 'develop'
          AUTO_COMPLETE: 'false'
      maxParallel: 3
    steps:
      - task: AzureKeyVault@2
        displayName: 'Retrieve secret from vault'
        inputs:
          azureSubscription: 'DevOps Bastion'
          KeyVaultName: pxv-vault-eun
          secretsFilter: 'GitHub-Dependabot-PAT'
          RunAsPreJob: true
      - template: Dependabot/dependabot-task.yml@templates
        parameters:
          GHPAT: "$(GitHub-Dependabot-PAT)"
          PAT: "$(PAT)"
          PROJECT: "$(PROJECT)"
          REPOSITORY: "$(REPOSITORY)"
          PACKAGE_MANAGER: "$(PACKAGE_MANAGER)"
          DIRECTORY: "$(DIRECTORY)"
          TARGET_BRANCH: "$(TARGET_BRANCH)"
          AUTO_COMPLETE: "$(AUTO_COMPLETE)"