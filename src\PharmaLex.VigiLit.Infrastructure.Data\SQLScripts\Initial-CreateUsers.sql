﻿DROP PROCEDURE IF EXISTS [CreateUser]
GO

CREATE PROCEDURE [CreateUser] @Email nvarchar(256), @FirstName nvarchar(512), @LastName nvarchar(512), @ClaimName nvarchar(1024)
AS
	DECLARE @ClaimId int
	DECLARE @UserId int

	SELECT @ClaimId = [Id] from [Claims] WHERE [Name] = @ClaimName
	
	BEGIN TRAN

		INSERT INTO [Users]([Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@Email, @FirstName, @LastName, NULL, GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script')

		SET @UserId = SCOPE_IDENTITY()

		INSERT INTO [UserClaims] ([ClaimsInternalId], [UsersInternalId])
			VALUES (@ClaimId, @UserId)  
			
	COMMIT TRAN
GO

EXEC [CreateUser]	@Email = '<EMAIL>',					@FirstName = 'Harry',	@LastName = 'Rowland',		@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',					@FirstName = 'Jim',		@LastName = 'Benham',		@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',					@FirstName = 'Nigel',	@LastName = 'Kitcher',		@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',				@FirstName = 'Yuliyan',	@LastName = 'Serbezki',		@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Dev',		@LastName = 'Ops',			@ClaimName = 'PharmaLexResearcher';
GO

DROP PROCEDURE IF EXISTS [CreateUser]
GO
