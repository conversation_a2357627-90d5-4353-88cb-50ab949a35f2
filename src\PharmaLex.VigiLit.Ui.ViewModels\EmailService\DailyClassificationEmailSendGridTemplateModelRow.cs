﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Ui.ViewModels.EmailService;

public class DailyClassificationEmailSendGridTemplateModelRow
{
    public int EmailRelevantEventId { get; set; }
    public EmailReason EmailReason { get; set; }
    public ReferenceSendGridTemplateModel Reference { get; set; }
    public ReferenceClassificationSendGridTemplateModel ReferenceClassification { get; set; }

    public DailyClassificationEmailSendGridTemplateModelRow(int emailRelevantEventId, EmailReason emailReason, ReferenceSendGridTemplateModel reference, ReferenceClassificationSendGridTemplateModel classification)
    {
        EmailRelevantEventId = emailRelevantEventId;
        EmailReason = emailReason;
        Reference = reference;
        ReferenceClassification = classification;
    }
}
