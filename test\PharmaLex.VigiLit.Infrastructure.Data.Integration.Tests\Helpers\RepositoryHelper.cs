﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Repositories;
using PharmaLex.VigiLit.ReferenceManagement.Service;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Helpers;

public class RepositoryHelper
{
    private readonly IMapper _mapper;
    private readonly VigiLitDbContext _context;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IContractRepository _contractRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IUserRepository _userRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;

    public RepositoryHelper(DatabaseFixture<VigiLitDbContext, SubstanceRepositoryTests> fixture)
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<ContractVersionProfile>());
        _context = fixture.Context;
        _mapper = config.CreateMapper();

        _substanceRepository = GetSubstanceRepository();
        _projectRepository = GetProjectRepository();
        _contractRepository = GetContractRepository();
        _companyRepository = GetCompanyRepository();
        _userRepository = GetUserRepository();
        _referenceRepository = GetReferenceRepository();
        _referenceClassificationRepository = GetReferenceClassificationRepository();
    }

    public async Task<User> AddUser(string firstName, string surname, string email)
    {
        var user = new User(firstName, surname, email);
        _userRepository.Add(user);
        await _userRepository.SaveChangesAsync();
        return user;
    }

    public async Task<Substance> AddSubstance(string name, string type)
    {
        var substance = new Substance(name, type);
        _substanceRepository.Add(substance);
        await _substanceRepository.SaveChangesAsync();
        return substance;
    }

    public async Task<Company> AddCompany(string name, string person, string email, bool active)
    {
        var company = new Company(name, person, email, active);
        _companyRepository.Add(company);
        await _companyRepository.SaveChangesAsync();
        return company;
    }

    public async Task<Project> AddProject(string name, Company company)
    {
        var project = new Project(name, company.Id);
        _projectRepository.Add(project);
        await _projectRepository.SaveChangesAsync();
        return project;
    }

    public async Task<Contract> AddContract(User user, Substance substance, Project project, bool active, int version)
    {
        var contract = new Contract(substance.Id, project.Id);

        var contractVersionPending = new ContractVersionEditModel();

        contractVersionPending.Version = version;
        contractVersionPending.UserId = user.Id;
        contractVersionPending.TimeStamp = DateTime.UtcNow;

        var contractVersion = _mapper.Map<ContractVersion>(contractVersionPending);
        contractVersion.SetContractVersion(ContractVersionStatus.Approved);
        contractVersion.IsActive = active;
        contract.AddContractVersion(contractVersion);

        _contractRepository.Add(contract);
        await _contractRepository.SaveChangesAsync();
        contract.CurrentContractVersionId = contract.GetCurrentContractVersion().Id;
        await _contractRepository.SaveChangesAsync();
        return contract;
    }

    public async Task<Reference> AddReference(string title)
    {
        var reference = new Reference { Title = title };
        _referenceRepository.Add(reference);
        await _referenceRepository.SaveChangesAsync();
        return reference;
    }

    public async Task<ReferenceClassification> AddClassification(Reference reference, Substance substance)
    {
        var referenceClassification = new ReferenceClassification(reference, substance.Id);
        _referenceClassificationRepository.Add(referenceClassification);
        await _referenceClassificationRepository.SaveChangesAsync();
        return referenceClassification;
    }

    public ISubstanceRepository GetSubstanceRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new SubstanceMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var substanceRepository = new PharmaLex.VigiLit.Data.Repositories.SubstanceRepository(_context, null, mapper, userContext);
        return substanceRepository;
    }

    public IContractRepository GetContractRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ContractMappingProfile());
        });
        return new ContractRepository(_context, userContext); ;
    }

    public IProjectRepository GetProjectRepository()
    {
        var userContext = new UserContext();
        var projectRepository = new ProjectRepository(_context, userContext);
        return projectRepository;
    }

    public ICompanyRepository GetCompanyRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new CompanyMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var companyRepository = new CompanyRepository(_context, mapper, userContext);
        return companyRepository;
    }

    public IUserRepository GetUserRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new UserMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var userRepository = new UserRepository(_context, mapper, userContext);
        return userRepository;
    }

    public IReferenceRepository GetReferenceRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ReferenceMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var referenceRepository = new ReferenceRepository(_context, mapper, userContext);
        return referenceRepository;
    }

    public IReferenceClassificationRepository GetReferenceClassificationRepository()
    {
        var userContext = new UserContext();
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ReferenceClassificationMappingProfile());
        });
        var mapper = mapperConfig.CreateMapper();
        var referenceClassificationRepository = new ReferenceClassificationRepository(_context, mapper, userContext);
        return referenceClassificationRepository;
    }
}
