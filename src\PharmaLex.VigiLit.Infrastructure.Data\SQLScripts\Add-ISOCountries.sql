﻿
-- Tanzania / Taiwan have been added manually
if not exists(select 1 from countries where iso = 'TZ')
	insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
	values (N'United Republic of Tanzania', N'TZ', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

if not exists(select 1 from countries where iso = 'TW')
	insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
	values (N'Taiwan (Province of China)', N'TW', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy)
values ('Åland Islands', 'AX', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Saint Barthélemy', N'BL', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Bolivia', N'BO', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Bonaire', N'BQ', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Congo (the Democratic Republic of)', N'CD', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Ivory Coast', N'CI', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Curaçao', N'CW', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Micronesia', N'FM', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Moldova', N'MD', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Palestine', N'PS', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Réunion', N'RE', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Saint Helena', N'SH', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Venezuela', N'VE', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Virgin Islands (British)', N'VG', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')

insert into countries(Name, Iso, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy) 
values (N'Virgin Islands (U.S.)', N'VI', GetUTCDate(),'<EMAIL>',GetUTCDate(),'<EMAIL>')
