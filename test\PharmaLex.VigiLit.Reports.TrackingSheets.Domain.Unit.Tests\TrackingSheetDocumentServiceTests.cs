﻿using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Models.Document.TrackingSheet;
using Shouldly;
using Xunit;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests;

public class TrackingSheetDocumentServiceTests
{
    private const int COMPANY_ID = 123;
    private const int YEAR = 2024;
    private const int WEEK = 1;
    private const string FILE_NAME = "TrackingSheet_2024_1.pdf";
    private readonly TrackingSheetDocumentDescriptor _trackingSheetDocumentDescriptor;

    private const string ACCOUNT_NAME = "vgtsharedeun";
    private const string CONTAINER_NAME = "tracking-sheet-document";
    private const string BLOB_NAME = "123/2024/1/TrackingSheet_2024_1.pdf";
    private readonly DocumentDescriptor _documentDescriptor;

    private readonly IDocumentService _mockDocumentService;

    private readonly ITrackingSheetDocumentService _trackingSheetDocumentService;

    public TrackingSheetDocumentServiceTests()
    {
        _trackingSheetDocumentDescriptor = new TrackingSheetDocumentDescriptor(COMPANY_ID, YEAR, WEEK, FILE_NAME);
        _documentDescriptor = new DocumentDescriptor(ACCOUNT_NAME, CONTAINER_NAME, BLOB_NAME);

        _mockDocumentService = Substitute.For<IDocumentService>();

        var azureStorageTrackingSheetDocumentOptions = Substitute.For<IOptions<AzureStorageTrackingSheetDocumentOptions>>();
        azureStorageTrackingSheetDocumentOptions.Value
            .Returns(new AzureStorageTrackingSheetDocumentOptions
            {
                AccountName = ACCOUNT_NAME,
                ContainerName = CONTAINER_NAME
            });

        _trackingSheetDocumentService = new TrackingSheetDocumentService(_mockDocumentService, azureStorageTrackingSheetDocumentOptions);
    }

    [Fact]
    public async Task Create_WhenCalled_CallsCreate()
    {
        // Arrange
        var stream = new MemoryStream();

        // Act
        await _trackingSheetDocumentService.Create(_trackingSheetDocumentDescriptor, stream);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .Create(default!, default);

        await _mockDocumentService
            .Received(1)
            .Create(_documentDescriptor, stream);
    }

    [Fact]
    public async Task Exists_BlobExists_ReturnsTrue()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptor)
            .Returns(true);

        // Act
        var result = await _trackingSheetDocumentService.Exists(_trackingSheetDocumentDescriptor);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Exists_BlobDoesNotExist_ReturnsFalse()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptor)
            .Returns(false);

        // Act
        var result = await _trackingSheetDocumentService.Exists(_trackingSheetDocumentDescriptor);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task OpenRead_BlobExists_ReturnsStream()
    {
        // Arrange
        var stream = new MemoryStream();
        _mockDocumentService
            .OpenRead(_documentDescriptor)
            .Returns(stream);

        // Act
        var result = await _trackingSheetDocumentService.OpenRead(_trackingSheetDocumentDescriptor);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(stream);
    }

    [Fact]
    public async Task Delete_BlobExists_CallsDelete()
    {
        // Act
        await _trackingSheetDocumentService.Delete(_trackingSheetDocumentDescriptor);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .Delete(default!);

        await _mockDocumentService
            .Received(1)
            .Delete(_documentDescriptor);
    }
}