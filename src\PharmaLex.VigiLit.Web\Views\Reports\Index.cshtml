@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports
@using PharmaLex.VigiLit.Web.Helpers;

@model ReportsPageModel

@{
    ViewData["Title"] = "Reports";
}

<div id="reports" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2>Reports</h2>
        <div class="controls">
        </div>
    </div>

    <section>
        <div class="cards" v-if="reports.length > 0">
            <div class="card" v-for="report in reports">
                <div class="card-info">
                    <h2>{{report.name}}</h2>
                    <p>{{report.description}}</p>
                </div>
                <div class="card-buttons">
                    <a class="button" :href="`/Reports/${report.id}`">Select</a>
                </div>
            </div>
        </div>
        <div class="empty" v-else>
            <p>You don't have permission to view any reports.</p>
        </div>
    </section>

</div>

@section Scripts {
<script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageModel = @Html.Raw(AntiXss.ToJson(Model));

        var pageConfig = {
            appElement: "#reports",
            data: function () {
                return {
                    reports: pageModel.reports
                };
            }
        };
</script>
}
