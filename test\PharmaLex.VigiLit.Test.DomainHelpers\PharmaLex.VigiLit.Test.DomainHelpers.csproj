﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="xunit" Version="2.9.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Data.Repositories\PharmaLex.VigiLit.Data.Repositories.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>
