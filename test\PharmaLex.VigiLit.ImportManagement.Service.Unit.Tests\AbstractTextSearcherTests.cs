﻿using PharmaLex.VigiLit.ImportManagement.Service.Matching;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class AbstractTextSearcherTests
{
    private readonly IAbstractTextSearcher _abstractTextSearcher;

    public AbstractTextSearcherTests()
    {
        _abstractTextSearcher = new AbstractTextSearcher();
        _abstractTextSearcher.AbstractText =
            "Oxytocin is a hormone, and Piperacillin Oxytocin is an antibiotic. Amoxicillin is also mentioned.";

    }

    [Theory]
    [InlineData("Amoxicillin")]
    [InlineData("Amox*")]
    [InlineData("Piperacillin Oxytocin")]
    [InlineData("Piperacillin Oxyt*")]
    [InlineData("Oxytocin is an antibiotic")]

    public void AbstractTextSearcher_CheckMatchCondition_ReturnsTrue(string searchTerm)
    {
        var result = _abstractTextSearcher.IsWordInAbstract(searchTerm);
        Assert.True(result);
    }

    [Theory]
    [InlineData("Amoxicllin")]
    [InlineData("Amx*")]
    [InlineData("Piperacillinrr Oxytocinr")]
    [InlineData("Piperacillinrr Oxytocinr*")]
    public void AbstractTextSearcher_CheckMatchCondition_ReturnsFalse(string searchTerm)
    {
        var result = _abstractTextSearcher.IsWordInAbstract(searchTerm);
        Assert.False(result);
    }
}