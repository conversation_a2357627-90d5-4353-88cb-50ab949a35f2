﻿using MassTransit.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.AiAnalysis.Entities.Enums;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;
using PharmaLex.VigiLit.AiAnalysis.Service;
using PharmaLex.VigiLit.AiAnalysis.Unit.Tests.Fakes;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.Test.Framework.ILogger;
using System.Net;
using System.Text.Json;

namespace PharmaLex.VigiLit.AiAnalysis.Unit.Tests.Service;

public class PreClassifyReferenceCommandHandlerTests
{
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler = new();
    private readonly Mock<ILogger<PreClassifyReferenceCommandHandler>> _mockLogger = new();
    private readonly InMemoryTestHarness _harness = new();
    private readonly ConsumerTestHarness<PreClassifyReferenceCommandHandler> _consumerHarness;
    private readonly Mock<IAiSuggestionRepository> _mockAiSuggestionRepository = new();
    private static IConfiguration Configuration { get; } = new ConfigurationBuilder().AddJsonFile("appsettings.Development.json").Build();

    public PreClassifyReferenceCommandHandlerTests()
    {
        var uri = Configuration.GetSection("AiEndpointSettings:Uri").Value;

        if (uri != null)
        {
            var aiEndpointOptions = Options.Create(new AiEndpointOptions());
            aiEndpointOptions.Value.Uri = uri;

            var fakeHttpClientFactory = new FakeHttpClientFactory(_mockHttpMessageHandler.Object);
            var consumer = new PreClassifyReferenceCommandHandler(_mockLogger.Object, fakeHttpClientFactory,
                _mockAiSuggestionRepository.Object, aiEndpointOptions);
            _consumerHarness = _harness.Consumer(() => consumer);
        }
        else
        {
            throw new ArgumentNullException(uri);
        }
    }

    [Fact]
    public async Task Given_pre_classification_message_When_sent_Then_message_is_consumed_and_is_successful()
    {
        // Arrange
        ArrangeSuccessfulApiCall();

        await _harness.Start();

        try
        {
            // Act
            await _harness.InputQueueSendEndpoint.Send(new PreClassifyReferenceCommand("title", "abstract", "123", "1", "substance", new List<string>()));

            // Assert
            Assert.True(await _consumerHarness.Consumed.Any<PreClassifyReferenceCommand>());
        }
        finally
        {
            // Clean up
            await _harness.Stop();
        }
    }

    [Fact]
    public async Task Given_pre_classification_message_When_sent_Then_message_is_persisted_and_is_successful()
    {
        // Arrange
        ArrangeSuccessfulApiCall();
        var aiSuggestionModel = GetAddAiSuggestionModel();

        await _harness.Start();

        try
        {
            // Act
            await _harness.InputQueueSendEndpoint.Send(new PreClassifyReferenceCommand("title", "abstract", "123", "1", "substance", new List<string>()));


            // Assert
            Assert.True(await _consumerHarness.Consumed.Any<PreClassifyReferenceCommand>());

            _mockAiSuggestionRepository.Verify(x => x.Add(It.Is<AiSuggestion>(x =>
                                                          x.SourceId == aiSuggestionModel.SourceId
                                                          && x.Substance == aiSuggestionModel.Substance
                                                      )), Times.Once);
            _mockAiSuggestionRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));

        }
        finally
        {
            // Clean up
            await _harness.Stop();
        }
    }



    [Fact]
    public async Task Given_pre_classification_message_Sent_And_API_is_successful_Then_response_is_updated()
    {
        // Arrange
        ArrangeSuccessfulApiCall();
        var aiSuggestionModel = GetAddAiSuggestionModel();

        await _harness.Start();

        try
        {
            // Act
            await _harness.InputQueueSendEndpoint.Send(new PreClassifyReferenceCommand("title", "abstract", "123", "1", "substance", new List<string>()));


            // Assert
            Assert.True(await _consumerHarness.Consumed.Any<PreClassifyReferenceCommand>());

            _mockAiSuggestionRepository.Verify(x => x.Add(It.Is<AiSuggestion>(x =>
                                                          x.SourceId == aiSuggestionModel.SourceId
                                                          && x.Substance == aiSuggestionModel.Substance
                                                      )), Times.Once);

            var updatedAiSuggestionModel = GetUpdateSuccessAiSuggestionModel();
            aiSuggestionModel.Update(updatedAiSuggestionModel.Category, updatedAiSuggestionModel.CategoryReason, updatedAiSuggestionModel.DosageForm, updatedAiSuggestionModel.DosageFormReason, updatedAiSuggestionModel.Status);
            _mockAiSuggestionRepository.Setup(x => x.SaveChangesAsync());

            Assert.Equal(aiSuggestionModel.Category, updatedAiSuggestionModel.Category);
            Assert.Equal(aiSuggestionModel.CategoryReason, updatedAiSuggestionModel.CategoryReason);
            Assert.Equal(aiSuggestionModel.DosageForm, updatedAiSuggestionModel.DosageForm);
            Assert.Equal(aiSuggestionModel.DosageFormReason, updatedAiSuggestionModel.DosageFormReason);
            Assert.Equal(aiSuggestionModel.Status, updatedAiSuggestionModel.Status);

            _mockAiSuggestionRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));

        }
        finally
        {
            // Clean up
            await _harness.Stop();
        }
    }
    [Fact]
    public async Task Given_pre_classification_message_When_AI_fails_Then_unsuccessful_path_taken_status_is_awaitingresponse()
    {
        // Arrange
        ArrangeFailedApiCall();
        var aiSuggestionModel = GetAddAiSuggestionModel();
        await _harness.Start();

        try
        {
            // Act
            await _harness.InputQueueSendEndpoint.Send(new PreClassifyReferenceCommand("title", "abstract", "123", "1", "substance", new List<string>()));
            _mockAiSuggestionRepository.Setup(x => x.SaveChangesAsync());

            // Assert
            Assert.True(await _consumerHarness.Consumed.Any<PreClassifyReferenceCommand>());
            Assert.Equal(AiAnalysisStatus.AwaitingResponse, aiSuggestionModel.Status);
            _mockLogger.VerifyLogging("Error invoking AI service : InternalServerError, ErrorMessage: Hello, World!", LogLevel.Error, Times.Once());
        }
        finally
        {
            // Clean up
            await _harness.Stop();
        }
    }

    [Fact]
    public async Task Given_pre_classification_message_When_AI_exceptions_Then_exception_is_logged_and_status_is_Failed()
    {
        // Arrange
        ArrangeExceptioningApiCall();
        await _harness.Start();

        try
        {
            // Act
            await _harness.InputQueueSendEndpoint.Send(new PreClassifyReferenceCommand("title", "abstract", "123", "1", "substance", new List<string>()));
            _mockAiSuggestionRepository.Setup(x => x.SaveChangesAsync());

            // Assert
            Assert.True(await _consumerHarness.Consumed.Any<PreClassifyReferenceCommand>());
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Critical,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => LogMessageContainsExpectedValues(v)),
                    It.IsAny<Exception>(),
                   It.IsAny<Func<It.IsAnyType, Exception?, string>>()
                ),
                Times.Once
            );
        }
        finally
        {
            // Clean up
            await _harness.Stop();
        }
    }

    private static bool LogMessageContainsExpectedValues(object v)
    {
        var message = v?.ToString();
        return !string.IsNullOrEmpty(message) &&
               message.Contains("ExceptionMessage") &&
               message.Contains(AiAnalysisStatus.Failed.ToString());
    }

    private void ArrangeSuccessfulApiCall()
    {
        var expectedResponse = GetAddAiSuggestionModel();
        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedResponse))
            });
    }

    private void ArrangeFailedApiCall()
    {
        var expectedResponse = "Hello, World!";
        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent(expectedResponse)
            });
    }

    private void ArrangeExceptioningApiCall()
    {
        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ThrowsAsync(new HttpRequestException("ExceptionMessage"));
    }
    private static AiSuggestion GetAddAiSuggestionModel()
    {
        return new AiSuggestion()
        {
            SourceId = "123",
            CorrelationId = "123",
            Substance = "substance",
            Status = AiAnalysisStatus.AwaitingResponse

        };
    }
    private static AiSuggestion GetUpdateSuccessAiSuggestionModel()
    {

        var model = GetAddAiSuggestionModel();
        model.Category = "category";
        model.CategoryReason = "category reason";
        model.DosageForm = "dosage Form";
        model.DosageFormReason = "dosageForm reason";
        model.Status = AiAnalysisStatus.Success;
        return model;

    }
}
