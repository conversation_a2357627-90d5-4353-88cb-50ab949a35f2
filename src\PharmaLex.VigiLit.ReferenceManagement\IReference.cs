﻿namespace PharmaLex.VigiLit.ReferenceManagement;

public interface IReference
{
    // Need to make sure the Reference and ReferenceUpdate entities both contain all of the fields.
    // Wanted to use inheritance instead, but EFCore got mad because the References table is temporal
    // (and the ReferenceUpdates table is not), and we already have another base class from a PharmaLex.DataAccess.

    public string Abstract { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }
    public string Authors { get; set; }
    public string CountryOfOccurrence { get; set; }
    public DateTime DateRevised { get; set; }
    public string Doi { get; set; }
    public string FullPagination { get; set; }
    public string Issn { get; set; }
    public string Issue { get; set; }
    public string Language { get; set; }
    public int SourceSystem { get; set; }
    public string SourceId { get; set; }
    public string PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    public string Title { get; set; }
    public string Volume { get; set; }
    public string VolumeAbbreviation { get; set; }
    public string Keywords { get; set; }
    public string MeshHeadings { get; set; }
    public string JournalTitle { get; set; }
}