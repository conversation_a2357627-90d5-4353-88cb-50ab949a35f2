﻿using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Integration.Tests;
public class ReferenceClassificationRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, ReferenceClassificationRepositoryTests>>
{
    private readonly IImportingReferenceClassificationRepository _referenceClassificationRepository;

    private readonly ReferenceHelper _referenceHelper;
    private readonly SubstanceHelper _substanceHelper;


    public ReferenceClassificationRepositoryTests(DatabaseFixture<VigiLitDbContext, ReferenceClassificationRepositoryTests> fixture)
    {
        _referenceClassificationRepository = RepositoryFactory.GetRepositoryInstance<ReferenceClassification, ReferenceClassificationRepository, ReferenceClassificationMappingProfile>(fixture.Context);
        IReferenceRepository referenceRepository = RepositoryFactory.GetRepositoryInstance<Reference, ReferenceRepository, ReferenceMappingProfile>(fixture.Context);
        ISubstanceRepository substanceRepository = RepositoryFactory.GetRepositoryInstance<Substance, SubstanceRepository, SubstanceMappingProfile>(fixture.Context);


        _referenceHelper = new ReferenceHelper(referenceRepository);
        _substanceHelper = new SubstanceHelper(substanceRepository);
    }


    [Fact]
    public async Task AddClassification_Creates_ClassificationInDatabaseWithoutError()
    {
        //Arrange
        var reference = await _referenceHelper.AddReference("TITLE");
        var substance = await _substanceHelper.AddSubstance("NAME", "TYPE");
        var referenceClassification = new ReferenceClassification()
        {
            SubstanceId = substance.Id,
            ReferenceId = reference.Id,
            DosageForm = "N.A"
        };

        //Act
        _referenceClassificationRepository.Add(referenceClassification);
        await _referenceClassificationRepository.SaveChangesAsync();

        var result = _referenceClassificationRepository.GetTemporalAll();
        //Assert
        Assert.Single(result);
    }


}
