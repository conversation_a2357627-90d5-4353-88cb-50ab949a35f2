﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddContractWeekdayIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive_ContractType",
                table: "Contracts");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive_ContractType_ContractWeekday",
                table: "Contracts",
                columns: new[] { "IsActive", "ContractType", "ContractWeekday" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contracts_IsActive_ContractType_ContractWeekday",
                table: "Contracts");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsActive_ContractType",
                table: "Contracts",
                columns: new[] { "IsActive", "ContractType" });
        }
    }
}
