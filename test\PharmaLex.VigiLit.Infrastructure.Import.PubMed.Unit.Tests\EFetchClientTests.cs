using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests;

public class EFetchClientTests
{
    [Theory]
    [InlineData("efetch_01.xml")]
    [InlineData("efetch_02.xml")]
    [InlineData("efetch_03.xml")]
    [InlineData("efetch_04.xml")]
    [InlineData("efetch_05.xml")]
    [InlineData("efetch_06.xml")]
    [InlineData("efetch_07.xml")]
    [InlineData("efetch_08.xml")]
    [InlineData("efetch_09.xml")]
    [InlineData("efetch_10.xml")]
    public void Deserialize(string fileName)
    {
        var fileStream = new FileStream(string.Format("./InputFiles/efetch/{0}", fileName), FileMode.Open);

        var client = CreateClient();

        PubmedArticleSet setBatch = client.Deserialize(fileStream);

        Assert.NotNull(setBatch);
    }

    private static EFetchClient CreateClient()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string> { { "PubMedApiKey", "testValue" }, })
            .Build();

        return new EFetchClient(null, new NullLoggerFactory(), configuration);
    }
}