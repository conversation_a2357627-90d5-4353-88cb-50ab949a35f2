﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PharmaLex.VigiLit.Test.Framework.Orderers;
using System.Runtime.InteropServices;

namespace PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;

/// <summary>
/// Generic Database fixture for use in EF Sql Server tests.
/// </summary>
/// <typeparam name="TContext">The type of the context.</typeparam>
/// <typeparam name="TTestClassType">The type of the test.</typeparam>
/// <remarks>
/// When creating integration tests for repositories you should use this class in the IClassFixture that the test class implements.
/// <example>
/// Example:
/// <code>
/// public class MyRepositoryTests : IClassFixture&lt;DatabaseFixture&lt;MyDbContext, TTestClassType&gt;&gt;, IDisposable
/// {
///    private readonly MyDbContext _context;
///
///    public UserRepositoryTests(DatabaseFixture&lt;MyDbContext, UserRepositoryTests&gt; fixture)
///    {
///        _context = fixture.Context;
///        _context.Database.BeginTransaction();
///    }
///
///    public void Dispose()
///    {
///        _context.Database.RollbackTransaction();
///    }
/// 
///    // TODO: Tests
/// 
/// }
/// </code>
/// </example>
/// <para>Following this pattern will ensure that each integration test starts in a known state and that each test does not affect the other tests.</para>
/// <para>Note that if chained tests are being used with the <seealso cref="AlphabeticalOrderer" />, <seealso cref="TestPriorityAttribute" /> or
/// <seealso cref="FollowsTestAttribute" />
/// then the <seealso cref="Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.BeginTransaction()">BeginTransaction</seealso> and
/// <seealso cref="Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.RollbackTransaction()">RollbackTransaction</seealso> pair should be removed from the example above.
/// </para>
/// <para>The database used for integration testing must be specified in appsettings.json.</para>
/// <example>
/// <code>
/// {
///     "ConnectionStrings": {
///         "integrationTestDatabase": "Server=(localdb)\\MSSQLLocalDB; Database=TEST;Trusted_Connection=True"
///     }
/// }
/// </code>
/// </example>
/// Sources of some of the ideas behind this: <br/>
/// <a href="https://www.endpointdev.com/blog/2022/01/database-integration-testing-with-dotnet/">https://www.endpointdev.com/blog/2022/01/database-integration-testing-with-dotnet/</a><br/>
/// <a href="https://www.damirscorner.com/blog/posts/20191206-EfCoreIntegrationTestingWithSqlLocalDb.html">https://www.damirscorner.com/blog/posts/20191206-EfCoreIntegrationTestingWithSqlLocalDb.html</a><br/>
/// <a href="https://martinwilley.com/net/code/localdbtest.html">https://martinwilley.com/net/code/localdbtest.html</a>
/// </remarks>
/// <seealso cref="System.IDisposable" />
public class DatabaseFixture<TContext, TTestClassType> : IDisposable where TContext : DbContext
{
    public TContext Context { get; private set; }

    public DatabaseFixture()
    {
        Context = CreateDbContext();
    }

    private TContext CreateDbContext()
    {
        IHost host;

        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            host = Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((hostContext, config) =>
                {
                    IConfiguration customConfig = LoadCustomConfiguration();
                    config.AddConfiguration(customConfig);
                })
                .Build();

        }
        else
        {
            host = Host.CreateDefaultBuilder().Build();
        }

        var config = host.Services.GetRequiredService<IConfiguration>();

        var dbName = typeof(TTestClassType).Name;

        var connectionString = config?.GetConnectionString("integrationTestDatabase")?.Replace("TEST", dbName);

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new ArgumentException("connectionString has not been set");
        }

        var options = new DbContextOptionsBuilder<TContext>()
            .UseSqlServer(connectionString)
            .Options;

        var obj = Activator.CreateInstance(typeof(TContext), options, new UserContext(), new DbConnectionService()) as TContext;

        Context = obj ?? throw new InvalidCastException($"Unable to create an instance of {typeof(TContext).Name}");
        Context.Database.EnsureDeleted();
        Context.Database.EnsureCreated();

        return Context;
    }

    static IConfiguration LoadCustomConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();

        return builder.Build();
    }

    private bool _disposed = false;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                Context.Dispose();
            }

            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}