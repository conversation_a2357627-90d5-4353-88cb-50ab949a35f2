﻿using Newtonsoft.Json;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Unit.Tests;

public class ExtensionMethodTests
{
    [Fact]
    public void TakeUpdates_Updates_Corresponding_Fields()
    {
        // Arrange
        var originalReference = new Reference
        {
            Abstract = "Abstract old",
            AffiliationTextFirstAuthor = "AffiliationTextFirstAuthor old",
            Authors = "Authors old",
            CountryOfOccurrence = "CountryOfOccurrence old",
            DateRevised = DateTime.MinValue,
            Doi = "Doi old",
            FullPagination = "FullPagination old",
            Issn = "Issn old",
            Issue = "Issue old",
            Keywords = "Keywords old",
            Language = "Language old",
            MeshHeadings = "MeshHeadings old",
            SourceId = "200",
            PublicationType = "PublicationType old",
            PublicationYear = 2042,
            Title = "Title old",
            Volume = "Volume old",
            VolumeAbbreviation = "VolumeAbbreviation old",
            JournalTitle = "JournalTitle old"
        };

        var newReference = new Reference
        {
            Abstract = "Abstract new",
            AffiliationTextFirstAuthor = "AffiliationTextFirstAuthor new",
            Authors = "Authors new",
            CountryOfOccurrence = "CountryOfOccurrence new",
            DateRevised = DateTime.MinValue,
            Doi = "Doi new",
            FullPagination = "FullPagination new",
            Issn = "Issn new",
            Issue = "Issue new",
            Keywords = "Keywords new",
            Language = "Language new",
            MeshHeadings = "MeshHeadings new",
            SourceId = "2000",
            PublicationType = "PublicationType new",
            PublicationYear = 2100,
            Title = "Title new",
            Volume = "Volume new",
            VolumeAbbreviation = "VolumeAbbreviation new",
            JournalTitle = "JournalTitle new"
        };

        // Act
        originalReference.TakeUpdates(newReference);

        // Assert
        Assert.Equal(JsonConvert.SerializeObject(originalReference), JsonConvert.SerializeObject(newReference));
        Assert.Equal("Abstract new", originalReference.Abstract);
    }
}
