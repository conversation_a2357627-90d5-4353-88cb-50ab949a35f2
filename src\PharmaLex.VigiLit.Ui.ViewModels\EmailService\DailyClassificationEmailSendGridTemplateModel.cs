﻿using System.Collections.Generic;

namespace PharmaLex.VigiLit.Ui.ViewModels.EmailService;

public class DailyClassificationEmailSendGridTemplateModel
{
    public string RecipientName { get; set; }
    public string RecipientEmail { get; set; }

    public string Subject { get; set; }

    public ICollection<DailyClassificationEmailSendGridTemplateModelRow> NewPotentialCases { get; set; }
    public ICollection<DailyClassificationEmailSendGridTemplateModelRow> ChangedFromPotentialCases { get; set; }
}
