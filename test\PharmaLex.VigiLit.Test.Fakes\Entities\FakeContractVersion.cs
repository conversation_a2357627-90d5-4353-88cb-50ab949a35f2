﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.Fakes.Entities;
public class FakeContractVersion : ContractVersion
{
    public FakeContractVersion(int id, ContractType contractType, ContractVersionStatus contractVersionStatus)
    {
        Id = id;
        ContractType = contractType;
        SetContractVersion(contractVersionStatus);
    }

    public FakeContractVersion(int id, ContractType contractType, ContractVersionStatus contractVersionStatus, Contract contract , int contractId)
    {
        Id = id;
        ContractType = contractType;
        SetContractDetails(contractVersionStatus, contract, contractId);
    }
}
