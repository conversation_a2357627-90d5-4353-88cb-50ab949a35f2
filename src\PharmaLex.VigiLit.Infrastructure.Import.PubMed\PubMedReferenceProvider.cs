﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.Core.Aggregator.Models;
using PharmaLex.VigiLit.Core.Aggregator.Providers;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public class PubMedReferenceProvider : IReferenceProvider<ESearchCriteria>
{
    private readonly IEFetchClient _eFetch;
    private readonly IESearchClient _eSearch;
    private readonly IReferenceBuilder _referenceBuilder;
    private readonly ILogger<PubMedReferenceProvider> _logger;

    public PubMedReferenceProvider(IEFetchClient eFetch, IESearchClient eSearch, IReferenceBuilder referenceBuilder, ILoggerFactory loggerFactory)
    {
        _eFetch = eFetch;
        _eSearch = eSearch;
        _referenceBuilder = referenceBuilder;
        _logger = loggerFactory.CreateLogger<PubMedReferenceProvider>();
    }

    public async Task<ReferenceResults> GetReferences(ESearchCriteria criteria, DateTime timeout)
    {
        var result = new ReferenceResults();

        if (string.IsNullOrWhiteSpace(criteria.Term))
        {
            _logger.LogInformation("Search string missing.");
            result.SearchStringMissing = true;
            return result;
        }

        var searchResult = await _eSearch.Search(criteria);

        if (searchResult == null || string.IsNullOrEmpty(searchResult.WebEnv))
        {
            _logger.LogWarning("Search failed.");
            result.SearchFailed = true;
            return result;
        }

        _logger.LogInformation("{count} search results.", searchResult.Count);

        if (searchResult.Count == 0)
        {
            return result;
        }

        var eFetchResult = await _eFetch.GetArticleSet(searchResult, timeout);

        if (eFetchResult.BatchFailed)
        {
            _logger.LogWarning("Failed to fetch article set.");
            result.FetchFailed = true;
            return result;
        }

        // Not a critical failure - we fetched as many results as possible, use them.
        if (eFetchResult.ResultsCapped)
        {
            _logger.LogInformation("Results capped.");
            result.ResultsCapped = true;
        }

        foreach (var article in eFetchResult.ArticleSet.PubMedArticles)
        {
            var reference = _referenceBuilder.Build(article);

            if (!string.IsNullOrEmpty(reference.Title))
            {
                reference.Title = UnReplaceTags(reference.Title);
            }

            if (!string.IsNullOrEmpty(reference.Abstract))
            {
                reference.Abstract = UnReplaceTags(reference.Abstract);
            }
            
            reference.SourceSystem = (int) SourceSystem.PubMed;

            result.References.Add(reference);
        }

        return result;
    }

    private string UnReplaceTags(string textToBeModified)
    {
        List<(string pattern, string replacementText)> rules = new()
        {
            (pattern: @"##i##", replacementText: "<i>"),
            (pattern: @"##/i##", replacementText: "</i>"),
            (pattern: @"##b##", replacementText: "<b>"),
            (pattern: @"##/b##", replacementText: "</b>"),
            (pattern: @"##sub##", replacementText: "<sub>"),
            (pattern: @"##/sub##", replacementText: "</sub>"),
            (pattern: @"##sup##", replacementText: "<sup>"),
            (pattern: @"##/sup##", replacementText: "</sup>"),
            (pattern: @"##u##", replacementText: "<u>"),
            (pattern: @"##/u##", replacementText: "</u>")
        };

        return Utilities.RegexMultiReplace(textToBeModified, rules);
    }
}