﻿using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Test.Framework.Orderers;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Repositories;

[TestCaseOrderer(
    ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
    ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
public class EmailSuppressionRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, EmailSuppressionRepositoryTests>>, IDisposable
{
    private readonly IUserRepository _userRepository;
    private readonly IEmailSuppressionRepository _emailSuppressionRepository;

    public EmailSuppressionRepositoryTests(DatabaseFixture<VigiLitDbContext, EmailSuppressionRepositoryTests> fixture)
    {
        _userRepository = RepositoryFactory.GetRepositoryInstance<User, UserRepository, UserMappingProfile>(fixture.Context);
        _emailSuppressionRepository = RepositoryFactory.GetRepositoryInstance<EmailSuppression, EmailSuppressionRepository, EmailSuppressionMappingProfile>(fixture.Context);
    }

    [Fact, TestPriority(1)]
    public async Task GetUserSuppression_Finds_Matched_User_Suppression()
    {
        // Arrange
        _userRepository.Add(new User("given name 1", "family name 1", "<EMAIL>"));
        _userRepository.Add(new User("given name 2", "family name 2", "<EMAIL>"));
        _userRepository.Add(new User("given name 3", "family name 3", "<EMAIL>"));
        await _userRepository.SaveChangesAsync();

        await _emailSuppressionRepository.AddRangeAsync(new List<EmailSuppression> {
            new EmailSuppression{ UserId = 1, Email = "<EMAIL>", Reason = "Reason 1", EmailSuppressionType = EmailSuppressionType.Bounce},
            new EmailSuppression{ UserId = 2, Email = "<EMAIL>", Reason = "Reason 2", EmailSuppressionType = EmailSuppressionType.Block},
            new EmailSuppression{ UserId = 3, Email = "<EMAIL>", Reason = "Reason 3", EmailSuppressionType = EmailSuppressionType.Spam},
        });

        // Act
        var result = await _emailSuppressionRepository.GetUserSuppression(2);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Reason 2", result.Reason);
        Assert.Equal(EmailSuppressionType.Block, result.EmailSuppressionType);
    }

    [Fact, TestPriority(2)]
    public async Task GetUserSuppression_Finds_No_Unmatched_User_Suppression()
    {
        // Act
        var result = await _emailSuppressionRepository.GetUserSuppression(500);

        // Assert
        Assert.Null(result);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        // Empty routine to satisfy RSPEC-3881 IDisposable message
    }
}
