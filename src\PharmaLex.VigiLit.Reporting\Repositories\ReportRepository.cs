using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Core.UserManagement;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;

namespace PharmaLex.VigiLit.Reporting.Repositories;

public class ReportRepository : TrackingGenericRepository<IReportEntity>, IReportRepository
{
    protected readonly IMapper _mapper;

    public ReportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IReportEntity> GetById(int id)
    {
        return await context.Set<Report>()
            .FirstAsync(i => i.Id == id);
    }

    public async Task<IEnumerable<ReportModel>> GetReports(IUserEntity user)
    {
        var query = context.Set<Report>()
            .OrderBy(i => i.Name)
            .AsNoTracking();

        query = AddClaimsCriteria(user, query);
        query = AddCompanyCriteria(user, query);

        return await _mapper.ProjectTo<ReportModel>(query).ToListAsync();
    }

    public async Task<bool> CanUserViewReport(IUserEntity user, string controllerName)
    {
        var query = context.Set<Report>()
            .AsQueryable();

        query = AddClaimsCriteria(user, query);
        query = AddCompanyCriteria(user, query);
        query = AddControllerNameCriteria(controllerName, query);

        return await query.AnyAsync();
    }

    private static IQueryable<Report> AddClaimsCriteria(IUserEntity user, IQueryable<Report> query)
    {
        var userClaims = user.GetClaims().Select(x => x.Name).ToList();
        var claimsInReport = query.ToDictionary(row => row.Name, row => row.Claims.Split([", "], StringSplitOptions.RemoveEmptyEntries));
        var keyMatchedList = claimsInReport
            .Where(kv => kv.Value.Any(val => userClaims.Contains(val, StringComparer.OrdinalIgnoreCase)))
            .Select(kv => kv.Key).ToList();

        query = query.Where(rep => keyMatchedList.Contains(rep.Name));
        return query;
    }

    private static IQueryable<Report> AddCompanyCriteria(IUserEntity user, IQueryable<Report> query)
    {
        if (user.IsCompanyUser())
        {
            query = query.Where(r => r.AllCompanies || r.CompanyReports.Any(cr => cr.CompanyId == user.GetActiveCompanyId()));
        }

        return query;
    }

    private static IQueryable<Report> AddControllerNameCriteria(string controllerName, IQueryable<Report> query)
    {
        query = query.Where(r => r.ControllerName == controllerName);
        return query;
    }
}
