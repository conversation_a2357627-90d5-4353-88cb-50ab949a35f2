﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{1D906FF8-CD77-4D10-9801-81D06F01DFF5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Entities", "src\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj", "{321A33C8-51E5-50D8-02D3-68882E7DCE6A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Service", "src\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj", "{76112B2E-C6B0-2CC9-4599-3147D9151F01}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Service.Tests", "test\PharmaLex.VigiLit.DataExtraction.Service.Tests\PharmaLex.VigiLit.DataExtraction.Service.Tests.csproj", "{67382A44-3A28-84AF-24A5-4AF3C69D22DC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{C03248A7-D85E-47CD-8DB8-8834679381A5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{7DC1E394-C95B-46BB-BA1C-A0E20EAE986A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PhlexVisionTestHarness", "PhlexVisionTestHarness", "{8603FFF8-FB20-480D-8708-FB435DFC6684}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PhlexVisionTestHarness", "tools\src\PhlexVisionTestHarness\PhlexVisionTestHarness.csproj", "{67F330D6-4658-2FA7-DBC1-16AE33AA178C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{321A33C8-51E5-50D8-02D3-68882E7DCE6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{321A33C8-51E5-50D8-02D3-68882E7DCE6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{321A33C8-51E5-50D8-02D3-68882E7DCE6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{321A33C8-51E5-50D8-02D3-68882E7DCE6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Release|Any CPU.Build.0 = Release|Any CPU
		{67382A44-3A28-84AF-24A5-4AF3C69D22DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67382A44-3A28-84AF-24A5-4AF3C69D22DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67382A44-3A28-84AF-24A5-4AF3C69D22DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67382A44-3A28-84AF-24A5-4AF3C69D22DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{67F330D6-4658-2FA7-DBC1-16AE33AA178C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67F330D6-4658-2FA7-DBC1-16AE33AA178C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67F330D6-4658-2FA7-DBC1-16AE33AA178C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67F330D6-4658-2FA7-DBC1-16AE33AA178C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{321A33C8-51E5-50D8-02D3-68882E7DCE6A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{76112B2E-C6B0-2CC9-4599-3147D9151F01} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{67382A44-3A28-84AF-24A5-4AF3C69D22DC} = {1D906FF8-CD77-4D10-9801-81D06F01DFF5}
		{7DC1E394-C95B-46BB-BA1C-A0E20EAE986A} = {C03248A7-D85E-47CD-8DB8-8834679381A5}
		{8603FFF8-FB20-480D-8708-FB435DFC6684} = {7DC1E394-C95B-46BB-BA1C-A0E20EAE986A}
		{67F330D6-4658-2FA7-DBC1-16AE33AA178C} = {8603FFF8-FB20-480D-8708-FB435DFC6684}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5C51A469-27DC-4235-A375-798416FD6856}
	EndGlobalSection
EndGlobal
