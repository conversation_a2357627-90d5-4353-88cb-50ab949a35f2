﻿using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class MedlineCitation
{
    public PMID PMID { get; set; }

    public DateCompleted DateCompleted { get; set; }

    public DateRevised DateRevised { get; set; }

    public Article Article { get; set; }

    public MedlineJournalInfo MedlineJournalInfo { get; set; }

    [XmlArrayItem("Chemical", IsNullable = false)]
    public List<Chemical> ChemicalList { get; set; } = new List<Chemical>();

    [XmlArrayItem("SupplMeshName", IsNullable = false)]
    public List<SupplMeshName> SupplMeshList { get; set; } = new List<SupplMeshName>();

    [XmlElement("CitationSubset")]
    public List<string> CitationSubset { get; set; } = new List<string>();

    [XmlArrayItem("CommentsCorrections", IsNullable = false)]
    public List<CommentsCorrections> CommentsCorrectionsList { get; set; } = new List<CommentsCorrections>();

    [XmlArrayItem("GeneSymbol", IsNullable = false)]
    public List<string> GeneSymbolList { get; set; } = new List<string>();

    [XmlArrayItem("MeshHeading", IsNullable = false)]
    public List<MeshHeading> MeshHeadingList { get; set; } = new List<MeshHeading>();

    public int NumberOfReferences { get; set; }

    [XmlArrayItem("PersonalNameSubject", IsNullable = false)]
    public List<PersonalNameSubject> PersonalNameSubjectList { get; set; } = new List<PersonalNameSubject>();

    [XmlElement("OtherID")]
    public List<OtherID> OtherID { get; set; } = new List<OtherID>();

    [XmlElement("OtherAbstract")]
    public List<OtherAbstract> OtherAbstract { get; set; } = new List<OtherAbstract>();

    [XmlElement("KeywordList")]
    public List<KeywordList> KeywordList { get; set; } = new List<KeywordList>();

    public List<CoiStatement> CoiStatement { get; set; } = new List<CoiStatement>();

    [XmlElement("SpaceFlightMission")]
    public List<string> SpaceFlightMission { get; set; }

    [XmlArrayItem("Investigator", IsNullable = false)]
    public List<Investigator> InvestigatorList { get; set; }

    [XmlElement("GeneralNote")]
    public List<GeneralNote> GeneralNote { get; set; }

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(MedlineCitationOwner.NLM)]
    public MedlineCitationOwner Owner { get; set; } = MedlineCitationOwner.NLM;

    [XmlAttribute()]
    public MedlineCitationStatus Status { get; set; }

    [XmlAttribute()]
    public string VersionID { get; set; }

    [XmlAttribute()]
    public string VersionDate { get; set; }

    [XmlAttribute()]
    public string IndexingMethod { get; set; }
}