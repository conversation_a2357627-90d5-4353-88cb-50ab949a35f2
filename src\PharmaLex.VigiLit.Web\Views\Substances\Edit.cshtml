@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain
@using PharmaLex.VigiLit.Domain.Enums;
@using PharmaLex.VigiLit.Web.Helpers;

@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.SubstanceModel

@{
    ViewData["Title"] = "Substances";
}

<div id="substance" v-cloak>

    <form method ="post" novalidate>
        <div class="sub-header">
            @if (Model.Id == default)
            {
                <h2>Create Substance</h2> 
            }
            else
            {
                <h2>Edit Substance</h2>
            }

            <div class="controls">
                <button asp-action="@(Model.Id == 0 ? "Create": "Edit" )" type="submit" class="button icon-button-save single-submit btn-default" :disabled="!substance.name">Save</button>
                <a class="button secondary icon-button-cancel btn-default" href="/Substances">Cancel</a>
            </div>
        </div>
        <section class="flex flex-wrap flex-gap card-container"> 
            <div id="details-column" class="section-card expand">
                <h2>Details</h2>
                <div class="form-group">
                    <label>Name</label>
                    <input name="Name" v-model="substance.name" type="text" maxlength="250" />
                    <label v-if="!substance.name" class="error-color mt-1">*Substance Name is required. </label>
                </div>
                <div class="form-group">
                    <label>Type</label>
                    <div class="custom-select">
                        <select name="Type" v-model="substance.type" id="type">
                            <option v-for="type in types" v-bind:value="type.value">
                                {{type.name}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="synonyms-column" class="section-card expand" v-if="substanceSynonyms!= undefined">
                <h2>Synonyms</h2>
                <div class="form-group flex">
                    <input id="addSynonym"  v-model="newSynonym" type="text" :disabled="!substance.name"/>
                    <button id="addBtn" type="button" class="button ml-1 large" :disabled="!newSynonym" v-on:click="add(newSynonym)">Add</button>
                </div>
                <div v-if="synonymNameSameAsSubstanceText">
                    <p class="error-color">{{synonymNameSameAsSubstanceText}}</p>
                </div>
                <div v-if="synonymAlreadyExistText">
                    <p class="error-color">{{synonymAlreadyExistText}}</p>
                </div>
                <hr v-if="isVisible" class="full-width-rule"/>
                <div class="form-group">
                    <div v-if="substanceSynonyms.length">
                        <table id="table-synonyms" aria-label="Table of substance synonyms">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(synonym, index) in substanceSynonyms'>
                                    <td>{{ synonym.name }}</td>
                                    <td v-if="synonym.id >0"><i class="m-icon" title="Delete" v-on:click="remove(synonym)">delete</i></td>
                                    <td v-if="synonym.id <=0"><i class="m-icon" title="Cancel" v-bind:disabled="synonym.id <=0" v-on:click="cancel(synonym)">delete</i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    @Html.AntiForgeryToken()
                    <modal-dialog v-if="confirmResolve"
                                  width="200px"
                                  height="150px"
                                  title="Delete Synonym"
                                  v-on:close="confirmReject"
                                  v-on:confirm="confirmResolve">
                        <p>Are you sure you want to delete this synonym?</p>
                    </modal-dialog>
                    <div v-for="(synonym, index) in substanceSynonyms">
                        <input type="hidden" :name="`SubstanceSynonyms[${index}].Id`" :id="`SubstanceSynonyms[${index}].Id`" v-model="substanceSynonyms[index].id" />
                        <input type="hidden" :name="`SubstanceSynonyms[${index}].Name`" :id="`SubstanceSynonyms[${index}].Name`" v-model="substanceSynonyms[index].name" />
                        <input type="hidden" :name="`SubstanceSynonyms[${index}].SubstanceId`" :id="`SubstanceSynonyms[${index}].SubstanceId`" v-model="substance.id" />
                    </div>
                </div>
                <span v-if="isVisible">*Changes will not be made until the form is saved.</span>
            </div>
        </section>
    </form>
</div>

@section scripts{
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var model = @Html.Raw(AntiXss.ToJson(Model));

        var pageConfig = {
            appElement: '#substance',
            data: function () {
                return {
                    confirmResolve: null,
                    confirmReject: null,
                    confirmText: null,
                    newSynonym:null,
                    synonymExist: false,
                    synonymAlreadyExistText: null,
                    synonymNameSameAsSubstanceText:null,
                    substance: model,
                    substanceSynonyms: model.substanceSynonyms || [],
                    types: [
                        { value: "ch", name: "Chemical" },
                        { value: "ph", name: "Phyto (Herbal)" },
                    ],
                };
            },
            methods: {
                add(synonym) {
                    if (synonym == "" || synonym == null || synonym == undefined)
                        return;

                    this.synonymExist = this.substanceSynonyms.find(x => x.name === synonym) ? true : false;

                    if (this.substance.name === synonym) {
                        this.synonymNameSameAsSubstanceText = "*Synonym cannot be same as Substance name";
                        this.synonymAlreadyExistText = null;

                    } else if (this.synonymExist && this.newSynonym !== "") {
                        this.synonymAlreadyExistText = "*Synonym already exists";
                        this.synonymNameSameAsSubstanceText = null;

                    } else {
                        this.substanceSynonyms.unshift({ id: 0, name: synonym });
                        this.newSynonym = "";
                        this.synonymNameSameAsSubstanceText = null;
                        this.synonymAlreadyExistText = null;
                    }
                },
                remove(synonym) {
                    var confirm = new Promise((resolve, reject) => {
                        this.confirmText = `Are you sure you want to delete this synonym?`;
                        this.confirmResolve = resolve;
                        this.confirmReject = reject;
                    });

                    confirm.then(() => {
                        fetch(`/Substances/deletesynonym/${synonym.id}`, {
                            method: 'DELETE',
                            credentials: 'same-origin',
                            headers: {
                                'Content-Type': 'application/json',
                                "RequestVerificationToken": token
                            }
                        }).then(result => {
                            if (result.ok) {
                                //Remove deleted synonym from table
                                this.substanceSynonyms.splice(this.substanceSynonyms.indexOf(synonym), 1);
                                return plx.toast.show('Synonym has been removed.', 2, 'confirm',null, 1000);
                            }
                            else {
                                return plx.toast.show('Removing synonym failed.', 2, 'failed', null, 1000);
                            }

                        });
                    }).catch((err) => { console.log() })
                        .finally(() => {
                            this.confirmResolve = this.confirmReject = null;
                            this.confirmTitle = null;

                        });
                },
                cancel(synonym) {
                    this.substanceSynonyms.splice(this.substanceSynonyms.indexOf(synonym), 1);
                }
            },
            computed: {
                newSynonyms() {
                    if (this.substanceSynonyms != undefined) {
                        return this.substanceSynonyms.filter(s => s.id == 0);
                    }
                },
                isVisible() {
                    if (this.substanceSynonyms != undefined && this.newSynonyms.length > 0 &&
                        !this.synonymNameSameAsSubstanceText && !this.synonymAlreadyExistText) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            watch:{
                newSynonym(newValue){
                    if(newValue === ""){
                        this.synonymNameSameAsSubstanceText = null;
                        this.synonymAlreadyExistText = null;
                    }
                }
            },
            created() {
                if (@Model.Id !== 0) {
                    this.substance = @Html.Raw(AntiXss.ToJson(Model));
                }
            },
            mounted() {
                // Set Default selection for substance type for create substance
                if (@Model.Id == 0) {
                    this.substance.type = this.types[0].value;
                }
            }

        };
    </script>
}
    @section VueComponentScripts{
    <partial name="Components/ModalDialog" />
}
