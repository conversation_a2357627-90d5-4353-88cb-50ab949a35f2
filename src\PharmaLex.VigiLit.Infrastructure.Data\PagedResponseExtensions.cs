﻿using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using PharmaLex.VigiLit.Domain;

namespace PharmaLex.VigiLit.Infrastructure.Data;

public static class PagedResponseExtensions
{
    public static async Task<PagedResponse<TModel>> ToPagedResultAsync<TModel, TEntity>(this IQueryable<TEntity> query, IMapper mapper, PagedRequest pagedRequest)
        where TModel : class
        where TEntity : class
    {
        var countQuery = query.CountAsync();
        var itemsQuery = query;

        if (!string.IsNullOrEmpty(pagedRequest.SortBy))
        {
            if (pagedRequest.SortDirection.ToLower() == "asc")
            {
                itemsQuery = itemsQuery.OrderBy(pagedRequest.SortBy);
            }
            else
            {
                itemsQuery = itemsQuery.OrderByDescending(pagedRequest.SortBy);
            }
        }

        var result = new PagedResponse<TModel>()
        {
            Count = await countQuery,
            Items = await itemsQuery
                .Skip(pagedRequest.Page * pagedRequest.PageSize)
                .Take(pagedRequest.PageSize)
                .ProjectTo<TModel>(mapper.ConfigurationProvider)
                .ToListAsync()
        };

        return result;
    }

    public static IOrderedQueryable<T> OrderBy<T>(this IQueryable<T> source, string propertyName)
    {
        return source.OrderBy(ToLambda<T>(propertyName));
    }

    public static IOrderedQueryable<T> OrderByDescending<T>(this IQueryable<T> source, string propertyName)
    {
        return source.OrderByDescending(ToLambda<T>(propertyName));
    }

    private static Expression<Func<T, object>> ToLambda<T>(string propertyName)
    {
        var parameter = Expression.Parameter(typeof(T));
        var property = Expression.Property(parameter, propertyName);
        var propAsObject = Expression.Convert(property, typeof(object));

        return Expression.Lambda<Func<T, object>>(propAsObject, parameter);
    }
}
