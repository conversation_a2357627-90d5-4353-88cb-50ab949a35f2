﻿using AutoMapper;
using Microsoft.Extensions.Time.Testing;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.ContractManagement.Enums;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;
using Contract = PharmaLex.VigiLit.Domain.Models.Contract;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;
public class ContractServiceTests
{
    private readonly IContractService _contractService;

    private readonly Mock<IContractRepository> _contractRepository = new();
    private readonly Mock<IUserRepository> _userRepository = new();
    private readonly Mock<IVigiLitUserContext> _userContext = new();
    private readonly FakeTimeProvider _fakeTimeProvider = new FakeTimeProvider();

    public ContractServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ContractMappingProfile());
            mc.AddProfile(new ContractVersionProfile());
        });

        var mapper = mapperConfig.CreateMapper();
        _contractService = new ContractService(_contractRepository.Object, mapper, _userRepository.Object, _userContext.Object, _fakeTimeProvider);
    }
    [Fact]
    public async Task AddAsync_throws_exception_if_search_period_days_are_invalid()
    {
        // Arrange
        var model = new ContractModel()
        {
            ContractVersionPending = new ContractVersionEditModel() { SearchPeriodDays = 2, ContractType = ContractType.AdHocOnly }
        };

        _contractRepository.Setup(x => x.Add(It.IsAny<Contract>()));
        _contractRepository.Setup(x => x.SaveChangesAsync());
        var expectedMessage = "Invalid search period days";
        // Act
        var exception = await Record.ExceptionAsync(() => _contractService.AddAsync(model));

        // Assert
        Assert.NotNull(exception);
        Assert.IsType<ArgumentException>(exception);
        Assert.Equal(expectedMessage, exception.Message);
    }

    [Fact]
    public async Task UpdateAsync_throws_exception_if_search_period_days_are_invalid()
    {
        // Arrange
        var model = new ContractModel()
        {
            ContractVersionPending = new ContractVersionEditModel() { SearchPeriodDays = 75 },
            ContractVersionCurrent = new ContractVersionEditModel() { ContractType = ContractType.AdHocOnly }
        };

        _contractRepository.Setup(x => x.Add(It.IsAny<Contract>()));
        _contractRepository.Setup(x => x.SaveChangesAsync());
        var expectedMessage = "Invalid search period days";
        // Act
        var exception = await Record.ExceptionAsync(() => _contractService.UpdateAsync(model));

        // Assert
        Assert.NotNull(exception);
        Assert.IsType<ArgumentException>(exception);
        Assert.Equal(expectedMessage, exception.Message);
    }

    [Fact]
    public async Task Given_an_invalid_id_When_GetByIdAsync_is_called_Then_contractModel_is_null()
    {
        // Arrange
        _contractRepository.Setup(x => x.GetByIdAsync(99)).ReturnsAsync((FakeContract?)null);

        // Act
        var result = await _contractService.GetByIdAsync(99);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task Given_contract_with_Version_that_has_no_journals_When_GetByIdAsync_is_called_Then_contractModel_is_set_to_global()
    {
        // Arrange
        var contract = GetContractWithVersionThatHasNoJournals();
        _contractRepository.Setup(x => x.GetByIdAsync(99)).ReturnsAsync(contract);

        // Act
        var result = await _contractService.GetByIdAsync(99);

        // Assert
        Assert.Equal((int)ScreeningType.Global, result.ContractVersionCurrent.ScreeningType);
        Assert.Empty(result.ContractVersionCurrent.Journals.SelectedIds);
    }

    [Fact]
    public async Task Given_contract_with_Version_that_has_journals_When_GetByIdAsync_is_called_Then_contractModel_is_set_to_local_and_journals_attached()
    {
        // Arrange

        var contract = GetContractWithVersionThatHasJournals();

        _contractRepository.Setup(x => x.GetByIdAsync(It.IsAny<int>())).ReturnsAsync(contract);

        // Act
        var result = await _contractService.GetByIdAsync(99);

        // Assert
        Assert.Equal((int)ScreeningType.Local, result.ContractVersionCurrent.ScreeningType);
        Assert.Equal(25, result.ContractVersionCurrent.Country);
        Assert.Equal(2, result.ContractVersionCurrent.Journals.SelectedIds.Count());
    }

    [Fact]
    public async Task AddAsync_ValidModel_AddsLocalLiteratureContractAndSavesChanges()
    {
        // Arrange
        var model = new ContractModel
        {
            SubstanceId = 101,
            ProjectId = 202,
            ContractVersionPending = new ContractVersionEditModel
            {
                SearchString = "Test Search String",
                SearchPeriodDays = 56,
                ScreeningType = (int)ScreeningType.Local,
                Journals = new MultiSelectFilterRequest
                {
                    SelectedIds = new List<int> { 1, 2, 3 }
                }
            }
        };

        _userContext.SetupGet(u => u.UserId).Returns(999);

        var contract = new Contract(model.SubstanceId, model.ProjectId);
        _contractRepository.Setup(r => r.Add(It.IsAny<Contract>()));

        // Act
        await _contractService.AddAsync(model);

        // Assert
        _contractRepository.Verify(r => r.Add(It.IsAny<Contract>()), Times.Once);
        _contractRepository.Verify(r => r.SaveChangesAsync(), Times.Exactly(2));
        Assert.Equal(999, model.ContractVersionPending.UserId);
        Assert.Equal(1, model.ContractVersionPending.Version);
    }

    [Fact]
    public async Task UpdateAsync_WhenLocalLiteratureContractExists_UpdatesAndSavesChanges()
    {
        // Arrange
        var model = new ContractModel
        {
            Id = 1,
            ContractVersionCurrent = new ContractVersionEditModel
            {
                Id = 10,
                ContractType = ContractType.AdHocOnly,
                Version = 1
            },
            ContractVersionPending = new ContractVersionEditModel
            {
                Id = 20,
                SearchPeriodDays = 56,
                ContractVersionStatus = (int)ContractVersionStatus.Approved,
                Journals = new MultiSelectFilterRequest
                {
                    SelectedIds = new List<int> { 101, 102 }
                }
            }
        };

        var contract = GetContractWithVersionThatHasJournals();
        _contractRepository.Setup(r => r.GetByIdAsync(model.Id))
                               .ReturnsAsync(contract);

        _userContext.SetupGet(u => u.UserId).Returns(999);

        // Act
        await _contractService.UpdateAsync(model);

        // Assert
        _contractRepository.Verify(r => r.SaveChangesAsync(), Times.Exactly(2));
        Assert.Equal(999, model.ContractVersionPending.UserId);

        Assert.Equal(ContractWeekday.None, model.ContractVersionCurrent.ContractWeekday);
        Assert.Contains(contract.ContractVersionsInternal, v => v.Id == 20);
        Assert.Equal(2, contract.ContractVersionsInternal[^1].ContractVersionJournals.Count);
    }

    [Fact]
    public async Task AddAsync_when_contract_is_inactive_Then_start_date_is_not_set()
    {
        // Arrange
        var model = new ContractModel
        {
            SubstanceId = 101,
            ProjectId = 202,
            ContractVersionPending = new ContractVersionEditModel
            {
                IsActive = false,
                SearchString = "Test Search String",
                SearchPeriodDays = 56,
                ScreeningType = (int)ScreeningType.Local,
                Journals = new MultiSelectFilterRequest
                {
                    SelectedIds = new List<int> { 1, 2, 3 }
                }
            }
        };

        _userContext.SetupGet(u => u.UserId).Returns(999);
        _contractRepository.Setup(r => r.Add(It.IsAny<Contract>()));

        // Act
        await _contractService.AddAsync(model);

        // Assert
        _contractRepository.Verify(r => r.Add(It.Is<Contract>(
                c => c.ContractStartDate < new DateTime(1901, 1, 1))
            ), Times.Once);
    }

    [Fact]
    public async Task AddAsync_when_contract_is_active_Then_start_date_is_set()
    {
        // Arrange
        var model = new ContractModel
        {
            SubstanceId = 101,
            ProjectId = 202,
            ContractVersionPending = new ContractVersionEditModel
            {
                IsActive = true,
                SearchString = "Test Search String",
                SearchPeriodDays = 56,
                ScreeningType = (int)ScreeningType.Local,
                Journals = new MultiSelectFilterRequest
                {
                    SelectedIds = new List<int> { 1, 2, 3 }
                }
            }
        };

        _userContext.SetupGet(u => u.UserId).Returns(999);
        _contractRepository.Setup(r => r.Add(It.IsAny<Contract>()));

        _fakeTimeProvider.SetUtcNow(new DateTimeOffset(new DateTime(2025, 4, 17), TimeSpan.Zero));

        // Act
        await _contractService.AddAsync(model);

        // Assert
        _contractRepository.Verify(r => r.Add(It.Is<Contract>(
            c => c.ContractStartDate == new DateTime(2025, 4, 17))
        ), Times.Once);
    }

    private static FakeContract GetContractWithVersionThatHasNoJournals()
    {
        var contractVersions = new List<ContractVersion>
        {
            new FakeContractVersion(10, ContractType.Scheduled, ContractVersionStatus.History),
            new FakeContractVersion(20, ContractType.Scheduled, ContractVersionStatus.Approved)
            {
                ContractVersionJournals = new List<ContractVersionJournal>(),
            }
        };

        var contract = new FakeContract(99, new Substance(), new Project(), DateTime.Now, contractVersions)
        {
            ScreeningType = (int)ScreeningType.Local
        };

        return contract;
    }

    private static FakeContract GetContractWithVersionThatHasJournals()
    {
        var countryId = 25;

        var contractVersions = new List<ContractVersion>
        {
            new FakeContractVersion(10, ContractType.Scheduled, ContractVersionStatus.History),
            new FakeContractVersion(20, ContractType.Scheduled, ContractVersionStatus.Approved)
            {
                ContractVersionJournals = new List<ContractVersionJournal>
                {
                    new ContractVersionJournal
                    {
                        Journal = new Journal { CountryId = countryId },
                        JournalId = 200
                    },
                    new ContractVersionJournal
                    {
                        Journal = new Journal { CountryId = countryId },
                        JournalId = 100
                    },
                }
            }
        };

        var contract = new FakeContract(99, new Substance(), new Project(), DateTime.Now, contractVersions)
        {
            ScreeningType = (int)ScreeningType.Local
        };

        return contract;
    }
}
