﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ReferenceManagement.Contracts;

public interface IReferenceManager
{
    /// <summary>
    /// Create a new classification and reference for this contract's substance using the new reference and adds it to the importContract
    /// </summary>
    /// <param name="importContract">Contract related to reference being imported</param>
    /// <param name="reference">Reference being imported</param>
    public void UpdateImportContractWithReference(ImportContract importContract, Reference reference);

    /// <summary>
    /// Add updated reference record to ReferenceUpdate repository and increment contract count
    /// </summary>
    /// <param name="importContract">Information related to import and contract</param>
    /// <param name="identifiers">Record containing reference id, source id and date/time  </param>
    /// <param name="reference">Reference data for updated reference</param>
    public void AddReferenceUpdate(ImportContract importContract, ReferenceIdentifiers identifiers, Reference reference);
}