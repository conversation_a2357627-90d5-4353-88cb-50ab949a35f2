@model List<PharmaLex.VigiLit.Ui.ViewModels.UserManagement.UserLocksModel>
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Locks";
}

<div id="locks" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2>Locks</h2>
        <div class="controls">
        </div>
    </div>
    
    <div id="locks-dashboard">
        <p v-if="userLocks.length > 0" style="margin-bottom:15px">
            <span style="height:19px;vertical-align:top;line-height:19px;">
                Locks prevent a classification from being edited by multiple users at the same time.
                Only release locks from a user if you are confident they are not classifying.
            </span>
        </p>
        <div v-if="userLocks.length > 0" class="cards">
            <div v-for="i in userLocks" class="card">
                <p>{{i.displayFullName}}</p>
                <div class="flex-container">
                    <div class="locksCount">
                        {{i.locksCount}}
                    </div>
                    <div class="release-button">
                        <button @@click.stop.prevent="release(i)">Release</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="empty" v-else>
            <p>There are no users with locks.</p>
        </div>
    </div>
</div>

@section Scripts {
<script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: "#locks",
            data: function () {
                return {
                    userLocks: @Html.Raw(AntiXss.ToJson(Model)),
                }
            },
            methods: {
                release: function (i) {
                    fetch(`/Locks/Release/${i.id}`, {
                        method: 'GET',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            this.userLocks.splice(this.userLocks.indexOf(i), 1);
                            plx.toast.show('Locks released.', 2, 'confirm', null, 2500);
                        }
                        else {
                            plx.toast.show('Failed.', 2, 'failed', null, 5000);
                        }
                    });
                }
            },
            created() {
                //console.log(this.userLocks);
            }
        }

</script>
}
