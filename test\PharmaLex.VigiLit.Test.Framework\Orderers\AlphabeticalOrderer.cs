﻿using Xunit.Abstractions;
using Xunit.Sdk;

namespace PharmaLex.VigiLit.Test.Framework.Orderers;

/// <summary>
/// Orderer for ordering the execution of XUnit tests alphabetically.
/// </summary>
/// <remarks>
/// Use this on a test class so that the XUnit runner executes tests alphabetically by name.
/// <example>
/// <code>
/// [TestCaseOrderer(
///     ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.AlphabeticalOrderer",
///     ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
/// public class MyTestClass
/// {
/// }
/// </code>
/// </example>
/// source: <a href="https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit">https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit</a>
/// </remarks>
/// <seealso cref="Xunit.Sdk.ITestCaseOrderer" />
public class AlphabeticalOrderer : ITestCaseOrderer
{
    public IEnumerable<TTestCase> OrderTestCases<TTestCase>(
        IEnumerable<TTestCase> testCases) where TTestCase : ITestCase =>
        testCases.OrderBy(testCase => testCase.TestMethod.Method.Name);
}