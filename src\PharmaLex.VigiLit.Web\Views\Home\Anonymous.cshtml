﻿
@{
    ViewData["Title"] = "";
}

<div class="flex justify-center height-100" style="align-items: center;">
    <div class="width-40"> 
        <div class="tile mb-1">
            <div class="flex"><img src="@Cdn.Host/images/smartphlex-logo.png" alt="SmartPHLEX" class="pl-2" /></div>
        </div>

        <div class="tile">
            @if (this.ViewData["unauthorised"] != null)
            {
                <p><i class="m-icon error-color">warning</i>Your account does not have access to the <strong>VigiLit</strong> system. If you think this is an error and you should have access please contact the system administrator.</p>
            }
            <div class="flex flex-align-start justify-space-between">
                <div class="login-text">
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                </div>
                <a id="login" href="/newlogin" class="button ml-4">Login</a>
            </div>
            <div id="warning" class="flex hidden">
                <span class="m-icon mr-2 warning-color">warning_amber</span>
                <div>
                    <p>This system is optimised for use with modern browsers and you are currently using an older browser which is not supported.</p>
                    <p>Please use an alternative browser to use this system such as the latest version of Chrome, Firefox, Edge or Safari to continue.</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">
        var plx = {
            init: function () {
                try {
                    eval('var x = () => { return true; }');
                } catch (e) {                    
                    document.getElementById('login').style.display = 'none';
                    document.getElementById('warning').style.display = 'block';
                    document.getElementById('smartphlex-login').style.display = 'none';
                }
            }
        };
        (function () { plx.init(); })();
    </script>
}