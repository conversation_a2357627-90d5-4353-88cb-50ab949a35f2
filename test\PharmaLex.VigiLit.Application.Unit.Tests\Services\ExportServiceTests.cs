﻿using PharmaLex.VigiLit.Application.ClassMap;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reporting.Services;
using Shouldly;
using System.Text;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class ExportServiceTests
{
    private const string FILENAME_PREFIX = "ExportTest";
    private const string CONTENT_TYPE = "text/csv";
    private const string NEW_LINE = "\r\n";

    private readonly IReadOnlyCollection<Person> _people;
    private readonly IReadOnlyCollection<Person> _hackermans;

    private readonly IExportService _exportService;

    public ExportServiceTests()
    {
        _people = new List<Person>
        {
            new (1, "Del Boy", new DateTime(1945, 7, 12, 0, 0, 0, DateTimeKind.Utc)),
            new (2, "<PERSON>", new DateTime(1960, 11, 22, 0, 0, 0, DateTimeKind.Utc)),
            new (3, "a,b,c", new DateTime(1910, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (4, "a;b;c", new DateTime(1920, 02, 02, 0, 0, 0, DateTimeKind.Utc))
        };

        _hackermans = new List<Person>
        {
            // field
            new (11, "=foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (12, "@foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (13, "+foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (14, "-foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (15, "\tfoo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (16, "\rfoo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),

            // quoted field
            new (21, "=fo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (22, "@fo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (23, "+fo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (24, "-fo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (25, "\tfo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (26, "\rfo,o", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),

            // multiple
            new (31, "===foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (32, "@@@foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (33, "+++foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (34, "---foo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (35, "\t\t\tfoo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
            new (36, "\r\r\rfoo", new DateTime(1901, 01, 01, 0, 0, 0, DateTimeKind.Utc)),
        };

        _exportService = new ExportService();
    }

    [Fact]
    public void Export_ValidData_ReturnsFileName()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people);

        // Assert - format should be "{prefix}-{datetime}.csv"
        result.ShouldNotBeNull();
        result.FileName.ShouldNotBeNull();

        var fileName = Path.GetFileNameWithoutExtension(result.FileName);
        var separatorPosition = fileName.IndexOf('-');

        var prefix = fileName[..separatorPosition];
        prefix.ShouldBe(FILENAME_PREFIX);

        var date = fileName[(separatorPosition + 1)..];
        DateTime.TryParse(date, out _).ShouldBeTrue();

        result.FileName.ShouldEndWith(".csv");
    }

    [Fact]
    public void Export_ValidData_ReturnsContentType()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people);

        // Assert
        result.ShouldNotBeNull();
        result.ContentType.ShouldBe(CONTENT_TYPE);
    }

    [Fact]
    public void Export_EmptyData_IncludesExcelSeparator()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, Array.Empty<Person>());

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldStartWith($"sep=,{NEW_LINE}");
    }

    [Fact]
    public void Export_EmptyData_ReturnsCsvWithCamelCaseHeader()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, Array.Empty<Person>());

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldContain($"Id,Name,Date Of Birth{NEW_LINE}");
    }

    [Fact]
    public void Export_ValidData_ReturnsCsvWithCorrectRowCount()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        var lines = csv.Split(NEW_LINE);
        lines.Length.ShouldBe(_people.Count + 3);
    }

    [Fact]
    public void Export_ValidData_ReturnsCsvWithFormattedData()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldContain($"1,Del Boy,12 Jul 1945{NEW_LINE}");
        csv.ShouldContain($"2,Rodney,22 Nov 1960{NEW_LINE}");
        csv.ShouldContain($"3,\"a,b,c\",01 Jan 1910{NEW_LINE}");
        csv.ShouldContain($"4,a;b;c,02 Feb 1920{NEW_LINE}");
    }

    [Fact]
    public void Export_HackerData_EscapesInjection()
    {
        // Act
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _hackermans);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);

        // field
        csv.ShouldContain($"11,\"'=foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"12,\"'@foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"13,\"'+foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"14,\"'-foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"15,\"'\tfoo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"16,\"'\rfoo\",01 Jan 1901{NEW_LINE}");

        // quoted field
        csv.ShouldContain($"21,\"'=fo,o\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"22,\"'@fo,o\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"23,\"'+fo,o\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"24,\"'-fo,o\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"25,\"'\tfo,o\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"26,\"'\rfo,o\",01 Jan 1901{NEW_LINE}");

        // multiple
        csv.ShouldContain($"31,\"'===foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"32,\"'@@@foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"33,\"'+++foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"34,\"'---foo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"35,\"'\t\t\tfoo\",01 Jan 1901{NEW_LINE}");
        csv.ShouldContain($"36,\"'\r\r\rfoo\",01 Jan 1901{NEW_LINE}");
    }

    [Fact]
    public void Export_When_Name_IsNullOrWhitespace_Throws_ArgumentNullException()
    {
        // Act
        // Assert
        Assert.Throws<ArgumentNullException>(() => _exportService.Export<Person, CamelCaseClassMap<Person>>("", Array.Empty<Person>()));
    }

    [Fact]
    public void Export_Obeys_Specified_Delimiter()
    {
        // Act
        var exportConfig = new ExportConfiguration() { Delimiter = ";", QuoteAllFields = false };
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people, exportConfig);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldContain($"sep=;{NEW_LINE}");
        csv.ShouldContain($"Id;Name;Date Of Birth{NEW_LINE}");
        csv.ShouldContain($"1;Del Boy;12 Jul 1945{NEW_LINE}");
        csv.ShouldContain($"2;Rodney;22 Nov 1960{NEW_LINE}");
        csv.ShouldContain($"3;a,b,c;01 Jan 1910{NEW_LINE}");
        csv.ShouldContain($"4;\"a;b;c\";02 Feb 1920{NEW_LINE}");
    }

    [Fact]
    public void Export_Obeys_Mandatory_Quotes_Including_Header()
    {
        // Act
        var exportConfig = new ExportConfiguration() { Delimiter = ";", QuoteAllFields = true };
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people, exportConfig);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldContain($"sep=;{NEW_LINE}");
        csv.ShouldContain($"\"Id\";\"Name\";\"Date Of Birth\"{NEW_LINE}");
        csv.ShouldContain($"\"1\";\"Del Boy\";\"12 Jul 1945\"{NEW_LINE}");
        csv.ShouldContain($"\"2\";\"Rodney\";\"22 Nov 1960\"{NEW_LINE}");
        csv.ShouldContain($"\"3\";\"a,b,c\";\"01 Jan 1910\"{NEW_LINE}");
        csv.ShouldContain($"\"4\";\"a;b;c\";\"02 Feb 1920\"{NEW_LINE}");
    }

    [Fact]
    public void Export_Obeys_IncludeSepLine_Equals_False()
    {
        // Act
        var exportConfig = new ExportConfiguration() { Delimiter = ";", QuoteAllFields = false, IncludeSepLine = false };
        var result = _exportService.Export<Person, CamelCaseClassMap<Person>>(FILENAME_PREFIX, _people, exportConfig);

        // Assert
        result.ShouldNotBeNull();
        var csv = Encoding.UTF8.GetString(result.Data, 0, result.Data.Length);
        csv.ShouldNotContain($"sep=;{NEW_LINE}");
        csv.ShouldContain($"Id;Name;Date Of Birth{NEW_LINE}");
        csv.ShouldContain($"1;Del Boy;12 Jul 1945{NEW_LINE}");
        csv.ShouldContain($"2;Rodney;22 Nov 1960{NEW_LINE}");
        csv.ShouldContain($"3;a,b,c;01 Jan 1910{NEW_LINE}");
        csv.ShouldContain($"4;\"a;b;c\";02 Feb 1920{NEW_LINE}");
    }

    private record Person(long Id, string Name, DateTime DateOfBirth);
}