﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Ui.Controllers;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

[Authorize]
[Route("/Reports/[controller]")]
public class TrackingSheetsController: BaseReportController
{
    private readonly IUserRepository<User> _userRepository;
    private readonly IAccessControlService _accessControlService;
    private readonly IService _service;

    public TrackingSheetsController(
                IUserRepository<User> userRepository,
                IAccessControlService accessControlService,
                IService service,
                IUserSessionService userSessionService,
                IConfiguration configuration) : base(userSessionService, configuration)
    {
        _userRepository = userRepository;
        _accessControlService = accessControlService;
        _service = service;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        try
        {
            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            var model = await _service.GetTrackingSheetsPageModel(await _userRepository.GetForSecurity(CurrentUserId));
            return View("../Reports/TrackingSheets/Index", model);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("/Reports/[controller]/[action]")]
    public async Task<IActionResult> GetCards([FromBody] CardsRequest request)
    {
        try
        {
            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            var cards = await _service.GetCards(await _userRepository.GetForSecurity(CurrentUserId), request.CompanyId, request.Year);

            return Ok(cards);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("/Reports/[controller]/[action]/{trackingSheetId}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Download(int trackingSheetId)
    {
        try
        {
            var reportContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(reportContext);

            await _service.CanUserDownloadTrackingSheet(this.CurrentUserId, trackingSheetId);

            var downloadFile = await _service.Download(trackingSheetId);
            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("/Reports/[controller]/[action]")]
    public async Task<IActionResult> Create([FromBody] CreateRequest request)
    {
        try
        {
            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var createContext = new CreateReportPermissionContext(CurrentUserId, request.CompanyId);
            await _accessControlService.HasPermission(createContext);

            await _service.Create(request);

            return Ok();
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }
}