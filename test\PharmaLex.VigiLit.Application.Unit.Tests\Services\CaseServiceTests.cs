﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using System.Text;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using Xunit;

namespace PharmaLex.VigiLit.Application.Tests.Services;

public class CaseServiceTests
{
    private readonly Mock<ICaseRepository> _mockCaseRepository = new();
    private readonly Mock<ICaseCompanyRepository> _mockCaseCompanyRepository = new();
    private readonly Mock<ICaseFileRepository> _mockCaseFileRepository = new();
    private readonly Mock<ICaseFileDocumentTypeService> _mockCaseFileDocumentTypeService = new();
    private readonly Mock<ICaseDocumentUploadService> _mockCaseDocumentUploadService = new();
    private readonly Mock<ICaseDocumentService> _mockCaseDocumentService = new();
    private readonly Mock<IClassificationService> _mockClassificationService = new();
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IMapper> _mockMapper = new();

    private readonly ICaseService _caseService;

    public CaseServiceTests()
    {
        _caseService = new CaseService(
            _mockCaseRepository.Object, _mockCaseCompanyRepository.Object, _mockCaseFileRepository.Object,
            _mockCaseFileDocumentTypeService.Object, _mockCaseDocumentUploadService.Object, _mockCaseDocumentService.Object,
            _mockClassificationService.Object, _mockReferenceClassificationRepository.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task GetDocumentStream_CaseFileNotExists_ExceptionIsThrown()
    {
        // Arrange
        const int caseId = 42;
        const int caseFileId = 43;
        const int nonMatchingCaseFileId = 44;

        var caseModel = new CaseModel()
        {
            CaseFiles =
            [
                new CaseFileModel(nonMatchingCaseFileId, caseId, "Filename.txt", 1024)
            ],
        };

        // Act
        // & Assert
        await Assert.ThrowsAsync<NotFoundException>(async () => await _caseService.GetDocumentStream(caseModel, caseFileId));
    }

    [Fact]
    public async Task GetDocumentStream_DocumentDoesNotExist_ExceptionIsThrown()
    {
        // Arrange
        const int caseId = 42;
        const int caseFileId = 43;
        var caseModel = new CaseModel()
        {
            CaseFiles =
            [
                new CaseFileModel(caseFileId, caseId, "Filename.txt", 1024)
            ],
        };

        _mockCaseDocumentService
            .Setup(x => x.Exists(
                It.Is<CaseDocumentDescriptor>(cdd => cdd.CaseId == caseId && cdd.FileName == "Filename.txt"), default(CancellationToken)))
            .ReturnsAsync(false);

        // Act
        // & Assert
        await Assert.ThrowsAsync<NotFoundException>(async () => await _caseService.GetDocumentStream(caseModel, caseFileId));
    }

    [Fact]
    public async Task GetDocumentStream_DocumentExists_StreamAndFilenameIsReturned()
    {
        // Arrange
        const int caseId = 42;
        var caseModel = new CaseModel()
        {
            Id = caseId,
        };
        const int caseFileId = 43;

        caseModel.CaseFiles =
        [
            new CaseFileModel(caseFileId, caseId, "Filename.txt", 1024)
        ];

        _mockCaseDocumentService
            .Setup(x => x.Exists(
                It.Is<CaseDocumentDescriptor>(cdd => cdd.CaseId == caseId && cdd.FileName == "Filename.txt"), default(CancellationToken)))
            .ReturnsAsync(true);

        var stream = new MemoryStream(0);
        _mockCaseDocumentService.Setup(
            x => x.OpenRead(
                It.Is<CaseDocumentDescriptor>(cdd => cdd.CaseId == caseId && cdd.FileName == "Filename.txt"),
                default(CancellationToken))).ReturnsAsync(stream);

        // Act
        var result = await _caseService.GetDocumentStream(caseModel, caseFileId);

        // Assert
        Assert.Equal(stream, result.stream);
        Assert.Equal("Filename.txt", result.filename);
    }

    [Fact]
    public async Task Validate_invalid_plxId_throws_error()
    {
        // Arrange
        const int plxId = 0;
        string uploadId = "uploadId";
        var stream = new MemoryStream(0);
        var error = "Please enter a valid PLX ID before uploading case files.";

        // Act
        var res = await _caseService.Validate("FileName.txt", stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);
    }


    [Fact]
    public async Task Validate_nullOrWhitespace_fileName_throws_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        var stream = new MemoryStream(1024);
        var error = "Please select a non-empty case file to upload.";

        // Act
        var res = await _caseService.Validate("", stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);
    }

    [Fact]
    public async Task Validate_empty_stream_throws_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        MemoryStream? stream = new MemoryStream();
        var error = "Please select a non-empty case file to upload.";

        // Act
        var res = await _caseService.Validate("FileName.txt", stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);
    }

    [Fact]
    public async Task Validate_invalid_filename_throws_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        var fileName = "File{{Name}}.txt";
        var error = $"Case file \"{fileName}\" must contain only letters, numbers, '-' and '_'";

        byte[] testData = Encoding.UTF8.GetBytes("test data.");
        MemoryStream stream = new MemoryStream(testData);

        // Act
        var res = await _caseService.Validate(fileName, stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);
    }

    [Fact]
    public async Task Validate_valid_filename_throws_no_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        var fileNameWithPdfExtension = "34-FileName.pdf";
        var fileNameWithXmlExtension = "34-FileName.xml";
        var caseFileDocumentTypesList = new List<CaseFileDocumentTypeModel>
        {
            new() { Name = "Portable Document format", MaxFileSizeMb = 10, Extension = "pdf"},
            new() { Name = "Extensible Markup Language", MaxFileSizeMb = 10, Extension = "xml"}

        };
        IEnumerable<CaseFileDocumentTypeModel> caseFileDocumentTypes = caseFileDocumentTypesList;
        byte[] testData = Encoding.UTF8.GetBytes("test data.");
        MemoryStream stream = new MemoryStream(testData);
        _mockCaseFileDocumentTypeService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(caseFileDocumentTypes));

        // Act
        var validatePdfFile = await _caseService.Validate(fileNameWithPdfExtension, stream, plxId, uploadId);
        var validateXmlFile = await _caseService.Validate(fileNameWithXmlExtension, stream, plxId, uploadId);

        // Assert
        Assert.True(validatePdfFile.IsSuccess);
        Assert.Null(validatePdfFile.Error);
        Assert.True(validateXmlFile.IsSuccess);
        Assert.Null(validateXmlFile.Error);
    }

    [Fact]
    public async Task Validate_invalid_filename_extension_throws_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        var fileName = "34-FileName.jpg";
        var caseFileDocumentTypesList = new List<CaseFileDocumentTypeModel>
        {
            new() { Name = "Portable Document format", MaxFileSizeMb = 10, Extension = "pdf"},
            new() { Name = "Extensible Markup Language", MaxFileSizeMb = 10, Extension = "xml"}

        };
        IEnumerable<CaseFileDocumentTypeModel> caseFileDocumentTypes = caseFileDocumentTypesList;
        byte[] testData = Encoding.UTF8.GetBytes("test data.");
        MemoryStream stream = new MemoryStream(testData);
        _mockCaseFileDocumentTypeService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(caseFileDocumentTypes));
        var error =
            $"Case file type \"jpg\" is not allowed. Allowed types: '{string.Join("', '", caseFileDocumentTypes.Select(type => type.Extension).OrderBy(extension => extension))}'.";

        // Act
        var res = await _caseService.Validate(fileName, stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);

    }
    [Fact]
    public async Task Validate_file_size_greater_then_maxsize_throws_error()
    {
        // Arrange
        const int plxId = 34;
        string uploadId = "uploadId";
        var fileName = "34-FileName.pdf";
        var caseFileDocumentTypesList = new List<CaseFileDocumentTypeModel>
        {
            new() { Name = "Portable Document format", MaxFileSizeMb = 10, Extension = "pdf"},
            new() { Name = "Extensible Markup Language", MaxFileSizeMb = 10, Extension = "xml"}

        };
        IEnumerable<CaseFileDocumentTypeModel> caseFileDocumentTypes = caseFileDocumentTypesList;


        // Define the size of the MemoryStream in bytes
        int sizeInBytes = 12 * 1024 * 1024; // 12 MB
        byte[] buffer = new byte[sizeInBytes];
        MemoryStream stream = new MemoryStream(buffer);
        _mockCaseFileDocumentTypeService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(caseFileDocumentTypes));
        var error = $"Case file cannot be larger than 10 MB.";

        // Act
        var res = await _caseService.Validate(fileName, stream, plxId, uploadId);

        // Assert
        Assert.False(res.IsSuccess);
        Assert.Equal(error, res.Error);

    }
}