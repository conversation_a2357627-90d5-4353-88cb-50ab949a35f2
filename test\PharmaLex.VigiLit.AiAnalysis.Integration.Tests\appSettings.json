{"MessageBus": {"RetryPolicy": {"Count": 10, "Interval": "00:00:05", "Type": "Interval", "MaxInterval": "01:00:00"}, "TransportType": "RabbitMq", "RabbitMq": {"Host": "localhost", "Port": 5672, "VirtualHost": "/", "Username": "test", "Password": "test"}, "AzureServiceBus": {"Namespace": "dev", "ConnectionString": "sb://*****.servicebus.windows.net"}, "Handlers": {"CreateTaskCommandHandler": {"PrefetchCount": 100}}}}