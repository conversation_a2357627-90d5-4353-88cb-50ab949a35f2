﻿@model PharmaLex.VigiLit.Ui.ViewModels.UserManagement.AssessorModel
@using System.Linq
@using PharmaLex.Authentication.B2C
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Application
@using PharmaLex.VigiLit.Application.UserManagement.Users
@using PharmaLex.VigiLit.Domain.Interfaces.Services
@using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule
@using PharmaLex.VigiLit.Web.Helpers;

@inject IUserService userService
@inject ISubstanceService substanceService
@{
    ViewData["Title"] = "Edit Assessor";
    IEnumerable<SubstanceModel> _substances = await substanceService.GetAllAsync();
}

<div id="assessor" v-cloak>
    <form method="post">
        <div class="sub-header">
            <h2>Edit Assessor | @Model.DisplayFullName</h2>
            <div class="controls">
                <button asp-action="Edit" type="submit" class="button icon-button-save btn-default">Save</button>
                <a class="button secondary icon-button-cancel btn-default" href="/assessors">Cancel</a>
            </div>
        </div>
        <section style="background: transparent">
            <div class="flex flex-wrap">
                <div id="substances-column" v-if="user" class="flex-80-percent">
                    <section>
                        <h2>Assigned Substances</h2>
                        <div v-for="substance in selectedSubstances" :key="substance.id" class="chip">
                            <span>{{substance.name}}</span>
                            <i class="m-icon" v-on:click="deselect(substance)">close</i>
                        </div>
                        <div v-if="selectedSubstances.length === 0">
                            None
                        </div>
                        <hr /> 
                        <h2>Available Substances</h2>
                        <div class="form-group grid-container" style="grid-template-columns: 25% 25% 25% 25%;">
                            <div v-for="substance in substances" :key="substance.id" class="grid-item">
                                <div class="checkbox-list-item">
                                    <input v-bind="bindSubstance(substance)" v-on:click="toggle(substance)" />
                                    <label :for="substance.Id">{{substance.name}}</label>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                <div id="settings-column" class="pl-2 flex-20-percent">
                    <section>
                        <h2>Settings</h2>
                        <div class="form-group">
                            <label>Quality Check %</label>
                            <div class="custom-select">
                                <select name="qualityCheckPercentage" v-model="user.qcPercentage">
                                    <option value="10">10%</option>
                                    <option value="15">15%</option>
                                    <option value="20">20%</option>
                                    <option value="25">25%</option>
                                    <option value="30">30%</option>
                                    <option value="35">35%</option>
                                    <option value="40">40%</option>
                                    <option value="45">45%</option>
                                    <option value="50">50%</option>
                                    <option value="55">55%</option>
                                    <option value="60">60%</option>
                                    <option value="65">65%</option>
                                    <option value="70">70%</option>
                                    <option value="75">75%</option>
                                    <option value="80">80%</option>
                                    <option value="85">85%</option>
                                    <option value="90">90%</option>
                                    <option value="95">95%</option>
                                    <option value="100">100%</option>
                                </select>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </section>
    </form>
</div>

@section Scripts {
<script type="text/javascript">
var pageConfig = {
    appElement: '#assessor',
    data: function() {
        return {
            user: @Html.Raw(AntiXss.ToJson(Model)),
            substances: @Html.Raw(AntiXss.ToJson(_substances)),
            selectedSubstanceIds: []
        };
    },
    methods: {
        bindSubstance(substance) {
            let binding = {
                id: `${substance.id}`,
                value: `${substance.id}`,
                name: 'SelectedSubstanceIds',
                type: 'checkbox'
            }

            if (this.selectedSubstanceIds.includes(substance.id)) {
                binding.checked = 'checked';
            }

            return binding;
        },
        toggle(substance) {
            const index = this.selectedSubstanceIds.indexOf(substance.id);
            if (index === -1) {
                this.selectedSubstanceIds.push(substance.id);
            }
            else {
                this.selectedSubstanceIds.splice(index, 1);
            }
        },
        deselect(substance) {
            const index = this.selectedSubstanceIds.indexOf(substance.id);
            if (index > -1) {
                this.selectedSubstanceIds.splice(index, 1);
            }
        }
    },
    computed: {
        selectedSubstances() {
            return this.substances.filter(substances => this.selectedSubstanceIds.includes(substances.id));
        }
    },
    created() {
        this.selectedSubstanceIds = this.user.userSubstances.map(({ substanceId }) => substanceId);
    }
};
</script>
}
