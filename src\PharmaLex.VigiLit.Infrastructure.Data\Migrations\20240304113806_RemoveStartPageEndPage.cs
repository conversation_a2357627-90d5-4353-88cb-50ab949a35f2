﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class RemoveStartPageEndPage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spApogephaClassificationReport.sql");
            migrationBuilder.SqlFileExec("spMerzClassificationReport.sql");

            migrationBuilder.DropColumn(
                name: "EndPage",
                table: "ReferenceUpdates");

            migrationBuilder.DropColumn(
                name: "StartPage",
                table: "ReferenceUpdates");

            migrationBuilder.DropColumn(
                name: "EndPage",
                table: "References")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferencesHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);

            migrationBuilder.DropColumn(
                name: "StartPage",
                table: "References")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferencesHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EndPage",
                table: "ReferenceUpdates",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StartPage",
                table: "ReferenceUpdates",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "EndPage",
                table: "References",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StartPage",
                table: "References",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
