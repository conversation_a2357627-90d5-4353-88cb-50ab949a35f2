using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class DescriptorName
{
    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(EnumYN.N)]
    public EnumYN MajorTopicYN { get; set; } = EnumYN.N;

    [XmlAttribute()]
    public DescriptorNameType Type { get; set; }

    [XmlIgnore()]
    public bool TypeSpecified { get; set; }

    [XmlAttribute()]
    public string UI { get; set; }

    [XmlText()]
    public string Value { get; set; }
}