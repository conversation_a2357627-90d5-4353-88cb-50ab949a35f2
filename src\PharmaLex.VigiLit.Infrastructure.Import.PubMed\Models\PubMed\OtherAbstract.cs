using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class OtherAbstract
{
    [XmlElement("AbstractText")]
    public List<AbstractText> AbstractText { get; set; } = new List<AbstractText>();

    public string CopyrightInformation { get; set; }

    [XmlAttribute()]
    public OtherAbstractType Type { get; set; }

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue("eng")]
    public string Language { get; set; } = "eng";
}