@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.CompanyWithProjectsModel

@{
    ViewData["Title"] = "Company Projects";
    Layout = "CompanyLayout";
}

@section Buttons {
    <a href="@($"/Companies/{Model.Id}/Project/Create")" class="button btn-default">Create Company Project</a>
}

<div id="project" v-cloak>
    <section>
        <filtered-table :items="project" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section SubPageScripts {
<script type="text/javascript">

    let editUrl = "@($"/Companies/{@Model.Id}/Project/")"

    var pageConfig = {
        appElement: "#project",
        data: function () {
            return {
                link: editUrl,
                project: @Html.Raw(AntiXss.ToJson(Model.Projects)),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            edit: { },
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        },
    };
</script>
}