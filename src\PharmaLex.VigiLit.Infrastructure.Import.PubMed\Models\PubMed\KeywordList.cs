using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class KeywordList
{
    [XmlElement("Keyword")]
    public List<Keyword> Keyword { get; set; } = new List<Keyword>();


    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(KeywordListOwner.NLM)]
    public KeywordListOwner Owner { get; set; } = KeywordListOwner.NLM;
}