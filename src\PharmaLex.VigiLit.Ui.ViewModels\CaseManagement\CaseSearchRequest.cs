﻿using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

public class CaseSearchRequest
{
    public CaseStatus? CaseStatus { get; set; }

    public int? CompanyId { get; set; }

    public int? PlxId { get; set; }

    public int? SubstanceId { get; set; }

    private DateTime? _createdFrom;
    public DateTime? CreatedFrom
    { 
        get => _createdFrom;

        set =>
            _createdFrom = value != null 
                ? new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc) 
                : null;
    }

    private DateTime? _createdTo;
    public DateTime? CreatedTo
    {
        get => _createdTo;

        set =>
            _createdTo = value != null 
                ? new DateTime(value.Value.Year, value.Value.Month, value.Value.Day, 0, 0, 0, DateTimeKind.Utc).AddDays(1) 
                : null;
    }
}