﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.Core.Aggregator.Providers;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using Polly;
using Polly.Extensions.Http;
using Polly.Retry;
using static PharmaLex.VigiLit.Infrastructure.Import.PubMed.Constants;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public static class ConfigureServices
{
    public static void RegisterPubmedImportServices(this IServiceCollection services)
    {
        services.AddHttpClient();

        services.AddHttpClient<IEFetchClient, EFetchClient>(client =>
        {
            client.BaseAddress = new Uri($"{BASEURL}/{FETCH_UTIL}");
        }).AddPolicyHandler(GetRetryPolicy);

        services.AddHttpClient<IESearchClient, ESearchClient>(client =>
        {
            client.BaseAddress = new Uri($"{BASEURL}/{SEARCH_UTIL}");
        }).AddPolicyHandler(GetRetryPolicy);

        services.AddScoped<IReferenceBuilder, ReferenceBuilder>();
        services.AddScoped<IReferenceProvider <ESearchCriteria>, PubMedReferenceProvider>();
    }

    private static AsyncRetryPolicy<HttpResponseMessage> GetRetryPolicy =>
        HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
}