﻿using PharmaLex.VigiLit.Domain;

namespace PharmaLex.VigiLit.Test.Fakes.Entities;

public class FakeUserContext : IVigiLitUserContext
{
    private readonly int _userId;

    public FakeUserContext(int userId)
    {
        _userId = userId;
    }

    public int UserId
    {
        get
        {
            return _userId;
        }
    }

    public string User => throw new NotImplementedException();
}
