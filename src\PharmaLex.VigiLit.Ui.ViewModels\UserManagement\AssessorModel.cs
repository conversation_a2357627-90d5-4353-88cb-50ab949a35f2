﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

public class AssessorModel : UserModel
{
    public List<UserSubstanceModel> UserSubstances { get; set; }

    public AssessorModel()
    {
        UserSubstances = new List<UserSubstanceModel>();
    }

    public int QCPercentage { get; set; }

    public int SubstancesCount => UserSubstances.Count;

    public string DisplayQCPercentage => string.Format("{0}%", QCPercentage);
}