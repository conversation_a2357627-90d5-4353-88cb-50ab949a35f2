﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CA1822</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;CA1822</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.Aggregators.PubMed\PharmaLex.VigiLit.Aggregators.PubMed.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Core.Aggregator\PharmaLex.VigiLit.Core.Aggregator.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ESearchDTDs\mathml-in-pubmed.mod">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>

</Project>
