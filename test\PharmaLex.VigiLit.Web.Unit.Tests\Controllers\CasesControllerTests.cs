﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Case;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using PharmaLex.VigiLit.Web.Controllers;

namespace PharmaLex.VigiLit.Web.Unit.Tests.Controllers;

// NOTE: We dont normally tests controllers, but we want this now before refactoring
public class CasesControllerTests
{
    const int CaseId = 1;
    const int CaseFileId = 2;

    private readonly Mock<ICaseService> _mockCaseService = new();
    private readonly Mock<ICaseDocumentUploadService> _mockCaseDocumentUploadService = new();
    private readonly Mock<IClassificationService> _mockClassificationService = new();
    private readonly Mock<ICompanyService> _mockCompanyService = new();
    private readonly Mock<IUserRepository<User>> _mockUserRepository = new();
    private readonly Mock<ISubstanceService> _mockSubstanceService = new();
    private readonly Mock<IVigiLitUserContext> _mockVigiLitUserContext = new();
    private readonly Mock<IUserService> _mockUserService = new();
    private readonly Mock<ICaseFileDocumentTypeService> _mockCaseFileDocumentTypeService = new();
    private readonly Mock<IAccessControlService> _mockAccessControlService = new();
    private readonly Mock<IUserSessionService> _mockUserSessionService = new();
    private readonly Mock<ILogger<CasesController>> _mockLogger = new();

    private readonly CasesController _controller;

    private readonly CaseModel _caseModel;

    public CasesControllerTests()
    {
        var host = Host.CreateDefaultBuilder().Build();
        var config = host.Services.GetRequiredService<IConfiguration>();

        _controller = new CasesController(_mockCaseService.Object, _mockCaseDocumentUploadService.Object,
            _mockClassificationService.Object, _mockCompanyService.Object,
            _mockUserRepository.Object, _mockSubstanceService.Object, _mockVigiLitUserContext.Object,
            _mockUserService.Object, _mockCaseFileDocumentTypeService.Object, _mockAccessControlService.Object,
            config, _mockUserSessionService.Object, _mockLogger.Object
            );

        _caseModel = new CaseModel();
        _mockCaseService.Setup(x => x.GetByIdAsync(CaseId)).ReturnsAsync(_caseModel);
    }

    [Fact]
    public async Task DownloadFile_CaseRecordDoesNotExistForCaseId_NotFound_is_returned()
    {
        // Arrange
        _mockCaseService.Setup(x => x.GetByIdAsync(CaseId)).ReturnsAsync(default(CaseModel)!);

        // Act
        var result = await _controller.DownloadFile(CaseId, CaseFileId);

        // Assert
        Assert.IsType<NotFoundObjectResult>(result);
        Assert.Equal("Something went wrong, please try again.", ((NotFoundObjectResult)result).Value);
    }

    [Fact]
    public async Task DownloadFile_CanNotGetDocumentStream_NotFound_is_returned()
    {
        // Arrange
        const int caseCompanyId = 42;

        _caseModel.CaseCompanies =
        [
            new CaseCompanyModel(1, caseCompanyId)
        ];
        _caseModel.CaseFiles =
        [
            new CaseFileModel(CaseFileId, CaseId, "Filename.txt", 1024)
        ];

        _mockCaseService.Setup(
                x => x.GetDocumentStream(_caseModel, CaseFileId))
        .Throws<NotFoundException>();

        // Act
        var result = await _controller.DownloadFile(CaseId, CaseFileId);

        // Assert
        Assert.IsType<NotFoundObjectResult>(result);
    }

    [Fact]
    public async Task DownloadFile_CanGetDocumentStream_File_is_returned()
    {
        // Arrange
        const int caseCompanyId = 42;

        _caseModel.CaseCompanies =
        [
            new CaseCompanyModel(1, caseCompanyId)
        ];
        _caseModel.CaseFiles =
        [
            new CaseFileModel(CaseFileId, CaseId, "Filename.txt", 1024)
        ];

        var stream1 = new MemoryStream(0);
        _mockCaseService.Setup(
                x => x.GetDocumentStream(_caseModel, CaseFileId))
            .ReturnsAsync((stream1, "Filename.Txt"));

        // Act
        var result = await _controller.DownloadFile(CaseId, CaseFileId);

        // Assert
        Assert.IsType<FileStreamResult>(result);
    }

    [Fact]
    public async Task DownloadFile_UserIsDeniedAccess_UnauthorisedResult_is_returned()
    {
        // Arrange
        _mockAccessControlService.Setup(x => x.HasPermission(It.IsAny<CaseAccessContext>()))
            .ThrowsAsync(new UnauthorizedAccessException(""));

        // Act
        var result = await _controller.DownloadFile(CaseId, CaseFileId);

        // Assert
        Assert.IsType<UnauthorizedResult>(result);
    }
}