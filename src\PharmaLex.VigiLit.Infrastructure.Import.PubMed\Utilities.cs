﻿using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public static class Utilities
{
    public static string RegexMultiReplace(string textToBeModified, List<(string pattern, string replacementText)> rules)
    {
        var matchesDictionary = new Dictionary<string, string>();

        foreach (var (pattern, replacementText) in rules)
        {
            var match = Regex.Match(textToBeModified, pattern, RegexOptions.None, TimeSpan.FromSeconds(60));
            
            if (match.Success)
            {
                matchesDictionary.Add(Regex.Match(textToBeModified, pattern, RegexOptions.None, TimeSpan.FromSeconds(60)).Value, replacementText);
            }
        }

        var jointRules = string.Join("|", rules.Select(r => r.pattern));

        var replaceResult = Regex.Replace(textToBeModified, jointRules, m =>
        {
            return matchesDictionary[m.Value];
        }, 
        RegexOptions.None, TimeSpan.FromSeconds(60));

        return replaceResult;
    }
}