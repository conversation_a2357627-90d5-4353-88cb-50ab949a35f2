using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Webhook;
using System.Text.Json;

namespace PharmaLex.VigiLit.Scraping.Service.Controllers;

/// <summary>
/// Test controller for Apify webhooks - mimics the external ApifyWebhookController
/// This controller is used for testing webhook functionality from external machines
/// Similar to how PhlexVisionApiController works in the DataExtraction service
/// </summary>
[Route("api/[controller]")]
[ApiController]
[AllowAnonymous]
public class ApifyWebhookTestController : ControllerBase
{
    private readonly IApifyNotification _apifyNotification;
    private readonly ILogger<ApifyWebhookTestController> _logger;

    public ApifyWebhookTestController(
        IApifyNotification apifyNotification,
        ILogger<ApifyWebhookTestController> logger)
    {
        _apifyNotification = apifyNotification;
        _logger = logger;
    }

    /// <summary>
    /// Test endpoint for successful Apify run webhooks
    /// This mimics the external ApifyWebhookController behavior
    /// Can be called from external machines to test webhook functionality
    /// </summary>
    [HttpPost("run-succeeded")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> RunSucceeded([FromBody] ApifyWebhookPayload payload)
    {
        try
        {
            _logger.LogInformation("ApifyWebhookTestController: RunSucceeded called with payload: {Payload}", 
                LogSanitizer.Sanitize(JsonSerializer.Serialize(payload)));

            if (payload?.resource == null)
            {
                _logger.LogWarning("ApifyWebhookTestController: Invalid payload received");
                return BadRequest("Invalid payload");
            }

            await _apifyNotification.RunSucceeded(payload);

            _logger.LogInformation("ApifyWebhookTestController: RunSucceeded completed successfully for run: {RunId}", 
                LogSanitizer.Sanitize(payload.resource.id ?? "Unknown"));

            return Ok(new { message = "Webhook processed successfully", runId = payload.resource.id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ApifyWebhookTestController: Error processing RunSucceeded webhook");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Test endpoint for failed Apify run webhooks
    /// This mimics the external ApifyWebhookController behavior
    /// </summary>
    [HttpPost("run-failed")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> RunFailed([FromBody] ApifyWebhookPayload payload)
    {
        try
        {
            _logger.LogInformation("ApifyWebhookTestController: RunFailed called with payload: {Payload}", 
                LogSanitizer.Sanitize(JsonSerializer.Serialize(payload)));

            if (payload?.resource == null)
            {
                _logger.LogWarning("ApifyWebhookTestController: Invalid payload received");
                return BadRequest("Invalid payload");
            }

            await _apifyNotification.RunFailed(payload);

            _logger.LogInformation("ApifyWebhookTestController: RunFailed completed successfully for run: {RunId}", 
                LogSanitizer.Sanitize(payload.resource.id ?? "Unknown"));

            return Ok(new { message = "Failed webhook processed successfully", runId = payload.resource.id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ApifyWebhookTestController: Error processing RunFailed webhook");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Health check endpoint to verify the controller is accessible
    /// </summary>
    [HttpGet("health")]
    public IActionResult Health()
    {
        _logger.LogInformation("ApifyWebhookTestController: Health check called");
        return Ok(new 
        { 
            status = "healthy", 
            timestamp = DateTime.UtcNow,
            controller = "ApifyWebhookTestController",
            message = "Webhook test controller is running and accessible from external machines"
        });
    }

    /// <summary>
    /// Test endpoint that returns sample webhook payload structure
    /// Useful for testing and understanding the expected payload format
    /// </summary>
    [HttpGet("sample-payload")]
    public IActionResult GetSamplePayload()
    {
        var samplePayload = new ApifyWebhookPayload
        {
            userId = "sample-user-id",
            createdAt = DateTime.UtcNow,
            eventType = "ACTOR.RUN.SUCCEEDED",
            eventData = new EventData
            {
                actorId = "sample-actor-id",
                actorRunId = "sample-run-id"
            },
            resource = new Resource
            {
                id = "sample-run-id",
                actId = "sample-actor-id",
                actorTaskId = "sample-task-id",
                userId = "sample-user-id",
                startedAt = DateTime.UtcNow.AddMinutes(-10),
                finishedAt = DateTime.UtcNow,
                status = "SUCCEEDED",
                defaultDatasetId = "sample-dataset-id",
                defaultKeyValueStoreId = "sample-kvstore-id"
            }
        };

        return Ok(new 
        { 
            message = "Sample Apify webhook payload",
            payload = samplePayload,
            usage = "POST this payload to /api/ApifyWebhookTest/run-succeeded to test webhook processing"
        });
    }
}
