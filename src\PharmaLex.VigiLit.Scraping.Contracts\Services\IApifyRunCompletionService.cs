using PharmaLex.VigiLit.Scraping.Entities;

namespace PharmaLex.VigiLit.Scraping.Contracts.Services;

public interface IApifyRunCompletionService
{
    /// <summary>
    /// Checks for completed Apify runs that are ready for metadata extraction
    /// and processes them by sending ExtractDataCommand for each PDF file
    /// </summary>
    Task ProcessCompletedRunsAsync();

    /// <summary>
    /// Processes a specific Apify run for metadata extraction
    /// </summary>
    /// <param name="apifyRunId">The Apify run ID to process</param>
    Task ProcessRunAsync(string apifyRunId);

    /// <summary>
    /// Updates the status of Apify runs by checking with the Apify API
    /// </summary>
    Task UpdateRunStatusesAsync();

    /// <summary>
    /// Registers a new Apify run for tracking
    /// </summary>
    /// <param name="apifyRunId">The Apify run ID</param>
    /// <param name="actorName">The name of the Apify actor</param>
    /// <returns>The registered ApifyRun entity</returns>
    Task<ApifyRun> RegisterRunAsync(string apifyRunId, string actorName);

    /// <summary>
    /// Marks files as transferred to Azure Blob Storage for a specific run
    /// </summary>
    /// <param name="apifyRunId">The Apify run ID</param>
    Task MarkFilesTransferredAsync(string apifyRunId);

    /// <summary>
    /// Marks a run as failed
    /// </summary>
    /// <param name="apifyRunId">The Apify run ID</param>
    Task MarkRunFailedAsync(string apifyRunId);
}
