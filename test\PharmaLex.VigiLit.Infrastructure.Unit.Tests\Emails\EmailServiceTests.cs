﻿using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Emails;

public class EmailServiceTests
{
    [Theory]
    [InlineData(0, 1)]
    [InlineData(1, 1)]
    [InlineData(99, 1)]
    [InlineData(100, 1)]
    [InlineData(101, 2)]
    [InlineData(450, 5)]
    public void GetEmailParts_CreatesCorrectPartsCount(int rows, int expectedPartsCount)
    {
        var input = new Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>
        {
            { EmailReason.NewPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() },
            { EmailReason.ChangedFromPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() }
        };

        for (int i = 0; i < rows; i++)
        {
            input[EmailReason.NewPotentialCase]
                .Add(new DailyClassificationEmailSendGridTemplateModelRow(
                    123,
                    EmailReason.NewPotentialCase,
                    new ReferenceSendGridTemplateModel(),
                    new ReferenceClassificationSendGridTemplateModel()
                ));
        }

        var emailService = CreateEmailService();

        var actualParts = emailService.GetEmailParts(input);

        Assert.Equal(expectedPartsCount, actualParts.Count);
    }

    [Fact]
    public void GetEmailParts_MaintainsCategoryOrder()
    {
        var input = new Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>
        {
            { EmailReason.NewPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() },
            { EmailReason.ChangedFromPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() }
        };

        // 80 potential cases
        for (int i = 0; i < 80; i++)
        {
            input[EmailReason.NewPotentialCase]
                .Add(new DailyClassificationEmailSendGridTemplateModelRow(
                    123,
                    EmailReason.NewPotentialCase,
                    new ReferenceSendGridTemplateModel(),
                    new ReferenceClassificationSendGridTemplateModel()
                ));
        }

        // 70 changed from potential case
        for (int i = 0; i < 70; i++)
        {
            input[EmailReason.ChangedFromPotentialCase]
                .Add(new DailyClassificationEmailSendGridTemplateModelRow(
                    123,
                    EmailReason.ChangedFromPotentialCase,
                    new ReferenceSendGridTemplateModel(),
                    new ReferenceClassificationSendGridTemplateModel()
                ));
        }

        var emailService = CreateEmailService();

        var actualParts = emailService.GetEmailParts(input);

        Assert.Equal(2, actualParts.Count);

        Assert.Equal(80, actualParts[1][EmailReason.NewPotentialCase].Count);
        Assert.Equal(20, actualParts[1][EmailReason.ChangedFromPotentialCase].Count);

        Assert.Empty(actualParts[2][EmailReason.NewPotentialCase]);
        Assert.Equal(50, actualParts[2][EmailReason.ChangedFromPotentialCase].Count);
    }

    private static EmailService CreateEmailService()
    {
        return new EmailService(
            new NullLoggerFactory(),
            Substitute.For<IEmailSender>(),
            Substitute.For<ICompanyRepository>(),
            Substitute.For<IEmailLogService>(),
            Substitute.For<ICompanyInterestRepository>(),
            Substitute.For<IReferenceRepository>(),
            Substitute.For<IReferenceClassificationRepository>(),
            Substitute.For<IEmailRepository>(),
            Substitute.For<IUrlRenderHelper>());
    }
}