﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;

namespace PharmaLex.VigiLit.Reporting.Services;

public class ReportService : IReportService
{
    private readonly IReportRepository _reportRepository;

    public ReportService(IReportRepository reportRepository)
    {
        _reportRepository = reportRepository;
    }

    public async Task<IReportEntity> GetReport(int id)
    {
        return await _reportRepository.GetById(id);
    }

    public async Task<ReportsPageModel> GetReportsPageModel(IUserEntity user)
    {
        var reports = await _reportRepository.GetReports(user);

        return new ReportsPageModel
        {
            Reports = reports
        };
    }
}
