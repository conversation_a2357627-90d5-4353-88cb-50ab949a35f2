﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230412a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"CREATE NONCLUSTERED INDEX [IX_ReferenceClassifications_Pick] ON [dbo].[ReferenceClassifications]
(
	[SubstanceId] ASC,
	[ReferenceId] ASC,
	[Id] ASC,
	[ReferenceState] ASC,
	[ClassifierId] ASC
)
INCLUDE([ClassificationCategoryId],[DosageForm],[MinimalCriteria],[PSURRelevanceAbstract],[CountryOfOccurrence],[ReasonForChange],[IsActive],[PeriodEnd],[PeriodStart],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy],[MasterAssessorId],[PreAssessorId]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]");

            migrationBuilder.Sql(@"CREATE NONCLUSTERED INDEX [IX_Substances_Pick] ON [dbo].[Substances]
(
	[Name] ASC
)
INCLUDE([Id],[Type],[PeriodEnd],[PeriodStart],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_ReferenceClassifications_Pick] ON [dbo].[ReferenceClassifications] WITH ( ONLINE = OFF )");

            migrationBuilder.Sql("DROP INDEX [IX_Substances_Pick] ON [dbo].[Substances] WITH ( ONLINE = OFF )");
        }
    }
}
