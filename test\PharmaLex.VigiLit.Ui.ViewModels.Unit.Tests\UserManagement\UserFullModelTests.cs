using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using Xunit;

namespace PharmaLex.VigiLit.Ui.ViewModels.Unit.Tests.UserManagement;

public class UserFullModelTests
{
    [Fact]
    public void UserFullModel_HasClaims_DisplayClaimsTextSortedByClaimsId()
    {
        // Arrange
        var userFullModel = new UserFullModel
        {
            Claims = new List<ClaimModel>()
            {
                new ()
                {
                    Id = 3, Name = "PreAssessor", DisplayName = "Pre Assessor", ClaimType = "operator"
                },
                new ()
                {
                    Id = 2, Name = "InternalSupport", DisplayName = "Internal Support", ClaimType = "admin"
                },
                new ()
                {
                    Id = 1, Name =  "SuperAdmin", DisplayName = "Super User", ClaimType = "admin"
                },
            }
        };

        // Assert
        Assert.Equal("Super User, Internal Support, Pre Assessor", userFullModel.DisplayClaimsText);
    }

    [Fact]
    public void UserFullModel_HasNoId_DisplayNameAndEmailIsEmpty()
    {
        // Arrange
        var userFullModel = new UserFullModel
        {
            Id = 0
        };

        // Assert
        Assert.Equal("", userFullModel.DisplayNameAndEmail);
    }

    [Fact]
    public void UserFullModel_HasId_DisplayNameAndEmailIsPopulatedWithGivenNameFamilyNameAndEmail()
    {
        // Arrange
        var userFullModel = new UserFullModel
        {
            Id = 42,
            GivenName = "GivenName",
            FamilyName = "FamilyName",
            Email = "<EMAIL>"
        };

        // Assert
        Assert.Equal("GivenName FamilyName (<EMAIL>)", userFullModel.DisplayNameAndEmail);
    }
}