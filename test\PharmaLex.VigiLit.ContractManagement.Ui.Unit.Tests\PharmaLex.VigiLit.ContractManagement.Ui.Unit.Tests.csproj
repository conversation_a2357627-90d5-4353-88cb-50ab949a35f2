﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="appsettings.Development.json" />
	</ItemGroup>
	<ItemGroup>
	  <Content Include="appsettings.Development.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.ContractManagement.Ui\PharmaLex.VigiLit.ContractManagement.Ui.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
	</ItemGroup>

</Project>
