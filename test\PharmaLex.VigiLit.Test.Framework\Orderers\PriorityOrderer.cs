﻿using Xunit.Abstractions;
using Xunit.Sdk;

namespace PharmaLex.VigiLit.Test.Framework.Orderers;

/// <summary>
/// Orderer for ordering the execution of XUnit tests based on a priority number.
/// </summary>
/// <remarks>
/// Use this on a test class so that the XUnit runner executes tests by a predefined order number.
/// <example>
/// The XUnit runner in the example below will execute "Test_with_setup" before "Chained_test"
/// <code>
/// [TestCaseOrderer(
///     ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
///     ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
/// public class MyTestsByPriorityOrder
/// {
/// 
///     [Fact, TestPriority(10)]
///     public void Chained_test()
///     {
///     }
/// 
///     [Fact, TestPriority(5)]
///     public void Test_with_setup()
///     {
///     }
/// }
/// </code>
/// </example>
/// source: <a href="https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit">https://learn.microsoft.com/en-us/dotnet/core/testing/order-unit-tests?pivots=xunit</a>
/// </remarks>
/// <seealso cref="Xunit.Sdk.ITestCaseOrderer" />
public class PriorityOrderer : ITestCaseOrderer
{
    public IEnumerable<TTestCase> OrderTestCases<TTestCase>(
        IEnumerable<TTestCase> testCases) where TTestCase : ITestCase
    {
        string assemblyName = typeof(TestPriorityAttribute).AssemblyQualifiedName!;
        var sortedMethods = new SortedDictionary<int, List<TTestCase>>();
        foreach (TTestCase testCase in testCases)
        {
            int priority = testCase.TestMethod.Method
                .GetCustomAttributes(assemblyName)
                .FirstOrDefault()
                ?.GetNamedArgument<int>(nameof(TestPriorityAttribute.Priority)) ?? 0;

            GetOrCreate(sortedMethods, priority).Add(testCase);
        }

        foreach (TTestCase testCase in
                 sortedMethods.Keys.SelectMany(
                     priority => sortedMethods[priority].OrderBy(
                         testCase => testCase.TestMethod.Method.Name)))
        {
            yield return testCase;
        }
    }

    private static TValue GetOrCreate<TKey, TValue>(
        SortedDictionary<TKey, TValue> dictionary, TKey key)
        where TKey : struct
        where TValue : new() =>
        dictionary.TryGetValue(key, out TValue? result)
            ? result
            : (dictionary[key] = new TValue());
}