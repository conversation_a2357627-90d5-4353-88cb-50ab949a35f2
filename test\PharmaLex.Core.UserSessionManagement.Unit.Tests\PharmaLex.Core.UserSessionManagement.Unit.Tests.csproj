<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
	  <PackageReference Include="Moq" Version="4.20.72" />
	  <PackageReference Include="xunit" Version="2.9.3" />
	  <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="coverlet.collector" Version="6.0.4">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
    <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
    <PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
