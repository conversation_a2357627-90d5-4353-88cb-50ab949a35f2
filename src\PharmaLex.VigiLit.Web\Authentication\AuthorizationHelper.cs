﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using System;

namespace PharmaLex.VigiLit.Web.Authentication;

public static class AuthorizationHelper
{
    public static IServiceCollection AddPolicies(this IServiceCollection services)
    {
        // TODO: Fix "Invalid object name 'Claims'" exception which occurs when
        // running Update-Database on a blank database.

        var claimsRepository = services.BuildServiceProvider().GetRequiredService<IClaimRepository>();
        var claims = claimsRepository.GetAll();

        // SuperAdmin is included in all policies.
        void configure(AuthorizationOptions o)
        {
            foreach (var c in claims)
            {
                // Add a policy for each individual assigned claim.
                // - used when checking a specific user permission (aka claim).
                o.AddPolicy(c.Name, p => p.RequireAssertion(x => x.User.HasClaim(y => y.Type == Policies.CreateClaimKey(c.ClaimType, c.Name) || y.Type == Policies.CreateClaimKey(ClaimTypes.Admin, Policies.SuperAdmin))));
            }

            // Add a policy called Admin which includes SuperAdmin and InternalSupport.
            // - permission required for most admin functionality.
            o.AddPolicy(Policies.Admin, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.Admin))));

            // Add a policy called Assessor which includes PreAssessor and MasterAssessor.
            // - used for Pre-Classification functionality.
            // - even though it's usually a PreAssessor doing it, the MasterAssessor can.
            o.AddPolicy(Policies.Assessor, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.Assessor))));

            // Add a policy for uploading and editing case records
            o.AddPolicy(Policies.CaseManagement, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.CaseManagement))));

            // Add a policy for viewing case records for the user's company
            o.AddPolicy(Policies.CaseExternal, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.CaseExternal))));

            // Add a policy for all internal PharmaLex Users
            o.AddPolicy(Policies.InternalUser, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.InternalUser))));

            // Add a policy for all external PharmaLex Users
            o.AddPolicy(Policies.ExternalUser, p => p.RequireAssertion(c => c.User.HasClaim(x => Policies.ContainsClaimKey(x.Type, Policies.ExternalUser))));
        }

        return services.Configure((Action<AuthorizationOptions>)configure);
    }
}