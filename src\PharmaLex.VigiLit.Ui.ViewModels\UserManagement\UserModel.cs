﻿using PharmaLex.Core.UserManagement.Ui;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

public class UserModel : UserViewModel
{
    public DateTime? ActivationExpiryDate { get; set; }
    public string InvitationEmailLink { get; set; }
    public string DisplayActivationExpiryDate => ActivationExpiryDate.HasValue ? ActivationExpiryDate.Value.ToString("dd MMMM yyyy HH:mm") : string.Empty;


    public EmailSuppressionModel EmailSuppression { get; set; }
}