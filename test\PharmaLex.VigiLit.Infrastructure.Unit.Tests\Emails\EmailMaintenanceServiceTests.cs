﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Emails;

public class EmailMaintenanceServiceTests
{
    private readonly Mock<IEmailSuppressionRepository> _mockSuppressionRepository = new();
    private readonly Mock<ICompanyUserRepository> _mockCompanyUserRepository = new();
    private readonly Mock<ILogger<EmailMaintenanceService>> _mockLogger = new();
    private readonly IEmailMaintenanceService _emailMaintenanceServiceService;
    private readonly FakeLoggerFactory<EmailMaintenanceService> _fakeFactory;

    public EmailMaintenanceServiceTests()
    {
        var host = Host.CreateDefaultBuilder().Build();
        var config = host.Services.GetRequiredService<IConfiguration>();

        _fakeFactory = new FakeLoggerFactory<EmailMaintenanceService>(_mockLogger);

        _emailMaintenanceServiceService = new EmailMaintenanceService(_fakeFactory, config, _mockCompanyUserRepository.Object, _mockSuppressionRepository.Object);
    }

    [Fact]
    public void ProcessResult_SetsValues()
    {
        // Arrange
        var results = new List<SendGridSuppressionResponse>
        {
            new() {reason="my reason 1", status="my status 1"},
            new() {reason="my reason 2", status="my status 2"},
            new() {reason="", status=""},
            new() {reason=null, status=null},
        };

        // Act
        EmailMaintenanceService.ProcessResult(results, EmailSuppressionType.Block);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(4, results.Count);

        Assert.Equal(EmailSuppressionType.Block, results.ToList()[0].EmailSuppressionType);
        Assert.Equal("my reason 1", results.ToList()[0].reason);
        Assert.Equal("my status 1", results.ToList()[0].status);

        Assert.Equal(EmailSuppressionType.Block, results.ToList()[1].EmailSuppressionType);
        Assert.Equal("my reason 2", results.ToList()[1].reason);
        Assert.Equal("my status 2", results.ToList()[1].status);

        Assert.Equal(EmailSuppressionType.Block, results.ToList()[2].EmailSuppressionType);
        Assert.Equal("", results.ToList()[2].reason);
        Assert.Equal("", results.ToList()[2].status);

        Assert.Equal(EmailSuppressionType.Block, results.ToList()[3].EmailSuppressionType);
        Assert.Equal("", results.ToList()[3].reason);
        Assert.Equal("", results.ToList()[3].status);
    }

    [Fact]
    public async Task GetUserSuppression_Returns_User_Suppression()
    {
        // Arrange
        var suppression = new EmailSuppressionModel { EmailSuppressionType = EmailSuppressionType.Block, Reason = "Some reason" };
        _mockSuppressionRepository.Setup(x => x.GetUserSuppression(100)).ReturnsAsync(suppression);

        // Act
        var result = await _emailMaintenanceServiceService.GetUserSuppression(100);

        // Assert
        Assert.NotNull(result);
    }
}
