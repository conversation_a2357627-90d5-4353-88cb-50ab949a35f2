using System.Collections.Generic;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class ContractModel
{
    public int Id { get; set; }
    public int CompanyId { get; set; }
    public int ProjectId { get; set; }
    public int SubstanceId { get; set; }
    public string SaveMode { get; set; }
    public bool ReadOnly { get; set; }
    public string RefererUrl { get; set; }
    public ContractVersionEditModel ContractVersionCurrent { get; set; }
    public ContractVersionEditModel ContractVersionPending { get; set; }

    public List<Country> Countries { get; set; }
}
