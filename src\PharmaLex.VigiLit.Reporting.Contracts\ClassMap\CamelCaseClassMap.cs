﻿using CsvHelper.Configuration;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Reporting.Contracts.ClassMap;

public class CamelCaseClassMap<T> : ClassMap<T>
{
    public CamelCaseClassMap()
    {
        var type = typeof(T);

        var propertyInfos = type.GetProperties();
        foreach (var propertyInfo in propertyInfos)
        {
            Map(type, propertyInfo).Name(AddSpaces(propertyInfo.Name));
        }
    }

    private static string AddSpaces(string s)
        => Regex.Replace(s, "([a-z])([A-Z])", "$1 $2", RegexOptions.None, TimeSpan.FromSeconds(10));
}