﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class ReferenceClassificationRepository : TrackingGenericRepository<ReferenceClassification>, IReferenceClassificationRepository, IImportingReferenceClassificationRepository
{
    protected readonly IMapper _mapper;

    public ReferenceClassificationRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<ReferenceClassification?> GetByIdAsync(int id)
    {
        return await context.Set<ReferenceClassification>()
            .Include(r => r.Reference)
            .Include(r => r.Substance)
            .FirstOrDefaultAsync(u => u.Id == id);
    }


    public async Task<ReferenceClassification?> GetClassificationForImport(int referenceId, int substanceId)
    {
        // This needs to perform well for the import, don't re-use it.
        return await context.Set<ReferenceClassification>()
            .Where(u => u.ReferenceId == referenceId && u.SubstanceId == substanceId)
            .FirstOrDefaultAsync();
    }

    public Task AddRangeAsync(IEnumerable<ReferenceClassification> references)
    {
        return context.Set<ReferenceClassification>().AddRangeAsync(references);
    }

    public async Task<IEnumerable<ReferenceClassification>> GetReferenceClassificationsToPreClassify_Locked(List<int> classificationIds)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc => classificationIds.Contains(rc.Id))
            .OrderBy(rc => rc.Id)
            .AsNoTracking();

        var classifications = await query.ToListAsync();

        return classifications;
    }

    public async Task<ReferenceClassification?> GetReferenceClassificationToPreClassify(int importId, List<int> allLockedClassificationIds)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // new or updated
                && (rc.ReferenceState == ReferenceState.New || context.Set<ReferenceUpdate>().Any(u => u.ReferenceId == rc.ReferenceId && u.SubstanceId == rc.SubstanceId))
                // not locked
                && !allLockedClassificationIds.Contains(rc.Id)
            )
            .OrderBy(rc => rc.Id)
            .AsNoTracking();

        var classification = await query.FirstOrDefaultAsync();

        return classification;
    }

    public async Task<ReferenceClassification?> GetReferenceClassificationToPreClassify_BySubstancePreference(int userId, int importId, List<int> allLockedClassificationIds)
    {
        var userPrefSubstanceIds = await context.Set<UserSubstance>().Where(us => us.UserId == userId).Select(us => us.SubstanceId).ToListAsync();

        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // new or updated
                && (rc.ReferenceState == ReferenceState.New || context.Set<ReferenceUpdate>().Any(u => u.ReferenceId == rc.ReferenceId && u.SubstanceId == rc.SubstanceId))
                // not locked
                && !allLockedClassificationIds.Contains(rc.Id)
                // user has substance preference
                && userPrefSubstanceIds.Contains(rc.SubstanceId)
            )
            .OrderBy(rc => rc.Id)
            .AsNoTracking();

        var classification = await query.FirstOrDefaultAsync();

        return classification;
    }

    public async Task<IEnumerable<ReferenceClassification>> GetReferenceClassificationsForDuplicates(int referenceClassificationId, string doi, int substanceId, List<int> allLockedClassificationIds)
    {
        // If there's no DOI there should be no results.
        // i.e. don't return all the other records with no DOI.
        if (string.IsNullOrWhiteSpace(doi))
        {
            return Enumerable.Empty<ReferenceClassification>();
        }

        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // duplicate
                rc.Id != referenceClassificationId && rc.Reference.Doi == doi && rc.SubstanceId == substanceId
                // not locked
                && !allLockedClassificationIds.Contains(rc.Id)
            )
            .AsNoTracking();

        var classifications = await query.ToListAsync();

        return classifications;
    }

    public async Task<IEnumerable<ReferenceClassification>> GetPreclassifiedPotentialCasesForApproval(int importId)
    {
        var references = await context.Set<ReferenceClassification>()
            .Include(rc => rc.ClassificationCategory)
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // pre-classified as potential case
                && rc.ReferenceState == ReferenceState.Preclassified && rc.ClassificationCategory.PushServiceRelevant)
            .OrderBy(rc => rc.Id)
            .AsNoTracking()
            .ToListAsync();

        return references;
    }

    public async Task<IEnumerable<ReferenceClassification>> GetPreclassifiedQualityCheckSample(int assessorId, int itemsToTake, int importId)
    {
        if (itemsToTake <= 0)
        {
            return await Task.FromResult(new List<ReferenceClassification>());
        }

        var classifications = await context.Set<ReferenceClassification>()
            .Include(rc => rc.ClassificationCategory)
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // pre-classified by user as non-potential case
                && rc.ReferenceState == ReferenceState.Preclassified && !rc.ClassificationCategory.PushServiceRelevant && rc.PreAssessorId == assessorId)
            .OrderBy(rc => rc.Id)
            // take the required sample size (ordered by Id)
            .Take(itemsToTake)
            .AsNoTracking()
            .ToListAsync();

        return classifications;
    }

    public async Task<IEnumerable<QualityCheckStatusReport>> GetAssessorQualityCheckStatusReports(int importId)
    {
        // Need to know how many classifications from the sample have already been approved
        // We will subtract that number from the sample size to get the number of classifications still requiring approval
        var classifiedCounts = await context.Set<ReferenceClassification>()
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // approved non-potential case
                && rc.ReferenceState == ReferenceState.Approved && !rc.ClassificationCategory.PushServiceRelevant)
            .GroupBy(rc => new { rc.PreAssessorId })
            .AsNoTracking()
            .Select(group => new QualityCheckStatusReport
            {
                AssessorId = group.Key.PreAssessorId.GetValueOrDefault(),
                ClassifiedCount = group.Count()
            })
            .ToDictionaryAsync(qcsr => qcsr.AssessorId);

        // Figure out how many non-potential case classifications each pre-assessor has done
        // Add to the result their QC percentage
        // Add to the result their "already approved" classification count
        var reports = await context.Set<ReferenceClassification>()
            .Include(rc => rc.ClassificationCategory)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // pre-classified non-potential case
                && rc.ReferenceState == ReferenceState.Preclassified && !rc.ClassificationCategory.PushServiceRelevant)
            .GroupBy(rc => new { rc.ClassifierId, rc.Classifier.QCPercentage })
            .AsNoTracking()
            .Select(group => new QualityCheckStatusReport
            {
                AssessorId = group.Key.ClassifierId.GetValueOrDefault(),
                QCPercentage = group.Key.QCPercentage,
                PreClassifiedCount = group.Count(),
                ClassifiedCount = classifiedCounts.ContainsKey(group.Key.ClassifierId.GetValueOrDefault()) ? classifiedCounts[group.Key.ClassifierId.GetValueOrDefault()].ClassifiedCount : 0
            })
            .ToListAsync();

        return reports;
    }

    public async Task<int> GetTodoCount(int importId)
    {
        return await context.Set<ReferenceClassification>()
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // new or updated
                && (rc.ReferenceState == ReferenceState.New || context.Set<ReferenceUpdate>().Any(u => u.ReferenceId == rc.ReferenceId && u.SubstanceId == rc.SubstanceId)))
            .CountAsync();
    }

    public async Task<ReferenceClassification?> GetReferenceClassificationForEdit(int referenceClassificationId)
    {
        return await context.Set<ReferenceClassification>()
            .FirstOrDefaultAsync(rc =>
                ReferenceStateTypeGroups.IsEditable.Contains(rc.ReferenceState)
                && !context.Set<ReferenceUpdate>().Any(u => u.ReferenceId == rc.ReferenceId && u.SubstanceId == rc.SubstanceId)
                && rc.Id == referenceClassificationId);
    }

    public async Task<int> GetPreclassifiedCount(int userId, int importId)
    {
        return await context.Set<ReferenceClassification>()
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // pre-classified by current user
                && rc.ReferenceState == ReferenceState.Preclassified && rc.PreAssessorId == userId)
            .CountAsync();
    }

    public async Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetAllClassified(int importId)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // approved
                && rc.ReferenceState == ReferenceState.Approved)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceClassificationWithReferenceModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetPreclassifiedByUserId(int userId, int importId)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.ClassificationCategory)
            .Include(rc => rc.Reference)
            .Include(rc => rc.Substance).ThenInclude(s => s.SubstanceSynonyms)
            .Include(rc => rc.Classifier)
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // pre-classified by user
                && rc.PreAssessorId == userId && rc.ReferenceState == ReferenceState.Preclassified)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceClassificationWithReferenceModel>(query).ToListAsync();
    }

    public async Task<int> GetClassifiedCount(int importId)
    {
        return await context.Set<ReferenceClassification>()
            .Where(rc =>
                // belongs to selected import
                rc.ImportContractReferenceClassifications.Any(icrc => icrc.ImportId == importId)
                // classified
                && rc.ReferenceState == ReferenceState.Approved)
            .CountAsync();
    }

    public async Task<IEnumerable<ReferenceClassification>> GetAllReferenceClassificationsForSignatureAsync()
    {
        return await context.Set<ReferenceClassification>()
            .Where(c => c.ReferenceState == ReferenceState.Approved || c.ReferenceState == ReferenceState.Preclassified)
            .ToListAsync();
    }

    public async Task<ReferenceClassificationWithSubstanceModel> GetTemporalAsOfAsync(DateTime timeStampOn, int referenceClassificationId)
    {
        var result = await context.Set<ReferenceClassification>()
            // Added 1 second to fix sometimes classification temporal table saving period start milliseconds later then history action Timestamp 
            .TemporalAsOf(timeStampOn.AddMilliseconds(1000))
            .Include(rc => rc.Substance)
            .Include(rc => rc.ClassificationCategory)
            .Include(rc => rc.Classifier)
            .SingleOrDefaultAsync(rc => rc.Id == referenceClassificationId);

        return _mapper.Map<ReferenceClassificationWithSubstanceModel>(result);
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }

    public async Task<IEnumerable<ReferenceClassificationWithSubstanceModel>> GetForReferenceDetails(int referenceId, User user)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Substance)
            .Where(rc => rc.ReferenceId == referenceId);

        if (user.IsCompanyUser())
        {
            if (!user.HasActiveCompany())
            {
                return Enumerable.Empty<ReferenceClassificationWithSubstanceModel>();
            }

            query = query.Where(rc => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == user.GetActiveCompanyId() && ci.ReferenceClassificationId == rc.Id));
        }

        query = query
            .OrderBy(rc => rc.Substance.Name)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceClassificationWithSubstanceModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<ReferenceClassificationSupportModel>> Search(ClassificationSearchRequest request, User user, int? maxRows)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .AsNoTracking();

        // Security
        query = AddSecurityCriteria(query, user, request.CompanyId);

        // Filter
        query = await AddFilterCriteria(query, request);

        // Order
        query = query.OrderByDescending(rc => rc.LastUpdatedDate);

        // 100 rows for UI. 10,000 rows for export. 
        if (maxRows.HasValue)
        {
            query = query.Take(maxRows.Value);
        }

        return await _mapper.ProjectTo<ReferenceClassificationSupportModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<PrintPreviewSearchResultModel>> PrintPreview(ClassificationSearchRequest request, User user, int? maxRows)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .AsNoTracking();

        // Security
        query = AddSecurityCriteria(query, user, request.CompanyId);

        // Filter
        query = await AddFilterCriteria(query, request);

        // Order
        query = query.OrderByDescending(rc => rc.LastUpdatedDate);

        // Limit number of rows returned
        if (maxRows.HasValue)
        {
            query = query.Take(maxRows.Value);
        }

        return await _mapper.ProjectTo<PrintPreviewSearchResultModel>(query).ToListAsync();
    }

    private IQueryable<ReferenceClassification> AddSecurityCriteria(IQueryable<ReferenceClassification> query, User user, int companyId)
    {
        // Security
        if (user.IsCompanyUser())
        {
            if (!user.HasActiveCompany())
            {
                query = query.Where(rc => false);
            }
            else
            {
                query = query.Where(rc => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == user.GetActiveCompanyId() && ci.ReferenceClassificationId == rc.Id));

                // External users can only see classified classifications
                query = query.Where(rc => rc.ClassificationCategoryId.HasValue);
            }
        }
        else
        {
            // Company
            if (companyId > 0)
            {
                query = query.Where(rc => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == companyId && ci.ReferenceClassificationId == rc.Id));
            }
        }

        return query;
    }

    private async Task<IQueryable<ReferenceClassification>> AddFilterCriteria(IQueryable<ReferenceClassification> query, ClassificationSearchRequest request)
    {
        // Term
        if (!string.IsNullOrWhiteSpace(request.Term))
        {
            var termTrimmed = request.Term?.Trim();
            _ = int.TryParse(termTrimmed, out int termInt);

            // Have to fetch the reference IDs in a separate query for perf.
            var referenceIds = await context.Set<Reference>()
                .AsNoTracking()
                .Where(r => r.SourceId == termTrimmed || r.Doi == termTrimmed)
                .Select(r => r.Id)
                .ToListAsync();

            query = query.Where(rc => referenceIds.Contains(rc.ReferenceId) || rc.Id == termInt);
        }

        query = AddDateQueryTerms(query, request.CreatedFrom, request.CreatedTo, request.LastUpdatedFrom, request.LastUpdatedTo);

        // Substance
        if (request.Substances.SelectedIds != null && request.Substances.SelectedIds.Any())
        {
            query = query.Where(rc => request.Substances.SelectedIds.ToList().Contains(rc.SubstanceId));
        }

        // Classification Category
        if (request.ClassificationCategories.SelectedIds != null && request.ClassificationCategories.SelectedIds.Any())
        {
            query = query.Where(rc => request.ClassificationCategories.SelectedIds.ToList().Contains((int)rc.ClassificationCategoryId!));
        }

        // PSUR Relevant
        if (!string.IsNullOrWhiteSpace(request.PSUR) && (Enum.TryParse(request.PSUR?.Trim(), out PSURRelevanceAbstract psurEnumValue)))
        {
            query = query.Where(rc => rc.PSURRelevanceAbstract == psurEnumValue);
        }

        query = AddFullTextQueryTerms(query, request.Title, request.MeshTerm, request.Keyword, request.SearchMeshTermWithOr, request.SearchKeywordWithOr);

        return query;
    }

    private static IQueryable<ReferenceClassification> AddDateQueryTerms(IQueryable<ReferenceClassification> query, DateTime? createdFrom, DateTime? createdTo, DateTime? lastUpdatedFrom, DateTime? lastUpdatedTo)
    {
        // Created From
        if (createdFrom.HasValue)
        {
            query = query.Where(rc => rc.CreatedDate >= createdFrom.Value);
        }

        // Created To
        if (createdTo.HasValue)
        {
            query = query.Where(rc => rc.CreatedDate <= createdTo.Value.AddDays(1));
        }

        // Last Updated From
        if (lastUpdatedFrom.HasValue)
        {
            query = query.Where(rc => rc.LastUpdatedDate >= lastUpdatedFrom.Value);
        }

        // Last Updated To
        if (lastUpdatedTo.HasValue)
        {
            query = query.Where(rc => rc.LastUpdatedDate <= lastUpdatedTo.Value.AddDays(1));
        }

        return query;
    }

    private static IQueryable<ReferenceClassification> AddFullTextQueryTerms(IQueryable<ReferenceClassification> query, string title, string meshTerm, string keyword, bool searchMeshTermWithOr, bool searchKeywordWithOr)
    {
        // Title
        if (!string.IsNullOrWhiteSpace(title))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.Title, BuildContainsSearchValue(title, false)));
        }

        // Mesh Term
        if (!string.IsNullOrWhiteSpace(meshTerm))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.MeshHeadings, BuildContainsSearchValue(meshTerm, searchMeshTermWithOr)));
        }

        // Keyword
        if (!string.IsNullOrWhiteSpace(keyword))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.Keywords, BuildContainsSearchValue(keyword, searchKeywordWithOr)));
        }

        return query;
    }

    private static string BuildContainsSearchValue(string requestTerm, bool searchWithOr)
    {
        char[] separators = new char[] { ' ', ',' };
        var terms = requestTerm.Split(separators, StringSplitOptions.RemoveEmptyEntries);
        string containsSearch;
        if (terms.Length > 1)
        {
            if (searchWithOr)
            {
                containsSearch = string.Join(" OR ", terms);
            }
            else
            {
                containsSearch = string.Join(" AND ", terms);
            }
        }
        else
        {
            containsSearch = requestTerm;
        }

        return containsSearch;
    }

    public async Task<ReferenceClassificationSendGridTemplateModel?> GetForEmail(int referenceClassificationId)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Substance)
            .Include(rc => rc.ClassificationCategory)
            .Where(u => u.Id == referenceClassificationId)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceClassificationSendGridTemplateModel>(query).FirstOrDefaultAsync();
    }

    public async Task<AiSuggestedClassificationModel> GetAiSuggestedClassification(DateTime dateRevised, string substanceName, string sourceId)
    {
        var results = await context.Set<AiSuggestedClassificationStoredProcResult>()
            .FromSqlRaw("exec spGetAiSuggestedClassification @DateRevised, @SubstanceName, @SourceId",
                new SqlParameter("@DateRevised", dateRevised),
                new SqlParameter("@SubstanceName", substanceName),
                new SqlParameter("@SourceId", sourceId))
            .ToListAsync();

        return _mapper.Map<AiSuggestedClassificationModel>(results.FirstOrDefault());
    }
}