﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

public class CaseModel
{
    [Key]
    public int Id { get; set; }
    public int PlxId { get; set; }
    public int NoOfFiles { get; set; }
    public List<CaseCompanyModel> CaseCompanies { get; set; }
    public List<CaseFileModel> CaseFiles { get; set; }
    public string SubstanceName { get; set; }
    public List<int> CompanyIds { get; set; }
    public string Company { get; set; }
    public string PSUR { get; set; }
    public string MLM { get; set; }
    public string Comment { get; set; }
    public string PvSafetyDatabaseId { get; set; }
    public CaseStatus Status { get; set; }
    public string CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public string LastUpdatedDate { get; set; }
    public string LastUpdatedBy { get; set; }
}