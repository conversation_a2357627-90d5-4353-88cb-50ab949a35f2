﻿using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.Reporting.Contracts.Security;
public class ViewReportPermissionContext : IAccessControlContext
{
    /// <summary>
    /// Context for checking permissions for a user to view a report.
    /// </summary>
    /// <seealso cref="PharmaLex.VigiLit.AccessControl.IAccessControlContext" />
    public ViewReportPermissionContext(int userId, string reportName)
    {
        UserId = userId;
        ReportName = reportName;
    }

    /// <summary>
    /// Gets the user identifier of the user who wants to view the report.
    /// </summary>
    /// <value>
    /// The user identifier.
    /// </value>
    public int UserId { get; }

    /// <summary>
    /// Gets the name of the report that the user wants to view.
    /// </summary>
    /// <value>
    /// The name of the report.
    /// </value>
    public string ReportName { get; }
}
