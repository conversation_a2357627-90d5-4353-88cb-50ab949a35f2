﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class RefactorEmailCols : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_IsModified_ModifiedDateSent",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_IsSigned_DateSent",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "IsModified",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.DropColumn(
                name: "IsSigned",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.RenameColumn(
                name: "ModifiedDateSent",
                table: "ImportContractReferenceClassifications",
                newName: "EmailSent");

            migrationBuilder.RenameColumn(
                name: "DateSent",
                table: "ImportContractReferenceClassifications",
                newName: "EmailQueued");

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_EmailQueued_EmailSent",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "EmailQueued", "EmailSent" });

            // efcore decided to rename cols instead of making new ones
            // so wipe the data manually...
            migrationBuilder.Sql("exec('UPDATE ImportContractReferenceClassifications SET EmailQueued = NULL')");
            migrationBuilder.Sql("exec('UPDATE ImportContractReferenceClassifications SET EmailSent = NULL')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_EmailQueued_EmailSent",
                table: "ImportContractReferenceClassifications");

            migrationBuilder.RenameColumn(
                name: "EmailSent",
                table: "ImportContractReferenceClassifications",
                newName: "ModifiedDateSent");

            migrationBuilder.RenameColumn(
                name: "EmailQueued",
                table: "ImportContractReferenceClassifications",
                newName: "DateSent");

            migrationBuilder.AddColumn<bool>(
                name: "IsModified",
                table: "ImportContractReferenceClassifications",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsSigned",
                table: "ImportContractReferenceClassifications",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_IsModified_ModifiedDateSent",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "IsModified", "ModifiedDateSent" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_IsSigned_DateSent",
                table: "ImportContractReferenceClassifications",
                columns: new[] { "IsSigned", "DateSent" });
        }
    }
}
