﻿using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reports.Accovion.Domain.Models;
using System.Globalization;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain;

internal class Service : IAccovionClientReportService
{
    private readonly IRepository _accovionClientReportRepository;
    private readonly IExportService _exportService;

    public Service(
        IRepository accovionClientReportRepository, 
        IExportService exportService)
    {
        _accovionClientReportRepository = accovionClientReportRepository;
        _exportService = exportService;
    }

    public PageModel GetPageModel()
    {
        var years = GetYears();
        var selectedYear = years.Max();
        var cards = GetCards(selectedYear);

        return new PageModel
        {
            Years = years,
            SelectedYear = selectedYear,
            Cards = cards
        };
    }

    public IEnumerable<CardModel> GetCards(int year)
    {
        if (!GetYears().Contains(year))
        {
            throw new ArgumentException("Invalid year.");
        }

        var cards = new List<CardModel>();

        for (int month = 12; month >= 1; month--)
        {
            var card = new CardModel
            {
                Year = year,
                Month = month,
                MonthName = CultureInfo.InvariantCulture.DateTimeFormat.GetMonthName(month),
                IsFuture = IsFuture(year, month)
            };

            cards.Add(card);
        }

        return cards;
    }

    public async Task<DownloadFile> Download(int year, int month)
    {
        if (!GetYears().Contains(year))
        {
            throw new ArgumentException("Invalid year.");
        }

        if (month < 1 || month > 12)
        {
            throw new ArgumentException("Invalid month.");
        }
        
        var data = await _accovionClientReportRepository.GetStoredProcResults(year, month);

        var exportConfig = new ExportConfiguration() { Delimiter = ",", QuoteAllFields = false, IncludeSepLine = false };

        var export = _exportService.Export<Result, ResultClassMap>("AccovionClientReport", data, exportConfig);

        return new DownloadFile()
        {
            FileName = $"Accovion Cases-data_{year}_{month}.csv",
            ContentType = export.ContentType,
            Bytes = export.Data
        };
    }

    private static bool IsFuture(int year, int month)
    {
        var now = DateTime.UtcNow;
        return year == now.Year && month >= now.Month;
    }

    private static List<int> GetYears()
    {
        var startYear = 2024;
        var currentYear = DateTime.UtcNow.Year;

        return Enumerable.Range(startYear, currentYear - startYear + 1).Reverse().ToList();
    }
}
