using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public partial class DataBankList
{
    [XmlElement("DataBank")]
    public List<DataBank> DataBank { get; set; } = new List<DataBank>();

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(DataBankListCompleteYN.Y)]
    public DataBankListCompleteYN CompleteYN { get; set; } = DataBankListCompleteYN.Y;
}