@model List<PharmaLex.VigiLit.Ui.ViewModels.EmailService.EmailMessageClassificationModel>
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Email Message Classifications";
}

<div id="emailMessageClassifications">
    <div class="sub-header">
        <h2>Email Message Classifications</h2>
        <div class="controls">
            <a class="button btn-default" onclick="history.back()">Back</a>
        </div>
    </div>
    <section>
        <filtered-table :items="emailMessageClassifications" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

    <script type="text/javascript">

        var pageConfig = {
            appElement: "#emailMessageClassifications",
            data: function () {
                return {
                    link: '/References/Classifications/',
                    emailMessageClassifications: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'sourceId',
                                sortKey: 'sourceId',
                                header: 'SourceId',
                                type: 'text',
                                style: 'width: 9%;'
                            },
                            {
                                dataKey: 'doi',
                                sortKey: 'doi',
                                header: 'DOI',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'referenceClassificationId',
                                sortKey: 'referenceClassificationId',
                                header: 'PLX ID',
                                type: 'text',
                                style: 'width: 9%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'title',
                                sortKey: 'title',
                                header: 'Title',
                                type: 'html',
                                style: 'width: 30%;'
                            },
                            {
                                dataKey: 'emailReason',
                                sortKey: 'emailReason',
                                header: 'Email Reason',
                                type: 'text',
                                style: 'width: 13%;'
                            },
                            {
                                dataKey: 'classificationCategoryName',
                                sortKey: 'classificationCategoryName',
                                header: 'Classification Category',
                                type: 'text',
                                style: 'width: 13%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'sourceId',
                            options: [],
                            type: 'search',
                            header: 'Search Source Id',
                            fn: v => p => p.sourceId.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'doi',
                            options: [],
                            type: 'search',
                            header: 'Search DOI',
                            fn: v => p => p.doi.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'referenceClassificationId',
                            options: [],
                            type: 'search',
                            header: 'Search PLX ID',
                            fn: v => p => p.referenceClassificationId.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'substanceName',
                            options: [],
                            type: 'search',
                            header: 'Search Substance',
                            fn: v => p => p.substanceName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'title',
                            options: [],
                            type: 'search',
                            header: 'Search Title',
                            fn: v => p => p.title.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'emailReason',
                            options: [],
                            type: 'search',
                            header: 'Search Email Reason',
                            fn: v => p => p.emailReason.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'classificationCategoryName',
                            options: [],
                            type: 'search',
                            header: 'Search Classification Case',
                            fn: v => p => p.classificationCategoryName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}