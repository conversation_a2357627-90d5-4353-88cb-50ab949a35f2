﻿using System.Globalization;
using System.Text;
using System.Web;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Exceptions;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.ESearch;
using static PharmaLex.VigiLit.Infrastructure.Import.PubMed.Constants;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public class ESearchClient : PubMedClientBase, IESearchClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ESearchClient> _logger;

    public ESearchClient(HttpClient httpClient, ILoggerFactory loggerFactory, IConfiguration configuration)
        : base(configuration)
    {
        _httpClient = httpClient;
        _logger = loggerFactory.CreateLogger<ESearchClient>();
    }

    public async Task<ESearchResult> Search(ESearchCriteria criteria)
    {
        await RateLimiter();

        string queryString = BuildQueryString(criteria);

        _logger.LogInformation("eSearch: {url}", _httpClient.BaseAddress + queryString);

        try
        {
            List<Retry> retries = new()
            {
                new Retry(1, 2),
                new Retry(2, 10),
                new Retry(3, 30)
            };

            ESearchResult result = await SearchWithRetriesRecursive(queryString, retries);
            return result;
        }
        catch (Exception e)
        {
            _logger.LogWarning("Search returning null due to exception. \nError: {error}", e.ToString());
            return null;
        }
    }

    private async Task<ESearchResult> SearchWithRetriesRecursive(string queryString, List<Retry> retries)
    {
        try
        {
            using Stream stream = await _httpClient.GetStreamAsync(queryString);

            ESearchResult result = Deserialize(stream);

            if (string.IsNullOrEmpty(result.WebEnv))
            {
                throw new WebEnvException("WebEnv is null or empty.");
            }

            if (retries.Any(r => r.Done))
            {
                _logger.LogWarning("Recovered.");
            }

            return result;
        }
        catch (Exception e) when (e is HttpRequestException || e is IOException || e is WebEnvException)
        {
            if (retries.Any(r => !r.Done))
            {
                Retry retry = retries.First(r => !r.Done);
                retry.Done = true;

                _logger.LogWarning("ESearch request failed: exceptionType={exceptionType}, using retry behaviour. retry={retry}/{totalRetries}, delaySeconds={delaySeconds}",
                    e.GetType().Name, retry.Number, retries.Count, retry.DelaySeconds);

                await Task.Delay(retry.DelaySeconds * 1000);

                return await SearchWithRetriesRecursive(queryString, retries);
            }
            else
            {
                throw;
            }
        }
    }

    private string BuildQueryString(ESearchCriteria criteria)
    {
        string modDate = criteria.ModifiedDate.Value.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture);

        string term = $"{criteria.Term} AND ({modDate}[Date - Modification])";

        if (criteria.CreateDateFrom.HasValue)
        {
            string createDateFrom = criteria.CreateDateFrom.Value.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture);

            term += $" AND ({createDateFrom}:{modDate}[Date - Create])";
        }

        StringBuilder builder = new();
        builder.Append($"?db={DBNAME}");
        builder.Append($"&term={HttpUtility.UrlEncode(term)}");
        builder.Append($"&retmax=0"); // don't return the default 20 UIDs in the search result xml
        builder.Append($"&{USE_HISTORY_YES}");
        builder.Append($"&api_key={PubMedApiKey}");

        return builder.ToString();
    }

    private ESearchResult Deserialize(Stream stream)
    {
        using XmlReader reader = CreateXmlReader(stream);
        
        XmlSerializer serializer = new XmlSerializer(typeof(ESearchResult));
        
        try
        {            
            var esearchResult = (ESearchResult)serializer.Deserialize(reader);
            return esearchResult;
        }
        catch (Exception ex)
        {
            _logger.LogError("Could not deserialize response from ESearch", ex);
        }

        return null;
    }
}
