﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ContractManagement.Ui.Services;
using Xunit;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests;

public class CriteriaValidatorTests
{
    private readonly ICriteriaValidator _criteriaValidator;
    private static IConfiguration Configuration { get; } = new ConfigurationBuilder().AddJsonFile("appsettings.Development.json").Build();


    public CriteriaValidatorTests()
    {
        using var factory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = factory.CreateLogger<CriteriaValidator>();
        _criteriaValidator = new CriteriaValidator(logger, Configuration);
    }

    [Theory]
    // Single letter term
    [InlineData("a")]
    // Multiple letter term
    [InlineData("ab")]
    // Multiple letter term
    [InlineData("abc")]
    // Single letter terms
    [InlineData("a b")]
    // Multiple letter terms
    [InlineData("ab ab")]
    // Multiple letter terms
    [InlineData("abc abc")]
    // Single letter phase
    [InlineData("\"a\"")]
    // Multiple letter phrase
    [InlineData("\"ab\"")]
    // Multiple letter phrase
    [InlineData("\"abc\"")]
    // Multiple single terms in phase
    [InlineData("\"a b\"")]
    // Multiple letter terms in phase
    [InlineData("\"ab ab\"")]
    // Multiple letter terms in phase
    [InlineData("\"abc abc\"")]
    // Single letter term in parentheses
    [InlineData("(a)")]
    // Multiple letter term in parentheses
    [InlineData("(ab)")]
    // Multiple letter term in parentheses
    [InlineData("(abc)")]
    // Single letter terms in parentheses
    [InlineData("(a b)")]
    // Multiple letter terms in parentheses
    [InlineData("(ab ab)")]
    // Multiple letter terms in parentheses
    [InlineData("(abc abc)")]
    // Single letter phase in parentheses
    [InlineData("(\"a\")")]
    // Multiple letter phrase in parentheses
    [InlineData("(\"ab\")")]
    // Multiple letter phrase in parentheses
    [InlineData("(\"abc\")")]
    // Multiple single terms in phase in parentheses
    [InlineData("(\"a b\")")]
    // Multiple letter terms in phase in parentheses
    [InlineData("(\"ab ab\")")]
    // Multiple letter terms in phase in parentheses
    [InlineData("(\"abc abc\")")]
    // Single letter terms with AND
    [InlineData("a AND b")]
    // Multiple letter terms with AND
    [InlineData("ab AND ab")]
    // Multiple letter terms with AND
    [InlineData("abc AND abc")]
    // Single letter terms in parentheses with AND
    [InlineData("(a AND b)")]
    // Multiple letter terms in parentheses with AND
    [InlineData("(ab AND ab)")]
    // Multiple letter terms in parentheses with AND
    [InlineData("(abc AND abc)")]
    // Single letter phrases with AND
    [InlineData("\"a\" AND \"b\"")]
    // Multiple letter phrases with AND
    [InlineData("\"ab\" AND \"ab\"")]
    // Multiple letter phrases with AND
    [InlineData("\"abc\" AND \"abc\"")]
    // Single letter phrases in parentheses with AND
    [InlineData("(\"a\" AND \"b\")")]
    // Multiple letter phrases in parentheses with AND
    [InlineData("(\"ab\" AND \"ab\")")]
    // Multiple letter phrases in parentheses with AND
    [InlineData("(\"abc\" AND \"abc\")")]
    // Single letter terms with OR
    [InlineData("a OR b")]
    // Multiple letter terms with OR
    [InlineData("ab OR ab")]
    // Multiple letter terms with OR
    [InlineData("abc OR abc")]
    // Single letter terms in parentheses with OR
    [InlineData("(a OR b)")]
    // Multiple letter terms in parentheses with OR
    [InlineData("(ab OR ab)")]
    // Multiple letter terms in parentheses with OR
    [InlineData("(abc OR abc)")]
    // Single letter phrases with OR
    [InlineData("\"a\" OR \"b\"")]
    // Multiple letter phrases with OR
    [InlineData("\"ab\" OR \"ab\"")]
    // Multiple letter phrases with OR
    [InlineData("\"abc\" OR \"abc\"")]
    // Single letter phrases in parentheses with OR
    [InlineData("(\"a\" OR \"b\")")]
    // Multiple letter phrases in parentheses with OR
    [InlineData("(\"ab\" OR \"ab\")")]
    // Multiple letter phrases in parentheses with OR
    [InlineData("(\"abc\" OR \"abc\")")]
    // Matching neutral double quotes
    [InlineData("\"neutral double quotes\"")]
    // Matching typographic quotes
    [InlineData("“typographic quotes”")]
    // Nest parentheses
    [InlineData("((a AND b) AND c)")]
    // Extra whitespace
    [InlineData("  (  (  a   AND   b  )   AND   c  )  ")]
    // Wildcard with four characters before it
    [InlineData("test*")]
    // Wildcard with five characters before it
    [InlineData("tests*")]
    // Multiple wildcards
    [InlineData("test*a*")]
    // Term with a tag
    [InlineData("Term[ad]")]
    // Two terms with tags
    [InlineData("Term1[ad] AND Term2[sb]")]
    // Phrase with allowed symbols
    [InlineData("(\"-(),/'+_.`\")")]
    // Criteria with new lines 
    [InlineData(@"Term1 AND
                          Term2")]
    // Negation of term
    [InlineData("NOT term")]
    // Negation of phrase
    [InlineData("NOT \"phrase\"")]
    public void ValidSyntaxShouldPass(string criteria)
    {
        // Arrange 

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.Empty(errorMessages);
        Assert.True(result);
    }

    [Theory]
    [InlineData("(a", "Unmatched right bracket in position 1")]
    [InlineData("\"neutral double quote", "Mis-matched quote in position 1")]
    [InlineData("“typographic quote", "Mis-matched quote in position 1")]
    [InlineData("”not opening typographic quote", "Unexpected character \"”\" in position 1")]
    [InlineData("\"neutral double quote and typographic quote”", "Mis-matched quote in position 1")]
    [InlineData("“typographic quote and neutral double quote\"", "Mis-matched quote in position 1")]
    [InlineData("Term[", "Invalid Search Field Tag  in position 5")]
    [InlineData("Term[ad", "Mis-matched closing tag in position 7")]
    public void InvalidSyntaxShouldFail(string criteria, string errorMessage)
    {
        // Arrange

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.False(result);
        Assert.Equal(errorMessage, errorMessages.First().ErrorMessage);
    }

    [Theory]
    [InlineData("*", "Wildcard must start with at least 4 characters in position 1")]
    [InlineData("t*", "Wildcard must start with at least 4 characters in position 2")]
    [InlineData("te*", "Wildcard must start with at least 4 characters in position 3")]
    [InlineData("tes*", "Wildcard must start with at least 4 characters in position 4")]
    public void InvalidWildcardSyntaxShouldFail(string criteria, string errorMessage)
    {
        // Arrange

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.False(result);
        Assert.Equal(errorMessage, errorMessages.First().ErrorMessage);
    }

    [Theory]
    [InlineData("NOT \"phrase\"")]
    [InlineData("NOT (a b c)")]
    [InlineData("NOT (NOT a)")]
    public void ValidNegationSyntaxShouldPass(string criteria)
    {
        // Arrange

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.Empty(errorMessages);
        Assert.True(result);
    }

    [Theory]
    [InlineData("NOT ", "Missing expression in position 4")]
    [InlineData("NOT (", "Unexpected character \"\0\" in position 5")]
    [InlineData("NOT ((", "Unmatched right bracket in position 5")]
    [InlineData("NOT OR NOT", "NOT cannot be followed by AND, OR or NOT in position 5")]
    public void InvalidNegationSyntaxShouldFail(string criteria, string errorMessage)
    {
        // Arrange

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.Contains(errorMessage, errorMessages.First().ErrorMessage);
        Assert.False(result);
    }

    [Theory]
    [InlineData("(Pre-clinical)")]
    public void ValidSyntaxOtherCharactersShouldPass(string criteria)
    {
        // Arrange 

        // Act
        var result = _criteriaValidator.TryValidate(criteria, out var errorMessages);

        // Assert
        Assert.Empty(errorMessages);
        Assert.True(result);
    }

}