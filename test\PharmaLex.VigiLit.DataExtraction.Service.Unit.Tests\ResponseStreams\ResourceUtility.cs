﻿using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Resources;
using System.Text.Json;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.ResponseStreams;

/// <summary>
/// Utility class for returning common resources that can be used as test data.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public static class ResourceUtility
{
    /// <summary>
    /// Gets a stream containing one reference.
    /// </summary>
    /// <returns></returns>
    public static Stream GetOneGoodReference()
    {
        return GetStream("ResponseBody_example_one_good_reference");
    }

    private static MemoryStream GetStream(string resourceName)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceManager = new ResourceManager("PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.ResponseStreams.ResponseStreams", assembly);
        var obj = resourceManager.GetObject(resourceName);
        var jsonString = JsonSerializer.Serialize(obj);
        var byteArray = JsonSerializer.Deserialize<byte[]>(jsonString);
        return new MemoryStream(byteArray!);
    }
}