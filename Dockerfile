#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
ARG projectName=PharmaLex.VigiLit.AiAnalysis
ARG configuration=Release

ARG waitScript='https://pxgstaticstoprodneu.blob.core.windows.net/scripts/wait'

FROM phlexglobal.azurecr.io/pg-aspnet-base:8.0-alpine AS base
USER app
WORKDIR /app

FROM phlexglobal.azurecr.io/pg-sdk-base:8.0-alpine AS build

ARG BUILD_CONFIGURATION=Release
ARG nugetSource=''
ARG nugetPassword=''

COPY "src/PharmaLex.Core.Configuration/" "src/PharmaLex.Core.Configuration/"
COPY "src/PharmaLex.VigiLit.Domain/" "src/PharmaLex.VigiLit.Domain/"
COPY "src/PharmaLex.Core.DataAccessLayer/" "src/PharmaLex.Core.DataAccessLayer/"
COPY "src/PharmaLex.VigiLit.DataAccessLayer/" "src/PharmaLex.VigiLit.DataAccessLayer/"
COPY "src/PharmaLex.VigiLit.DataAccessLayer.Base/" "src/PharmaLex.VigiLit.DataAccessLayer.Base/"
COPY "src/PharmaLex.VigiLit.Infrastructure.Data/" "src/PharmaLex.VigiLit.Infrastructure.Data/"
COPY "src/PharmaLex.Core.UserSessionManagement.Entities" "src/PharmaLex.Core.UserSessionManagement.Entities"
COPY "src/PharmaLex.VigiLit.ImportManagement/" "src/PharmaLex.VigiLit.ImportManagement/"
COPY "src/PharmaLex.VigiLit.ImportManagement.Entities/" "src/PharmaLex.VigiLit.ImportManagement.Entities/"
COPY "src/PharmaLex.VigiLit.ImportManagement.Client/" "src/PharmaLex.VigiLit.ImportManagement.Client/"
COPY "src/PharmaLex.VigiLit.ImportManagement.Contracts/" "src/PharmaLex.VigiLit.ImportManagement.Contracts/"
COPY "src/PharmaLex.VigiLit.ReferenceManagement/" "src/PharmaLex.VigiLit.ReferenceManagement/"
COPY "src/PharmaLex.VigiLit.DataExtraction.Entities/" "src/PharmaLex.VigiLit.DataExtraction.Entities/"

### Place here any additional non-AI related projects for now - These need to be removed as we decouple it ###
COPY "src/PharmaLex.VigiLit.Reporting.Contracts/" "src/PharmaLex.VigiLit.Reporting.Contracts/"
COPY "src/PharmaLex.Core.UserManagement/" "src/PharmaLex.Core.UserManagement/"
COPY "src/PharmaLex.VigiLit.AccessControl/" "src/PharmaLex.VigiLit.AccessControl/"
### Place here any additional non-AI related projects for now - These need to be removed as we decouple it ###

COPY "src/PharmaLex.VigiLit.MessageBroker.Contracts/" "src/PharmaLex.VigiLit.MessageBroker.Contracts/"
COPY "src/PharmaLex.VigiLit.AiAnalysis.Service/" "src/PharmaLex.VigiLit.AiAnalysis.Service/"
COPY "src/PharmaLex.VigiLit.AiAnalysis.Entities/" "src/PharmaLex.VigiLit.AiAnalysis.Entities/"

COPY "test/PharmaLex.VigiLit.AiAnalysis.Unit.Tests" "test/PharmaLex.VigiLit.AiAnalysis.Unit.Tests/"
COPY "test/PharmaLex.VigiLit.Test.Framework" "test/PharmaLex.VigiLit.Test.Framework/"
COPY "test/PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore" "test/PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore/"

RUN dotnet nuget add source ${nugetSource} -n Phlexglobal -u ignore -p ${nugetPassword} --store-password-in-clear-text \
&& dotnet restore src/PharmaLex.VigiLit.AiAnalysis.Service/PharmaLex.VigiLit.AiAnalysis.Service.csproj \
&& dotnet restore test/PharmaLex.VigiLit.AiAnalysis.Unit.Tests/PharmaLex.VigiLit.AiAnalysis.Unit.Tests.csproj 

RUN dotnet build "src/PharmaLex.VigiLit.AiAnalysis.Service/PharmaLex.VigiLit.AiAnalysis.Service.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "src/PharmaLex.VigiLit.AiAnalysis.Service/PharmaLex.VigiLit.AiAnalysis.Service.csproj" -c $BUILD_CONFIGURATION -o /app/publish \
/p:UseAppHost=false 

# Stage 1, Test application
FROM publish AS testrunner
WORKDIR /
ARG projectName
ARG configuration
ARG waitScript
ENV configuration_env=${configuration}
ENV testProjectName=test/${projectName}.Unit.Tests/${projectName}.Unit.Tests.csproj

# Add the wait script to the image
ADD ${waitScript} /wait
RUN chmod +x /wait 

ENTRYPOINT /wait && dotnet test ${testProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/UnitTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults"  --filter Environment!=Full

FROM base AS final
USER dotnetuser
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PharmaLex.VigiLit.AiAnalysis.Service.dll"]