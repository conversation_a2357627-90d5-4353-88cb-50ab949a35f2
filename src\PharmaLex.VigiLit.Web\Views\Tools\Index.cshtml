@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.SubstanceModel>
@using PharmaLex.VigiLit.Domain;
@using PharmaLex.VigiLit.Domain.Enums;

@{
    ViewData["Title"] = "Tools";
}

<div id="tools" v-cloak>
    
    <div class="sub-header">
        <h2>Tools</h2>
        <div class="controls">
        </div>
    </div>

    <section>
        <h2>Enqueue Scheduled Import</h2>
        <p>Identify contracts to enqueue, identify completed usa days since the last run, add one queue entry per contract-day. If your last run was today you'll get an empty queued import.</p>
        <p>Normally runs in the functions app scheduled overnight. This button will run the same logic but in the web app. Should only take a few seconds to execute.</p>
        <form asp-action="EnqueueScheduledImport" method="post" ref="enqueueScheduledImportForm">
            <button id="EnqueueScheduledImportButton" :disabled="disabled" v-on:click="disable('enqueueScheduledImportForm')">Enqueue Scheduled Import</button>
        </form>
    </section>

    <br />

    <section>
        <h2>Send Daily Reference Classification Emails</h2>
        <p>Normally runs in the functions app scheduled daily. This button will run the same logic but in the web app. Should only take a few seconds to execute.</p>
        <form asp-action="SendDailyReferenceClassificationEmails" method="post" ref="sendDailyReferenceClassificationEmailsForm">
            <button id="SendDailyReferenceClassificationEmailsButton" :disabled="disabled" v-on:click="disable('sendDailyReferenceClassificationEmailsForm')">Send Daily Reference Classification Emails</button>
        </form>
    </section>

    <br />

    <section>
        <h2>Send Daily Case Files Emails</h2>
        <p>Normally runs in the functions app scheduled daily. This button will run the same logic but in the web app. Should only take a few seconds to execute.</p>
        <form asp-action="SendDailyCaseFilesEmails" method="post" ref="sendDailyCaseFilesEmailsForm">
            <button id="SendDailyCaseFilesEmailsButton" :disabled="disabled" v-on:click="disable('sendDailyCaseFilesEmailsForm')">Send Daily Case Files Emails</button>
        </form>
    </section>

</div>

@section Scripts {
<script type="text/javascript">
    var pageConfig = {
        appElement: "#tools",
        data: function () {
            return {
                disabled: false
            };
        },
        methods: {
            disable (formRef) {
                this.disabled = true;
                this.$refs[formRef].submit();
            }
        },
    };
</script>
}
