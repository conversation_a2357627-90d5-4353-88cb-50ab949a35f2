﻿using AutoMapper;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.AutoMapper;
public class ContractMappingProfileTests
{
    readonly IMapper _mapper;
    public ContractMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ContractMappingProfile>();
            cfg.AddProfile<ContractVersionProfile>();
        });
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_Contract_To_SimpleContractModel()
    {
        //Arrange
        var contractVersion = new FakeContractVersion(43, ContractType.Scheduled, ContractVersionStatus.Approved);
        contractVersion.IsActive = true;
        contractVersion.SearchPeriodDays = 365;
        contractVersion.ContractWeekday = ContractWeekday.Friday;

        var currentDate = DateTime.UtcNow;
        var substance = new FakeSubstance(1, "Substance", "Type");
        var company = new Company("CompanyName", "PersonName", "PersonEmail", true);
        var project = new Project { Name = "Project", Company = company };
        var contract = new FakeContract(42, substance, project, currentDate, new List<ContractVersion> { contractVersion });

        //Act
        var model = _mapper.Map<SimpleContractModel>(contract);

        //Assert
        Assert.Equal(42, model.Id);
        Assert.Equal(substance.Name, model.SubstanceName);
        Assert.Equal(project.Name, model.ProjectName);
        Assert.Equal(contractVersion.IsActive, model.IsActive);
        Assert.Equal(currentDate, model.ContractStartDate);
        Assert.Equal(company.Name, model.CompanyName);
        Assert.Equal("Friday", model.ContractWeekday);
        Assert.Equal("365 days", model.SearchPeriod);
        Assert.Equal("Scheduled", model.ContractType);
    }

    [Fact]
    public void Maps_Contract_To_ExportContractModel()
    {
        //Arrange
        var currentContractVersion = new FakeContractVersion(43, ContractType.Scheduled, ContractVersionStatus.Approved);
        currentContractVersion.IsActive = true;
        currentContractVersion.SearchPeriodDays = 365;
        currentContractVersion.ContractWeekday = ContractWeekday.Friday;
        currentContractVersion.Version = 2;
        currentContractVersion.SearchString = "current search string";

        var previousContractVersion = new FakeContractVersion(83, ContractType.Scheduled, ContractVersionStatus.History);
        previousContractVersion.Version = 1;

        var currentDate = DateTime.UtcNow;
        var substance = new FakeSubstance(1, "Substance", "Type");
        var company = new Company("CompanyName", "PersonName", "PersonEmail", true);
        var project = new Project { Name = "Project", Company = company };
        var contract = new FakeContract(42, substance, project, currentDate, new List<ContractVersion> { currentContractVersion, previousContractVersion });

        //Act
        var model = _mapper.Map<ExportContractModel>(contract);

        //Assert
        Assert.Equal(42, model.Id);
        Assert.True(model.SubstanceName == substance.Name);
        Assert.True(model.ProjectName == project.Name);
        Assert.True(model.IsActive == currentContractVersion.IsActive);
        Assert.True(model.ContractStartDate == currentDate);
        Assert.Equal("Friday", model.ContractWeekday);
        Assert.Equal("365 days", model.SearchPeriod);
        Assert.Equal("Scheduled", model.ContractType);
        Assert.Equal(currentContractVersion.Version, model.SearchVersion);
        Assert.Equal(currentContractVersion.SearchString, model.SearchString);
        Assert.Equal(company.Name, model.CompanyName);
    }

    [Fact]
    public void Maps_Contract_To_ContractDetailsModel()
    {
        //Arrange
        var currentDate = DateTime.UtcNow;
        var contractVersion = new FakeContractVersion(43, ContractType.AdHocOnly, ContractVersionStatus.Approved);
        var substance = new FakeSubstance(1, "Substance", "Type");
        var project = new Project { Name = "Project" };
        var contract = new FakeContract(42, substance, project, currentDate, new List<ContractVersion> { contractVersion });

        //Act
        var model = _mapper.Map<ContractDetailsModel>(contract);

        //Assert
        Assert.Equal(42, model.Id);
        Assert.NotNull(model.ContractsVersions);
        Assert.Single(model.ContractsVersions);
        Assert.True(model.SubstanceName == substance.Name);
        Assert.True(model.ProjectName == project.Name);
    }
}

