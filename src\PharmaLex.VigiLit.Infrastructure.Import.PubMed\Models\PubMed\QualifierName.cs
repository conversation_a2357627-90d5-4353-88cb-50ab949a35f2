using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class QualifierName
{
    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(EnumYN.N)]
    public EnumYN MajorTopicYN { get; set; } = EnumYN.N;

    [XmlAttribute()]
    public string UI { get; set; }

    [XmlText()]
    public string Value { get; set; }
}