CREATE OR ALTER PROC dbo.spMerzClassificationReport
/*---------------------------------------------------------------------------------------------------------------------
--
-- Summary:		Returns data for Merz Client Report
--
-- Parameters:	@Company   = company name
--				@StartDate = report start date in any date format, e.g '20230701', '1 Jul 2023', etc.
--				@EndDate   = report end date
--
-- Notes:		Used by company 'Merz Therapeutics'

-- Usage:		exec spMerzClassificationReport 'Merz Therapeutics', '2023-07-01', '2023-08-31'
--
---------------------------------------------------------------------------------------------------------------------*/

	@CompanyName varchar(100),
	@StartDate datetime,
	@EndDate datetime
AS

    SELECT DISTINCT 
        rc.id as PlxId,
        s.Name as Substance, 
        ISNULL(r.Authors,'') as Authors, 
        ISNULL(r.Title,'') as Title, 
        CASE WHEN NULLIF(r.Volume,'') IS NOT NULL
        THEN
    	    CASE WHEN NULLIF(r.FullPagination,'') IS NOT NULL
    		THEN
    			CONCAT(r.VolumeAbbreviation,' ',r.PublicationYear,'; ',r.Volume,' : ',r.FullPagination)
    		ELSE
    			CONCAT(r.VolumeAbbreviation,' ',r.PublicationYear,'; ',r.Volume)
    		END
        ELSE
    	    CONCAT(r.VolumeAbbreviation,' ',r.PublicationYear,'; ')
        END as 'Citation', 
        FORMAT(rc.CreatedDate, 'yyyy-MM-dd') as ImportDate, 
        'Pubmed' as Source, 
	    cv.Version as ContractVersion,
		'' as CreatedBy,
		getdate() as CreatedDate,
		'' as LastUpdatedBy,
		getdate() as LastUpdatedDate
        FROM Imports i 
        INNER JOIN ImportContracts ic on i.id = ic.ImportId
        INNER JOIN ContractVersions cv on ic.ContractId = cv.ContractId and ic.ContractVersionId = cv.Id
        INNER JOIN ImportContractReferenceClassifications icrc on icrc.ImportContractId = ic.id and icrc.ImportId = i.id
        INNER JOIN ReferenceClassifications rc on rc.id = icrc.ReferenceClassificationId
        INNER JOIN [References] r on rc.ReferenceId = r.Id        
        INNER JOIN Contracts c on c.id = ic.ContractId
        INNER JOIN Substances s ON c.SubstanceId = s.id
        INNER JOIN Projects p on c.ProjectId = p.Id
        INNER JOIN Companies co on p.CompanyId = co.id
        WHERE rc.CreatedDate BETWEEN @StartDate and @EndDate
    	    AND co.Name = @CompanyName    	    
GO