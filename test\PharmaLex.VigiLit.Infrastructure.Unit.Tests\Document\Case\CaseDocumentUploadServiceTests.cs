﻿using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Document.Case;
using Shouldly;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Document.Case;

public class CaseDocumentUploadServiceTests
{
    private const string UPLOAD_ID = "********.155638.17";
    private const long CASE_ID = 123;
    private const string FILE_NAME = "Zinc.pdf";

    private readonly CaseDocumentUploadDescriptor _caseDocumentUploadDescriptor;
    private readonly CaseDocumentDescriptor _caseDocumentDescriptor;

    private const string ACCOUNT_NAME_UPLOAD = "vgtsharedeunupload";
    private const string CONTAINER_NAME_UPLOAD = "case-document-upload";
    private const string BLOB_NAME_UPLOAD = $"{UPLOAD_ID}/{FILE_NAME}";
    private readonly DocumentDescriptor _documentDescriptorUpload;

    private const string ACCOUNT_NAME = "vgtsharedeun";
    private const string CONTAINER_NAME = "case-document";
    private readonly string BLOB_NAME = $"{CASE_ID}/{FILE_NAME}";
    private readonly DocumentDescriptor _documentDescriptor;

    private readonly IDocumentService _mockDocumentService;

    private readonly ICaseDocumentUploadService _caseDocumentUploadService;

    public CaseDocumentUploadServiceTests()
    {
        _caseDocumentUploadDescriptor = new CaseDocumentUploadDescriptor(UPLOAD_ID, FILE_NAME);
        _caseDocumentDescriptor = new CaseDocumentDescriptor(CASE_ID, FILE_NAME);
        _documentDescriptorUpload = new DocumentDescriptor(ACCOUNT_NAME_UPLOAD, CONTAINER_NAME_UPLOAD, BLOB_NAME_UPLOAD);
        _documentDescriptor = new DocumentDescriptor(ACCOUNT_NAME, CONTAINER_NAME, BLOB_NAME);

        _mockDocumentService = Substitute.For<IDocumentService>();

        var azureStorageCaseDocumentUploadOptions = Substitute.For<IOptions<AzureStorageCaseDocumentUploadOptions>>();
        azureStorageCaseDocumentUploadOptions.Value
            .Returns(new AzureStorageCaseDocumentUploadOptions
            {
                AccountName = ACCOUNT_NAME_UPLOAD,
                ContainerName = CONTAINER_NAME_UPLOAD
            });

        var azureStorageCaseDocumentOptions = Substitute.For<IOptions<AzureStorageCaseDocumentOptions>>();
        azureStorageCaseDocumentOptions.Value
            .Returns(new AzureStorageCaseDocumentOptions
            {
                AccountName = ACCOUNT_NAME,
                ContainerName = CONTAINER_NAME
            });

        _caseDocumentUploadService = new CaseDocumentUploadService(_mockDocumentService, azureStorageCaseDocumentUploadOptions, azureStorageCaseDocumentOptions);
    }

    [Fact]
    public async Task Create_WhenCalled_CallsCreate()
    {
        // Arrange
        var stream = new MemoryStream();

        // Act
        await _caseDocumentUploadService.Create(_caseDocumentUploadDescriptor, stream);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .Create(default!, default);

        await _mockDocumentService
            .Received(1)
            .Create(_documentDescriptorUpload, stream);
    }

    [Fact]
    public async Task Exists_BlobExists_ReturnsTrue()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptorUpload)
            .Returns(true);

        // Act
        var result = await _caseDocumentUploadService.Exists(_caseDocumentUploadDescriptor);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Exists_BlobDoesNotExist_ReturnsFalse()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptorUpload)
            .Returns(false);

        // Act
        var result = await _caseDocumentUploadService.Exists(_caseDocumentUploadDescriptor);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task OpenRead_BlobExists_ReturnsStream()
    {
        // Arrange
        var stream = new MemoryStream();
        _mockDocumentService
            .OpenRead(_documentDescriptorUpload)
            .Returns(stream);

        // Act
        var result = await _caseDocumentUploadService.OpenRead(_caseDocumentUploadDescriptor);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(stream);
    }

    [Fact]
    public async Task Delete_BlobExists_CallsDelete()
    {
        // Act
        await _caseDocumentUploadService.Delete(_caseDocumentUploadDescriptor);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .Delete(default!);

        await _mockDocumentService
            .Received(1)
            .Delete(_documentDescriptorUpload);
    }

    [Fact]
    public async Task Save_BlobExists_CallsCopyFrom()
    {
        // Act
        await _caseDocumentUploadService.Save(_caseDocumentUploadDescriptor, _caseDocumentDescriptor);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .CopyFrom(_documentDescriptorUpload, _documentDescriptor);

        await _mockDocumentService
            .Received(1)
            .CopyFrom(_documentDescriptorUpload, _documentDescriptor);
    }
}