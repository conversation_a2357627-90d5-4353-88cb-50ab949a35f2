﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddReferenceHistoryActionsTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReferenceHistoryActions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ReferenceClassificationId = table.Column<int>(type: "int", nullable: false),
                    ReferenceHistoryActionType = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: true),
                    TimeStamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReferenceHistoryActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReferenceHistoryActions_ReferenceClassifications_ReferenceClassificationId",
                        column: x => x.ReferenceClassificationId,
                        principalTable: "ReferenceClassifications",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ReferenceHistoryActions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceHistoryActions_ReferenceClassificationId",
                table: "ReferenceHistoryActions",
                column: "ReferenceClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceHistoryActions_UserId",
                table: "ReferenceHistoryActions",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReferenceHistoryActions");
        }
    }
}
