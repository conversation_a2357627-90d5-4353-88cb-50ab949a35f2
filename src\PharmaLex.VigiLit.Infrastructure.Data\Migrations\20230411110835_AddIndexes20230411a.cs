﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexes20230411a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_Id_ReferenceId_SubstanceId_ReferenceState",
                table: "ReferenceClassifications",
                columns: new[] { "Id", "ReferenceId", "SubstanceId", "ReferenceState" });

            migrationBuilder.CreateIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications",
                column: "ImportContractId")
                .Annotation("SqlServer:Include", new[] { "ReferenceClassificationId", "ICRCType", "EmailQueued", "EmailSent", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "EmailReason" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_Id_ReferenceId_SubstanceId_ReferenceState",
                table: "ReferenceClassifications");

            migrationBuilder.DropIndex(
                name: "IX_ImportContractReferenceClassifications_ImportContractId",
                table: "ImportContractReferenceClassifications");
        }
    }
}
