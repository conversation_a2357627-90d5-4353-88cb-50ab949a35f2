﻿using System.Collections.ObjectModel;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Resources;
using System.Text.Json;

namespace PharmaLex.VigiLit.Test.Framework;

/// <summary>
/// Utility class for returning common resources that can be used as test data.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public static class ResourceUtility
{
    /// <summary>
    /// Gets the docx stream.
    /// </summary>
    /// <returns></returns>
    public static Stream GetDocXStream()
    {
        return GetStream("Test_DOCX_Attachment");
    }

    /// <summary>
    /// Gets the XLS stream.
    /// </summary>
    /// <returns></returns>
    public static Stream GetXlsStream()
    {
        return GetStream("Test_XLS_Attachment");
    }

    /// <summary>
    /// Gets the XLS stream.
    /// </summary>
    /// <returns></returns>
    public static Stream GetPdfStream()
    {
        return GetStream("Test_PDF_Attachment");
    }

    /// <summary>
    /// Gets the multiple document streams.
    /// </summary>
    /// <returns></returns>
    public static IEnumerable<Stream> GetAttachments() 
    {
        var streams = new List<Stream>()
        {
            GetDocXStream(),
            GetXlsStream(),
            GetPdfStream(),
        };

        return new ReadOnlyCollection<Stream>(streams);
    }

    private static MemoryStream GetStream(string resourceName)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceManager = new ResourceManager("PharmaLex.VigiLit.Test.Framework.Resources", assembly);
        var obj = resourceManager.GetObject(resourceName);
        var jsonString = JsonSerializer.Serialize(obj);
        var byteArray = System.Text.Encoding.UTF8.GetBytes(jsonString);
        return new MemoryStream(byteArray);
    }
}