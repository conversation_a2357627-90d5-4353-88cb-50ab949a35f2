﻿@model List<PharmaLex.VigiLit.Ui.ViewModels.UserManagement.AssessorModel>
@using PharmaLex.VigiLit.Domain
@using System.Collections.Generic;
@using PharmaLex.Caching.Data;
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Application.UserManagement.Users
@using PharmaLex.VigiLit.Web.Helpers;

@inject IUserService userService
@{
    ViewData["Title"] = "Assessors";
}

<div id="assessors">
    <div class="sub-header">
        <h2>Assessors</h2>
        <div class="controls">
        </div>
    </div>
    <section>
        <filtered-table :items="users" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

<script type="text/javascript">

    var pageConfig = {
        appElement:"#assessors",
        data: function () {
            return {
                link: '/assessors/edit/',
                users: @Html.Raw(AntiXss.ToJson(Model)),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'familyName',
                            sortKey: 'familyName',
                            header: 'Last Name',
                            type: 'text',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'givenName',
                            sortKey: 'givenName',
                            header: 'First Name',
                            type: 'text',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'email',
                            sortKey: 'email',
                            header: 'Email',
                            type: 'text',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'substancesCount',
                            sortKey: 'substancesCount',
                            header: 'Substances',
                            type: 'number',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'displayQCPercentage',
                            sortKey: 'displayQCPercentage',
                            header: 'Quality Check %',
                            type: 'text',
                            style: 'width: 20%;'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'familyName',
                        options: [],
                        type: 'search',
                        header: 'Search Last Name',
                        fn: v => p => p.familyName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'givenName',
                        options: [],
                        type: 'search',
                        header: 'Search First Name',
                        fn: v => p => p.givenName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'email',
                        options: [],
                        type: 'search',
                        header: 'Search Email',
                        fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}