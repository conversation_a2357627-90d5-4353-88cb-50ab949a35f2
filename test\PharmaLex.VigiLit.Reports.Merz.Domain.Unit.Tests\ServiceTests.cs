﻿using Moq;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Reporting.Contracts.Models;

namespace PharmaLex.VigiLit.Reports.Merz.Domain.Unit.Tests;

public class ServiceTests
{
    private readonly Service _merzClientReportService;

    private readonly Mock<IRepository> _merzClientReportRepository = new();
    private readonly Mock<IExportService> _exportService = new();

    public ServiceTests()
    {
        _merzClientReportService = new Service(
            _merzClientReportRepository.Object,
            _exportService.Object);
    }

    [Fact]
    public void GetPageModel_Returns_Model()
    {
        // Arrange
        // Act
        var model = _merzClientReportService.GetPageModel();

        // Assert
        Assert.NotNull(model);
        Assert.NotEmpty(model.Years);
        Assert.Equal(model.Years.Max(), model.SelectedYear);
        Assert.Equal(12, model.Cards.Count());
    }

    [Fact]
    public void GetCards_Returns_Cards()
    {
        // Arrange
        // Act
        var cards = _merzClientReportService.GetCards(2024);

        // Assert
        Assert.NotNull(cards);
        Assert.Equal(12, cards.Count());

        var lastCard = cards.Last();

        Assert.NotNull(lastCard);
        Assert.Equal(2024, lastCard.Year);
        Assert.Equal(1, lastCard.Month);
        Assert.Equal("January", lastCard.MonthName);
        Assert.False(lastCard.IsFuture);
    }

    [Fact]
    public void GetCards_InvalidYear_Throws_ArgumentException()
    {
        // Act
        // Assert
        Assert.Throws<ArgumentException>(() => _merzClientReportService.GetCards(2023));
    }

    [Fact]
    public async Task Download_Returns_DownloadFile()
    {
        // Arrange
        var export = new ExportModel("MerzClientReport.csv", "text/csv", Array.Empty<byte>());

        _exportService
            .Setup(x => x.Export<MerzClientReportResult, ResultClassMap>(
                "MerzClientReport",
                It.IsAny<List<MerzClientReportResult>>(),
                It.IsAny<ExportConfiguration>()))
            .Returns(export);

        // Act
        var downloadFile = await _merzClientReportService.Download(2024, 1);

        // Assert
        Assert.NotNull(downloadFile);
        Assert.Equal("MerzClientReport_2024_1.csv", downloadFile.FileName);
        Assert.Equal("text/csv", downloadFile.ContentType);
        Assert.True(export.Data.SequenceEqual(downloadFile.Bytes));
    }

    [Fact]
    public async Task Download_InvalidYear_Throws_ArgumentException()
    {
        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _merzClientReportService.Download(2023, 1));
    }

    [Fact]
    public async Task Download_InvalidMonth_Throws_ArgumentException()
    {
        // Act
        // Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _merzClientReportService.Download(2024, 13));
    }
}
