﻿@using PharmaLex.VigiLit.Web.ViewModels

@model CacheViewModel

@{
    ViewData["Title"] = Model.Key + " - Cache Inspect";
}

<div class="sub-header">
    <h2>Inspect cache: <span class="brand-color">@Model.Key</span></h2>
    <div class="controls">
        <a class="button" href="/cache">Cache list</a>
        <a class="button" id="deleteCache" href="">Delete</a>
        <form id="deleteForm" style="display: none;" method="post" action="/cache/delete">
            @Html.AntiForgeryToken()
            <input type="text" name="key" value="@Model.Key" />
        </form>
    </div>
</div>

<section>
    <form method="post">
        <div>
            <div class="cache-warning m-icon-group mb-4">
                <i class="m-icon warning-color flex-align-self-start">warning</i>
                <div>
                    <p>Updating cache values is dangerous and can lead to exceptions in the application.</p>
                    <p>Do not change the structure of the JSON and always verify the new cache value is a valid JSON string before you update it.</p>
                </div>
            </div>
            <textarea asp-for="Value" style="width: 100%; height:500px;"></textarea>
            <input type="hidden" name="Key" value="@Model.Key" />
        </div>
        <div class="buttons mt-2">
            <a class="button secondary" href="/cache">Cancel</a>
            <button type="submit">Save</button>
        </div>
    </form>
</section>

@section Scripts {
    <script type="text/javascript">
        plx.contracts = {
            init: function () {
                let textEl = document.getElementById('Value');
                textEl.value = JSON.stringify(JSON.parse('@Html.Raw(Model.Value.Replace("'", "&#39;"))'), undefined, 4);

                $('#deleteCache').on('click', e => {
                    e.preventDefault();

                    let deleteForm = document.getElementById('deleteForm');

                    deleteForm.submit();
                });
            }
        };
        plx.contracts.init();
    </script>
}
