﻿// _containers.scss
// The % symbol in the cdn is the text '-percent' instead, which is invalid.
// Most noticeable on the Assessors page as the columns weren't the correct width.
@for $percentage from 1 through 20 {
    .flex-#{$percentage * 5}-percent {
        flex-basis: #{$percentage * 5 + '%'} !important;
        position: relative;
    }
}

// _tables.scss
// In VigiLit this used to contain a typo 'work-break', the typo was fixed in the cdn.
// Applying 'word-break: break-word' causes DOIs to wrap, which we don't want.
// Most noticeable in the Search results table. 
table td {
    word-break: unset;
}
