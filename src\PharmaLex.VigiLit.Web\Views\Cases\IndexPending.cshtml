﻿@using Microsoft.AspNetCore.Authorization
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@inject IAuthorizationService AuthorizationService

@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.CaseManagement.CaseModel>

@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "Pending Case Files";
}

<div class="sub-header">
    <h2>Pending Case Files</h2>
    <div class="controls">
        <div class="controls">
            <a class="button btn-default" id="addNewCaseRecordBtn">Add New</a>
        </div>
    </div>
</div>

<div id="cases" v-cloak>
    <button type="button" id="refreshCaseTable" @@click="refreshData" hidden="hidden" style="display:none;"></button>
    <section>
        <filtered-table v-if="caseData !== null" :items="caseData" :columns="columns"></filtered-table>
    </section>
    <modal-dialog
        v-if="isConfirmDeleteVisible"
        width="300px"
        height="150px"
        title="Delete Case"
        v-on:close="hideConfirmDelete"
        v-on:confirm="confirmedDelete"
        id="modal-dialog-case-delete">
        <p>Are you sure you want to delete this case?</p>
    </modal-dialog>
</div>

<div class="modal fade" id="caseRecordEditModal" role="dialog">
    <div class="modal-dialog" style="width:900px;">
        <div class="modal-content">
            <div class="modal-body" style="overflow:visible;"></div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: "#cases",
        data: function () {
            return {
                isDeleting: false,
                isConfirmDeleteVisible: false,
                selectedCase: null,
                parentTableRowElement: null,
                caseData: @Html.Raw(AntiXss.ToJson(Model)),
                token: document.getElementsByName("__RequestVerificationToken")[0]?.value,
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'plxId',
                            sortKey: 'plxId',
                            header: 'PLX ID',
                            type: 'number'
                        },
                        {
                            dataKey: 'noOfFiles',
                            sortKey: 'noOfFiles',
                            header: 'No of Files',
                                type: 'number'
                        },
                        {
                            dataKey: 'substanceName',
                            sortKey: 'substanceName',
                            header: 'Substance',
                            type: 'text'
                        },
                        {
                            dataKey: 'company',
                            sortKey: 'company',
                            header: 'Company',
                            type: 'text'
                        },
                        {
                            dataKey: 'psur',
                            sortKey: 'psur',
                            header: 'PSUR',
                            type: 'text',
                            isYesNo: true
                        },
                        {
                            dataKey: 'mlm',
                            sortKey: 'mlm',
                            header: 'MLM',
                            type: 'text',
                            isYesNo: true

                        },
                        {
                            dataKey: 'pvSafetyDatabaseId',
                            sortKey: 'pvSafetyDatabaseId',
                            header: 'PV Safety Database ID',
                            type: 'text'
                        },
                        {
                            dataKey: 'createdDate',
                            sortKey: 'createdDate',
                            header: 'Created On',
                            type: 'text'
                        },
                        {
                            dataKey: 'lastUpdatedBy',
                            sortKey: 'lastUpdatedBy',
                            header: 'Modified By',
                            type: 'text'
                        },
                        {
                            edit: {
                                enabled: true
                            },
                            delete: {
                                enabled: true
                            }
                        }
                    ],
                    edit: {
                        click: (row, event) => {
                            $.ajax({
                                url: "/Cases/CaseRecord/" + row.id,
                                success: function (data) {
                                    $('.modal-body').html(data);
                                    $("#caseRecordEditModal").modal("show");
                                }
                            });
                        }
                    },
                    delete: {
                        click: (row, event) => {
                            this.showConfirmDelete(row, event);
                        }
                    }
                }
            };
        },
        methods: {
            refreshData: function () {
                this.caseData = null;
                fetch(`/Cases/Pending/CaseTable`, {
                    method: "GET"
                })
                .then(res => {
                    if (!res.ok) {
                        throw res;
                    }
                    return res.json();
                })
                .then(data => {
                    this.caseData = data;
                })
                .catch(error => {
                    plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                });
            },
            deleteCase: function (page) {
                $('#modal-dialog-case-delete').find('a.button').addClass('disabled');
                $.ajax({
                    url: "/Cases/CaseRecord/" + page.selectedCase.id,
                    type: "DELETE",
                    headers: { "RequestVerificationToken": page.token },
                    success: function (result) {
                        $('#refreshCaseTable').click();
                        plx.toast.show('Case successfully deleted.', 2, 'confirm', null, 2500);
                    },
                    error: function (result) {
                        plx.toast.show('Something went wrong, could not delete the case.', 2, 'failed', null, 5000);
                    },
                    complete: function (result) {
                        page.isDeleting = false;
                        page.hideConfirmDelete();
                        $('#modal-dialog-case-delete').find('a.button').removeClass('disabled');
                    }
                });
            },
            confirmedDelete: function () {
                if (!this.selectedCase) {
                    plx.toast.show('An error occurred trying to resolve the case for deletion.', 2, 'failed', null, 5000);
                    return;
                }
                if (this.isDeleting) {
                    return;
                }
                this.isDeleting = true;
                this.deleteCase(this);
            },
            showConfirmDelete: function (row, event) {
                this.isConfirmDeleteVisible = true;
                this.selectedCase = row;
                this.parentTableRowElement = $(event.target).parents('tr.table-row').first();
                this.parentTableRowElement.addClass('delete-highlighted');
            },
            hideConfirmDelete: function () {
                if (this.isDeleting) {
                    return;
                }
                this.isConfirmDeleteVisible = false;
                this.selectedCase = null;
                this.parentTableRowElement.removeClass('delete-highlighted');
            }
        }
    };

    document.addEventListener("DOMContentLoaded", () => {
        $("#addNewCaseRecordBtn").on("click", function () {
            $.ajax({
                url: "@Url.Action("CaseRecord", "Cases")",
                success: function (data) {
                    $('.modal-body').html(data);
                    $("#caseRecordEditModal").modal("show");
                }
            });
        });
    });
</script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
    <partial name="Components/ModalDialog" />
}