
<!--
     File isodia.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     Entity names in this file are derived from files carrying the
     following notice:

     (C) International Organization for Standardization 1986
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.

-->

<!ENTITY acute            "&#x000B4;" ><!--=acute accent -->
<!ENTITY breve            "&#x002D8;" ><!--=breve -->
<!ENTITY caron            "&#x002C7;" ><!--=caron -->
<!ENTITY cedil            "&#x000B8;" ><!--=cedilla -->
<!ENTITY circ             "&#x002C6;" ><!--circumflex accent -->
<!ENTITY dblac            "&#x002DD;" ><!--=double acute accent -->
<!ENTITY die              "&#x000A8;" ><!--=dieresis -->
<!ENTITY dot              "&#x002D9;" ><!--=dot above -->
<!ENTITY grave            "&#x00060;" ><!--=grave accent -->
<!ENTITY macr             "&#x000AF;" ><!--=macron -->
<!ENTITY ogon             "&#x002DB;" ><!--=ogonek -->
<!ENTITY ring             "&#x002DA;" ><!--=ring -->
<!ENTITY tilde            "&#x002DC;" ><!--=tilde -->
<!ENTITY uml              "&#x000A8;" ><!--=umlaut mark -->
