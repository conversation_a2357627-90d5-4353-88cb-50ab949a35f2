//// ------------------------------------------------------------------------------
////  <auto-generated>
////    Generated by Xsd2Code++. Version 6.0.32.0. www.xsd2code.com
////    {"TargetFramework":"NetCore","NameSpace":"PubMed","Properties":{},"XmlAttribute":{"Enabled":true},"ClassParams":{},"Miscellaneous":{}}
////  </auto-generated>
//// ------------------------------------------------------------------------------
//#pragma warning disable
//namespace PubMed
//{
//    using System;
//    using System.Diagnostics;
//    using System.Xml.Serialization;
//    using System.Runtime.Serialization;
//    using System.Collections;
//    using System.Xml.Schema;
//    using System.ComponentModel;
//    using System.Xml;
//    using System.Collections.Generic;


//    [System.CodeDom.Compiler.GeneratedCode("System.Xml", "4.8.4084.0")]
//    [Serializable]
//    [DebuggerStepThrough]
//    [DesignerCategory("code")]
//    [XmlType(AnonymousType = true, Namespace = "http://tempuri.org/esearch")]
//    [XmlRoot(Namespace="http://tempuri.org/esearch", IsNullable=false)]


//}
//#pragma warning restore
