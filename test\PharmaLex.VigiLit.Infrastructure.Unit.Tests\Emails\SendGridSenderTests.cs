﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Test.Framework;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Test.Framework.ILogger;
using PharmaLex.VigiLit.Test.Framework.SendGrid;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net;
using System.Net.Http.Json;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Emails;

public class SendGridSenderTests
{
    const string recipientName = "RecipientName";
    const string recipientEmail = "RecipientEmail";
    const string subject = "subject";
    private readonly Uri viewCaseUri = new Uri("http://www.contoso.com/");

    private Mock<ILogger<SendGridSender>> _mockLogger;
    private Mock<ISendGridClient> _mockClient;
    private Mock<IOptions<EmailOptions>> _mockEmailOptions;

    private EmailOptions _emailOptions;

    private SendGridSender _sendGridSender;

    public SendGridSenderTests()
    {
        _mockLogger = new Mock<ILogger<SendGridSender>>();
        var fakeFactory = new FakeLoggerFactory<SendGridSender>(_mockLogger);

        _mockClient = new Mock<ISendGridClient>();
        _mockEmailOptions = new Mock<IOptions<EmailOptions>>();

        _emailOptions = new EmailOptions();
        _mockEmailOptions.Setup(x => x.Value).Returns(_emailOptions);

        _sendGridSender = new SendGridSender(fakeFactory, _mockClient.Object, _mockEmailOptions.Object);
    }

    [Fact]
    public async Task SendCaseEmail_When_only_one_attachment_Then_SendEmailSync_has_one_attachment()
    {
        // Arrange
        var message = new HttpResponseMessage();
        var headers = message.Headers;

        Response? response = new Response(HttpStatusCode.OK, new FakeHttpContent(), headers);
        _mockClient.Setup(x => x.SendEmailAsync(
            It.IsAny<SendGridMessage>(),
            new CancellationToken())).Returns(Task.FromResult(response));

        var caseEmailModel = new CaseEmailModel(recipientName, recipientEmail, subject, new List<CaseEmailSubstanceModel>(), viewCaseUri);
        var caseEmailAttachmentModels = new List<CaseEmailAttachmentModel>()
        {
            new CaseEmailAttachmentModel(1, "filename.xls", ResourceUtility.GetXlsStream()),
        };

        var streamHelper = new StreamHelper();
        var zippedAttachments = streamHelper.GetZipOfBytesFromFileStreams(caseEmailAttachmentModels.ToDictionary(x => x.FileName, x => x.Stream));

        // Act
        await _sendGridSender.SendCaseEmail(caseEmailModel, "files.zip", zippedAttachments);

        // Assert
        _mockClient.Verify(x => x.SendEmailAsync(
            It.Is<SendGridMessage>(msg => msg.Attachments.Count == 1),
            new CancellationToken()), Times.Once());
    }

    [Fact]
    public async Task SendCaseEmail_When_only_two_attachments_Then_SendEmailSync_has_one_attachment()
    {
        // Arrange
        var message = new HttpResponseMessage();
        var headers = message.Headers;

        Response? response = new Response(HttpStatusCode.OK, new FakeHttpContent(), headers);
        _mockClient.Setup(x => x.SendEmailAsync(
            It.IsAny<SendGridMessage>(),
            new CancellationToken())).Returns(Task.FromResult(response));

        var caseEmailModel = new CaseEmailModel(recipientName, recipientEmail, subject, new List<CaseEmailSubstanceModel>(), viewCaseUri);
        var caseEmailAttachmentModels = new List<CaseEmailAttachmentModel>()
        {
            new CaseEmailAttachmentModel(1, "filename.xls", ResourceUtility.GetXlsStream()),
            new CaseEmailAttachmentModel(1, "filename.docx", ResourceUtility.GetDocXStream()),
        };

        var streamHelper = new StreamHelper();
        var zippedAttachments = streamHelper.GetZipOfBytesFromFileStreams(caseEmailAttachmentModels.ToDictionary(x => x.FileName, x => x.Stream));

        // Act
        await _sendGridSender.SendCaseEmail(caseEmailModel, "files.zip", zippedAttachments);

        // Assert
        _mockClient.Verify(x => x.SendEmailAsync(
            It.Is<SendGridMessage>(msg => msg.Attachments.Count == 1),
            new CancellationToken()), Times.Once());
    }

    [Fact]
    public async Task SendCaseEmail_When_send_fails_Then_logs_warning()
    {
        // Arrange
        var message = new HttpResponseMessage();
        var headers = message.Headers;

        HttpContent content = JsonContent.Create(ResponseBodyUtility.GetStringDynamicCollection());

        Response? response = new Response(HttpStatusCode.BadRequest, content, headers);

        _mockClient.Setup(x => x.SendEmailAsync(
            It.IsAny<SendGridMessage>(),
            new CancellationToken())).Returns(Task.FromResult(response));

        var caseEmailModel = new CaseEmailModel(recipientName, recipientEmail, subject, new List<CaseEmailSubstanceModel>(), viewCaseUri);
        var caseEmailAttachmentModels = new List<CaseEmailAttachmentModel>();

        var streamHelper = new StreamHelper();
        var zippedAttachments = streamHelper.GetZipOfBytesFromFileStreams(caseEmailAttachmentModels.ToDictionary(x => x.FileName, x => x.Stream));

        // Act
        await _sendGridSender.SendCaseEmail(caseEmailModel, "files.zip", zippedAttachments);

        // Assert
        _mockLogger.VerifyLogging("SendCaseEmail email failed to send.\nTo: [0] RecipientName <RecipientEmail>\nkey1: value1\nkey2: value2", LogLevel.Warning);
    }

    [Fact]
    public async Task SendInvitationEmail_When____Then_SendEmailSync_message_properties_set()
    {
        // Arrange
        var message = new HttpResponseMessage();
        var headers = message.Headers;

        _emailOptions.NoReplyEmail = "NoReplyEmail";
        _emailOptions.NoReplyEmailName = "NoReplyEmailName";
        _emailOptions.InvitationEmailTemplateId = "1234";

        Response? response = new Response(HttpStatusCode.OK, new FakeHttpContent(), headers);
        _mockClient.Setup(x => x.SendEmailAsync(
            It.IsAny<SendGridMessage>(),
            new CancellationToken())).Returns(Task.FromResult(response));

        var model = new InvitationEmailModel()
        {
            CompanyUser = new CompanyUserModel()
            {
                Email = "<EMAIL>",
                GivenName = "Given",
                FamilyName = "Family",
            },
        };

        // Act
        await _sendGridSender.SendInvitationEmail(model);

        // Assert

        // NOTE: We are checking the actual implementation of SendGridMessage here which isn't ideal as its not our code
        // We should really check that we are calling the correct methods on the SendGridMessage however its not currently coded in such
        // a way that would allow us to do that.
        mockClientVerifySendEmailAsync(msg => msg.From.Email == _emailOptions.NoReplyEmail, "From Email");
        mockClientVerifySendEmailAsync(msg => msg.From.Name == _emailOptions.NoReplyEmailName, "From Name");
        mockClientVerifySendEmailAsync(msg => msg.Personalizations.Single().Tos.Single().Name == "Given Family", "To Name");
        mockClientVerifySendEmailAsync(msg => msg.Personalizations.Single().Tos.Single().Email == "<EMAIL>", "To Email");
        mockClientVerifySendEmailAsync(msg => msg.TemplateId == "1234", "TemplateId");
    }

    [Fact]
    public async Task SendDailyReferenceClassificationEmail_When____Then_SendEmailSync_message_properties_set()
    {
        // Arrange
        var message = new HttpResponseMessage();
        var headers = message.Headers;

        _emailOptions.NoReplyEmail = "NoReplyEmail";
        _emailOptions.NoReplyEmailName = "NoReplyEmailName";
        _emailOptions.DailyReferenceClassificationEmailTemplateId = "1234";

        Response? response = new Response(HttpStatusCode.OK, new FakeHttpContent(), headers);
        _mockClient.Setup(x => x.SendEmailAsync(
            It.IsAny<SendGridMessage>(),
            new CancellationToken())).Returns(Task.FromResult(response));

        var model = new DailyClassificationEmailSendGridTemplateModel()
        {
            RecipientEmail = "<EMAIL>",
            Subject = "Subject",
        };

        // Act
        await _sendGridSender.SendDailyReferenceClassificationEmail(model);

        // Assert

        // NOTE: We are checking the actual implementation of SendGridMessage here which isn't ideal as its not our code
        // We should really check that we are calling the correct methods on the SendGridMessage however its not currently coded in such
        // a way that would allow us to do that.
        mockClientVerifySendEmailAsync(msg => msg.From.Email == _emailOptions.NoReplyEmail, "Email");
        mockClientVerifySendEmailAsync(msg => msg.From.Name == _emailOptions.NoReplyEmailName, "Name");
        mockClientVerifySendEmailAsync(msg => msg.TemplateId == "1234", "TemplateId");
    }

    private void mockClientVerifySendEmailAsync(Func<SendGridMessage, bool> condition, string property)
    {
        _mockClient.Verify(x => x.SendEmailAsync(
                It.Is<SendGridMessage>(msg => condition(msg) == true), new CancellationToken()), Times.Once(), $"Condition failed on {property}");
    }
}