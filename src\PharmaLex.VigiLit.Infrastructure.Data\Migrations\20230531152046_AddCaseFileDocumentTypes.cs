﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddCaseFileDocumentTypes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CaseFileDocumentTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    Extension = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    MaxFileSizeMb = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CaseFileDocumentTypes", x => x.Id);
                });

            migrationBuilder.Sql("SET IDENTITY_INSERT [dbo].[CaseFileDocumentTypes] ON; " +

                                 "INSERT INTO [dbo].[CaseFileDocumentTypes] ( [Id], [Name], [Extension], [MaxFileSizeMb], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 1, 'Portable Document Format', 'pdf', '10', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[CaseFileDocumentTypes] WHERE [Id] = 1); " +

                                 "INSERT INTO [dbo].[CaseFileDocumentTypes] ( [Id], [Name], [Extension], [MaxFileSizeMb], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 2, 'Extensible Markup Language', 'xml', '10', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[CaseFileDocumentTypes] WHERE [Id] = 2); " +

                                 "INSERT INTO [dbo].[CaseFileDocumentTypes] ( [Id], [Name], [Extension], [MaxFileSizeMb], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 3, 'Word 97-2003 Document', 'doc', '10', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[CaseFileDocumentTypes] WHERE [Id] = 3); " +

                                 "INSERT INTO [dbo].[CaseFileDocumentTypes] ( [Id], [Name], [Extension], [MaxFileSizeMb], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 4, 'Word Document', 'docx', '10', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[CaseFileDocumentTypes] WHERE [Id] = 4); " +

                                 "SET IDENTITY_INSERT [dbo].[CaseFileDocumentTypes] OFF; ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CaseFileDocumentTypes");
        }
    }
}
