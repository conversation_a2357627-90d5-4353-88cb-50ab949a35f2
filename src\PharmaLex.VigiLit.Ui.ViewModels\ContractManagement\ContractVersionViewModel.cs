﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class ContractVersionViewModel
{
    public int Id { get; set; }
    [MaxLength(4000)]
    public string SearchString { get; set; }
    public int ContractVersionStatus { get; set; }
    public int Version { get; set; }
    public string ReasonForChange { get; set; }
    public DateTime TimeStamp { get; set; }
    public string ContractType { get; set; }
    public string ContractWeekday { get; set; }
    public string SearchPeriodDays { get; set; }
    public bool IsActive { get; set; }
    public string LastModifiedByUserName { get; set; }
    public ICollection<string> JournalTitles { get; set; } = new List<string>();

}