﻿using PharmaLex.VigiLit.Reporting.Contracts.ClassMap;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain;

internal sealed class ResultClassMap : CamelCaseClassMap<Result>
{
    public ResultClassMap()
    {
        Map(m => m.MonthlyDate).Name("MonthlyDate");
        Map(m => m.EmailData).Name("EmailData");

        Map(m => m.CreatedDate).Ignore();
        Map(m => m.CreatedBy).Ignore();
        Map(m => m.LastUpdatedDate).Ignore();
        Map(m => m.LastUpdatedBy).Ignore();
    }
}