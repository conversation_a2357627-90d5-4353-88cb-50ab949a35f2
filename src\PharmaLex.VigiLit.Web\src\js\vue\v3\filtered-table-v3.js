﻿vueApp.component('collection-cell', {
    template: '#collection-cell-template',
    props: {
        val: {
            required: true,
            type: Array
        }
    },
    computed: {
        tipContent() {
            return `<ul>
                ${this.val.slice(1, this.val.length)
                    .map(x => `<li>${x}</li>`).join(' ')
                }</ul>`;
        }
    }
});

vueApp.component('text-cell', {
    template: '#text-cell-template',
    props: {
        val: String,
        isYesNo: Boolean
    }
});

vueApp.component('text-edit-select-cell', {
    template: '#text-edit-select-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object
    },
    methods: {
        onChange(e) {
            if (this.invalid)
                e.target.reportValidity();
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});

vueApp.component('text-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'text'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid)
                e.target.reportValidity();
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});

vueApp.component('link-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'url'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid) {
                e.target.reportValidity();
            }
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});

vueApp.component('date-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'date'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid) {
                e.target.reportValidity();
            }
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});

vueApp.component('link-cell', { template: '<td><a class="action-link" :href="href" target="_blank" v-on:click.stop>{{val}}</a></td>', props: ['val', 'href'] });

vueApp.component('multiline-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('html-cell', { template: '<td v-html="val"></td>', props: ['val'] });

vueApp.component('number-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('email-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('url-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('date-cell', { template: '<td>{{convert ? convert(val) : val}}</td>', props: ['val', 'convert'] });

vueApp.component('picklist-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('relationship-cell', { template: '<td>{{val}}</td>', props: ['val'] });

vueApp.component('bool-cell', {
    template: '#bool-cell-template',
    props: {
        val: Boolean
    }
});

vueApp.component('select-filter', {
    template: '#select-filter-template',
    data: function () {
        return {
            isOpen: false,
            textField: this.config.display,
            dataField: this.config.dataKey,
            strings: {
                ...this.resources
            }
        };
    },
    emits: ['update:value', 'filter'],
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        },
        resources: Object
    },
    computed: {
        availableOptions: function () {
            return [...new Set(this.filtered.map(p => p[this.config.filterCollection]).flat())]
                .map(i => {
                    if (this.textField === this.dataField) {
                        return {
                            key: i === null ? '-' : i,
                            value: i === null ? this.strings.noValue : i
                        };
                    }

                    const v = this.config.options.find(x => x[this.dataField] === i);
                    return {
                        key: i === null ? '-' : i,
                        value: v ? v[this.textField] : this.strings.noValue
                    };
                })
                .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;
            this.$nextTick(function () {
                this.$refs.select.focus();
            });
        },
        change(val) {
            this.isOpen = false;
            this.$emit('update:value', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});

vueApp.component('data-table-pager', {
    template: '#data-table-pager-template',
    data() {
        return {
            strings: {
                ...this.resources
            }
        };
    },
    emits: ['page-size-change', 'page-index-change'],
    props: {
        isPaged: Boolean,
        itemCount: Number,
        pageIndex: Number,
        pageSize: Number,
        location: {
            type: String,
            default: 'bottom'
        },
        totalItems: {
            type: Number,
            required: true
        },
        resources: Object
    },
    computed: {
        startIndex: function () {
            return this.pageSize * this.pageIndex;
        },
        endIndex: function () {
            return Math.min(parseInt(this.startIndex) + parseInt(this.pageSize), this.itemCount);
        },
        pageCount: function () {
            let extra = this.itemCount % this.pageSize ? 1 : 0;
            return Math.floor(this.itemCount / this.pageSize) + extra;
        }
    },
    methods: {
        interpolate(format, ...keys) {
            const parts = format.split('{}');
            let result = parts[0];

            keys.forEach((k, i) => {
                result += k;
                if (parts.length > i + 1) result += parts[i + 1];
            });

            return result;
        },
        pageSizeChange(size) {
            this.$emit('page-size-change', +size);
        }
    }
});

vueApp.component('search-filter', {
    template: '#search-filter-template',
    data() {
        return {
            isOpen: false
        };
    },
    emits: ['update:value', 'filter'],
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;
            if (this.isOpen)
                this.$nextTick(function () {
                    this.$refs.search.focus();
                });
        },
        change(val) {
            this.$emit('update:value', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        close() {
            this.isOpen = false;
        },
        clear() {
            this.change('');
            this.close();
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});

vueApp.component('filtered-table', {
    template: '#filtered-table-template',
    data: function () {
        return {
            data: {
                count: this.items?.length || 0,
                items: this.items?.length ? this.items.map(x => {
                    return { ...x };
                }) : [],
                paged: !this.items
            },
            size: this.pageSize,
            pageIndex: 0,
            filterModel: this.filters.reduce((acc, val) => {
                acc[val.key] = '';
                return acc;
            }, {}),
            selectedItems: [],
            sortMode: this.columns.sortMode || 'single',
            sortModel: this.columns.config.reduce((acc, c) => {
                if (c.sortKey)
                    acc[c.sortKey] = c.sortDirection || 0;
                return acc;
            }, {}),
            internalFilters: this.filters.map(f => {
                let fi = { ...f };

                let optionId = fi.options[0]?.hasOwnProperty('id') ? 'id' : 'Id';
                let optionName = fi.options[0]?.hasOwnProperty('name') ? 'name' : 'Name';

                fi.dataKey = fi.dataKey ? fi.dataKey : optionId;
                fi.display = fi.display ? fi.display : optionName;

                fi.convert = fi.convert || (v => v);
                fi.fn = fi.fn || (v => p => (p[fi.dataKey] === null ? this.strings.noValue : p[fi.dataKey]).toLowerCase().includes(v.toLowerCase()));

                return fi;
            }),
            storageKey: `${window.location.href}(${this.$attrs.id || ''})`,
            editItem: null,
            working: false,
            strings: {
                ...{
                    noRecordsMessage: 'No matching records found',
                    sortByFormat: 'Sort by {}',
                    filterByFormat: 'Filter by {}',
                    searchInFormat: 'Search in {}',
                    clearFilters: 'Clear filters',
                    addItem: 'Add item',
                    edit: 'Edit',
                    remove: 'Delete',
                    save: 'Save',
                    cancel: 'Cancel',
                    noValue: '-- No Value',
                    pager: {
                        showingFormat: 'Showing {} to {} of {} entries',
                        showingFilteredFormat: '(filtered from {})',
                        pageSize: 'Page size',
                        first: 'First',
                        previous: 'Previous',
                        next: 'Next',
                        last: 'Last',
                    }
                },
                ...this.resources
            }
        };
    },
    emits: ['filter', 'on-selection-change'],
    props: {
        items: Array,
        filters: {
            type: Array,
            default: () => []
        },
        pageSize: {
            type: Number,
            default: 25
        },
        pagerLocation: {
            type: String,
            default: 'bottom'
        },
        columns: Object,
        styling: String,
        link: String,
        itemsUrl: {
            type: String,
            default: window.location.href + '/paged'
        },
        behavior: String,
        addurl: String,
        resources: {
            type: Object,
            default() {
                return {};
            }
        },
        selectable: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        appliedFilters: function () {
            return Object.keys(this.filterModel).reduce((acc, fKey) => {
                let fVal = this.filterModel[fKey];

                if (fVal || fVal === false || fVal === 0) {
                    let f = this.getFilterByKey(fKey);
                    acc[fKey] = f.fn(f.convert(fVal));
                }
                return acc;
            }, {});
        },
        appliedSorts: function () {
            return Object.keys(this.sortModel).reduce((acc, sKey) => {
                let col = this.columns.config.find(x => x.sortKey === sKey);
                let dataTypeComparer = this.data.items[0] ? typeof this.data.items[0][col.sortKey] : null;
                let comparerType = col ? col.sortComparer || dataTypeComparer : null;

                //console.log({ sKey: sKey, comparerType: comparerType });
                // HACK: if the first row was empty then it uses 'object' type - use 'string' instead.
                // This is of course flawed if the column was meant to be another type, e.g. date.
                // The proper solution is to not use this branch but instead use the actual shared code which apparently doesn't have this issue.
                if (comparerType === 'object') {
                    comparerType = 'string';
                }

                acc.push({
                    key: sKey,
                    comparer: this.getComparer(sKey, this.sortModel[sKey], comparerType)
                });
                return acc;
            }, []);
        },
        itemCount: function () {
            return this.data.paged ? this.data.count : this.filterItems.length;
        },
        filterItems: function () {
            if (this.data.paged) {
                return this.data.items;
            }

            this.pageIndex = 0;
            return Object.keys(this.appliedFilters).reduce((toFilter, key) => {
                return toFilter.filter(this.appliedFilters[key]);
            }, this.data.items);
        },
        sortItems: function () {
            if (this.data.paged) {
                return this.filterItems.slice();
            }

            return this.appliedSorts.reduce((acc, val) => {
                return acc.sort(val.comparer);
            }, this.filterItems.slice());
        },
        pageItems: function () {
            if (this.data.paged) {
                return this.sortItems.slice();
            }

            return this.sortItems.slice(this.size * this.pageIndex, this.size * (this.pageIndex + 1));
        },
        pageItemsAdd() {
            return this.editItem && !this.editItem.id ? [this.editItem, ...this.pageItems] : this.pageItems;
        },
        isFiltered() {
            return !!Object.keys(this.appliedFilters).length;
        },
        allSelected() {
            return this.pageItemsAdd.length && this.pageItemsAdd.filter(x => this.selectedItems.includes(x.id)).length == this.pageItemsAdd.length;
        }
    },
    methods: {
        interpolate(format, ...keys) {
            const parts = format.split('{}');
            let result = parts[0];

            keys.forEach((k, i) => {
                result += k;
                if (parts.length > i + 1) result += parts[i + 1];
            });

            return result;
        },
        loadState() {
            try {
                let stateString = localStorage.getItem(this.storageKey);
                let state = stateString ? JSON.parse(stateString) : { date: new Date() };
                
                if (stateIsFromToday(state)) {

                    if (state.filterModel) {
                        this.filterModel = state.filterModel;
                        Object.keys(this.filterModel).forEach(key => {
                            if (this.filterModel[key])
                                this.filter({ key });
                        });
                    }

                    if (state.sortModel) {
                        state.sortModel.forEach(s => {
                            let [key, order] = s;
                            this.sortModel[key] = order;
                        });
                    }

                    this.size = state.pageSize || this.size;
                    if (this.data.paged) {
                        this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                    } else {
                        this.$nextTick(function () {
                            this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                        });
                    }
                } else localStorage.removeItem(this.storageKey);
            } catch {
                localStorage.removeItem(this.storageKey);
            }

            function stateIsFromToday(state) {
                let now = new Date();
                let stateDate = new Date(state.date);
                return now.getDay() === stateDate.getDay() &&
                    now.getMonth() === stateDate.getMonth() &&
                    now.getYear() === stateDate.getYear();
            }
        },
        updateState(partial) {
            let stateString = localStorage.getItem(this.storageKey);
            let state = stateString ? JSON.parse(stateString) : {};

            partial = { ...partial, date: new Date() };
            localStorage.setItem(this.storageKey, JSON.stringify(Object.assign(state, partial)));
        },
        getCellType(row, column) {
            if (this.editItem?.id === row[this.columns.idKey] && column.edit) {
                return `${column.type}-edit-${column.edit.type}-cell`;
            } else {
                return `${column.type}-cell`;
            }
        },
        getHref(item, column) {
            if (column?.type === 'link') return item[column.dataKey];
            if (!this.link) return '/';

            let [match, field] = this.link.match(/\{([a-z0-9]+)\}/i) || [];
            if (field) {
                return this.link.replace(match, item[field]);
            }

            return this.link + item[this.columns.idKey];
        },
        rowClicked(item) {
            if (this.editItem) return;

            if (this.link?.length > 0) {
                let href = this.getHref(item);

                if (this.behavior === 'newtab') {
                    let link = document.createElement('a');
                    link.href = href;
                    link.setAttribute('target', '_blank');
                    link.click();
                } else
                    window.location.href = href;
            } else {
                this.$emit('row-clicked', item);
            }
        },
        getComparer(key, direction = 0, comparer = 'string') {
            if (comparer === 'number')
                return (a, b) => {
                    let akey = a[key] || 0,
                        bkey = b[key] || 0;

                    if (akey === bkey) return 0;
                    if (akey > bkey) return 1 * direction;

                    return -1 * direction;
                };

            if (comparer === 'string')
                return (a, b) => {
                    let akey = a[key] || '',
                        bkey = b[key] || '';

                    return direction * (akey).localeCompare(bkey, 'en', { sensitivity: 'base' });
                };

            if (comparer === 'boolean')
                return (a, b) => {
                    let akey = a[key] ? 1 : 0,
                        bkey = b[key] ? 1 : 0;

                    return direction * (akey - bkey);
                };

            if (comparer === 'date')
                return (a, b) => {
                    let akeyStr = a[key] || '1970-1-1',
                        bkeyStr = b[key] || '1970-1-1';
                    let akey = new Date(akeyStr),
                        bkey = new Date(bkeyStr);

                    if (akeyStr === bkeyStr) return 0;
                    if (akey > bkey) return 1 * direction;

                    return -1 * direction;
                };

            return () => 0;
        },
        sort(column) {
            if (column.sortKey) {
                let order = 0;
                let currentSort = this.sortModel[column.sortKey];

                if (!currentSort)
                    order = 1;
                else if (currentSort === 1)
                    order = -1;

                if (this.sortMode === 'single') {
                    Object.keys(this.sortModel).forEach(k => this.sortModel[k] = 0);
                }

                this.sortModel[column.sortKey] = order;

                this.updateState({
                    sortModel: Object.entries(this.sortModel)
                });
                this.loadItems();
            }
        },
        filter({ key }) {
            let val = this.filterModel[key];
            this.$emit('filter', { key, val });

            this.updateState({
                filterModel: this.filterModel,
                pageIndex: 0
            });

            if (this.selectable) {
                const selectedCount = this.selectedItems.length;
                this.selectedItems = this.selectedItems.filter(x => this.filterItems.map(y => y.id).includes(x));

                if (selectedCount !== this.selectedItems.length) {
                    this.$emit('on-selection-change', this.selectedItems);
                }
            }
        },
        getFilterByKey(key) {
            return this.internalFilters.find(f => f.key === key);
        },
        select(id) {
            let toggleIndex = this.selectedItems.indexOf(id);
            if (toggleIndex >= 0) {
                this.selectedItems.splice(toggleIndex, 1);
            } else {
                this.selectedItems.push(id);
            }

            this.$emit('on-selection-change', this.selectedItems);
        },
        toggleAll(on) {
            if (on) {
                this.selectedItems = [...new Set([...this.selectedItems, ...this.pageItemsAdd.map(x => x.id)])]
            } else {
                this.selectedItems = this.selectedItems.filter(x => !this.pageItemsAdd.map(y => y.id).includes(x));
            }

            this.$emit('on-selection-change', this.selectedItems);
        },
        getColumnStyle(column) {
            let s = '';
            if (column.type === 'bool') s += '';
            return (column.style || '') + s;
        },
        clear() {
            this.internalFilters.forEach((f, i) => {
                this.filterModel[f.key] = '';
                this.updateState({ filterModel: this.filterModel });
            });

            this.$emit('filter');
        },
        changePage(index) {
            this.pageIndex = +index;
            this.updateState({ pageIndex: this.pageIndex });
            this.loadItems();
        },
        changePageSize(size) {
            this.changePage(0);
            this.size = +size;
            this.updateState({ pageSize: this.size });
            this.loadItems();
        },
        tippify() {
            tippy('.tipified', {
                interactive: true,
                placement: 'auto-end',
                animateFill: false
            });
        },
        saveItemClicked(item) {
            if (this.working) return;
            this.working = true;
            if (this.editItem) {
                if (this.columns.config.find(({ dataKey }) => this.editItem[dataKey].invalid)) {
                    this.working = false;
                    return;
                }

                item = this.columns.config.reduce((acc, conf) => {
                    const col = conf.dataKey;
                    const filter = this.internalFilters.find(x => x.key === col);
                    const columnId = filter?.filterCollection ?? col;

                    const defaultOption = conf.edit ? null : filter?.options[0];
                    acc[columnId] = this.editItem[col].selected || defaultOption?.id || null;
                    if (columnId !== col) {
                        acc[col] = acc[columnId] ? filter?.options.find(x => x.id === acc[columnId]).name : defaultOption?.name;
                    }
                    return acc;
                }, { ...item });

                const applyChange = (res) => {
                    this.editItem = null;
                    if (item.id) {
                        const idx = this.data.items.findIndex(x => x[this.columns.idKey] === item.id);
                        this.data.items.splice(idx, 1, item);
                        this.$emit('edit', item);
                    }
                    else {
                        item.id = res.id;
                        if (res.path) item.path = res.path;
                        this.$emit('add', item);
                        this.data.items.push(item);
                    }
                    this.working = false;
                };

                const actionConfig = item.id ? this.columns.edit : this.columns.add;
                if (actionConfig?.enabled &&
                    (!actionConfig.canEditProp || item[actionConfig.canEditProp])) {
                    if (actionConfig.claim) {
                        actionConfig.claim(item).then(applyChange, () => { this.working = false; });

                    }
                    else {
                        applyChange();
                    }
                }
            }
        },
        editItemClicked(item = {}) {
            this.editItem = this.columns.config.reduce((acc, conf) => {
                const col = conf.dataKey;
                const filter = this.internalFilters.find(x => x.key === col);
                const columnId = filter?.filterCollection ?? col;
                const placeholder = `-- ${conf.header || col} --`;

                let isInvalid = false;
                if (!acc.id && conf.edit?.required) {
                    isInvalid = !item[columnId] && !conf.edit?.value;
                }

                acc[col] = {
                    selected: item[columnId] || conf.edit?.value || '',
                    selectedText: item[col] || conf.edit?.value || filter?.options[conf.edit?.default ?? 0]?.name,
                    options: [...filter?.options || []],
                    columnId,
                    placeholder,
                    invalid: isInvalid,
                    ...conf.edit || {}
                };

                acc[col].options.splice(0, 0, { id: '', name: placeholder });
                return acc;
            }, { id: item[this.columns.idKey] });
        },
        removeItemClicked(item) {
            const applyChange = () => {
                this.data.items.splice(this.data.items.indexOf(item), 1);
                this.$emit('remove', item);
            };
            if (this.columns.remove?.enabled &&
                (!this.columns.remove.canRemoveProp || item[this.columns.remove.canRemoveProp])) {
                if (this.columns.remove.claim) {
                    this.columns.remove.claim(item).then(applyChange, () => { });
                } else {
                    applyChange();
                }
            }
        },
        addItemClicked() {
            if (this.addurl) {
                window.location.href = this.addurl
                return window.location.href;
            }

            this.editItemClicked();
        },
        loadItems() {
            if (this.itemsUrl && this.data.paged) {
                Object.keys(this.filterModel).reduce((acc, key) => {
                    acc[0] = key;
                    acc[1] = this.filterModel[key];
                    return acc;
                }, [[], []]);

                let sortBy = Object.keys(this.sortModel).find(x => this.sortModel[x]) || Object.keys(this.sortModel)[0];

                let sortDirectionDescPart = this.sortModel[sortBy] < 0 ? 'desc' : '';
                let sortDirection = this.sortModel[sortBy] > 0 ? 'asc' : sortDirectionDescPart;

                let pagingRequest = {
                    page: this.pageIndex,
                    pageSize: this.size,
                    sortBy: sortBy,
                    sortDirection: sortDirection,
                    filterKeys: ['type'],
                    filterValues: ['ch']
                };

                fetch(`${this.itemsUrl}`, {
                    method: 'POST',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(pagingRequest)
                }).then(result => result.json())
                    .then(data => {
                        this.data = { ...this.data, ...data };
                    });
            }
        }
    },
    updated() {
        this.$nextTick(function () {
            this.tippify();
        });
    },
    mounted() {
        this.tippify();
        this.loadState();
        this.loadItems();
    }
});