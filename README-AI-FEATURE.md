﻿# Introduction 

The AI Analysis feature of Vigilit uses PhlexNeuron to obtain pre-classification suggestions for the user. It is implemented two main areas:

- Import - during the import of references from sources such as Pubmed.
- Pre classification - when the user picks records to pre-classify, AI suggestions are displayed to the user.

# AI Processing Overview
The high level architecture of the application is shown below:

![Architecture Overview](./docs/ai-architecture-overview.png)

A functional overview of the import process and it's relation to AI is shown below:

![AI Processing Flow](./docs/ai-software-workflow.png)

During reference import processing, the import routine in Pharmalex.VigiLit.ImportApp retrieves a batch of references and if they are new (not in the 
VigiLit database) or are updates to existing references then they are sent to the AI processing using Pharmalex.VigiLit.AiAnalysis.Client which 
uses MassTransit to put them onto a RabbitMQ queue hosted in a Docker container. The section below entitled Docker settings gives more information on this.

Messages on the queue are consumed using PreClassifyReferenceCommandHandler in Pharmalex.VigiLit.AiAnalysis.Service and a HTTP request is sent to the AI processing endpoint. 
An HTTP response is sent back from the AI processor that contains the AI analysis of the reference. This is stored in the database table AiSuggestion.

After the import is complete, any references that have associated AI suggestions during pre-classification cause the UI to display the suggestion.


# Project Structure

```
├── src
│    └── PharmaLex.VigiLit.AiAnalysis.Client                     # Project containing the AI Analysis client for publishing messages
│    └── PharmaLex.VigiLit.AiAnalysis.Service                    # Project containing the AI Analysis service for consuming messages, calling PhlexNeuron, updating repo
│    └── PharmaLex.VigiLit.AiAnalysis.Entities                   # Repository entities
│    └── PharmaLex.Core.Configuration                    # Project providing configuration via keyvault, user secrets and environment variables
│    └── PharmaLex.VigiLit.DataAccessLayer                       # Project providing database context
│    └── PharmaLex.VigiLit.Domain                                # Project defining database models, enums and other database related functionality
│    └── PharmaLex.VigiLit.Infrastructure.Data                   # Project containing database set up configuration and EF migration scripts
│    └── PharmaLex.VigiLit.MessageBroker.Contracts               # Message contracts
├── test
│    └── PharmaLex.VigiLit.AiAnalysis.Integration.Tests          # Integration tests that can be run locally or as part of pipeline
│    └── PharmaLex.VigiLit.AiAnalysis.Tests                      # Unit tests
│    └── PharmaLex.VigiLit.Test.Framework                        # Project containing common functionality for testing 
│    └── PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore    # Project containing common database functionality for testing 
```

# Configuration
The configuration is via user secrets and is the same as the Configuration section in README.md for the VigiLit project.

Ai processing can be switched on or off via two settings. These can be defined in the key vault (for deployments) but should be overridden in the 
user secrets when in local development. The override for these values is found in the user secrets in the README.md file.

Key Vault
FeatureManagement--AiMessageBusSend         Toggles whether the import should send new references to AI for analysis and storage
FeatureManagement--DisplayAiSuggestions     Toggles whether the UI displays the AI suggestion


# Development
For full end to end testing of the AI and the import, the following projects should be set up as start up projects:

- docker-compose
- PharmaLex.VigiLit.ImportApp
- PharmaLex.VigiLit.Web

The ```docker-compose``` project starts all the services required for the AI feature. It uses the ```docker-compose-develop.yml``` file which points
to the ```Dockerfile``` within the PharmaLex.VigiLit.AiAnalysis.Service project.

The ```Dockerfile``` that exists in the config folder of the solution is the one used by the Azure Build Pipeline as specified 
by the ```docker-compose-develop.yml``` file that is also in the SLN config folder.

To only view the results of previous AI suggestions, set only PharmaLex.VigiLit.Web as the startup project.

AI Analysis makes use of the Phlex.Core.MessageBus package and requires RabbitMq to be running. 

The ```PharmaLex.VigiLit.AiAnalysis.Service``` runs within a docker container. During development ensure that the ```appsettings.json``` for the service includes the following:

```
    "TransportType": "RabbitMq",
    "RabbitMq": {
      "Host": "host.docker.internal",
      "Port": 5672,
      "VirtualHost": "/",
      "Username": "test",
      "Password": "test"
    },
```

Note that RabbitMQ is only used in the local develop environment; when the application is deployed to develop, staging and production 
environments, the queue will be hosted by Azure Service Bus.

Ensure that user secrets have been set up according to the instructions that mention "Manage User Secrets" in README.md.

The service can be started within Visual Studio as per any other application.

# Docker Settings
There are 3 Docker related configuration files associated with the project:

1. src/docker-compose-develop.yml
2. src/docker-compose-develop.override.yml
3. PharmaLex.VigiLit.AiAnalysis.Service/Dockerfile


## src/docker-compose-develop.yml
The file docker-compose.yml contains the full configuration for Docker. The following are hosted in Docker:

   - PharmaLex.VigiLit.AiAnalysis.Service

   - rabbitMQ

   - sqlserver

   - azurite

To view the queue contents for RabbitMQ via the RabbitMQ management console, use the values of RABBITMQ_DEFAULT_USER and RABBITMQ_DEFAULT_PASS 
as specified in the rabbitmq section docker-compose.yml

The SQL Server configuration sets up a SQL Server database (stored in C:\volumes\mssql). To connect to this via SSMS, use 
   - (local) for the server name
   - SQL Server Authentication
   - Login is "sa" and the password is specified as SA_PASSWORD in the sqlserver section docker-compose.yml

Azurite is used to store blob storage for case files when debugging locally and does not relate to the AI.

## src/docker-compose-develop.override.yml
This file contains development overrides. It is used to set the environment and tell the app to use user secrets instead of the key vault.

## Dockerfile
This sets up the build of src/PharmaLex.VigiLit.AiAnalysis.Service within Docker.

# Breaking Changes and Version history