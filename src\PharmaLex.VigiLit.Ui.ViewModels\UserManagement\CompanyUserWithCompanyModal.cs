﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

public class CompanyUserWithCompanyModal : UserModel
{
    public bool IsActive { get; set; }
    public int CompanyId { get; set; }
    public bool IsCompanyActive { get; set; }
    public virtual List<int> ClaimIds { get; set; }
    public CompanyUserType CompanyUserType { get; set; }
}