using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public partial class BookDocument
{
    public PMID PMID { get; set; }

    [XmlArrayItem("ArticleId", IsNullable = false)]
    public ArticleId[] ArticleIdList { get; set; }

    public Book Book { get; set; }

    [XmlElement("LocationLabel")]
    public List<LocationLabel> LocationLabel { get; set; } = new List<LocationLabel>();

    public ArticleTitle ArticleTitle { get; set; }

    public VernacularTitle VernacularTitle { get; set; }

    public Pagination Pagination { get; set; }

    [XmlElement("Language")]
    public List<string> Language { get; set; } = new List<string>();

    [XmlElement("AuthorList")]
    public List<AuthorList> AuthorList { get; set; } = new List<AuthorList>();

    [XmlArrayItem("Investigator", IsNullable = false)]
    public List<Investigator> InvestigatorList { get; set; } = new List<Investigator>();

    [XmlElement("PublicationType")]
    public List<PublicationType> PublicationType { get; set; } = new List<PublicationType>();

    public Abstract Abstract { get; set; }

    [XmlArrayItem("Section", IsNullable = false)]
    public List<Section> Sections { get; set; } = new List<Section>();

    [XmlElement("KeywordList")]
    public List<KeywordList> KeywordList { get; set; } = new List<KeywordList>();

    public ContributionDate ContributionDate { get; set; }

    public DateRevised DateRevised { get; set; }

    public GrantList GrantList { get; set; }

    [XmlElement("ReferenceList")]
    public List<ReferenceList> ReferenceList { get; set; }
}