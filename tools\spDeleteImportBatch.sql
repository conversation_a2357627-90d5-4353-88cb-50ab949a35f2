﻿CREATE PROCEDURE dbo.spDeleteImportBatch
	--------------------------------------------------------
	-- Deletes batch of imports between date range
	-- 
	-- Paramenters:
	-- @startDate		Start date of import batch to remove
	-- @endDate			End date of import batch to remove
	--
	-- Syntax:
	-- exec spDeleteImportBatch '2023-07-01 00:00:00', '2023-07-05 10:00:00'
	--
	--------------------------------------------------------
@startDate DateTime,
@endDate   DateTime

AS
BEGIN
	
	DECLARE @currentImportId int
	DECLARE importCursor CURSOR FOR
	SELECT Id FROM Imports WHERE CreatedDate BETWEEN @startDate and @endDate

	OPEN importCursor

	FETCH NEXT FROM importCursor INTO @currentImportId

	WHILE @@FETCH_STATUS = 0
	BEGIN
		PRINT 'Deleting imports for import Id: ' + CAST(@currentImportId AS VARCHAR)

		exec spDeleteImport @importId = @currentImportId

		FETCH NEXT FROM importCursor INTO @currentImportId
	END;

	CLOSE importCursor
	DEALLOCATE importCursor
END

