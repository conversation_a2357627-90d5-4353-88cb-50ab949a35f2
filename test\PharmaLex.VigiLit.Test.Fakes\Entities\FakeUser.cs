﻿using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Test.Fakes.Entities;

public class FakeUser : User
{
    public FakeUser(int id)
    {
        Id = id;
    }

    public FakeUser(string givenName, string familyName, string email, int id, DateTime? lastLoginDate, DateTime? activationExpiryDate)
        : base(givenName, familyName, email)
    {
        Id = id;
        LastLoginDate = lastLoginDate;
        ActivationExpiryDate = activationExpiryDate;
    }
}