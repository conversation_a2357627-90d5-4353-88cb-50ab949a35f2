﻿using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests;

public class MeshTermsTests
{
    private readonly IReferenceBuilder _referenceBuilder;

    public MeshTermsTests()
    {
        _referenceBuilder = new ReferenceBuilder();
    }

    private static bool IsArrayInAlphabeticalOrder(string?[] array) => array.Zip(array.Skip(1), (a, b) => string.Compare(a?.Trim(), b?.Trim())).All(x => x == -1 || x == 0);

    private static bool IsNextCharacterExpected(string meshHeadings, string searchCharacter, string expectedNextCharacter)
    {
        var finished = false;
        var index = 0;

        while (!finished)
        {
            index = meshHeadings.IndexOf(searchCharacter, index) + 1;

            if (index == 0 || index >= meshHeadings.Length - 1)
            {
                finished = true;
            }
            else if (!string.Equals(expectedNextCharacter, meshHeadings.Substring(index, 1)))
            {
                return false;
            }
        }

        return true;
    }

    private static bool AreDescriptorAndQualifiersInAlphabeticalOrder(string meshHeadings)
    {
        if (meshHeadings != null)
        {
            var descriptors = meshHeadings.Split(";");

            if (!IsArrayInAlphabeticalOrder(descriptors))
            {
                return false;
            }

            foreach (var desc in descriptors)
            {
                var qualifierText = desc.Split(":");

                if (qualifierText.Length > 1)
                {
                    var qualifiers = qualifierText[1].Split("/");

                    if (!IsArrayInAlphabeticalOrder(qualifiers))
                    {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    private static PubmedArticle EmptyArticle => new()
    {
        MedlineCitation = new MedlineCitation
        {
            DateRevised = new DateRevised { Day = 1, Month = "1", Year = 2000 },
            Article = new Article { },
            PMID = new PMID { Value = 999 },
            MeshHeadingList = new List<MeshHeading>()
        }
    };

    private static MeshHeading MeshHeading0Qualifier => new()
    {
        DescriptorName = new DescriptorName { Value = "AAA" }
    };

    private static MeshHeading MeshHeading1Qualifier => new()
    {
        DescriptorName = new DescriptorName { Value = "BBB" },
        QualifierName = new List<QualifierName> { new() { Value = "b-1" } }
    };

    private static MeshHeading MeshHeading2Qualifiers => new()
    {
        DescriptorName = new DescriptorName { Value = "CCC" },
        QualifierName = new List<QualifierName> { new() { Value = "c-2" }, new() { Value = "c-1" } }
    };

    private static MeshHeading MeshHeading3Qualifiers => new()
    {
        DescriptorName = new DescriptorName { Value = "DDD" },
        QualifierName = new List<QualifierName> { new() { Value = "d-3" }, new() { Value = "d-1" }, new() { Value = "d-2" } }
    };

    private static MeshHeading MeshHeadingMajorDescriptorTopicY => new()
    {
        DescriptorName = new DescriptorName
        {
            Value = "EEE",
            MajorTopicYN = EnumYN.Y
        },
        QualifierName = new List<QualifierName> { new() { Value = "e-3" }, new() { Value = "e-1" }, new() { Value = "e-2" } }
    };

    private static MeshHeading MeshHeadingMajorDescriptorTopicN => new()
    {
        DescriptorName = new DescriptorName
        {
            Value = "FFF",
            MajorTopicYN = EnumYN.N
        },
        QualifierName = new List<QualifierName> { new() { Value = "f-3" }, new() { Value = "f-1" }, new() { Value = "f-2" } }
    };

    private static MeshHeading MeshHeadingMajorQualifierTopicY => new()
    {
        DescriptorName = new DescriptorName { Value = "GGG" },
        QualifierName = new List<QualifierName> { new() { Value = "g-3", MajorTopicYN = EnumYN.Y }, new() { Value = "g-1" }, new() { Value = "g-2" } }
    };

    private static MeshHeading MeshHeadingMajorQualifierTopicN => new()
    {
        DescriptorName = new DescriptorName { Value = "HHH" },
        QualifierName = new List<QualifierName> { new() { Value = "h-3", MajorTopicYN = EnumYN.N }, new() { Value = "h-1" }, new() { Value = "h-2" } }
    };

    [Fact]
    public void NoMeshTerms_ReturnsNull()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Null(reference.MeshHeadings);
    }

    [Fact]
    public void SemiColons_HaveSpaceAfter()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading3Qualifiers);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading2Qualifiers);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading1Qualifier);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading0Qualifier);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.True(IsNextCharacterExpected(reference.MeshHeadings, ";", " "));
    }

    [Fact]
    public void NoSemiColon_AtEnd()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading3Qualifiers);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading2Qualifiers);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading1Qualifier);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading0Qualifier);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.NotEqual(';', reference.MeshHeadings.Trim().Last());
    }

    [Fact]
    public void DescriptorAndQualifier_OrderderedAlphabetically()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading2Qualifiers);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading1Qualifier);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading0Qualifier);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading3Qualifiers);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.True(AreDescriptorAndQualifiersInAlphabeticalOrder(reference.MeshHeadings));
    }

    [Fact]
    public void Descriptor_MajorTopic_HasAsteriskAtEndOfDescriptor()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorDescriptorTopicY);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorDescriptorTopicN);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Contains("EEE*", reference.MeshHeadings);
    }

    [Fact]
    public void Descriptor_NonMajorTopic_DoesNotHaveAsteriskAtEndOfDescriptor()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorDescriptorTopicY);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorDescriptorTopicN);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Contains("FFF", reference.MeshHeadings);
    }

    [Fact]
    public void Qualifier_MajorTopic_HasAsteriskAtEndOfDescriptor()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorQualifierTopicY);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorQualifierTopicN);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Contains("g-3*", reference.MeshHeadings);
    }

    [Fact]
    public void Qualifier_NonMajorTopic_DoesNotHaveAsteriskAtEndOfDescriptor()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorQualifierTopicY);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeadingMajorQualifierTopicN);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Contains("h-3", reference.MeshHeadings);
    }

    [Fact]
    public void Build_Sets_MeshHeadings_And_Excludes_Items_With_Empty_DescriptorName()
    {
        // Arrange
        var pubmedArticle = EmptyArticle;
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading1Qualifier);
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(new() { DescriptorName = new() { Value = " " } });
        pubmedArticle.MedlineCitation.MeshHeadingList.Add(MeshHeading2Qualifiers);

        // Act
        var reference = _referenceBuilder.Build(pubmedArticle);

        // Assert
        Assert.Equal("BBB: b-1; CCC: c-1 / c-2", reference.MeshHeadings);
    }
}
