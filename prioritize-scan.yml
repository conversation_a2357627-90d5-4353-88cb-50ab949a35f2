parameters:
  - name: AnalysePackages
    default: false
  - name: AzureSubscription
    default: "DevOps Bastion"
  - name: KeyVaultName
    default: "pxv-vault-eun"
  - name: WSS_URL
    default: "https://saas-eu.whitesourcesoftware.com/agent"
  - name: BuildConfiguration
    type: string
    default: 'Release'
  - name: LongLivedBranch
    default: false
  - name: WhiteSourceProductName
    default: $(System.TeamProject)_$(Build.SourceBranchName)
  - name: WhiteSourceProjectName
    default: "$(Build.Repository.Name)_$(Build.SourceBranchName)"
  - name: AnalyseProjectName
    default: "$(Build.Repository.Name)"

steps:
- task: AzureKeyVault@1
  displayName: "Retrieve Secrets from KeyVault ${{ parameters.KeyVaultName }}"  
  condition: and(succeeded(), or(eq(${{ parameters.LongLivedBranch }}, true), eq(${{ parameters.AnalysePackages }}, true)))
  inputs:
    azureSubscription: "${{ parameters.AzureSubscription }}"
    KeyVaultName: "${{ parameters.KeyVaultName }}"
    SecretsFilter: 'whitesource-api-key,whitesource-user-key'
    RunAsPreJob: false
- script: |
      curl -LJO https://unified-agent.s3.amazonaws.com/wss-unified-agent.jar
      echo Unified Agent downloaded successfully
      java -jar wss-unified-agent.jar -d $(Build.SourcesDirectory)/src/
  env:
    WS_APIKEY: $(whitesource-api-key)
    WS_USERKEY: $(whitesource-user-key)
    WS_WSS_URL: ${{ parameters.WSS_URL }}
    WS_PRODUCTNAME: ${{ parameters.WhiteSourceProductName }}
    WS_PROJECTNAME: ${{ parameters.WhiteSourceProjectName }}
    WS_SCANCOMMENT: $(Build.Repository.Name)_$(Build.SourceBranchName)
    WS_EXCLUDES: excludes=**/.*,**/node_modules,**/test,**/src/test,**/testdata,**/*sources.jar,**/*javadoc.jar
    WS_FILESYSTEMSCAN: false
  displayName: Mend Unified Agent Scan
  condition: and(succeeded(), or(eq(${{ parameters.LongLivedBranch }}, true), eq(${{ parameters.AnalysePackages }}, true)))