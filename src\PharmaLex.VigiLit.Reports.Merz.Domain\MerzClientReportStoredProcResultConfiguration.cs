using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Merz.Domain;

public class MerzClientReportStoredProcResultConfiguration : EntityBaseMap<MerzClientReportResult>
{
    public override void Configure(EntityTypeBuilder<MerzClientReportResult> builder)
    {
        builder.HasNoKey().ToView(null);
    }
}