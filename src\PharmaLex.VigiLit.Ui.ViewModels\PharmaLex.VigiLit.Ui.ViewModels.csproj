﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="ReferenceModule\ReferenceSearchResultsModel.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FluentValidation" Version="11.11.0" />
	</ItemGroup>
	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="UserNotification\" />
	</ItemGroup>

</Project>
