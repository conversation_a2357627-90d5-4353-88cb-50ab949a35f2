/* Multi-select */
.multi-select {
    position: absolute;
    display: flex;
    flex-direction: column;
    background-color: $white;
    align-items: flex-start;
    z-index: 1;
    user-select: none;
    border: 1px solid $grey-3;
    border-radius: 8px;
    overflow: hidden;
    width: inherit;

    label {
        margin-bottom: 0px;
    }

    .preview-box {
        display: flex;
        flex-direction: row;
        align-self: stretch;
        align-items: center;
        padding: 6px 10px;
        border-bottom: 1px solid transparent;

        .selected-label {
            display: block;
            align-self: center;
            margin-right: auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: normal;
            color: $black;
        }

        .selected-count {
            display: flex;
            justify-content: center;
            align-items: center;
            justify-self: end;
            margin-left: 10px;
            text-align: center;
            background-color: $brand;
            color: $white;
            min-height: calc((3rem / 2) + 2px);
            min-width: calc((3rem / 2) + 2px);
            border-radius: 50%;
        }

        .filter-multiselect {
            height: 30px;
            margin: 0px;
            border: none
        }
    }

    .item-container {
        display: flex;
        align-self: stretch;
        flex-direction: column;
        padding-bottom: 1px;
        overflow-y: auto;
        max-height: 16em;
    }

    .multi-select-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 4px;

        &.disabled-item {
            background-color: $grey-4;
            opacity: 0.5;
            pointer-events: none;
        }

        &:hover {
            background-color: #e9e9e9;

            input {
                background: $white;
            }
        }

        input {
            top: 0;
            margin: 0;
            margin-right: 5px;
            border-radius: 3px;
        }

        .multiSelectItemLabel {
            padding: 6px 0px;
            width: 100%;
            font-weight: normal;
            color: $black;
        }

        .formatLabel {
            display: flex;
            align-items: center;
            align-self: normal;
        }
    }

    .hidden {
        display: none;
    }

    &:hover {
        border: 1px solid $grey-2;
    }

    &.active {
        border: 1px solid $brand;

        .preview-box {
            border-bottom: 1px solid $brand;
        }
    }

    &.disabled {
        pointer-events: none;
        background-color: #f2f4f6;

        .selected-label {
            color: #949494;
        }

        .selected-count {
            background-color: #949494;
        }
    }
}

/* Companies Multi-select */
.companies-multi-select {
    position: absolute;
    display: flex;
    flex-direction: column;
    background-color: $white;
    align-items: flex-start;
    z-index: 1;
    user-select: none;
    border: 1px solid $grey-3;
    border-radius: 8px;
    overflow: hidden;
    width: calc(40% - 4rem);

    label {
        margin-bottom: 0px;
    }

    .select-box {
        display: flex;
        flex-direction: row;
        align-self: stretch;
        padding: 6px 10px;
        border-bottom: 1px solid transparent;

        .selected-label {
            display: block;
            align-self: center;
            margin-right: auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .selected-count {
            display: flex;
            justify-content: center;
            align-items: center;
            justify-self: end;
            margin-left: 10px;
            text-align: center;
            background-color: $brand;
            color: $white;
            min-height: 20px;
            min-width: 20px;
            border-radius: 50%;
        }
    }

    .check-box {
        display: flex;
        align-self: stretch;
        flex-direction: column;
        padding-bottom: 1px;
        overflow-y: auto;
        max-height: 16em;
    }

    .companies-multi-select-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 4px;

        &:hover {
            background-color: #e9e9e9;

            input {
                background: $white;
            }
        }

        input {
            top: 0;
            margin: 0;
            margin-right: 5px;
            border-radius: 3px;
        }

        .multiSelectItemLabel {
            padding: 6px 0px;
            width: 100%;
        }

        .formatLabel {
            display: flex;
            align-items: center;
            align-self: normal;
        }
    }

    .hidden {
        display: none;
    }

    &:hover {
        border: 1px solid $grey-2;
    }

    &.active {
        border: 1px solid $brand;

        .select-box {
            border-bottom: 1px solid $brand;
        }
    }
}

.switch-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    width: 100%;
}