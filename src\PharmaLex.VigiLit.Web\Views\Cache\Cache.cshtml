﻿@using PharmaLex.VigiLit.Web.ViewModels

@model IEnumerable<CacheViewModel>

@{
    ViewData["Title"] = "Cache";
}

<div>
    <div class="sub-header">
        <h2>Cache</h2>
        <div class="controls">
            <a id="deleteCache" class="button" href="">Flush All Cached Data</a>
            <form id="deleteForm" style="display: none;" method="post" action="/cache/flush">
                @Html.AntiForgeryToken()
            </form>
        </div>
    </div>
    <section>
        <table id="cache-table" class="my-3">
            <thead>
                <tr>
                    <th>Dependency</th>
                    <th>Key</th>
                </tr>
            </thead>
            <tbody>
                @foreach (CacheViewModel o in Model)
                {
                    <tr data-link="/cache/edit/@o.Value">
                        <td>@o.Value</td>
                        <td>@o.Key</td>
                    </tr>
                }
            </tbody>
        </table>
    </section>
</div>

@section Scripts {
    <script src="@Cdn.Host/js/bundles/datatables-custom.min.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            var table = $('#cache-table').DataTable({
                pageLength: 25,
                pagingType: 'full_numbers',
                orderFixed: [[1, 'asc']],
                autoWidth: false,
                rowGroup: {
                    dataSrc: 1
                },
                columnDefs: [{ targets: 0, type: 'locale-compare' }, { targets: 1, visible: false }]
            });
            $('#cache-table tbody').on('click', 'tr[role=row]', (e) => document.location.href = $(e.currentTarget).data('link'));
            $('#cache-table_filter input').keyup(() => table.search(jQuery.fn.dataTable.ext.type.search.string(this.value)).draw());

            $('#deleteCache').on('click', e => {
                e.preventDefault();

                let deleteForm = document.getElementById('deleteForm');

                deleteForm.submit();
            });
        });
    </script>
}
