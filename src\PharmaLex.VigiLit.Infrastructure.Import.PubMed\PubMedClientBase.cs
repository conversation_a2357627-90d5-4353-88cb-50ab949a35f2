﻿using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Configuration;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public class PubMedClientBase
{
    protected string PubMedApiKey { get; set; }

    public PubMedClientBase(IConfiguration configuration)
    {
        PubMedApiKey = configuration.GetValue<string>("PubMedApiKey");

        if (string.IsNullOrEmpty(PubMedApiKey))
        {
            throw new ArgumentException("Failed to retrieve PubMedApiKey from Key vault.");
        }
    }

    protected XmlRootAttribute GetRootElement(string elementName)
    {
        XmlRootAttribute xRoot = new()
        {
            ElementName = elementName,
            IsNullable = true
        };

        return xRoot;
    }

    protected XmlReader CreateXmlReader(Stream stream)
    {
        var settings = new XmlReaderSettings() { DtdProcessing = DtdProcessing.Ignore };

        return XmlReader.Create(stream, settings);
    }

    protected async Task RateLimiter()
    {
        // NCBI's approach: 
        // - ESearch and EFetch have a rate limit. (all E-Utilities do)
        // - Breaking the rate limit can result in being blocked. (unknown likelihood)
        // - The rate limit is 3 requests per second without an api key and 10 requests per second with an api key. (we have an api key)
        // - Even with an api key, the recommendation is 3 requests per second for performance/stability reasons.

        // Our approach: 
        // - Don't get blocked.
        // - Be a good ecosystem player.
        // - Our code is slow, so it's naturally delayed. Local is much faster than dev.

        await Task.Delay(100);
    }
}
