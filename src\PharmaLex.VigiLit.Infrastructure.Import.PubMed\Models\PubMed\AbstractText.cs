using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class AbstractText
{
    //[XmlElement("DispFormula", typeof(DispFormula))]
    //public DispFormula DispFormula { get; set; }

    private object[] itemsField;

    private string[] textField;

    private string labelField;

    //private AbstractTextNlmCategory nlmCategoryField;

    //private bool nlmCategoryFieldSpecified;

    /// <remarks/>
    [XmlElement("DispFormula", typeof(DispFormula))]
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }

    /// <remarks/>
    [XmlAttribute()]
    public string Label
    {
        get
        {
            return this.labelField;
        }
        set
        {
            this.labelField = value;
        }
    }


    [XmlAttribute()]
    public AbstractTextNlmCategory NlmCategory { get; set; }

    [XmlIgnore()]
    public bool NlmCategorySpecified { get; set; }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.8.3928.0")]
[Serializable()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
//[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public partial class b
{

    private object[] itemsField;

    private string[] textField;

    /// <remarks/>
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}



//[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.8.3928.0")]
//[System.SerializableAttribute()]
//[System.Diagnostics.DebuggerStepThroughAttribute()]
//[System.ComponentModel.DesignerCategoryAttribute("code")]
//[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public partial class i
{

    private object[] itemsField;

    private string[] textField;

    /// <remarks/>
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.8.3928.0")]
[Serializable()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
//[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public partial class sub
{

    private object[] itemsField;

    private string[] textField;

    /// <remarks/>
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.8.3928.0")]
[Serializable()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
//[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public partial class sup
{

    private object[] itemsField;

    private string[] textField;

    /// <remarks/>
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.8.3928.0")]
[Serializable()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
//[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tempuri.org/pubmed_190101")]
//[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tempuri.org/pubmed_190101", IsNullable = false)]
public partial class u
{

    private object[] itemsField;

    private string[] textField;

    /// <remarks/>
    [XmlElement("b", typeof(b))]
    [XmlElement("i", typeof(i))]
    [XmlElement("sub", typeof(sub))]
    [XmlElement("sup", typeof(sup))]
    [XmlElement("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [XmlText()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}