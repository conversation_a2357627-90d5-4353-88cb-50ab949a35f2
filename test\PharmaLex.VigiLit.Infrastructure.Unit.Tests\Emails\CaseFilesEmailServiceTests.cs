﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System.Net;
using System.Text;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Emails;

public class CaseFilesEmailServiceTests
{
    private readonly ICompanyService _companyService;
    private readonly ICaseService _caseService;
    private readonly ICaseDocumentService _caseDocumentService;
    private readonly IEmailSender _emailSender;
    private readonly IEmailLogService _emailLogService;
    private readonly IWebsiteUriProvider _websiteUriProvider;
    private readonly ILogger<CaseFilesEmailService> _logger;
    private readonly IOptions<EmailOptions> _emailOptions;

    private readonly CompanyUserModel _companyUserPhlexglobal;
    private readonly CompanyUserModel _companyUserPhlexglobalNotActive;
    private readonly CompanyUserModel _companyUserPharmaLex;
    private readonly CompanyUserModel _companyUserPharmaLexNoCaseEmail;

    public readonly int _plxId1Copper = 111;
    public readonly int _plxId2Copper = 112;
    public readonly int _plxId3Iron = 222;

    public readonly string _substanceAloe = "Aloe";
    public readonly string _substanceCopper = "Copper";
    public readonly string _substanceIron = "Iron";
    public readonly string _substancePotassium = "Potassium";
    public readonly string _substanceZinc = "Zinc";

    public readonly string _fileNameCopperPdf = "Cu.pdf";
    public readonly string _fileNameCopperXml = "Cu.xml";
    public readonly string _fileNameCopperDocx = "Cu.docx";
    public readonly string _fileNameIronPdf = "Fe.pdf";
    public readonly string _fileNameIronXml = "Fe.xml";

    public readonly string _fileContentCopperPdf = "PDF document about Copper.";
    public readonly string _fileContentCopperXml = "XML document about Copper";
    public readonly string _fileContentCopperDocx = "DOCX document about Copper";
    public readonly string _fileContentIronPdf = "PDF file for Iron";
    public readonly string _fileContentIronXml = "XML file for Iron";

    public readonly Stream _streamCopperPdf;
    public readonly Stream _streamCopperXml;
    public readonly Stream _streamCopperDocx;
    public readonly Stream _streamIronPdf;
    public readonly Stream _streamIronXml;

    public readonly StreamHelper _streamHelper;

    private const string _websiteUri = "https://localhost:5001";

    private readonly CaseFilesEmailService _caseFilesEmailService;

    public CaseFilesEmailServiceTests()
    {
        const int _companyIdPhlexglobal = 1;
        const int _companyIdPharmaLex = 2;

        _companyUserPhlexglobal = new()
        {
            CompanyId = _companyIdPhlexglobal,
            Id = 11,
            GivenName = "Davy",
            FamilyName = "Jones",
            Email = "<EMAIL>",
            Active = true,
            EmailPreferenceIds = new List<int> { (int)EmailType.DailyCaseFilesEmail }
        };
        _companyUserPhlexglobalNotActive = new()
        {
            CompanyId = _companyIdPhlexglobal,
            Id = 12,
            GivenName = "Not",
            FamilyName = "Active",
            Email = "<EMAIL>",
            Active = false,
            EmailPreferenceIds = new List<int> { (int)EmailType.DailyCaseFilesEmail }
        };
        _companyUserPharmaLex = new()
        {
            CompanyId = _companyIdPharmaLex,
            Id = 21,
            GivenName = "Captain",
            FamilyName = "Morgan",
            Email = "<EMAIL>",
            Active = true,
            EmailPreferenceIds = new List<int> { (int)EmailType.DailyCaseFilesEmail }
        };
        _companyUserPharmaLexNoCaseEmail = new()
        {
            CompanyId = _companyIdPharmaLex,
            Id = 22,
            GivenName = "No Email",
            FamilyName = "Preference",
            Email = "<EMAIL>",
            Active = true,
            EmailPreferenceIds = new List<int> { (int)EmailType.DailyReferenceClassificationEmail }
        };

        var companyEmailModels = new List<CompanyEmailModel>
        {
            new()
            {
                Id = _companyIdPhlexglobal,
                Name = "Phlexglobal",
                CompanyUsers = new List<CompanyUserModel> { _companyUserPhlexglobal, _companyUserPhlexglobalNotActive }
            },
            new()
            {
                Id = _companyIdPharmaLex,
                Name = "PharmaLex",
                CompanyUsers = new List<CompanyUserModel> { _companyUserPharmaLex, _companyUserPharmaLexNoCaseEmail }
            }
        };

        var companyWithContractsPhlexglobal = new CompanyWithContractsModel
        {
            Id = _companyIdPhlexglobal,
            Contracts = new List<SimpleContractModel>
            {
                new() { Id = 40, SubstanceName = _substanceCopper, IsActive = true },
                new() { Id = 41, SubstanceName = _substanceCopper, IsActive = true },
                new() { Id = 43, SubstanceName = _substanceIron, IsActive = true }
            }
        };
        var companyWithContractsPharmaLex = new CompanyWithContractsModel
        {
            Id = _companyIdPharmaLex,
            Contracts = new List<SimpleContractModel>
            {
                new() { Id = 50, SubstanceName = _substanceCopper, IsActive = true },
                new() { Id = 51, SubstanceName = _substancePotassium, IsActive = true },
                new() { Id = 52, SubstanceName = _substanceZinc, IsActive = true },
                new() { Id = 53, SubstanceName = _substanceAloe, IsActive = false }
            }
        };

        var caseModelCopper1 = new CaseModel
        {
            Id = 101,
            PlxId = _plxId1Copper,
            SubstanceName = _substanceCopper,
            CaseFiles = new List<CaseFileModel> { new(1011, 101, _fileNameCopperPdf, _fileContentCopperPdf.Length), new(1012, 101, _fileNameCopperXml, _fileContentCopperXml.Length) }
        };
        var caseModelCopper2 = new CaseModel
        {
            Id = 102,
            PlxId = _plxId2Copper,
            SubstanceName = _substanceCopper,
            CaseFiles = new List<CaseFileModel> { new(1021, 102, _fileNameCopperDocx, _fileContentCopperDocx.Length) }
        };
        var caseModelIron = new CaseModel
        {
            Id = 202,
            PlxId = _plxId3Iron,
            SubstanceName = "Iron",
            CaseFiles = new List<CaseFileModel> { new(2021, 202, _fileNameIronPdf, _fileContentIronPdf.Length), new(2022, 202, _fileNameIronXml, _fileContentIronXml.Length) }
        };
        var caseModelsPhlexglobal = new List<CaseModel> { caseModelCopper1, caseModelCopper2, caseModelIron };
        var caseModelsPharmaLex = new List<CaseModel> { caseModelCopper1 };

        _companyService = Substitute.For<ICompanyService>();
        _companyService
            .GetActiveCompaniesWithUsers()
            .Returns(companyEmailModels);
        _companyService
            .GetCompanyWithContracts(_companyIdPhlexglobal)
            .Returns(companyWithContractsPhlexglobal);
        _companyService
            .GetCompanyWithContracts(_companyIdPharmaLex)
            .Returns(companyWithContractsPharmaLex);

        _caseService = Substitute.For<ICaseService>();
        _caseService
            .SearchAsync(Arg.Is<CaseSearchRequest>(x => x.CaseStatus == CaseStatus.Pending && x.CompanyId == _companyIdPhlexglobal))
            .Returns(caseModelsPhlexglobal);
        _caseService
            .SearchAsync(Arg.Is<CaseSearchRequest>(x => x.CaseStatus == CaseStatus.Pending && x.CompanyId == _companyIdPharmaLex))
            .Returns(caseModelsPharmaLex);

        _streamCopperPdf = new MemoryStream(Encoding.ASCII.GetBytes(_fileContentCopperPdf));
        _streamCopperXml = new MemoryStream(Encoding.ASCII.GetBytes(_fileContentCopperXml));
        _streamCopperDocx = new MemoryStream(Encoding.ASCII.GetBytes(_fileContentCopperDocx));
        _streamIronPdf = new MemoryStream(Encoding.ASCII.GetBytes(_fileContentIronPdf));
        _streamIronXml = new MemoryStream(Encoding.ASCII.GetBytes(_fileContentIronXml));
        _caseDocumentService = Substitute.For<ICaseDocumentService>();
        _caseDocumentService
            .Exists(new CaseDocumentDescriptor(caseModelCopper1.Id, _fileNameCopperPdf))
            .Returns(true);
        _caseDocumentService
            .OpenRead(new CaseDocumentDescriptor(caseModelCopper1.Id, _fileNameCopperPdf))
            .Returns(_streamCopperPdf);
        _caseDocumentService
            .Exists(new CaseDocumentDescriptor(caseModelCopper1.Id, _fileNameCopperXml))
            .Returns(true);
        _caseDocumentService
            .OpenRead(new CaseDocumentDescriptor(caseModelCopper1.Id, _fileNameCopperXml))
            .Returns(_streamCopperXml);
        _caseDocumentService
            .Exists(new CaseDocumentDescriptor(caseModelCopper2.Id, _fileNameCopperDocx))
            .Returns(true);
        _caseDocumentService
            .OpenRead(new CaseDocumentDescriptor(caseModelCopper2.Id, _fileNameCopperDocx))
            .Returns(_streamCopperDocx);
        _caseDocumentService
            .Exists(new CaseDocumentDescriptor(caseModelIron.Id, _fileNameIronPdf))
            .Returns(true);
        _caseDocumentService
            .OpenRead(new CaseDocumentDescriptor(caseModelIron.Id, _fileNameIronPdf))
            .Returns(_streamIronPdf);
        _caseDocumentService
            .Exists(new CaseDocumentDescriptor(caseModelIron.Id, _fileNameIronXml))
            .Returns(true);
        _caseDocumentService
            .OpenRead(new CaseDocumentDescriptor(caseModelIron.Id, _fileNameIronXml))
            .Returns(_streamIronXml);

        _emailSender = Substitute.For<IEmailSender>();
        _emailSender
            .SendCaseEmail(default!, default!, default!)
            .ReturnsForAnyArgs(new SendGrid.Response(HttpStatusCode.OK, default, default));

        _emailLogService = Substitute.For<IEmailLogService>();
        _emailLogService
            .CreateAndSaveEmailLog(EmailType.DailyCaseFilesEmail, EmailTriggerType.Manual)
            .Returns(new Email());

        _websiteUriProvider = Substitute.For<IWebsiteUriProvider>();
        _websiteUriProvider
            .Provide()
            .Returns(new Uri(_websiteUri));

        _logger = Substitute.For<ILogger<CaseFilesEmailService>>();
        _logger = Substitute.For<ILogger<CaseFilesEmailService>>();

        _emailOptions = Substitute.For<IOptions<EmailOptions>>();
        _emailOptions.Value
            .Returns(new EmailOptions
            {
                CaseEmailAttachmentMaxBytes = 10485760
            });

        _caseFilesEmailService = new CaseFilesEmailService(_companyService, _caseService, _caseDocumentService, _emailSender, _emailLogService, _websiteUriProvider, _logger, _emailOptions);

        _streamHelper = new StreamHelper();
    }

    [Fact]
    public async Task Send_NoActiveCompaniesWithUsers_DoesNotSendEmails()
    {
        // Arrange
        _companyService
            .GetActiveCompaniesWithUsers()
            .Returns(Array.Empty<CompanyEmailModel>());

        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .DidNotReceiveWithAnyArgs()
            .SendCaseEmail(default!, default!, default!);
    }

    [Theory]
    [InlineData(EmailTriggerType.Auto)]
    [InlineData(EmailTriggerType.Manual)]
    public async Task Send_NoActiveCompaniesWithUsers_LogsNoEmailsSent(EmailTriggerType emailTriggerType)
    {
        // Arrange
        _companyService
            .GetActiveCompaniesWithUsers()
            .Returns(Array.Empty<CompanyEmailModel>());

        // Arrange
        var email = new Email();
        _emailLogService
            .CreateAndSaveEmailLog(EmailType.DailyCaseFilesEmail, emailTriggerType)
            .Returns(email);

        // Act
        await _caseFilesEmailService.Send(emailTriggerType);

        // Assert
        await _emailLogService
            .ReceivedWithAnyArgs(1)
            .CreateAndSaveEmailLog(default, default);

        await _emailLogService
            .ReceivedWithAnyArgs(1)
            .LogOutcome(null, default, default, default);

        await _emailLogService
            .Received(1)
            .LogOutcome(email, EmailStatusType.Completed, 0, 0);
    }

    [Fact]
    public async Task Send_ActiveCompanyWithUserNotActive_UserDoesNotReceiveEmail()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .DidNotReceive()
            .SendCaseEmail(Arg.Is<CaseEmailModel>(model => model.RecipientEmail == _companyUserPhlexglobalNotActive.Email), Arg.Any<string>(), Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompanyWithUserWithoutEmailPreference_UserDoesNotReceiveEmail()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .DidNotReceive()
            .SendCaseEmail(Arg.Is<CaseEmailModel>(model => model.RecipientEmail == _companyUserPharmaLexNoCaseEmail.Email), Arg.Any<string>(), Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_SendsEmailsToCorrectUsers()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                    && model.RecipientEmail == _companyUserPhlexglobal.Email),

                Arg.Any<string>(),
                Arg.Any<byte[]>());

        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPharmaLex.DisplayFullName
                    && model.RecipientEmail == _companyUserPharmaLex.Email),

                Arg.Any<string>(),
                Arg.Any<byte[]>());

        await _emailSender
            .Received(2)
            .SendCaseEmail(Arg.Any<CaseEmailModel>(), Arg.Any<string>(), Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_SendsEmailsWithCorrectSubject()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                    && model.RecipientEmail == _companyUserPhlexglobal.Email

                    && model.Subject.StartsWith("3 New Cases")),

                Arg.Any<string>(),
                Arg.Any<byte[]>());

        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPharmaLex.DisplayFullName
                    && model.RecipientEmail == _companyUserPharmaLex.Email

                    && model.Subject.StartsWith("1 New Case")),

                Arg.Any<string>(),
                Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_SendsEmailsWithCorrectSubstances()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                    && model.RecipientEmail == _companyUserPhlexglobal.Email

                    && model.Substances.Count == 2

                    && model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substanceCopper
                        && substanceModel.Cases.Count == 2
                        && substanceModel.Cases.Any(caseModel => caseModel.PlxId == _plxId1Copper)
                        && substanceModel.Cases.Any(caseModel => caseModel.PlxId == _plxId2Copper))

                    && model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substanceIron
                        && substanceModel.Cases.Count == 1
                        && substanceModel.Cases.Any(caseModel => caseModel.PlxId == _plxId3Iron))
                    ),
                Arg.Any<string>(),
                Arg.Any<byte[]>());

        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPharmaLex.DisplayFullName
                    && model.RecipientEmail == _companyUserPharmaLex.Email

                    && model.Substances.Count == 3

                    && model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substanceCopper
                        && substanceModel.Cases.Count == 1
                        && substanceModel.Cases.Any(caseModel => caseModel.PlxId == _plxId1Copper))

                    && model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substancePotassium
                        && substanceModel.Cases.Count == 0)

                    && model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substanceZinc
                        && substanceModel.Cases.Count == 0)

                    && !model.Substances.Any(substanceModel =>
                        substanceModel.Name == _substanceAloe)
                    ),
                Arg.Any<string>(),
                Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_SendsEmailsWithCorrectCaseUri()
    {
        // Arrange
        var viewCaseUri = new Uri($"{_websiteUri}/Cases");

        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                    && model.RecipientEmail == _companyUserPhlexglobal.Email

                    && model.ViewCaseUri == viewCaseUri),

                Arg.Any<string>(),
                Arg.Any<byte[]>());

        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPharmaLex.DisplayFullName
                    && model.RecipientEmail == _companyUserPharmaLex.Email

                    && model.ViewCaseUri == viewCaseUri),

                Arg.Any<string>(),
                Arg.Any<byte[]>());
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_SendsEmailsWithCorrectAttachments()
    {
        var phlexGlobalStreams = new Dictionary<string, Stream>
            {
                { "101\\" +  _fileNameCopperPdf, _streamCopperPdf},
                { "101\\" + _fileNameCopperXml, _streamCopperXml},
                { "102\\" + _fileNameCopperDocx, _streamCopperDocx},
                { "202\\" + _fileNameIronPdf, _streamIronPdf},
                { "202\\" + _fileNameIronXml, _streamIronXml}
            };

        var pharmalexStreams = new Dictionary<string, Stream>
            {
                { "101\\" + _fileNameCopperPdf, _streamCopperPdf},
                { "101\\" + _fileNameCopperXml, _streamCopperXml}
            };

        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
                .Received(1)
                .SendCaseEmail(
                    Arg.Is<CaseEmailModel>(model =>
                        model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                        && model.RecipientEmail == _companyUserPhlexglobal.Email),

                    Arg.Any<string>(),

                    Arg.Is<byte[]>(model => _streamHelper.AreDictionariesTheSame(phlexGlobalStreams, _streamHelper.UnzipBytesToDictionaryOfStreams(model))
                ));

        await _emailSender
                .Received(1)
                .SendCaseEmail(
                    Arg.Is<CaseEmailModel>(model =>
                        model.RecipientName == _companyUserPharmaLex.DisplayFullName
                        && model.RecipientEmail == _companyUserPharmaLex.Email),

                    Arg.Any<string>(),

                    Arg.Is<byte[]>(model => _streamHelper.AreDictionariesTheSame(pharmalexStreams, _streamHelper.UnzipBytesToDictionaryOfStreams(model))
            ));
    }

    [Fact]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferencesWithLowEmailLimit_SendsEmailsWithLimitedAttachments()
    {
        // Arrange
        _emailOptions.Value
            .Returns(new EmailOptions
            {
                CaseEmailAttachmentMaxBytes = _fileContentCopperPdf.Length + _fileContentCopperXml.Length + _fileContentCopperDocx.Length + _fileContentIronPdf.Length
            });

        var phlexGlobalStreams = new Dictionary<string, Stream>
            {
                { "101\\" + _fileNameCopperPdf, _streamCopperPdf},
                { "101\\" + _fileNameCopperXml, _streamCopperXml},
                { "102\\" + _fileNameCopperDocx, _streamCopperDocx}
            };

        var pharmalexStreams = new Dictionary<string, Stream>
            {
                { "101\\" + _fileNameCopperPdf, _streamCopperPdf},
                { "101\\" + _fileNameCopperXml, _streamCopperXml}
            };

        var sut = new CaseFilesEmailService(_companyService, _caseService, _caseDocumentService, _emailSender, _emailLogService, _websiteUriProvider, _logger, _emailOptions);

        // Act
        await sut.Send(EmailTriggerType.Manual);

        // Assert
        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPhlexglobal.DisplayFullName
                    && model.RecipientEmail == _companyUserPhlexglobal.Email),

                Arg.Any<string>(),

                Arg.Is<byte[]>(model => _streamHelper.AreDictionariesTheSame(phlexGlobalStreams, _streamHelper.UnzipBytesToDictionaryOfStreams(model)))
            );

        await _emailSender
            .Received(1)
            .SendCaseEmail(
                Arg.Is<CaseEmailModel>(model =>
                    model.RecipientName == _companyUserPharmaLex.DisplayFullName
                    && model.RecipientEmail == _companyUserPharmaLex.Email),

                Arg.Any<string>(),

                Arg.Is<byte[]>(model => _streamHelper.AreDictionariesTheSame(pharmalexStreams, _streamHelper.UnzipBytesToDictionaryOfStreams(model)))
            );
    }

    [Theory]
    [InlineData(EmailTriggerType.Auto)]
    [InlineData(EmailTriggerType.Manual)]
    public async Task Send_ActiveCompaniesWithActiveUsersWithEmailPreferences_LogsCountOfEmailsSent(EmailTriggerType emailTriggerType)
    {
        // Arrange
        var email = new Email();
        _emailLogService
            .CreateAndSaveEmailLog(EmailType.DailyCaseFilesEmail, emailTriggerType)
            .Returns(email);

        // Act
        await _caseFilesEmailService.Send(emailTriggerType);

        // Assert
        await _emailLogService
            .ReceivedWithAnyArgs(1)
            .CreateAndSaveEmailLog(default, default);

        await _emailLogService
            .ReceivedWithAnyArgs(1)
            .LogOutcome(null, default, default, default);

        await _emailLogService
            .Received(1)
            .LogOutcome(email, EmailStatusType.Completed, 2, 0);
    }

    [Fact]
    public async Task Send_WhenCalled_PublishesPendingCases()
    {
        // Act
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        // Assert
        await _caseService
            .ReceivedWithAnyArgs(1)
            .PublishPendingCasesAsync();
    }
}