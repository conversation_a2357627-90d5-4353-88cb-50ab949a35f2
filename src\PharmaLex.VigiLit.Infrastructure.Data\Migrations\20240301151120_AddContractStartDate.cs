﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddContractStartDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ContractStartDate",
                table: "Contracts",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GetUTCDate()");

            migrationBuilder.SqlFileExec("Update-ContractStartDate.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContractStartDate",
                table: "Contracts");
        }
    }
}
