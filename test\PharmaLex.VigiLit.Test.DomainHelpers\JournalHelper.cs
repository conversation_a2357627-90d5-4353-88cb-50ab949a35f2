﻿using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class JournalHelper
{
    private readonly IJournalRepository _journalRepository;

    public JournalHelper(IJournalRepository journalRepository)
    {
        _journalRepository = journalRepository;
    }

    public async Task<Journal> AddJournal(string name, Country country, bool enabled)
    {
        var journal = new Journal
        {
            Name = name,
            Country = country,
            Enabled = enabled
        };

        _journalRepository.Add(journal);
        await _journalRepository.SaveChangesAsync();
        return journal;
    }
}