@using PharmaLex.Core.Web.Helpers
@model PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models.PageModel

@{
    ViewData["Title"] = "Tracking Sheets";
}

<div id="tracking-sheets" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2><a href="../Reports">Reports</a> > Tracking Sheets</h2>
        <div class="controls">
        </div>
    </div>

    <section>
        <h2>Weeks</h2>
        <p>Choose a week below to view the Tracking Sheet.</p>
        <br />
    
        <div class="filters flex gapped">
            <div class="custom-select">
                <select id="company" name="company" class="white-background" v-model='selectedCompanyId' v-on:change="loadCards" v-if="companies.length > 0" aria-label="Company">
                    <option value='0'>-- Select Company --</option>
                    <option v-for='company in companies' :value='company.id' :class="[{'inactive':!company.isActive}]">{{ company.name }}</option>
                </select>
            </div>
            <div class="custom-select">
                <select id="year" name="year" class="white-background" v-model='selectedYear' v-on:change="loadCards" aria-label="Year">
                    <option v-for='year in years' :value='year'>{{ year }}</option>
                </select>
            </div>
        </div>

        <div v-if="companies.length > 0 && selectedCompanyId == 0">
            <h2>{{selectedYear}}</h2>
            <p>Please select a company.</p>
        </div>

        <div v-if="(companies.length > 0 && selectedCompanyId > 0) || (companies.length === 0)">
            <h2>{{selectedYear}}</h2>
            <div class="cards-loading" v-if="cardsLoading">
                <p>Loading...</p>
            </div>
            <div class="cards" v-else>
                <div v-for="card in cards" :class="['card', {'future':card.isFuture}, {'missing':!card.isFuture && card.trackingSheetId===0}]">
                    <div class="card-info">
                        <h2>{{card.weekNumber}}</h2>
                        <p>{{card.weekStart}} - {{card.weekEnd}}</p>
                    </div>
                    <div class="card-buttons">
                        <a v-if="!card.isFuture && card.trackingSheetId > 0" href="#" @@click.stop.prevent="download(card)">Download</a>
                        <a v-if="!card.isFuture && card.trackingSheetId === 0" href="#" @@click.stop.prevent="create(card)">Create</a>
                        &nbsp;
                    </div>
                </div>
            </div>
        </div>

    </section>

</div>

@section Scripts {
<script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageModel = @Html.Raw(AntiXss.ToJson(Model));

        var pageConfig = {
            appElement: "#tracking-sheets",
            data: function () {
                return {
                    companies: pageModel.companies,
                    years: pageModel.years,
                    cards: pageModel.cards,
                    selectedYear: pageModel.selectedYear,
                    selectedCompanyId: 0,
                    cardsLoading: false
                };
            },
            methods: {
                loadCards: function () {
                    this.cardsLoading = true;
                    this.cards = [];

                    let url = '/Reports/TrackingSheets/GetCards';

                    let params = { companyId: this.selectedCompanyId, year: this.selectedYear };

                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(params),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.cards = data;
                        this.cardsLoading = false;
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                download: function (card) {
                    let url = `/Reports/TrackingSheets/Download/${card.trackingSheetId}`;
                    
                    var onFailure = function () {
                        plx.toast.show('Unable to download, please try again', 2, 'failed', null, 2500);
                    }
                    var onComplete = function () {
                    }

                    DownloadFile.fromUrl(url, null, null, onFailure, onComplete);
                },
                create: function (card) {
                    this.cardsLoading = true;

                    let url = `/Reports/TrackingSheets/Create`;

                    var params = {
                        companyId: card.companyId,
                        year: card.year,
                        week: card.weekNumber
                    };

                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(params),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            this.loadCards();
                        }
                        else {
                            this.cardsLoading = false;
                            console.log(error);
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        }
                    });
                }
            }
        };

</script>
}
