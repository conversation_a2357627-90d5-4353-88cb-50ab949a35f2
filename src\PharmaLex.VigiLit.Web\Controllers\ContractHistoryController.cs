﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.ContractManagement.Security;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using System;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;
[Route("[controller]")]
[Authorize(Policy = Policies.ExternalUser)]
public class ContractHistoryController : BaseController
{
    private readonly IAccessControlService _accessControlService;
    private readonly ICompanyUserService _companyUserService;
    private readonly ICompanyService _companyService;
    private readonly IContractService _contractService;

    public ContractHistoryController(IAccessControlService accessControlService, ICompanyUserService companyUserService, ICompanyService companyService, 
        IContractService contractService, IUserSessionService userSessionService, IConfiguration configuration)
        : base(userSessionService, configuration)
    {
        _accessControlService = accessControlService;
        _companyUserService = companyUserService;
        _companyService = companyService;
        _contractService = contractService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var companyUser = await _companyUserService.GetCompanyUserByEmail(HttpContext.User.GetEmail());
        if (companyUser == null)
        {
            return NotFound();
        }
        var company = await _companyService.GetCompanyWithContracts(companyUser.CompanyId);
        return View(company);
    }

    [HttpGet("{contractId}")]
    public async Task<IActionResult> GetContractVersions(int contractId)
    {
        try
        {
            var viewContext = new ViewContractPermissionContext(CurrentUserId, contractId);
            await _accessControlService.HasPermission(viewContext);
            
            var contract = await _contractService.GetContractDetails(contractId);

            return View("ContractHistoryVersions", contract);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("[action]/{contractId}")]
    public async Task<IActionResult> PrintPreview(int contractId)
    {
        try
        {
            var viewContext = new ViewContractPermissionContext(CurrentUserId, contractId);
            await _accessControlService.HasPermission(viewContext);

            var contract = await _contractService.GetContractWithLatestVersion(contractId);
            return View(contract);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

}