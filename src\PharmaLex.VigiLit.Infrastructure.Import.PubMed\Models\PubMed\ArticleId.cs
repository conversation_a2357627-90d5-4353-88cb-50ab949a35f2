using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public partial class ArticleId
{
    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(ArticleIdIdType.pubmed)]
    public ArticleIdIdType IdType { get; set; } = ArticleIdIdType.pubmed;

    [XmlText()]
    public string Value { get; set; }
}