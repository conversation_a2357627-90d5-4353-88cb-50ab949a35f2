﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class UpdateControllerName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"UPDATE [dbo].[Reports] SET [ControllerName] = 'TrackingSheets' WHERE [Name] = 'Tracking Sheets';" +

                                  "UPDATE [dbo].[Reports] SET [ControllerName] = 'ApogephaClientReport' WHERE [Name] = 'Apogepha Client Report';");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
