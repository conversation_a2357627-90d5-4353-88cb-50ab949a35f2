﻿@model PharmaLex.FeatureManagement.Entities.FeatureFlag;
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;
@{
    ViewData["Title"] = "Settings";
}
<div id="settings" v-cloak>
    @Html.AntiForgeryToken()
    <div class="sub-header">
        <h2>Settings</h2>
    </div>
    <section>
        <ul class="tabs">
            <li @@click="loadGlobalSettings" v-bind:class="{ active: activeTab == 'globalSettingsTab' }">
                <a @@click.prevent href="">Global</a>
            </li>
        </ul>
        <div class="global-container" v-if="activeTab == 'globalSettingsTab'">
            <div class="sidebar">
                <ul class="menu">
                    <li @@click="activeMenu = 'aiSettings'" v-bind:class="{ active: activeMenu == 'aiSettings' }">
                        <a @@click.prevent href="#">AI</a>
                    </li>
                    <li @@click="activeMenu = 'journalsSettings'" v-bind:class="{ active: activeMenu == 'journalsSettings' }">
                        <a @@click.prevent href="#">Journals</a>
                    </li>
                </ul>
            </div>
            <div class="main-content">
                <div v-if="activeMenu == 'aiSettings'">
                    <form method="post">
                        <div class="ai-suggestion-toggle-panel">
                            <h2>AI Suggestions</h2>
                            <p>The AI Assistant uses AI to help your agents work more efficiently.When a feature is switched on, it will be instantly available for agents</p>
                            @if (Model.Enabled)
                            {
                                <p>You can see AI suggestions on the pre-classification screen, and data is processed.</p>
                            }
                            else
                            {
                                <p>You won't see AI suggestions on the pre-classification screen, but data is still processed.</p>
                            }
                            <label class="switch-container">
                                <input class="switch" name="active" id="active" :value="aiSuggestionFlagObj.enabled" v-model="aiSuggestionFlagObj.enabled" type="checkbox" :onchange="handleAiSuggestionCheckboxChange" aria-label="ON" />
                                <label class="switch" for="active"></label>
                                @if (Model.Enabled)
                                {
                                    <label for="active">Enable AI Suggestions</label>
                                }
                                else
                                {
                                    <label for="active">Disable AI Suggestions</label>
                                }

                            </label>
                        </div>
                    </form>
                </div>
                <div v-if="activeMenu == 'journalsSettings'">
                    <journals-table>
                    </journals-table>
                </div>

            </div>


        </div>
        <modal-dialog v-if="showConfirmDialog"
                      :title="confirmTitle"
                      :btntxt="'Ok'"
                      width="500px"
                      height="150px"
                      v-on:close="clearDialog"
                      v-on:confirm="updateAiSuggestionFlag">
            <p>{{aiSuggestionsFlagChangeMsgTxt}}</p>
        </modal-dialog>
    </section>
</div>

@section Scripts {
    <script type="text/javascript">
        var model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: "#settings",
            data: function () {
                return {
                    aiSuggestionFlagObj: model,
                    activeTab: "globalSettingsTab", //Default tab
                    activeMenu:"aiSettings", //Default Menu
                    showConfirmDialog: false,
                    confirmTitle:'',
                    aiSuggestionsFlagChangeMsgTxt: '',
                    aiSuggestionToggleTxt: ''
                };
            },
            methods: {
                loadGlobalSettings() {
                    this.activeTab = 'globalSettingsTab';
                },
                handleAiSuggestionCheckboxChange(event) {
                    this.showConfirmDialog = true;
                    this.setTextByAiSuggestionsToggle(this.aiSuggestionFlagObj.enabled);
                },
                clearDialog() {
                    this.showConfirmDialog = false;
                    window.location.reload();
                },
                setTextByAiSuggestionsToggle(enabled) {
                    if (enabled) {
                        this.confirmTitle = 'Confirm AI suggestions feature will be turned ON';
                        this.aiSuggestionsFlagChangeMsgTxt = 'You will see AI suggestions on the pre-classification screen, and data is processed.';
                    }
                    else {
                        this.confirmTitle = 'Confirm AI suggestions feature will be turned OFF';
                        this.aiSuggestionsFlagChangeMsgTxt = 'You won\'t see AI suggestions on the pre-classification screen, but data is still processed.';
                    }
                },
                updateAiSuggestionFlag() {
                    fetch(`/Settings/Edit`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(this.aiSuggestionFlagObj),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                    }).then(result => {
                        if (result.ok) {
                            window.location.reload();
                        }
                        else {
                            plx.toast.show('There was a problem updating ai suggestions feature flag. Please try again.', 2, 'failed', null, 5000)
                            console.log(error);
                        }

                    })
                }
            }
        };
    </script>
}
@section VueComponentScripts {
    <partial name="Components/ModalDialog" />
    <partial name="Components/Vue3/FilteredTable" />
    <partial name="Components/JournalsTable" />
    <partial name="Components/AddEditJournalModal" />
}
