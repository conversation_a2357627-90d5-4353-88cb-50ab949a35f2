﻿using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Test.DomainHelpers;
using PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore;
using PharmaLex.VigiLit.Test.Framework.Orderers;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.Repositories;

[TestCaseOrderer(
    ordererTypeName: "PharmaLex.VigiLit.Test.Framework.Orderers.PriorityOrderer",
    ordererAssemblyName: "PharmaLex.VigiLit.Test.Framework")]
public class CompanyRepositoryTests : IClassFixture<DatabaseFixture<VigiLitDbContext, CompanyRepositoryTests>>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IUserRepository _userRepository;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IContractRepository _contractRepository;

    UserHelper _userHelper;
    CompanyHelper _companyHelper;
    ContractHelper _contractHelper;
    ProjectHelper _projectHelper;
    SubstanceHelper _substanceHelper;

    public CompanyRepositoryTests(DatabaseFixture<VigiLitDbContext, CompanyRepositoryTests> fixture)
    {
        _userRepository = RepositoryFactory.GetRepositoryInstance<User, UserRepository, UserMappingProfile>(fixture.Context);
        _companyRepository = RepositoryFactory.GetRepositoryInstance<Company, CompanyRepository, CompanyMappingProfile>(fixture.Context);
        _projectRepository = RepositoryFactory.GetRepositoryInstance<Project, ProjectRepository, ProjectMappingProfile>(fixture.Context);
        _contractRepository = RepositoryFactory.GetRepositoryInstance<Contract, ContractRepository>(fixture.Context);
        _substanceRepository = RepositoryFactory.GetRepositoryInstance<Substance, SubstanceRepository, SubstanceMappingProfile>(fixture.Context);

        _userHelper = new UserHelper(_userRepository);
        _companyHelper = new CompanyHelper(_companyRepository);
        _projectHelper = new ProjectHelper(_projectRepository);
        _contractHelper = new ContractHelper(_contractRepository);
        _substanceHelper = new SubstanceHelper(_substanceRepository);
    }

    [Fact, TestPriority(1)]
    public async Task GetActiveCompaniesWithActiveContractsForSubstance_Returns_NoSubstancesForInactiveCompanies()
    {
        // Arrange
        int sequenceNo = 1;
        await AddSubstanceWithNoActiveCompanyToRepository(sequenceNo);

        // Act       
        var companies = (await _companyRepository.GetActiveCompaniesWithActiveContractsForSubstance(sequenceNo)).ToList();

        // Assert
        Assert.Empty(companies);
    }

    [Fact, TestPriority(2)]
    public async Task GetActiveCompaniesWithActiveContractsForSubstance_Returns_NoSubstancesForActiveCompaniesWithDifferentSubstance()
    {
        // Arrange
        int sequenceNo = 2;
        await AddUnrelatedSubstanceWithActiveCompanyToRepository(sequenceNo);

        // Act   
        var companies = (await _companyRepository.GetActiveCompaniesWithActiveContractsForSubstance(99)).ToList();

        // Assert
        Assert.Empty(companies);
    }

    [Fact, TestPriority(3)]
    public async Task GetActiveCompaniesWithActiveContractsForSubstance_Returns_SubstanceForActiveCompaniesActiveContracts()
    {
        // Arrange
        int sequenceNo = 3;
        await AddSubstanceWithActiveContractToRepository(sequenceNo);

        // Act
        var companies = (await _companyRepository.GetActiveCompaniesWithActiveContractsForSubstance(sequenceNo)).ToList();

        // Assert
        Assert.Single(companies);
    }

    [Fact, TestPriority(4)]
    public async Task GetActiveCompaniesWithActiveContractsForSubstance_Returns_NoSubstancesForInctiveContract()
    {
        // Arrange
        int sequenceNo = 4;
        await AddSubstanceWithActiveCompanyInactiveContractToRepository(sequenceNo);

        // Act   
        var companies = (await _companyRepository.GetActiveCompaniesWithActiveContractsForSubstance(sequenceNo)).ToList();

        // Assert
        Assert.Empty(companies);
    }

    private async Task AddSubstanceWithNoActiveCompanyToRepository(int seqNo)
    {
        await SetUpTestRecord(seqNo, false, true);
    }

    private async Task AddUnrelatedSubstanceWithActiveCompanyToRepository(int seqNo)
    {
        await SetUpTestRecord(seqNo, true, true);
    }

    private async Task AddSubstanceWithActiveContractToRepository(int seqNo)
    {
        await SetUpTestRecord(seqNo, true, true);
    }

    private async Task AddSubstanceWithActiveCompanyInactiveContractToRepository(int seqNo)
    {
        await SetUpTestRecord(seqNo, true, false);
    }

    private async Task SetUpTestRecord(int seqNo, bool activeCompany, bool activeProject)
    {
        var user = await _userHelper.AddUser($"User{seqNo}", $"Surname{seqNo}", $"user{seqNo}@email.com");

        var substance = await _substanceHelper.AddSubstance($"Substance {seqNo}", "");

        var company = await _companyHelper.AddCompany($"Company {seqNo}", "", "", activeCompany);

        var project = await _projectHelper.AddProject($"Project {seqNo}", company);

        await _contractHelper.AddContract(user, substance, project, activeProject, 1, 2);
    }

}
