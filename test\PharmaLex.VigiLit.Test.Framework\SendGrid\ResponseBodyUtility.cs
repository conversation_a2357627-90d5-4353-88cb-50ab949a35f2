﻿using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.Test.Framework.SendGrid;

[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public static class ResponseBodyUtility
{
    public static Dictionary<string, dynamic> GetStringDynamicCollection()
    {
        var dictionary = new Dictionary<string, dynamic>()
        {
            {
                "key1", "value1"
            },
            {
                "key2", "value2"
            },
        };

        return dictionary;
    }
}