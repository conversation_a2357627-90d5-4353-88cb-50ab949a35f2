[{"Name": "Basic Profile", "Projects": [{"Path": "src\\PharmaLex.VigiLit.Web\\PharmaLex.VigiLit.Web.csproj", "Action": "Start"}, {"Path": "docker-<PERSON>.dcproj", "Action": "Start"}, {"Path": "src\\PharmaLex.VigiLit.ImportApp\\PharmaLex.VigiLit.ImportApp.csproj", "Action": "Start"}]}, {"Name": "Function App Only", "Projects": [{"Path": "src\\PharmaLex.VigiLit.ImportApp\\PharmaLex.VigiLit.ImportApp.csproj", "Action": "Start"}]}, {"Name": "Debug Data Extraction", "Projects": [{"Path": "tools\\src\\PhlexVisionTestHarness\\PhlexVisionTestHarness.csproj", "Action": "Start"}, {"Path": "docker-<PERSON>.dcproj", "Action": "Start"}]}]