using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Unit.Tests;

public class QualityCheckStatusReportTests
{
    [Theory]
    [InlineData(10, 100, 0, 10)]
    [InlineData(15, 100, 0, 15)]
    [InlineData(20, 100, 0, 20)]
    [InlineData(25, 100, 0, 25)]
    [InlineData(30, 100, 0, 30)]
    [InlineData(35, 100, 0, 35)]
    [InlineData(40, 100, 0, 40)]
    [InlineData(45, 100, 0, 45)]
    [InlineData(50, 100, 0, 50)]
    [InlineData(55, 100, 0, 55)]
    [InlineData(60, 100, 0, 60)]
    [InlineData(65, 100, 0, 65)]
    [InlineData(70, 100, 0, 70)]
    [InlineData(75, 100, 0, 75)]
    [InlineData(80, 100, 0, 80)]
    [InlineData(85, 100, 0, 85)]
    [InlineData(90, 100, 0, 90)]
    [InlineData(95, 100, 0, 95)]
    [InlineData(100, 100, 0, 100)]
    public void ItemsToTake_ObeysPercentage(int percentage, int preClassifiedCount, int classifiedCount, int expectedItemsToTake)
    {
        var report = new QualityCheckStatusReport()
        {
            QCPercentage = percentage,
            PreClassifiedCount = preClassifiedCount,
            ClassifiedCount = classifiedCount
        };

        Assert.Equal(expectedItemsToTake, report.ItemsToTake);
    }

    [Theory]
    [InlineData(10, 100, 0, 10)]
    [InlineData(10, 99, 1, 9)]
    [InlineData(10, 98, 2, 8)]
    [InlineData(10, 97, 3, 7)]
    [InlineData(10, 96, 4, 6)]
    [InlineData(10, 95, 5, 5)]
    [InlineData(10, 94, 6, 4)]
    [InlineData(10, 93, 7, 3)]
    [InlineData(10, 92, 8, 2)]
    [InlineData(10, 91, 9, 1)]
    [InlineData(10, 90, 10, 0)]
    public void ItemsToTake_SubtractsClassified(int percentage, int preClassifiedCount, int classifiedCount, int expectedItemsToTake)
    {
        var report = new QualityCheckStatusReport()
        {
            QCPercentage = percentage,
            PreClassifiedCount = preClassifiedCount,
            ClassifiedCount = classifiedCount
        };

        Assert.Equal(expectedItemsToTake, report.ItemsToTake);
    }

    [Theory]
    [InlineData(100, 13, 0, 13)]
    [InlineData(10, 13, 0, 2)]
    [InlineData(100, 7, 8, 7)]
    [InlineData(50, 7, 8, 0)]
    [InlineData(60, 7, 8, 1)]
    public void ItemsToTake_OtherExamples(int percentage, int preClassifiedCount, int classifiedCount, int expectedItemsToTake)
    {
        var report = new QualityCheckStatusReport()
        {
            QCPercentage = percentage,
            PreClassifiedCount = preClassifiedCount,
            ClassifiedCount = classifiedCount
        };

        Assert.Equal(expectedItemsToTake, report.ItemsToTake);
    }
}