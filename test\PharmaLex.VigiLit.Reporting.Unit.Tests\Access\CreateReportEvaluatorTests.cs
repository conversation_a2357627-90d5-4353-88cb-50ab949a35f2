﻿using Moq;
using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Application.Tests.Fakes;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Access;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.Reporting.Unit.Tests.Access;

public class CreateReportEvaluatorTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new Mock<IUserRepository>();

    private const int UserId = 42;
    private const int CompanyId = 43;
    private readonly Mock<IUserEntity> _mockUser = new Mock<IUserEntity>();

    [Fact]
    public async Task CreateReportEvaluator_When_user_does_not_exists_Throws_UnauthorisationAccessException()
    {
        // Arrange
        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync((User?)null);

        var createReportEvaluator = new CreateReportEvaluator(_mockUserRepository.Object);
        var createReportPermissionContext = new CreateReportPermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await createReportEvaluator.HasPermissions(createReportPermissionContext);
        });
    }

    [Fact]
    public async Task CreateReportEvaluator_When_user_is_not_a_company_user_is_an_internal_user_Returns_true()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "NOT ClientResearcher"));

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var createReportEvaluator = new CreateReportEvaluator(_mockUserRepository.Object);
        var createReportPermissionContext = new CreateReportPermissionContext(UserId, CompanyId);

        // Act
        var result = await createReportEvaluator.HasPermissions(createReportPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CreateReportEvaluator_When_user_is_an_external_user_but_user_has_no_active_company_Throws_UnauthorisationAccessException()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = false;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var createReportEvaluator = new CreateReportEvaluator(_mockUserRepository.Object);
        var createReportPermissionContext = new CreateReportPermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await createReportEvaluator.HasPermissions(createReportPermissionContext);
        });
    }

    [Fact]
    public async Task CreateReportEvaluator_When_user_is_an_external_user_and_company_is_active_but_BelongsToDifferentCompany_Throws_UnauthorisationAccessException()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = true;
        stubbedUser.CompanyUser.CompanyId = 44;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var createReportEvaluator = new CreateReportEvaluator(_mockUserRepository.Object);
        var createReportPermissionContext = new CreateReportPermissionContext(UserId, CompanyId);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await createReportEvaluator.HasPermissions(createReportPermissionContext);
        });
    }

    [Fact]
    public async Task CreateReportEvaluator_When_user_is_a_company_user_and_company_is_active_but_User_BelongsToCompany_Returns_true()
    {

        // Arrange
        var stubbedUser = new FakeUser(11);
        stubbedUser.AddClaim(new FakeClaim(1, "ClientResearcher"));
        stubbedUser.CompanyUser = new FakeCompanyUser();
        stubbedUser.CompanyUser.Active = true;
        stubbedUser.CompanyUser.CompanyId = 43;

        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(stubbedUser);

        var createReportEvaluator = new CreateReportEvaluator(_mockUserRepository.Object);
        var createReportPermissionContext = new CreateReportPermissionContext(UserId, CompanyId);

        // Act
        var result = await createReportEvaluator.HasPermissions(createReportPermissionContext);

        // Assert
        Assert.True(result);
    }
}

