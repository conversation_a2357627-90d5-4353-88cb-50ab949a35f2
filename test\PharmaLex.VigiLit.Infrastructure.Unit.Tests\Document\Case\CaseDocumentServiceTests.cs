﻿using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Document.Case;
using Shouldly;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Document.Case;

public class CaseDocumentServiceTests
{
    private const long CASE_ID = 123;
    private const string FILE_NAME = "Zinc.pdf";
    private readonly CaseDocumentDescriptor _caseDocumentDescriptor;

    private const string ACCOUNT_NAME = "vgtsharedeun";
    private const string CONTAINER_NAME = "case-document";
    private const string BLOB_NAME = "123/Zinc.pdf";
    private readonly DocumentDescriptor _documentDescriptor;

    private readonly IDocumentService _mockDocumentService;

    private readonly ICaseDocumentService caseDocumentService;

    public CaseDocumentServiceTests()
    {
        _caseDocumentDescriptor = new CaseDocumentDescriptor(CASE_ID, FILE_NAME);
        _documentDescriptor = new DocumentDescriptor(ACCOUNT_NAME, CONTAINER_NAME, BLOB_NAME);

        _mockDocumentService = Substitute.For<IDocumentService>();

        var azureStorageCaseDocumentOptions = Substitute.For<IOptions<AzureStorageCaseDocumentOptions>>();
        azureStorageCaseDocumentOptions.Value
            .Returns(new AzureStorageCaseDocumentOptions
            {
                AccountName = ACCOUNT_NAME,
                ContainerName = CONTAINER_NAME
            });

        caseDocumentService = new CaseDocumentService(_mockDocumentService, azureStorageCaseDocumentOptions);
    }

    [Fact]
    public async Task Exists_BlobExists_ReturnsTrue()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptor)
            .Returns(true);

        // Act
        var result = await caseDocumentService.Exists(_caseDocumentDescriptor);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Exists_BlobDoesNotExist_ReturnsFalse()
    {
        // Arrange
        _mockDocumentService
            .Exists(_documentDescriptor)
            .Returns(false);

        // Act
        var result = await caseDocumentService.Exists(_caseDocumentDescriptor);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task OpenRead_BlobExists_ReturnsStream()
    {
        // Arrange
        var stream = new MemoryStream();
        _mockDocumentService
            .OpenRead(_documentDescriptor)
            .Returns(stream);

        // Act
        var result = await caseDocumentService.OpenRead(_caseDocumentDescriptor);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(stream);
    }

    [Fact]
    public async Task Delete_BlobExists_CallsDelete()
    {
        // Act
        await caseDocumentService.Delete(_caseDocumentDescriptor);

        // Assert
        await _mockDocumentService
            .ReceivedWithAnyArgs(1)
            .Delete(default!);

        await _mockDocumentService
            .Received(1)
            .Delete(_documentDescriptor);
    }
}