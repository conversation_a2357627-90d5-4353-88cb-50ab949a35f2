﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class MovePvSafetyDatabaseId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PvSafetyDatabaseId",
                table: "Cases");

            migrationBuilder.AddColumn<string>(
                name: "PvSafetyDatabaseId",
                table: "ReferenceClassifications",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PvSafetyDatabaseId",
                table: "ReferenceClassifications")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferenceClassificationsHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);

            migrationBuilder.AddColumn<string>(
                name: "PvSafetyDatabaseId",
                table: "Cases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }
    }
}
