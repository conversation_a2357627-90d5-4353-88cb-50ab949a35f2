﻿using Microsoft.Data.SqlClient;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace PharmaLex.VigiLit.Test.Framework.Exceptions;

/// <summary>
/// A workaround for creating a SqlException because it has no constructors.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Testing Framework")]
public static class SqlExceptionFactory
{
    public static SqlException CreateSqlException(int errorNumber, string message)
    {
        var ex = (SqlException)RuntimeHelpers.GetUninitializedObject(typeof(SqlException)); 
        
        var errors = GenerateSqlErrorCollection(errorNumber, message); 
        
        SetPrivateFieldValue(ex, "_errors", errors);
        SetPrivateFieldValue(ex, "_message", message);

        return ex;
    }

    /// <summary>
    /// SqlException has a collection of SqlErrors. SqlException.Number comes from the first SqlError.
    /// </summary>
    private static SqlErrorCollection GenerateSqlErrorCollection(int errorNumber, string message)
    {
        var t = typeof(SqlErrorCollection);
        var col = (SqlErrorCollection)RuntimeHelpers.GetUninitializedObject(t);
        SetPrivateFieldValue(col, "_errors", new List<object>());
        var sqlError = GenerateSqlError(errorNumber, message);
        var method = t.GetMethod("Add", BindingFlags.NonPublic | BindingFlags.Instance);
        method?.Invoke(col, new object[] { sqlError });
        return col;
    }

    private static SqlError GenerateSqlError(int errorNumber, string message)
    {
        var sqlError = (SqlError)RuntimeHelpers.GetUninitializedObject(typeof(SqlError));
        SetPrivateFieldValue(sqlError, "_number", errorNumber);
        if (!string.IsNullOrEmpty(message)) SetPrivateFieldValue(sqlError, "_message", message);
        return sqlError;
    }

    private static void SetPrivateFieldValue(object obj, string field, object val)
    {
        var member = obj.GetType().GetField(field, BindingFlags.NonPublic | BindingFlags.Instance);
        member?.SetValue(obj, val);
    }
}