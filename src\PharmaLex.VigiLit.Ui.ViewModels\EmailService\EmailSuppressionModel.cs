﻿using PharmaLex.VigiLit.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Ui.ViewModels.EmailService;

public class EmailSuppressionModel
{
    public EmailSuppressionType EmailSuppressionType { get; set; }

    public long Created { get; set; }

    [MaxLength(2000)]
    public string Reason { get; set; }

    public string CreatedDisplayDate =>
            DateTime.UnixEpoch.AddMilliseconds(Created * 1000).ToString("dd MMM yyyy HH:mm");
}
