﻿namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;

public class ArticleTitle
{
    private object[] itemsField;

    private string[] textField;

    private string bookField;

    private string partField;

    private string secField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("b", typeof(b))]
    [System.Xml.Serialization.XmlElementAttribute("i", typeof(i))]
    [System.Xml.Serialization.XmlElementAttribute("sub", typeof(sub))]
    [System.Xml.Serialization.XmlElementAttribute("sup", typeof(sup))]
    [System.Xml.Serialization.XmlElementAttribute("u", typeof(u))]
    public object[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string book
    {
        get
        {
            return this.bookField;
        }
        set
        {
            this.bookField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string part
    {
        get
        {
            return this.partField;
        }
        set
        {
            this.partField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string sec
    {
        get
        {
            return this.secField;
        }
        set
        {
            this.secField = value;
        }
    }
}
