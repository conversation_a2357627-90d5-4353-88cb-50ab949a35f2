﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class ClientReportsSetup : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spTrackingSheetReport.sql");
            migrationBuilder.SqlFileExec("spCaseFileReport.sql");
            migrationBuilder.SqlFileExec("spApogephaClassificationReport.sql");
            migrationBuilder.SqlFileExec("spMerzClassificationReport.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Method intentionally left empty.
        }
    }
}
