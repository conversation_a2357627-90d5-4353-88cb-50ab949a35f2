﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class ClassificationServiceTests
{
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IVigiLitUserContext> _mockUserContext = new();

    private readonly Mock<IImportContractReferenceClassificationRepository>
        _mockImportContractReferenceClassificationRepository = new();

    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<ISubstanceRepository> _mockSubstanceRepository = new();
    private readonly Mock<IImportRepository> _mockImportRepository = new();
    private readonly Mock<IReferenceHistoryActionRepository> _mockReferenceHistoryActionRepository = new();
    private readonly Mock<ICompanyInterestRepository> _mockCompanyInterestRepository = new();

    private readonly IClassificationService _classificationService;

    private const int UserId = 99;

    public ClassificationServiceTests()
    {
        _classificationService = new ClassificationService(
            _mockMapper.Object,
            _mockUserContext.Object,
            _mockImportContractReferenceClassificationRepository.Object,
            _mockReferenceClassificationRepository.Object,
            _mockReferenceRepository.Object,
            _mockSubstanceRepository.Object,
            _mockImportRepository.Object,
            _mockReferenceHistoryActionRepository.Object,
            _mockCompanyInterestRepository.Object);

        _mockUserContext.Setup(x => x.UserId).Returns(UserId);
    }

    [Fact]
    public async Task GetByIdAsync_Returns_CorrectReference_When_ReferenceFound()
    {
        // Arrange
        FakeReferenceClassification classification = new(123);
        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(123)).ReturnsAsync(classification);

        // Act
        var result = await _classificationService.GetByIdAsync(123);

        // Assert
        Assert.Equal(123, result.Id);
    }
}