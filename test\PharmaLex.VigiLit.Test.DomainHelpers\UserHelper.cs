﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class UserHelper
{
    private readonly IUserRepository _userRepository;

    public UserHelper(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<User> AddUser(string firstName, string surname, string email)
    {
        var user = new User(firstName, surname, email);
        _userRepository.Add(user);
        await _userRepository.SaveChangesAsync();
        return user;
    }
}
