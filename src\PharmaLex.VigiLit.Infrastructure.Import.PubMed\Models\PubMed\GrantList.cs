using System.Xml.Serialization;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;
public class GrantList
{
    [XmlElement("Grant")]
    public List<Grant> Grant { get; set; } = new List<Grant>();

    [XmlAttribute()]
    [System.ComponentModel.DefaultValue(GrantListCompleteYN.Y)]
    public GrantListCompleteYN CompleteYN { get; set; } = GrantListCompleteYN.Y;
}