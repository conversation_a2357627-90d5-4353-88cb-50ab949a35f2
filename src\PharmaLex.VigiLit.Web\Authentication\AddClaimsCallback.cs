﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using System.Security.Claims;
using System.Threading.Tasks;
using Claim = System.Security.Claims.Claim;

namespace PharmaLex.VigiLit.Web.Authentication;

public class AddClaimsCallback : ITicketReceivedCallback
{
    public async Task OnTicketReceived(TicketReceivedContext context)
    {
        ClaimsIdentity ci = context.Principal.Identity as ClaimsIdentity;

        var userRepository = context.HttpContext.RequestServices.GetService<IUserRepository>();
        var user = await userRepository.GetByEmail(ci.GetEmail());
        if (user == null || user.GetClaims().Count == 0)
        {

            context.Response.Redirect("/unauthorised");
            context.HandleResponse();
        }
        else
        {
            foreach (Domain.UserManagement.Claim uc in user.GetClaims())
            {
                ci.AddClaim(new Claim($"{uc.ClaimType}:{uc.Name}", uc.Id.ToString()));
            }
            ci.AddClaim(new Claim("plx:userid", user.Id.ToString()));
        }

        await userRepository.SaveChangesAsync();

        if (user != null)
        {
            var userSessionService = context.HttpContext.RequestServices.GetService<IUserSessionService>();
            await userSessionService.InitialiseUserSession(user.Id, context.HttpContext.Connection.RemoteIpAddress?.ToString());
        }
    }
}