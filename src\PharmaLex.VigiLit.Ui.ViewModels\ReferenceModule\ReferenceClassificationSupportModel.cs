﻿namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceClassificationSupportModel
{
    public string SourceId { get; set; }
    public string Doi { get; set; }
    public int Id { get; set; }
    public string Title { get; set; }
    public string Abstract { get; set; }
    public string Authors { get; set; }
    public string MeshTerms { get; set; }
    public string Keywords { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }
    public string FullPagination { get; set; }
    public string Issn { get; set; }
    public string Issue { get; set; }
    public string Volume { get; set; }
    public string PublicationType { get; set; }
    public string JournalTitle { get; set; }
    public ushort? PublicationYear { get; set; }
    public string Substance { get; set; }
    public string ClassificationCategory { get; set; }
    public string MinimalCriteria { get; set; }
    public string CountryOfOccurrence { get; set; }
    public string PSURRelevanceAbstract { get; set; }
    public string DosageForm { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
}
