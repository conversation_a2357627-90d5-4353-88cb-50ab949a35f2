﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddCaseFileEmailPreference : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("SET IDENTITY_INSERT [dbo].[EmailPreferences] ON; " +

                                 "INSERT INTO [dbo].[EmailPreferences] ( [Id], [Name] ,[DisplayName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 2, 'DailyCaseFiles', 'Case Files', GETUTCDATE(),'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[EmailPreferences] WHERE [Id] = 2); " +

                                 "SET IDENTITY_INSERT [dbo].[EmailPreferences] OFF; ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM [EmailPreferences] WHERE Id = 2;");
        }
    }
}
