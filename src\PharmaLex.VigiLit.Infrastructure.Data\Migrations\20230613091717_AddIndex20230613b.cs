﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndex20230613b : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_CompanyInterests_CompanyId_Id",
                table: "CompanyInterests",
                columns: new[] { "CompanyId", "Id" })
                .Annotation("SqlServer:Include", new[] { "ReferenceId", "ReferenceClassificationId", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CompanyInterests_CompanyId_Id",
                table: "CompanyInterests");
        }
    }
}
