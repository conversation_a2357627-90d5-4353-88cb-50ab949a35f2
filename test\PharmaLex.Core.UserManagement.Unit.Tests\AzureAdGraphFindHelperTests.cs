using AutoMapper;
using Azure.Identity;
using Moq;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserManagement.Mappers;
using PharmaLex.Core.UserManagement.Unit.Tests.Fake;
using PharmaLex.Core.UserManagement.Users;


namespace PharmaLex.Core.UserManagement.Unit.Tests;

public class AzureAdGraphFindHelperTests
{
    private readonly Mock<IUserRepository<FakeUser>> _mockRepository = new();
    private readonly Mock<IAzureAdGraphService> _mockAzureAdGraphService = new();

    private readonly IAzureAdGraphFindHelper _azureAdGraphFindHelper;

    public AzureAdGraphFindHelperTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<UserEntityMapper<FakeUser>>();
            cfg.AddProfile<GraphUserMapper>();
        });
        var mapper = config.CreateMapper();

        _azureAdGraphFindHelper = new AzureAdGraphFindHelper<FakeUser>(_mockRepository.Object, _mockAzureAdGraphService.Object, mapper);
    }

    [Fact]
    public async Task Find_ReturnsNotNull()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(new List<UserFindResult>());

        var adUsers = new List<Microsoft.Graph.Models.User>();
        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>())).ReturnsAsync(adUsers);

        // Act
        var result = await _azureAdGraphFindHelper.Find("");

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task Find_when_call_to_AD_exceptions_Throws_exception()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(new List<UserFindResult>());

        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>()))
            .ThrowsAsync(new AuthenticationFailedException("test"));

        // Act
        Task Result() => _azureAdGraphFindHelper.Find("");

        // Assert
        await Assert.ThrowsAsync<AuthenticationFailedException>(Result);
    }

    [Fact]
    public async Task Find_NotFound_ReturnsEmpty()
    {
        // Arrange
        var dbUsers = new List<UserFindResult>()
        {
            new UserFindResult()
            {
                Email = "<EMAIL>",
                Value = "<EMAIL>",
                FamilyName = "Mouse",
                GivenName = "Mickey"
            },
        };

        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(dbUsers);

        var adUsers = new List<Microsoft.Graph.Models.User>();
        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>())).ReturnsAsync(adUsers);

        // Act
        var result = await _azureAdGraphFindHelper.Find("donald");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Find_NotInAd_ReturnsEmpty()
    {
        // Arrange
        var dbUsers = new List<UserFindResult>()
        {
            new UserFindResult()
            {
                Email = "<EMAIL>",
                Value = "<EMAIL>",
                FamilyName = "Mouse",
                GivenName = "Mickey"
            },
        };

        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(dbUsers);

        var adUsers = new List<Microsoft.Graph.Models.User>();
        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>())).ReturnsAsync(adUsers);

        // Act
        var result = await _azureAdGraphFindHelper.Find("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Find_InAdNotInDatabase_ReturnsUser()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(new List<UserFindResult>());

        var adUsers = new List<Microsoft.Graph.Models.User>()
        {
            new Microsoft.Graph.Models.User
            {
                UserPrincipalName = "<EMAIL>",
                Surname = "Duck",
                GivenName = "Donald"
            },
        };

        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>())).ReturnsAsync(adUsers);

        // Act
        var result = await _azureAdGraphFindHelper.Find("");

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.Equal("Donald", result.First().GivenName);
        Assert.Equal("Duck", result.First().FamilyName);
        Assert.Equal("<EMAIL>", result.First().Email);
    }

    [Fact]
    public async Task Find_FoundInDatabaseAndIsInAD_ReturnsUser()
    {
        // Arrange
        var dbUsers = new List<UserFindResult>()
        {
            new UserFindResult()
            {
                Email = "<EMAIL>",
                Value = "<EMAIL>",
                FamilyName = "Duck",
                GivenName = "Donald"
            },
        };

        _mockRepository.Setup(x => x.GetAllUsers(false)).ReturnsAsync(dbUsers);

        var adUsers = new List<Microsoft.Graph.Models.User>()
        {
            new Microsoft.Graph.Models.User
            {
                UserPrincipalName = "<EMAIL>",
                Surname = "Duck",
                GivenName = "Donald"
            },
        };

        _mockAzureAdGraphService.Setup(x => x.FindUsers(It.IsAny<string>())).ReturnsAsync(adUsers);

        // Act
        var result = await _azureAdGraphFindHelper.Find("");

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.Equal("Donald", result.First().GivenName);
        Assert.Equal("Duck", result.First().FamilyName);
        Assert.Equal("<EMAIL>", result.First().Email);
    }
}