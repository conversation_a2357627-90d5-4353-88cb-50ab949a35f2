﻿@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.Enums;
@using PharmaLex.VigiLit.Web.Helpers;
@model PharmaLex.VigiLit.Ui.ViewModels.CaseManagement.CaseResponse

@Html.AntiForgeryToken()

<div class="case-upload-form" id="case-upload-form" data-case-id="@Model.CaseId" data-upload-id="@Model.UploadId">
    <div class="case-upload-header">
        <h2 id="CaseHeader" class="brand-color">Upload Files</h2>
        <span id="CaseStatus" class="case-status">New</span>
    </div>

    <div class="case-upload-content">
        <div class="case-upload-form-left">
            <div class="case-upload-plxid-wrapper">
                <label class="case-upload-label" for="plxIdInput">PLX ID (required)</label>
                <input class="case-upload-input" id="plxIdInput" name="PlxId" type="number" value="@Model.PlxId" required />
            </div>

            <div class="case-upload-substance-wrapper">
                <div class="case-upload-divider first-substance-divider"></div>
                <label class="case-upload-label" for="selectedSubstanceLabel">Substance</label>
                <label class="case-upload-label" id="selectedSubstanceLabel">@Model.SubstanceName</label>
                <div class="case-upload-divider last-substance-divider"></div>
            </div>
            <div class="case-upload-company-dropdown-wrapper">
                <label class="case-upload-label">Companies (required)</label>
                <label id="CompanyLabelNoPlxId">Please enter the PLX ID.</label>
                <label id="CompanyLabelInvalidPlxId" style="display: none;">Please enter a valid PLX ID.</label>
                <label id="CompanyLabelNoCompany" style="display: none;">No Companies available for entered PLX ID.</label>
                <div id="MultiSelectContainer" class="companies-multi-select hidden">
                    <div id="CompanyMultiSelect" class="select-box">
                        <label id="SelectedPreviewLabel" class="selected-label">Select companies...</label>
                        <div id="SelectedCount" class="selected-count">0</div>
                    </div>
                    <div id="CompanyCheckBoxes" class="check-box hidden"></div>
                </div>
            </div>
            <div class="case-upload-dropdown-group">
                <div class="case-upload-psur-dropdown-wrapper">
                    <label class="case-upload-label" for="PSUR">PSUR (required)</label>
                    <div class="custom-select small">
                        <select class="case-upload-input" asp-for="PSUR" asp-items="Html.GetEnumSelectList<PSURRelevanceAbstract>().Where(p => p.Value != '0'.ToString())"></select>
                    </div>
                </div>

                <div class="case-upload-mlm-dropdown-wrapper">
                    <label class="case-upload-label" for="MLM">MLM Duplicate</label>
                    <div class="custom-select small">
                        <select class="case-upload-input" asp-for="MLM">
                            @{
                                foreach (CaseMLMDuplicate mlmDuplicate in Enum.GetValues(typeof(CaseMLMDuplicate)))
                                {
                                    <option value="@mlmDuplicate">@(mlmDuplicate.GetDescription())</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="case-upload-pv-safety-id-wrapper">
                <label class="case-upload-label" for="pvSafetyIdInput">PV Safety Database ID</label>
                <input class="case-upload-input" id="pvSafetyIdInput" type="text" value="@Model.PvSafetyDatabaseId" />
            </div>

            <div class="case-upload-comment-wrapper">
                <label class="case-upload-label" for="caseCommentInput">Comment</label>
                <textarea id="caseCommentInput" type="text" maxlength="2500">@Model.Comment</textarea>
            </div>
        </div>
        <div class="case-upload-form-right">
            <div id="file-drop-area" class="file-drop-area">
                <div id="fileDropMask" class="file-drop-mask" />
                <div id="file-drop-text">
                    <label id="file-drop-msg" class="file-drop-msg file-count disabled">Click to upload or drag and drop</label>
                    <label id="file-drop-msg-sub" class="file-drop-msg-sub disabled">Max file size: 10 MB</label>
                </div>
                <input hidden disabled class="file-drop-input" id="fileInput" type="file" aria-label="File drop area" multiple>
                <div hidden id="file-drop-loading-wrapper" class="file-drop-loading-wrapper">
                    <div class="file-drop-loading-ball1"></div>
                    <div class="file-drop-loading-ball2"></div>
                    <div class="file-drop-loading-ball3"></div>
                    <div class="file-drop-loading-ball4"></div>
                </div>
            </div>
            <div class="case-upload-file-list">
                <ul id="uploadedFilenames">
                    @if (Model.UploadedFiles != null)
                    {
                        foreach (var uploadedFile in Model.UploadedFiles)
                        {
                            var fileSizeText = uploadedFile.FileSize == 0 ? string.Empty : $"({Math.Round(uploadedFile.FileSize / 1024.0 / 1024.0, 2)} MB)";
                            <li class="case-upload-file-item">
                                <div>@uploadedFile.FileName @fileSizeText</div>
                                <div class="case-upload-file-options" data-case-file-id="@uploadedFile.CaseFileId" data-case-file-name="@uploadedFile.FileName">
                                    <div class="case-upload-file-download">download</div>
                                    <div class="case-upload-file-delete">delete</div>
                                </div>
                            </li>
                        }
                    }
                </ul>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="button secondary icon-button-cancel btn-default" data-dismiss="modal">Discard</button>
        <button type="button" id="saveCaseBtn" class="btn btn-default btn-spinner"><span class="btn-label btn-span">Save</span> <span class="spinner btn-span"></span></button>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(async function () {
        const model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        const allowedFileTypes = await getAllowedFileTypesAsync();
        let uploadedDocumentFilenames = [];
        const deletedDocumentFileIds = [];
        var isEditMode = false; // "false" = Add, "true" = Edit
        var isLoading = false;
        var multiSelectContainer = $('#MultiSelectContainer');
        var multiSelectPreview = $('#SelectedPreviewLabel');
        var multiSelectCount = $('#SelectedCount');
        var multiSelectCheckboxes = $('#CompanyCheckBoxes');
        var multiSelectExpanded = false;
        var companies = [];
        var selectedCompanies = [];
        var lastPlxId = 0;
        
        addPageListener();
        loadCaseIfEditMode();

        function toggleMultiSelectDropdown(isOpen) {
            if (isLoading) {
                isOpen = false;
            }
            multiSelectExpanded = (isOpen === undefined) ? !multiSelectExpanded : isOpen;
            if (multiSelectExpanded) {
                multiSelectCheckboxes.removeClass('hidden');
                multiSelectContainer.addClass('active');
            } else {
                multiSelectCheckboxes.addClass('hidden');
                multiSelectContainer.removeClass('active');
            }
        }

        async function getAllowedFileTypesAsync() {
            return await fetch("/Cases/File/Types", {
                method: "GET",
                credentials: 'same-origin',
                headers: {
                    "RequestVerificationToken": token,
                }
            }).then(async (response) => {
                var fileTypesObject = JSON.parse(await response.text());
                return fileTypesObject;
            }).catch((errorResponse) => {
                return [];
            });
        }

        $('#CompanyMultiSelect').click(function (event) {
            toggleMultiSelectDropdown();
        });

        var captureDropFunc = function (e) {
            e = e || event;
            e.preventDefault();
        };

        function disableFileDrop() {
            $("#fileDropMask").show();
            $('#file-drop-area').addClass('disabled');
            $('#file-drop-msg').addClass('disabled');
            $('#file-drop-msg-sub').addClass('disabled');
            window.addEventListener("dragover", captureDropFunc, false);
            window.addEventListener("drop", captureDropFunc, false);
        }
        disableFileDrop();

        function enableFileDrop() {
            $("#fileDropMask").hide();
            $('#file-drop-area').removeClass('disabled');
            $('#file-drop-msg').removeClass('disabled');
            $('#file-drop-msg-sub').removeClass('disabled');

            window.removeEventListener("dragover", captureDropFunc);
            window.removeEventListener("drop", captureDropFunc);
        }

        function getClassification() {
            var companiesLabelNoPlxId = $('#CompanyLabelNoPlxId');
            var companiesLabelInvalidPlxId = $('#CompanyLabelInvalidPlxId');
            var companiesLabelNoCompany = $('#CompanyLabelNoCompany');
            companiesLabelNoPlxId.hide();
            companiesLabelInvalidPlxId.hide();
            companiesLabelNoCompany.hide();

            var id = $("#plxIdInput").val().trim();
            if (!id) {
                disableFileDrop();
                companiesLabelNoPlxId.show();
                changeMultiSelectVisibility(false);
                return;
            }

            $.ajax({
                url: "/Cases/Classification",
                data: { plxId: id },
                type: "GET",
                success: function (result) {
                    $("#selectedSubstanceLabel").text(result.substanceName);
                    enableFileDrop();
                    $("#fileInput").show();
                    $('#case-upload-form').find('input, select, button').prop('disabled', false);
                    $("#PSUR").val(result.psur);
                    $('#pvSafetyIdInput').val(result.pvSafetyDatabaseId);
                    if (result.companies.length > 0) {
                        companies = result.companies;
                        createMultiSelectItems();
                        changeMultiSelectVisibility(true);
                    }
                    else {
                        changeMultiSelectVisibility(false);
                        companiesLabelNoCompany.show();
                    }
                    lastPlxId = id;
                },
                error: function (result) {
                    if (result.status == 400 || result.status == 404) {
                        plx.toast.show(result.responseText, 2, 'failed', null, 2500);
                        companiesLabelInvalidPlxId.show();
                    }
                    else {
                        plx.toast.show('An error occurred while retrieving the classification.', 2, 'failed', null, 5000);
                    }
                    $("#selectedSubstanceLabel").empty();
                    changeMultiSelectVisibility(false);
                    disableFileDrop();
                    lastPlxId = 0;
                },
                complete: function () {
                    updateMultiSelectPreview();
                    if (isEditMode) {
                        loadCase();
                    }
                }
            });
        };

        $('#plxIdInput').keydown(function (event) {
            if (event.key === 'Enter') {
                getClassification();
            }
        });

        $('#plxIdInput').blur(function () {
            getClassification();
        });

        function downloadFile() {
            var caseFileId = $(this).parent().attr('data-case-file-id');
            var caseFileName = $(this).parent().attr('data-case-file-name');
            if (caseFileId !== undefined) {
                var caseId = $("#case-upload-form").attr('data-case-id');
                download(`/Cases/File?caseId=${caseId}&caseFileId=${caseFileId}`, this, caseFileName);
            }
            else {
                var uploadId = $("#case-upload-form").attr('data-upload-id');
                download(`/Cases/File/Temporary?uploadId=${uploadId}&caseFileName=${caseFileName}`, this, caseFileName);
            }
        }

        $('#case-upload-form .case-upload-file-options div.case-upload-file-download').click(downloadFile);

        function download(downloadUrl, button, fileName) {
            $(button).text('downloading');
            plx.toast.show(`Started downloading ${fileName}, please wait...`, 2, 'confirm', null, 2500);

            var onFailure = function () {
                plx.toast.show('Unable to reach the file, please try again', 2, 'failed', null, 2500);
            }
            var onComplete = function () {
                $(button).text('download');
            }
            DownloadFile.fromUrl(downloadUrl, null, null, onFailure, onComplete);

        }

        function deleteFile() {
            var caseFileId = $(this).parent().attr('data-case-file-id');
            if (caseFileId !== undefined) {
                deletedDocumentFileIds.push(caseFileId);
            }
            var caseFileName = $(this).parent().attr('data-case-file-name');
            uploadedDocumentFilenames = uploadedDocumentFilenames.filter((uploadedDocumentFilename) => uploadedDocumentFilename !== caseFileName);
            $(this).parent().parent().remove();
        }

        $('#case-upload-form .case-upload-file-options div.case-upload-file-delete').click(deleteFile);

        $("#saveCaseBtn").click(function (event) {
            var caseRecord = {
                __RequestVerificationToken: token,
                CaseId: $("#case-upload-form").attr('data-case-id'),
                PlxId: parseInt($("#plxIdInput").val()),
                Companies: selectedCompanies,
                MLM: $("#MLM option:selected").val(),
                PSUR: $("#PSUR option:selected").val(),
                PvSafetyDatabaseId: $('#pvSafetyIdInput').val(),
                Comment: $('#caseCommentInput').val(),
                UploadId: $("#case-upload-form").attr('data-upload-id'),
                UploadedFileNames: uploadedDocumentFilenames,
                DeletedFileIds: deletedDocumentFileIds
            };
            var validationText = validateCaseModel(caseRecord);
            if (validationText) {
                plx.toast.show(validationText, 2, 'failed', null, 5000);
                return;
            }
            changeIsLoadingForControls(true);
            toggleMultiSelectDropdown(false);
            $("#saveCaseBtn").attr('data-loading', '');
            event.preventDefault();
            $("#fileInput").hide();
            $.ajax({
                url: "/Cases/Case",
                data: caseRecord,
                type: "POST",
                success: function (result) {
                    $("#caseRecordEditModal").modal("hide");
                    if ($('#refreshCaseTable')) {
                        $('#refreshCaseTable').click();
                    }
                },
                error: function (result) {
                    if (result.status == 400) {
                        plx.toast.show(result.responseText, 2, 'failed', null, 2500);
                    }
                    else {
                        plx.toast.show('An error occurred while saving the case.', 2, 'failed', null, 5000);
                    }
                    $("#fileInput").show();
                },
                complete: function () {
                    $("#saveCaseBtn").removeAttr('data-loading');
                    changeIsLoadingForControls(false);
                }
            });
        });

        // highlight file drop area
        var fileinput = document.querySelector('.file-drop-input');
        var filedroparea = document.querySelector('.file-drop-area');
        fileinput.addEventListener('dragenter', dropAreaActivate);
        fileinput.addEventListener('focus', dropAreaActivate);
        fileinput.addEventListener('click', dropAreaActivate);

        // back to normal state file drop area
        fileinput.addEventListener('dragleave', dropAreaDeactivate);
        fileinput.addEventListener('blur', dropAreaDeactivate);
        fileinput.addEventListener('drop', dropAreaDeactivate);

        // add active class to file drop area
        function dropAreaActivate() {
            filedroparea.classList.add('is-active');
        }

        // remove active class to file drop area
        function dropAreaDeactivate() {
            filedroparea.classList.remove('is-active');
        }

        function getSizeMbFromBytes(fileSize, decimalPlaces = 2) {
            return (fileSize / (1024 * 1024)).toFixed(decimalPlaces);
        }
        function validateFileNameContainsPlxId(fileName, plxId) {
            var id = plxId.toString();
            var fileParts = fileName.split('.');
            fileParts.pop();
            fileNameWithoutExtension = fileParts.join('.');
            var fileNameUnified = fileNameWithoutExtension.replaceAll('-', '_');

            return fileNameUnified === id
                || fileNameUnified.startsWith(`${id}_`)
                || fileNameUnified.endsWith(`_${id}`)
                || fileNameUnified.includes(`_${id}_`);
        }

        function appendFileToUploadedList(fileName, fileSize) {
            var uploadedFile = $("<div>")
                .text(`${fileName} (${getSizeMbFromBytes(fileSize)} MB)`);

            var downloadOption = $("<div>")
                .addClass("case-upload-file-download")
                .text("download")
                .click(downloadFile);

            var deleteOption = $("<div>")
                .addClass("case-upload-file-delete")
                .text("delete")
                .click(deleteFile);

            var fileOptions = $("<div>")
                .addClass("case-upload-file-options")
                .attr("data-case-file-name", fileName);

            downloadOption.appendTo(fileOptions);
            deleteOption.appendTo(fileOptions);

            var listItem = $("<li>")
                .addClass("case-upload-file-item")

            uploadedFile.appendTo(listItem);
            fileOptions.appendTo(listItem);

            var uploadedFilesContainer = $("#uploadedFilenames");

            listItem.appendTo(uploadedFilesContainer);
        }

        fileinput.addEventListener('change', async function () {
            event.preventDefault();
            var plxId = parseInt($("#plxIdInput").val());

            // Check case file has been dropped
            var files = $('#fileInput')[0].files;
            if (!files || !files.length > 0) {
                plx.toast.show("Please select a case file to upload.", 2, 'failed', null, 5000);
                return;
            }
            
            // Check if all file properties, such as type, size and name are valid.
            for (const file of files) {
                var validationResultText = validateFileProperties(file.name, file.size, plxId);

                if (validationResultText) {
                    $('#fileInput').val('');
                    plx.toast.show(validationResultText, 2, 'failed', null, 5000);
                    return;
                }
            }

            // Hide and disable some elements, display loading dots
            $("#plxIdInput").attr("disabled", true);
            $("#fileInput").hide();
            $('#file-drop-text').hide();
            $('#file-drop-loading-wrapper').show();
            $('.file-drop-area').addClass('dropping');
            $('.modal-body').addClass('dropping');

            // Save
            var uploadId = $("#case-upload-form").attr('data-upload-id');
            fileFormRequests = [];

            for (const file of files) {
                fileFormRequests.push(temporaryFileFormRequest(uploadId, plxId, file));
            }
            var responses = await Promise.allSettled(fileFormRequests);
            var statusCodes = responses.map(p => p.value.status);
            var erroredResponses = responses.filter(p => p.value.status != 200);
            var errorMessages = (await Promise.all(erroredResponses.map(p => p.value.text())));

            if (erroredResponses.length <= 0) {
                for (const file of files) {
                    uploadedDocumentFilenames.push(file.name);
                    appendFileToUploadedList(file.name, file.size);
                }
                plx.toast.show(`Case file${files.length === 0 ? ' has' : 's have'} been uploaded successfully.`, 2, 'confirm', null, 2500);
            } else {
                if (errorMessages.length > 0) {
                    plx.toast.show(errorMessages[0], 2, 'failed', null, 2500);
                }
                else {
                    plx.toast.show('An error occurred while uploading the case file(s).', 2, 'failed', null, 5000);
                }
                if (uploadedDocumentFilenames.length === 0 && !isEditMode) {
                    $("#plxIdInput").attr("disabled", false);
                }
            }
            $('#fileInput').val('');
            $("#fileInput").show();
            $('#file-drop-loading-wrapper').hide();
            $('#file-drop-text').show();
            $('.file-drop-area').removeClass('dropping');
            $('.modal-body').removeClass('dropping');
        });

        async function temporaryFileFormRequest(uploadId, plxId, file) {
            var formData = new FormData();
            formData.append("uploadId", uploadId);
            formData.append("plxId", plxId);
            formData.append("caseFile", file);

            return await fetch("/Cases/File/Temporary", {
                method: "POST",
                credentials: 'same-origin',
                body: formData,
                headers: {
                    "RequestVerificationToken": token,
                }
            });
        }

        // Toggle (or specify) visibility of the entire multi-select container
        function changeMultiSelectVisibility(isVisible) {
            if (isVisible) {
                multiSelectContainer.removeClass('hidden');
            } else {
                multiSelectContainer.addClass('hidden');
            }
        }

        // Create multi-select drop-down items for all companies
        function createMultiSelectItems(shouldWipeSelected = true) {
            selectedCompanies = shouldWipeSelected ? [] : selectedCompanies;
            multiSelectCheckboxes.empty();
            for (var i = 0; i < companies.length; i++) {
                var company = companies[i];
                createMultiSelectItem(multiSelectCheckboxes, company);
            }
        }

        // Add multi-select item for a company to the dropdown
        function createMultiSelectItem(parent, company) {
            var checked = model.selectedCompanyIds.includes(company.id) ? " checked" : "";
            parent.append($(`
                <div class="companies-multi-select-item">
                    <label class="formatLabel"><input type="checkbox" id="${company.id}"${checked}></label>
                    <label class="multiSelectItemLabel" for="${company.id}">${company.name}</label>
                </div>
                `)); // Extra label surrounding checkbox is for improved click registration.
            var itemInput = document.getElementById(`${company.id}`);
            if (itemInput) {
                itemInput.onchange = function () {
                    multiSelectItemChanged(this, company.id);
                };
            }
        }

        // When checking or unchecking an item, this is called
        function multiSelectItemChanged(inputElement, idToggled) {
            if (companies === undefined || companies.length < 1) {
                disableFileDrop();
                return;
            }
            if (inputElement.checked) {
                selectedCompanies.push(companies.find(c => c.id == idToggled));
            } else {
                selectedCompanies = selectedCompanies.filter(c => c.id != idToggled);
            }
            updateMultiSelectPreview();
        }

        // Updates the text showing a preview e.g. "Company A, Company B, Comp..." along with the number of selected items
        function updateMultiSelectPreview() {
            var previewText = '';
            const defaultText = 'Select companies...';
            if (selectedCompanies.length == 0) {
                previewText = defaultText;
            } else {
                var companyStrings = selectedCompanies.map(function (c) {
                    return c['name'];
                });
                previewText = companyStrings.join(', ');
            }
            multiSelectPreview.empty();
            multiSelectPreview.append(previewText);
            multiSelectCount.empty();
            multiSelectCount.append(selectedCompanies.length)
        }

        // Listens for when the user has clicked off of the multi select container, so it can close.
        function addPageListener() {
            $(document).click(function (event) {
                var $target = $(event.target);
                if ($target.closest('#MultiSelectContainer').length <= 0) {
                    toggleMultiSelectDropdown(false);
                }
            });
        }

        // Disables controls and marks as loading while saving a case.
        function changeIsLoadingForControls(loading) {
            isLoading = loading;
            if (isLoading) {
                disableFileDrop();
                $('#case-upload-form').find('input, select, button').prop('disabled', true);
                $('#caseCommentInput').prop('disabled', true);
                $('#case-upload-form .case-upload-file-options div').addClass("disabled");
                multiSelectContainer.addClass('disabled');
            } else {
                enableFileDrop();
                $('#case-upload-form').find('input, select, button').prop('disabled', false);
                $('#caseCommentInput').prop('disabled', false);
                multiSelectContainer.removeClass('disabled');
                $('#case-upload-form .case-upload-file-options div').removeClass("disabled");
            }
        }

        // Checks if there is an existing model and PLX ID to edit and enters edit mode.
        function loadCaseIfEditMode() {
            if (!model.plxId) {
                isEditMode = false;
                return;
            }

            isEditMode = true;
            $('#CaseStatus').text('Pending');
            $('#CaseStatus').addClass('pending');
            $('#CaseHeader').text('Edit Files');

            getClassification();
        }

        // Loads the case information from the model given.
        function loadCase() {
            $("#plxIdInput").attr("disabled", true);
            for (var i = 0; i < companies.length; i++) {
                if (model.selectedCompanyIds.includes(companies[i].id)) {
                    selectedCompanies.push(companies[i]);
                }
            }
            createMultiSelectItems(false);
            updateMultiSelectPreview();
        }

        function getAllowedTypesText() {
            var extensionNames = allowedFileTypes.map(t => t.extension).sort();
            return extensionNames.join(', ');
        }

        // Returns the file size allowed for the extension type. 
        // If not found, we return max number so that we don't falsely catch other validation blocks, and we can catch this server-side.
        function getMaxFileSizeMbByExtension(fileExtension) {
            var relevantExtension = allowedFileTypes.filter(t => t.extension == fileExtension)[0];
            return relevantExtension ? relevantExtension.maxFileSizeMb : Number.MAX_SAFE_INTEGER;
        }

        function validateFileProperties(fileName, fileSize, plxId) {
            const existingCaseFile = $(`#case-upload-form .case-upload-file-options[data-case-file-name='${fileName}']`);
            const fileExtension = fileName.split('.').pop();
            const maxFileSizeMb = getMaxFileSizeMbByExtension(fileExtension);

            if (existingCaseFile.length > 0) {
                return `Case file has already been uploaded with name "${fileName}".`;
            }
            if (!allowedFileTypes.map(t => t.extension).includes(fileExtension)) {
                return `Case file type "${fileExtension}" is not allowed. Allowed types: ${getAllowedTypesText()}.`;
            }
            if (getSizeMbFromBytes(fileSize) > maxFileSizeMb) {
                return `Case file "${fileName}" is more than ${maxFileSizeMb} MB.`;
            }
            if (fileSize <= 0) {
                return `Case file "${fileName}" could not be uploaded as it is an empty file.`;
            }
            if (!validateFileName(fileName)) {
                return `Case file "${fileName}" could not be uploaded as file name can only contain letters numbers, '-' and '_'.`;
            }
            if (!validateFileNameContainsPlxId(fileName, plxId)) {
                return `Case file "${fileName}" does not contain PLX ID - e.g. "${plxId}-Case-File.pdf" or "Case_File_${plxId}.xml".`;
            }
        }

        function validateFileName(fileName) {
            var validFileNameRegex = /^[a-zA-Z0-9\-_]+$/;
            var fileParts = fileName.split('.');
            fileParts.pop();
            fileNameWithoutExtension = fileParts.join('.');
            return validFileNameRegex.test(fileNameWithoutExtension);
        }

        // Check save case request model for validation issues.
        function validateCaseModel(caseRecord) {
            if (isEditMode) {
                if (!caseRecord.CaseId) {
                    return 'Could not save case, requires a valid a Case ID.';
                }
            }

            if (!caseRecord.PlxId || caseRecord.PlxId <= 0) {
                return 'Could not save case, requires a valid PLX ID.';
            }
            if (!caseRecord.Companies || caseRecord.Companies.length == 0) {
                return 'Could not save case, requires at least one company.';
            }
            if (!caseRecord.MLM) {
                return 'Could not save case, requires a valid MLM value.';
            }
            if (!caseRecord.PSUR) {
                return 'Could not save case, requires a valid PSUR value.';
            }
            if (caseRecord.PvSafetyDatabaseId) {
                if (!caseRecord.PvSafetyDatabaseId.match(/^[a-z0-9]+$/i)) {
                    return 'Could not save case, PV Safety Database ID must be alphanumeric.';
                }
                if (caseRecord.PvSafetyDatabaseId.length > 100) {
                    return 'Could not save case, PV Safety Database ID must be 100 characters or less.';
                }
            }
            if (!caseRecord.UploadId) {
                return 'Cannot save case, requires a valid Upload ID.';
            }

            var existingFileCount = model.uploadedFiles.length ?? 0;
            var newFileCount = caseRecord.UploadedFileNames.length ?? 0;
            var deletedFileCount = caseRecord.DeletedFileIds.length ?? 0;
            if ((existingFileCount + newFileCount - deletedFileCount) <= 0) {
                return 'Could not save case, requires at least one file.'
            }

            return null;
        }
    });

</script>