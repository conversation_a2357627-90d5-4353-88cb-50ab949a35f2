@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.ProjectModel

@{
    ViewData["Title"] = "Company Projects";    
}

<form method="post" asp-action="@(Model.Id == 0 ? "CreateCompanyProject": "EditCompanyProject")">
    <div class="sub-header">
        @if (Model.Id == default)
        {
            <h2>Create Company Project</h2>
        }
        else
        {
            <h2>Edit Company Project</h2>
        }
        <div class="controls">
            <button asp-action="@(Model.Id == 0 ? "CreateCompanyProject": "EditCompanyProject")" type="submit" class="button icon-button-save btn-default">Save</button>
            <a class="button secondary icon-button-cancel btn-default" href="@($"/Companies/CompanyProjects/{Model.CompanyId}")">Cancel</a>
        </div>
    </div>

    <section class="flex card-container">
        <div method="post" class="section-card expand">
            @Html.HiddenFor(m => m.Id)
            @Html.HiddenFor(m => m.CompanyId)
            <h2>Details</h2>

            <div class="form-group">
                <label for=Name>Name</label>
                <input asp-for="Name" type="text" required pattern=".*\S+.*" />
                <span asp-validation-for="Name"></span>
            </div>
        </div>
    </section>
</form>