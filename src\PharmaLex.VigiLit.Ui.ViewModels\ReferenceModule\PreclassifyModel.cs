﻿using System.Collections.Generic;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class PreclassifyModel
{
    public ImportSelectionModel SelectedImport { get; set; }

    public int TodoCount { get; set; }
    public int CompletedCount { get; set; }
    
    public IEnumerable<ClassificationCategoryModel> ClassificationCategories { get; set; }
    public IEnumerable<string> Countries { get; set; }

    public IEnumerable<PreclassifyReferenceModel> PreclassifyReferenceModels { get; set; }

    public bool DisplayAiSuggestions { get; set; }
}
