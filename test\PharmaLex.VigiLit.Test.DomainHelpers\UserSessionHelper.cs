﻿using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class UserSessionHelper
{
    private readonly IUserSessionRepository _userSessionRepository;

    public UserSessionHelper(IUserSessionRepository userSessionRepository)
    {
        _userSessionRepository = userSessionRepository;
    }

    public async Task AddUserSession(int userId, string ipAddress, User user)
    {
        _userSessionRepository.Add(new UserSession { UserId = userId, IpAddress = ipAddress, User = user });
        await _userSessionRepository.SaveChangesAsync();
    }
}
