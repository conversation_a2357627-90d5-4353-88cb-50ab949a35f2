﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Ui.Controllers;
using PharmaLex.VigiLit.Reports.Accovion.Domain.Models;

namespace PharmaLex.VigiLit.Reports.Accovion.Domain;

[Authorize]
[Route("/Reports/[controller]")]
public class AccovionClientReportController: BaseReportController
{
    private readonly IAccessControlService _accessControlService;
    private readonly IAccovionClientReportService _accovionClientReportService;

    public AccovionClientReportController(
                        IAccessControlService securityService,
                        IAccovionClientReportService accovionClientReportService,
                        IUserSessionService userSessionService,
                        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _accessControlService = securityService;
        _accovionClientReportService = accovionClientReportService;
    }

    [HttpGet]
    public  async Task<IActionResult> Index()
    {
        try
        {
            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var model = _accovionClientReportService.GetPageModel();

            return View("../Reports/AccovionClientReport/Index", model);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("/Reports/[controller]/[action]")]
    public async Task<IActionResult> GetCards([FromBody] CardsRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var cards = _accovionClientReportService.GetCards(request.Year);

            return Ok(cards);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("/Reports/[controller]/[action]/{year}/{month}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Download(int year, int month)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var viewContext = new ViewReportPermissionContext(CurrentUserId, ControllerName);
            await _accessControlService.HasPermission(viewContext);

            var downloadFile = await _accovionClientReportService.Download(year, month);

            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }
}