using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

public class ProjectModel
{
    public int Id { get; set; }
    [Required]
    [Remote("ValidateProjectName", "Companies", AdditionalFields = "Id,CompanyId", ErrorMessage = "Project name already exists")]
    [MaxLength(250)]
    public string Name { get; set; }
    [Required]
    public int CompanyId { get; set; }
}