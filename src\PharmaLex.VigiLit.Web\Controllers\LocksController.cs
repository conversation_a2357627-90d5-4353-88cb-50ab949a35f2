﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.Admin)]
[Route("[controller]")]
public class LocksController: BaseController
{
    private readonly IUserService _userService;
    private readonly IPreClassificationService _preClassificationService;

    public LocksController(IUserService userService,
                            IPreClassificationService preClassificationService,
                            IUserSessionService userSessionService,
                            IConfiguration configuration) : base(userSessionService, configuration)
    {
        _userService = userService;
        _preClassificationService = preClassificationService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var data = await _userService.GetUserLocks();
        return View(data);
    }

    [HttpGet("[action]/{userId}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Release(int userId)
    {
        await _preClassificationService.Release(userId);
        return Ok();
    }
}

