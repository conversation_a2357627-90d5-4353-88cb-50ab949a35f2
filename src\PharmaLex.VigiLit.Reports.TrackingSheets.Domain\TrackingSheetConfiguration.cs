﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class TrackingSheetConfiguration : EntityBaseMap<TrackingSheet>
{
    public override void Configure(EntityTypeBuilder<TrackingSheet> builder)
    {
        base.Configure(builder);

        builder.ToTable("TrackingSheets");

        builder.HasIndex(p => new { p.CompanyId, p.Year, p.Week }).IsUnique();

        builder.<PERSON><PERSON>ey(e => e.Id);
    }
}