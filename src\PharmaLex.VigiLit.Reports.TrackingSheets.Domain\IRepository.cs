using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Models;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public interface IRepository : ITrackingRepository<TrackingSheet>
{
    /// <summary>
    /// Gets a tracking sheet by id.
    /// </summary>
    /// <param name="id">The identifier.</param>
    /// <returns>The tracking sheet; or null if not exists.</returns>
    Task<TrackingSheet> GetById(int id);

    /// <summary>
    /// Gets tracking sheets by company and year.
    /// </summary>
    /// <param name="companyId">The company identifier.</param>
    /// <param name="year">The year.</param>
    /// <returns>Tracking sheets for the company and year.</returns>
    Task<IEnumerable<TrackingSheet>> GetByCompanyAndYear(int companyId, int year);

    /// <summary>
    /// Gets the tracking sheet pdf model.
    /// </summary>
    /// <param name="company">The company.</param>
    /// <param name="year">The year.</param>
    /// <param name="week">The week.</param>
    /// <returns>The tracking sheet pdf model.</returns>
    Task<TrackingSheetPdfModel> GetTrackingSheetPdfModel(Company company, int year, int week);

    /// <summary>
    /// Check if a tracking sheet exists.
    /// </summary>
    /// <param name="companyId">The company identifier.</param>
    /// <param name="year">The year.</param>
    /// <param name="week">The week.</param>
    /// <returns>true, if exists; otherwise false.</returns>
    Task<bool> Exists(int companyId, int year, int week);
}