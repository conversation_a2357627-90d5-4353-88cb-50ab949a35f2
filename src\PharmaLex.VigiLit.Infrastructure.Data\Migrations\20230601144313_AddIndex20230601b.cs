﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndex20230601b : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_LastUpdatedDate_Id_ClassificationCategoryId_ReferenceId",
                table: "ReferenceClassifications",
                columns: new[] { "LastUpdatedDate", "Id", "ClassificationCategoryId", "ReferenceId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_LastUpdatedDate_Id_ClassificationCategoryId_ReferenceId",
                table: "ReferenceClassifications");
        }
    }
}
