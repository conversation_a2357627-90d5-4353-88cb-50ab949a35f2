﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Moq;
using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.Helpers;

namespace PharmaLex.Core.UserSessionManagement.Unit.Tests;

public class UserSessionServiceTests
{
    private readonly UserSessionService _userSessionService;

    private readonly Mock<IUserSessionRepository> _mockUserSessionRepository = new();

    private const string testIpAddress1 = "127.0.0.1";

    public UserSessionServiceTests()
    {
        _userSessionService = new UserSessionService(_mockUserSessionRepository.Object);
    }

    [Fact]
    public async Task ProcessIncomingSession_Initialises_NewSession_With_Incoming_SessionId()
    {
        // Arrange
        var userId = 1;
        var sessionLengthSeconds = 60;
        var httpSessionId = "sessionId12345";
        var databaseUserSession = new UserSession { UserId = userId, IpAddress = testIpAddress1 };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);

        var actionExecutingContext = GetActionExecutingContext(httpSessionId, "controller", "Home", "GET");

        // Act
        var result = await _userSessionService.ProcessIncomingSession(actionExecutingContext, userId, sessionLengthSeconds);

        // Assert
        Assert.True(result?.SessionData == httpSessionId);
    }

    [Fact]
    public async Task ProcessIncomingSession_Subsequent_Calls_Are_Valid()
    {
        // Arrange
        var userId = 1;
        var sessionLengthSeconds = 60;
        var httpSessionId = "sessionId12345";
        var databaseUserSession = new UserSession { UserId = userId, IpAddress = testIpAddress1 };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);

        var actionExecutingContext = GetActionExecutingContext(httpSessionId, "controller", "Home", "GET");
        await _userSessionService.ProcessIncomingSession(actionExecutingContext, userId, sessionLengthSeconds);

        // Act
        var result = await _userSessionService.ProcessIncomingSession(actionExecutingContext, userId, sessionLengthSeconds);

        // Assert
        Assert.True(result?.SessionData == httpSessionId);
    }

    [Fact]
    public async Task ProcessIncomingSession_No_Session_For_User_Throws_Exception()
    {
        // Arrange
        var userId = 1;
        var sessionLengthSeconds = 60;
        var httpSessionId = "sessionId12345";
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()));

        var actionExecutingContext = GetActionExecutingContext(httpSessionId, "controller", "Home", "GET");

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () => await _userSessionService.ProcessIncomingSession(actionExecutingContext, userId, sessionLengthSeconds));
    }

    [Fact]
    public async Task ProcessIncomingSession_InValid_Session_Throws_Exception_When_HttpSession_And_Stored_Session_Dont_Match()
    {
        // Arrange
        var userId = 1;
        var sessionLengthSeconds = 60;
        var httpSessionId = "sessionId12345";
        var dbSessionId = "non_matching_sessionid";
        var databaseUserSession = new UserSession { UserId = userId, IpAddress = testIpAddress1, SessionData = dbSessionId };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);

        var actionExecutingContext = GetActionExecutingContext(httpSessionId, "controller", "Home", "GET");

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () => await _userSessionService.ProcessIncomingSession(actionExecutingContext, userId, sessionLengthSeconds));
    }

    [Fact]
    public void IsValidSession_Returns_Valid_If_Route_Is_Logout()
    {
        // Arrange
        var databaseUserSession = new UserSession();
        var httpSessionId = "session_1";
        var routeValues = GetRouteDataDictionary("action", "LogoutEx");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsValidSession_Returns_True_If_Session_Has_Not_Expired()
    {
        // Arrange
        var httpSessionId = "session_1";
        var databaseUserSession = new UserSession { SessionExpiry = DateTime.UtcNow.AddMinutes(10), SessionData = httpSessionId };
        var routeValues = GetRouteDataDictionary("action", "LogoutEx");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsValidSession_Returns_True_If_Session_Has_Expired()
    {
        // Arrange
        var httpSessionId = "session_1";
        var databaseUserSession = new UserSession { SessionExpiry = DateTime.UtcNow.AddMinutes(-10), SessionData = httpSessionId };
        var routeValues = GetRouteDataDictionary("action", "LogoutEx");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsValidSession_Returns_True_When_Sessions_Match()
    {
        // Arrange
        var databaseUserSession = new UserSession { SessionData = "session_1" };
        var httpSessionId = "session_1";
        var routeValues = GetRouteDataDictionary("action", "LogoutEx");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsValidSession_Returns_False_When_Sessions_Dont_Match()
    {
        // Arrange
        var databaseUserSession = new UserSession { SessionData = "session_2" };
        var httpSessionId = "session_1";
        var routeValues = GetRouteDataDictionary("controller", "Home");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsValidSession_Returns_True_When_Saved_HttpSessionId_Is_Empty()
    {
        // Arrange
        var databaseUserSession = new UserSession { SessionData = null };
        var httpSessionId = "session_1";
        var routeValues = GetRouteDataDictionary("action", "LogoutEx");

        // Act
        var result = _userSessionService.IsValidSession(databaseUserSession, httpSessionId, routeValues);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ProcessTimerCall_Returns_False_When_UserNotFoundOrUserNotAuthorised()
    {
        // Arrange
        var userNotFound = 0;
        var httpSessionId = "sessionId12345";
        var databaseUserSession = new UserSession { UserId = userNotFound, IpAddress = testIpAddress1 };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);
        var context = GetHttpContext(httpSessionId, "controller", "Home", "GET");

        // Act
        var result = await _userSessionService.ProcessTimerCall(context, userNotFound);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ProcessTimerCall_Returns_False_When_UserIsLoggingOut()
    {
        // Arrange
        var userId = 40;
        var httpSessionId = "sessionId12345";
        var databaseUserSession = new UserSession { UserId = userId, IpAddress = testIpAddress1 };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);
        var context = GetHttpContext(httpSessionId, "action", "LogoutEx", "GET");

        // Act
        var result = await _userSessionService.ProcessTimerCall(context, userId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ProcessTimerCall_Returns_False_When_UserSessionNotFound()
    {
        // Arrange
        var userId = 40;
        string? sessionNotFound = null;
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()));
        var context = GetHttpContext(sessionNotFound!, "controller", "Home", "GET");

        // Act
        var result = await _userSessionService.ProcessTimerCall(context, userId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ProcessTimerCall_Returns_True_When_UserSessionFoundAndUserNotLoggingOutAndUserFound()
    {
        // Arrange
        var userId = 40;
        var httpSessionId = "sessionId12345";
        var databaseUserSession = new UserSession { UserId = userId, IpAddress = testIpAddress1 };
        _mockUserSessionRepository.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ReturnsAsync(databaseUserSession);
        var context = GetHttpContext(httpSessionId, "controller", "Home", "GET");

        // Act
        var result = await _userSessionService.ProcessTimerCall(context, userId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task InitialiseUserSession_CreatesSessionForUser()
    {
        // Arrange
        int userId = 100;
        _mockUserSessionRepository.Setup(x => x.Add(It.IsAny<UserSession>()));
        _mockUserSessionRepository.Setup(x => x.Remove(It.IsAny<UserSession>()));
        _mockUserSessionRepository.Setup(x => x.GetAllByUserIdAsync(It.IsAny<int>()));
        _mockUserSessionRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _userSessionService.InitialiseUserSession(userId, testIpAddress1);

        // Assert
        _mockUserSessionRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(2));

        _mockUserSessionRepository.Verify(x => x.Add(It.Is<UserSession>(
                                                        x => x.UserId == userId &&
                                                        x.IpAddress == testIpAddress1
                                                        )));
    }

    [Fact]
    public async Task DeleteUserSessionsAsync_DeletesSessionsForListOfUsers()
    {
        // Arrange
        var userIds = new List<int> { 1, 2, 3 };
        var userSessions = new List<UserSession> { new UserSession { UserId = 1 }, new UserSession { UserId = 2, }, new UserSession { UserId = 3, } };
        _mockUserSessionRepository.Setup(x => x.GetSessionsForUsersAsync(It.IsAny<List<int>>())).ReturnsAsync(userSessions);
        _mockUserSessionRepository.Setup(x => x.Remove(It.IsAny<UserSession>()));
        _mockUserSessionRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _userSessionService.DeleteUserSessionsAsync(userIds);

        // Assert
        _mockUserSessionRepository.Verify(x => x.Remove(It.IsAny<UserSession>()), Times.Exactly(3));
        _mockUserSessionRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task DeleteUserSessionAsync_DeletesUserSession()
    {
        // Arrange        
        var userId = 1;
        var userSessions = new List<UserSession> { new UserSession { UserId = userId, IpAddress = testIpAddress1 }, new UserSession { UserId = userId, IpAddress = "*************" } };
        _mockUserSessionRepository.Setup(x => x.GetAllByUserIdAsync(It.IsAny<int>())).ReturnsAsync(userSessions);
        _mockUserSessionRepository.Setup(x => x.Remove(It.IsAny<UserSession>()));
        _mockUserSessionRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _userSessionService.DeleteUserSessionAsync(1);

        // Assert
        _mockUserSessionRepository.Verify(x => x.Remove(It.IsAny<UserSession>()), Times.Exactly(2));
        _mockUserSessionRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    private static ActionExecutingContext GetActionExecutingContext(string sessionId, string routeKey, string routeValue, string method)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Method = method;
        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.Id).Returns(sessionId);
        httpContext.Session = sessionMock.Object;

        var routeData = new RouteData();
        routeData.Values.Add(routeKey, routeValue);

        routeData.Values.AddRange(new RouteValueDictionary());

        var actionContext = new ActionContext(httpContext,
                                                routeData,
                                                new ActionDescriptor(),
                                                new ModelStateDictionary());

#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        return new ActionExecutingContext(actionContext,
                                            Mock.Of<IList<IFilterMetadata>>(),
                                            actionArguments: Mock.Of<IDictionary<string, object>>(),
                                            Mock.Of<Controller>());
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.

    }

    private static DefaultHttpContext GetHttpContext(string sessionId, string routeKey, string routeValue, string method)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Method = method;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.Id).Returns(sessionId);
        httpContext.Session = sessionMock.Object;

        httpContext.Request.RouteValues.Add(routeKey, routeValue);

        return httpContext;
    }


    private static RouteValueDictionary GetRouteDataDictionary(string key, string value)
    {
        return new RouteValueDictionary
        {
            { key, value}
        };
    }
}
