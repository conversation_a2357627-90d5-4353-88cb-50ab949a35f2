﻿using Moq;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Access;
using PharmaLex.VigiLit.Reporting.Contracts.Security;
using PharmaLex.VigiLit.Reporting.Interfaces.Repositories;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.Reporting.Unit.Tests.Access;

public class ViewReportEvaluatorTests
{
    private readonly Mock<IUserRepository> _mockUserRepository = new Mock<IUserRepository>();
    private readonly Mock<IReportRepository> _mockReportRepository = new Mock<IReportRepository>();

    private const int UserId = 42;
    private const string ReportName = "ReportName";
    private readonly User _user = new FakeUser(UserId);


    [Fact]
    public async Task ViewReportEvaluator_When_user_can_view_report_Returns_true()
    {
        // Arrange
        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(_user);
        _mockReportRepository.Setup(x => x.CanUserViewReport(_user, ReportName)).ReturnsAsync(true);

        var viewReportEvaluator = new ViewReportEvaluator(_mockUserRepository.Object, _mockReportRepository.Object);

        var viewReportPermissionContext = new ViewReportPermissionContext(UserId, ReportName);

        // Act
        var result = await viewReportEvaluator.HasPermissions(viewReportPermissionContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ViewReportEvaluator_When_user_can_not_view_report_Throws_UnauthorisationAccessException()
    {
        // Arrange
        _mockUserRepository.Setup(x => x.GetForSecurity(UserId)).ReturnsAsync(_user);
        _mockReportRepository.Setup(x => x.CanUserViewReport(_user, ReportName)).ReturnsAsync(false);

        var viewReportEvaluator = new ViewReportEvaluator(_mockUserRepository.Object, _mockReportRepository.Object);

        var viewReportPermissionContext = new ViewReportPermissionContext(UserId, ReportName);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
        {
            var x = await viewReportEvaluator.HasPermissions(viewReportPermissionContext);
        });
    }
}
