@using Microsoft.AspNetCore.Authorization;
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.Interfaces.Services;
@using PharmaLex.VigiLit.Web.Helpers;
@using PharmaLex.VigiLit.Domain.UserManagement;
@using PharmaLex.VigiLit.Ui.ViewModels.EmailService

@inject IAuthorizationService AuthorizationService
@inject ICompanyUserService companyUserService
@inject IEmailMaintenanceService emailMaintenanceService

@{
    ViewData["Title"] = "Company Users";
}

@{
    List<EmailPreferenceModel> _emailPreferences = await companyUserService.GetEmailPreferencesAsync();
    var _emailSuppression = await emailMaintenanceService.GetUserSuppression(Model.Id);
}

@model PharmaLex.VigiLit.Ui.ViewModels.UserManagement.CompanyUserModel

<div id="company-user-edit" v-cloak>
    <form method="post">

        @Html.HiddenFor(m => m.CompanyId)

            <div class="sub-header">
                @if (Model.Id == 0)
                {
                    <h2 class="brand-color">Create User</h2>
                }
                else
                {
                    <h2>@Model.DisplayFullName</h2>
                }
                <div class="controls">
                    <button asp-action="@(Model.Id == 0 ? "CreateCompanyUser": "EditCompanyUser")" type="submit" class="button icon-button-save btn-default" :disabled="!modelValid || isLoading">Save</button>
                    <a class="button secondary icon-button-cancel btn-default" href="/Companies/@Model.CompanyId/CompanyUsers">Cancel</a>
                </div>
            </div>

          <section class="flex flex-wrap flex-gap card-container">
            <div id="details-column" v-if="!isModelSet" class="pr-2" v-bind:class="['section-card', 'expand']">
                <div v-if="!isModelSet">
                    <h2> User Details</h2>
                    <div class="form-group">
                        <label>User Type</label>
                        <select name="CompanyUserType" id="CompanyUserType" v-model="companyUserType" v-on:change="handleUserTypeChange($event.target.value)">
                            <option disabled value="">Please select user type</option>
                            <option value="1">Internal</option>
                            <option value="2">External</option>
                        </select>
                    </div>
                </div>
                <div v-if="companyUserType=='1' && !isModelSet" class="py-4" v-bind:class="[{'hidden': !user}]">
                    <div id="search-column" class="flex-x2">
                        <h2>Find representative</h2>
                        <div class="form-group">
                            <label>Start typing the user's name or email address</label>
                            <autocomplete :config="config" v-on:selected="selectedItem"></autocomplete>
                        </div>
                    </div>
                </div>
            </div>

            <div id="details-column" v-if="user && (companyUserType=='2' || isModelSet ) " v-bind:class="['section-card', 'expand', {'hidden': !user}]">
                <div class="flex justify-space-between">
                    <h2>Details</h2>
                    <label v-if="user.id" class="switch-container switch-active-inactive">
                        <input class="switch" name="active" id="active" :value="user.active"  v-model="user.active " type="checkbox" aria-label="Active" />
                        <label class="switch" for="active"></label>
                    </label>
                </div>
                <div class="form-group">
                    <label for="GivenName">First name</label>
                    <input name="GivenName" id="GivenName" v-model="user.givenName" type="text" required />
                </div>
                <div class="form-group">
                    <label for="FamilyName">Last name</label>
                    <input name="FamilyName" id="FamilyName" v-model="user.familyName" type="text" required />
                </div>
                <div class="form-group">
                    <label for="Email">Email</label>
                    <input v-bind:class="{ 'pointer-events-none disabled': user.id }" name="Email" id="Email" v-model="user.email" type="email" required />
                    <span asp-validation-for="Email" class="error-color"></span>
                </div>
                <div v-if="user.id">
                    <div class="form-group">
                        <hr class="full-width-rule">
                    </div>
                    <div class="form-group">
                        <label for="lastLoginDate">Last login date</label>
                        <input name="lastLoginDate" id="lastLoginDate" v-model="user.displayLastLoginDate" type="text" disabled />
                    </div>
                    <div class="form-group" v-if="user.companyUserType== '2'">
                        <label for="invitationExpiryDate">Invitation expiry date</label>
                        <input name="invitationExpiryDate" id="invitationExpiryDate" v-model="user.displayActivationExpiryDate" type="text" disabled />
                    </div>
                    <div class="form-group" v-if="user.companyUserType== '2'">
                        <div class="flex flex-gap">
                            <div v-bind:class="{ 'cursor-not-allowed': disableResendButton }">
                                <a class="button" v-bind:class="{ 'secondary pointer-events-none': disableResendButton }" @@click="resend">Resend Invitation</a>
                            </div>
                            <div v-bind:class="{ 'cursor-not-allowed': disableCopyLinkButton }">
                                <a class="button" v-bind:class="{ 'secondary pointer-events-none': disableCopyLinkButton }" @@click="copyLink">Copy Invitation Link</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section-card" v-if="(companyUserType=='2' || isModelSet )">
                <h2>Mail settings</h2>
                <div v-for="emailPreference in emailPreferences" :key="emailPreference.id">
                    <div class="switch-wrapper">
                        <span :id="emailPreference.id + '-label'">{{emailPreference.displayName}}</span>
                        <label class="switch-container">
                            <input aria-labelled-by="emailPreference.id + '-label'" class="switch" name="EmailPreferenceIds" :id="emailPreference.id" :value="emailPreference.id" v-model="user.emailPreferenceIds" type="checkbox" :checked="isChecked(emailPreference.id)" />
                            <label :for="emailPreference.id" class="switch"></label>
                        </label>
                    </div>
                </div>
                @if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalSupport)).Succeeded)
                { 
                    <div v-if="(isBounce || isBlock || isSpam)" class="section-card expand">

                        <div v-if="isBounce">
                            <h2>Email is on bounce list</h2>
                            <div class="form-group">
                                <label>Bounced date</label>
                            <input name="emailBouncedDate" v-model="emailSuppressionDate" type="text" disabled />
                            </div>
                            <div class="form-group">
                                <label>Suppression reason</label>
                                <div class="suppression-reason">@_emailSuppression?.Reason</div>    
                            </div>
                        </div>
                        <div v-if="isBlock">
                            <h2>Email is on block list</h2>
                            <div class="form-group">
                                <label>Blocked date</label>
                                <input name="emailBlockedDate" v-model="emailSuppressionDate" type="text" disabled />
                            </div>
                            <div class="form-group">
                                <label>Suppression reason</label>
                                <div class="suppression-reason">@_emailSuppression?.Reason</div>
                            </div>
                        </div>
                        <div v-if="isSpam">
                            <h2>Email is on spam list</h2>
                            <div class="form-group">
                                <label>Spam date</label>
                                <input name="emailSpamDate" v-model="emailSuppressionDate" type="text" disabled />
                            </div>
                            <div class="form-group">
                                <label>Suppression reason</label>
                                <div class="suppression-reason">@_emailSuppression?.Reason</div>
                            </div>
                        </div>
                        <div v-if="isBounce || isBlock">    
                            <div class="form-group">
                                <div class="flex flex-gap">
                                    <div v-bind:class="{ 'cursor-not-allowed': disableBounceButton }">
                                        <a class="button" v-bind:class="{ 'secondary pointer-events-none': disableBounceButton }" @@click="deleteBounce()">Delete From Bounce List</a>
                                    </div>
                                    <div v-bind:class="{ 'cursor-not-allowed': disableBlockButton }">
                                        <a class="button" v-bind:class="{ 'secondary pointer-events-none': disableBlockButton }" @@click="deleteBlock()">Delete From Block List</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            
        </section>
        <input type="hidden" name="Id" :value="user.id" />
        <input type="hidden" name="CompanyUserType" :value="user.companyUserType" />

    </form>
</div>



@section Scripts {
    <script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: '#company-user-edit',
            data: function () {
                return {
                    config: {
                        canAddNew: false,
                        searchUrl: '/users/find?term={term}'
                    },
                    user: @Html.Raw(AntiXss.ToJson(Model)),
                    isLoading: false,
                    emailPreferences: @Html.Raw(AntiXss.ToJson(_emailPreferences)),
                    emailSuppressionDate: '@_emailSuppression?.CreatedDisplayDate',                    
                    emailSuppressionType: '@_emailSuppression?.EmailSuppressionType',
                    isModelSet: false,
                    companyUserType : ""
                };
            },
            methods: {
                selectedItem(selectedUser) {
                    this.user.givenName = selectedUser.givenName;
                    this.user.familyName = selectedUser.familyName;
                    this.user.email = selectedUser.email;
                    this.isModelSet = true;
                },
                handleUserTypeChange(value){
                    this.user.companyUserType = value;
                },
                resend() {
                    document.body.style.cursor = "wait";
                    this.isLoading = true;
                    fetch(`/companies/resendsignoninvitation`, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        },
                        body: JSON.stringify({ ...this.user })
                    }).then(response => {

                        if (response.ok) {
                            response.json().then(data => {
                                this.user = JSON.parse(data);
                            });
                            plx.toast.show('Invitation email sent successfully', 2, 'confirm', null, 2500);
                            this.unsetLoading();
                        }
                        else {
                            plx.toast.show('There was a problem sending the invitation email', 2, 'failed', null, 5000);
                            console.log(response);
                            this.unsetLoading();
                        }
                    })
                        .catch(error => {
                            plx.toast.show('There was a problem sending the invitation email', 2, 'failed', null, 5000);
                            console.log(error);
                            this.unsetLoading();
                        });
                },
                copyLink() {
                    if (this.user.invitationEmailLink && !this.lastLoginDate) {
                        navigator.clipboard.writeText(this.user.invitationEmailLink);
                        plx.toast.show('Invitation email link has been copied to the clipboard', 2, 'confirm', null, 2500);
                    }
                },
                isChecked(emailPreferenceId) {
                    var checked = 'checked'
                    if (this.user.emailPreferenceIds && this.user.emailPreferenceIds.includes(emailPreferenceId)) {
                        return checked;
                    }
                },
                deleteBounce() {
                    this.deleteSuppression("/companies/delete-user-from-bounce-list", "bounce");
                },
                deleteBlock() {
                    this.deleteSuppression("/companies/delete-user-from-block-list", "block");
                },
                deleteSuppression(url, type) {
                    document.body.style.cursor = "wait";
                    this.isLoading = true;
                    fetch(url, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        },
                        body: JSON.stringify({ ...this.user })
                    }).then(response => {

                        if (response.ok) {
                            response.json().then(data => {
                                this.user = JSON.parse(data);
                            });
                            plx.toast.show(`User successfully successfully deleted from ${type} list`, 2, 'confirm', null, 2500);
                            this.unsetLoading();
                        }
                        else {
                            plx.toast.show(`There was a problem deleting the user from ${type} list`, 2, 'failed', null, 5000);
                            console.log(response);
                            this.unsetLoading();
                        }
                    })
                        .catch(error => {
                            plx.toast.show(`There was a problem deleting the user from ${type} list`, 2, 'failed', null, 5000);
                            console.log(error);
                            this.unsetLoading();
                        });
                },
                unsetLoading() {
                    document.body.style.cursor = "default";
                    this.isLoading = false;
                }
            },
            created() {
                if (@Model.Id !== 0) {
                    this.user = @Html.Raw(AntiXss.ToJson(Model));
                    this.companyUserType = "";
                    this.isModelSet = true;
                }
            },
            computed: {
                modelValid() {
                    return this.user.givenName && this.user.familyName && this.user.email;
                },
                isBounce() {
                    return this.emailSuppressionType.toUpperCase() == 'BOUNCE';
                },
                isBlock() {
                    return this.emailSuppressionType.toUpperCase() == 'BLOCK';                    
                },
                isSpam() {
                    return this.emailSuppressionType.toUpperCase() == 'SPAM';                    
                },
                disableResendButton() {
                    return this.user.lastLoginDate || this.isLoading;
                },
                disableCopyLinkButton() {
                    return !this.user.invitationEmailLink || this.user.lastLoginDate || this.isLoading;
                },
                disableBounceButton() {
                    return !this.isBounce || this.isLoading;
                },
                disableBlockButton() {
                    return !this.isBlock || this.isLoading;
                }
            }, 
            watch: {
                companyUserType(newValue) {
                    if (newValue == "2") {
                        this.user.givenName = '';
                        this.user.familyName = '';
                        this.user.email = '';
                        this.user.emailPreferenceIds = [];
                        this.isModelSet = true;
                    } else if (newValue == "1") {
                        this.isModelSet = false;
                    }
                }
            }
        };
    </script>
}
@section VueComponentScripts{
    <partial name="Components/Vue3/AutoCompleteList" />
 }