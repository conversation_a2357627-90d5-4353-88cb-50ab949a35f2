﻿using AutoMapper;
using Moq;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Application.Tests.Fakes;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class CompanyServiceTests
{
    private readonly Mock<ICompanyRepository> _mockCompanyRepository = new();
    private readonly Mock<IUserSessionService> _mockUserSessionService = new();
    private readonly ICompanyService _companyService;

    public CompanyServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new CompanyMappingProfile());
            mc.AddProfile(new CompanyUserMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();

        _companyService = new CompanyService(_mockCompanyRepository.Object, _mockUserSessionService.Object, mapper);
    }

    [Fact]
    public async Task GetActiveCompaniesWithActiveContractsForSubstance_Returns_ExpectedValue()
    {
        // Arrange
        var companies = new List<CompanyModel> {
            new CompanyModel{ Id = 1, Name = "Company 1", IsActive = true },
            new CompanyModel{ Id = 2, Name = "Company 2", IsActive = true },
            new CompanyModel{ Id = 3, Name = "Company 3", IsActive = true }
        };

        _mockCompanyRepository.Setup(x => x.GetActiveCompaniesWithActiveContractsForSubstance(1)).ReturnsAsync(companies);

        // Act
        var result = await _companyService.GetActiveCompaniesWithActiveContractsForSubstance(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());
        Assert.Equal("Company 1", result.ToList()[0].Name);
        Assert.Equal("Company 2", result.ToList()[1].Name);
        Assert.Equal("Company 3", result.ToList()[2].Name);
    }

    [Fact]
    public async Task GetCompanyUsers_Returns_Users()
    {
        // Arrange
        var company = new Company("Company 1", "Contact person 1", "Contact email", true);

        _mockCompanyRepository.Setup(x => x.GetWithUsersAndSuppressionsByIdAsync(1)).ReturnsAsync(company);

        // Act
        var result = await _companyService.GetCompanyUsers(1);

        // Assert
        Assert.Equal("Company 1", result.Name);
        Assert.Equal("Contact person 1", result.ContactPersonName);
        Assert.Equal("Contact email", result.ContactPersonEmail);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task UpdateAsync_UpdatesCompany()
    {
        // Arrange
        var company = new Company("Company 1", "Contact person 1", "Contact email", true);
        var companyModel = new CompanyModel { Id = 1, Name = "Company 1", IsActive = true };

        _mockCompanyRepository.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(company);

        // Act
        await _companyService.UpdateAsync(companyModel);

        // Assert
        _mockCompanyRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task UpdateAsync_ClearsUserSessions_For_Inactive_Company()
    {
        // Arrange
        var company = new Company("Company 1", "Contact person 1", "Contact email", false);
        var companyModel = new CompanyModel { Id = 1, Name = "Company 1", IsActive = false };

        var usersExpected = new List<int> { 100, 200, 300 };

        Company companyFake = new FakeCompany(1);
        companyFake.CompanyUsers = new List<CompanyUser> {
            new FakeCompanyUser{ Active = true, User = new FakeUser("Given Name 1", "Family Name 1", "<EMAIL>", 100, DateTime.UtcNow, DateTime.UtcNow)},
            new FakeCompanyUser{ Active = true, User = new FakeUser("Given Name 2", "Family Name 2", "<EMAIL>", 200, DateTime.UtcNow, DateTime.UtcNow)},
            new FakeCompanyUser{ Active = true, User = new FakeUser("Given Name 3", "Family Name 3", "<EMAIL>", 300, DateTime.UtcNow, DateTime.UtcNow)}
        };

        _mockCompanyRepository.Setup(x => x.GetWithUsersAndSuppressionsByIdAsync(1)).ReturnsAsync(companyFake);
        _mockUserSessionService.Setup(x => x.DeleteUserSessionsAsync(It.IsAny<List<int>>()));
        _mockCompanyRepository.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(company);

        // Act
        await _companyService.UpdateAsync(companyModel);

        // Assert
        _mockUserSessionService.Verify(x => x.DeleteUserSessionsAsync(usersExpected), Times.Once());
    }
}
