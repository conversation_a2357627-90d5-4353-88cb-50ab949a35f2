﻿using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class CountryHelper
{
    private readonly ICountryRepository _countryRepository;

    public CountryHelper(ICountryRepository countryRepository)
    {
        _countryRepository = countryRepository;
    }

    public async Task<Country> AddCountry(string name, string iso)
    {
        var country = new Country
        {
            Name = name,
            Iso = iso,
        };

        _countryRepository.Add(country);
        await _countryRepository.SaveChangesAsync();
        return country;
    }
}