﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddTrackingSheetsIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TrackingSheets_CompanyId",
                table: "TrackingSheets");

            migrationBuilder.CreateIndex(
                name: "IX_TrackingSheets_CompanyId_Year_Week",
                table: "TrackingSheets",
                columns: new[] { "CompanyId", "Year", "Week" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TrackingSheets_CompanyId_Year_Week",
                table: "TrackingSheets");

            migrationBuilder.CreateIndex(
                name: "IX_TrackingSheets_CompanyId",
                table: "TrackingSheets",
                column: "CompanyId");
        }
    }
}
