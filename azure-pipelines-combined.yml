trigger:
  - master
  - develop
# pr trigger specifies which branches cause a pull request build to run.
pr:
  - master
  - develop

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  Major: '1'
  Minor: '0'
  Patch:  $[counter('', 820)]
name: VigiLit $(Major).$(Minor).$(Patch).$(Build.SourceBranchName)

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git
stages:
- stage: BuildAndTest
  jobs:
    - job: BuildTest
      variables:
        - group: Nuget
        - name: NuGet_Source
          value: $[variables.Source]
        - name: LongLivedBranch
          ${{ if or(eq(variables['Build.SourceBranchName'], 'develop'), eq(variables['Build.SourceBranchName'], 'master')) }}:
            value : true
          ${{ else }}:
            value: false

      displayName: "Build And Test"
      steps:
        - template: Build/dotnet/build-test-analyse.yml@templates
          parameters:
            NugetSource: '$(NuGet_Source)'
            SourceBranch: "variables['Source_Branch']"
            VersionNumber: "$(Major).$(Minor).$(Patch)"
            SonarProjectKey: "phlexglobal_pharmalex-vigilit"
            SonarProjectName: "PharmaLex.Vigilit"
            SolutionName: '**/Vigilit.sln'
            TestProjects: '**/*.Tests.csproj'
            LongLivedBranch: ${{ variables.LongLivedBranch }}

- stage: BuildArtifacts
  dependsOn:
    - BuildAndTest
  jobs:
    - job: BuildArtifacts
      displayName: "Build Artifacts"            
      steps:
        - checkout: self
          submodules: true
          fetchDepth: 1
        - task: UseDotNet@2
          displayName: 'Install Dotnet Core 8' 
          inputs:
            packageType: 'sdk'
            version: '8.0.x'

        - task: DotNetCoreCLI@2
          displayName: 'dotnet restore'
          inputs:
            command: 'restore'
            projects: '**/*.csproj'
            feedsToUse: 'select'
            vstsFeed: '780c2041-bb89-4053-a6e6-13d4c16ae672'

        - task: CmdLine@2
          displayName: yarn install
          inputs:
            script: yarn install
            workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'

        - task: CmdLine@2
          displayName: yarn build
          inputs:
            script: yarn build-prod
            workingDirectory: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web'
       
        - task: DotNetCoreCLI@2
          displayName: 'dotnet build'
          inputs:
            command: 'build'
            projects: '**/*.csproj'
            arguments: '--configuration $(BuildConfiguration) --no-restore'
       
        - task: AzureCLI@1
          displayName: EF Migrations
          inputs:
            connectedServiceNameARM: d1d6e9a2-5725-47ed-bfc1-83158e696758
            scriptLocation: inlineScript
            inlineScript: >-
              dotnet tool install -g dotnet-ef --version 8.0.0

              dotnet ef migrations script --project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" --startup-project "$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.Web\PharmaLex.VigiLit.Web.csproj" --output $(Build.ArtifactStagingDirectory)\Migrations\migration.sql --idempotent --verbose --no-build --configuration $(BuildConfiguration)

        - task: DotNetCoreCLI@2
          displayName: 'dotnet publish web'
          inputs:
            command: 'publish'
            publishWebProjects: true
            arguments: '--configuration $(BuildConfiguration) --output $(build.artifactstagingdirectory)'

        - task: DotNetCoreCLI@2
          displayName: 'dotnet publish import function'
          inputs:
            command: 'publish'
            publishWebProjects: false
            projects: '$(Build.SourcesDirectory)\src\PharmaLex.VigiLit.ImportApp\PharmaLex.VigiLit.ImportApp.csproj'
            arguments: '--configuration Release --output $(build.artifactstagingdirectory)\import'
            zipAfterPublish: false
            modifyOutputPath: false

        - task: ArchiveFiles@2
          inputs:
            rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\import'
            includeRootFolder: false
            archiveType: 'zip'
            archiveFile: '$(Build.ArtifactStagingDirectory)\VigiLitImportApp.zip'
            replaceExistingArchive: true

        - task: PublishBuildArtifacts@1
          displayName: 'publish artifacts drop'
          inputs:
            PathtoPublish: '$(Build.ArtifactStagingDirectory)'
            ArtifactName: 'drop'
            publishLocation: 'Container'
