﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;
public class QualityControlCheckerTests
{
    private readonly Dictionary<string, string?> _inMemorySettings = new Dictionary<string, string?>
    {
        { "DataExtraction:MandatoryFieldMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:NonMandatoryFieldMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:OverallMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:MandatoryWeight", "2.0" },
        { "DataExtraction:NonMandatoryWeight", "1.0" },
    };

    private readonly IQualityControlChecker _qualityControlChecker;
    private readonly Mock<ILogger<QualityControlChecker>> _mockLogger = new();

    public QualityControlCheckerTests()
    {
        var fakeFactory = new FakeLoggerFactory<QualityControlChecker>(_mockLogger);

        IConfiguration configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(_inMemorySettings)
            .Build();

        _qualityControlChecker = new QualityControlChecker(fakeFactory, configuration);
    }

    [Fact]
    public void Given_extracted_reference_When_all_fields_set_and_above_thresholds_Then_passes_QC()
    {
        // Arrange 
        var extractedReference = GetBlankExtractedReference();

        // Act
        var result = _qualityControlChecker.DoesPassQualityControl(extractedReference);

        // Assert
        Assert.True(result);
    }

    // temporary commented out for PRDVGLT-3611
#pragma warning disable S1144, S125

    //[Theory]
    //[InlineData("Title")]
    //[InlineData("Abstract")]
    //[InlineData("JournalTitle")]
    //[InlineData("CountryOfOccurrence")]
    //public void Given_extracted_reference_When_mandatory_property_is_empty_Then_fails_QC(string propertyName)
    //{
    //    // Arrange 
    //    var extractedReference = GetExtractedReferenceWithStringEmptyProperty(propertyName);

    //    // Act
    //    var result = _qualityControlChecker.DoesPassQualityControl(extractedReference);

    //    // Assert
    //    Assert.False(result);
    //}


    //[Theory]
    //[InlineData("Title")]
    //[InlineData("Abstract")]
    //[InlineData("JournalTitle")]
    //[InlineData("CountryOfOccurrence")]
    //public void Given_extracted_reference_When_mandatory_property_confidence_is_below_threshold_Then_fails_QC(string propertyName)
    //{
    //    // Arrange 
    //    var extractedReference = GetExtractedReferenceWithConfidenceSet(propertyName, 0.50F);

    //    // Act
    //    var result = _qualityControlChecker.DoesPassQualityControl(extractedReference);

    //    // Assert
    //    Assert.False(result);
    //}

    //[Theory]
    //[InlineData("Doi")]
    //[InlineData("IssueNumber")]
    //[InlineData("Volume")]
    //[InlineData("Issn")]
    //[InlineData("Year")]
    //[InlineData("Pages")]
    //[InlineData("CountryOfOccurrence")]
    //[InlineData("Keywords")]
    //public void Given_extracted_reference_When_non_mandatory_property_confidence_is_below_threshold_Then_fails_QC(string propertyName)
    //{
    //    // Arrange 
    //    var extractedReference = GetExtractedReferenceWithConfidenceSet(propertyName, 0.50F);

    //    // Act
    //    var result = _qualityControlChecker.DoesPassQualityControl(extractedReference);

    //    // Assert
    //    Assert.False(result);
    //}

#pragma warning restore S1144, S125

    private static ExtractedReference GetExtractedReferenceWithStringEmptyProperty(string propertyName)
    {
        var extractedReference = GetBlankExtractedReference();

        var propertyInfo = typeof(ExtractedReference).GetProperty(propertyName);
        var extractedProperty = propertyInfo?.GetValue(extractedReference) as IExtractedProperty;
        var valueProp = extractedProperty?.GetType().GetProperty("Value");
        valueProp?.SetValue(extractedProperty, string.Empty);

        return extractedReference;
    }

    private static ExtractedReference GetExtractedReferenceWithConfidenceSet(string propertyName, float confidence)
    {
        var extractedReference = GetBlankExtractedReference();

        var propertyInfo = typeof(ExtractedReference).GetProperty(propertyName);
        var extractedProperty = propertyInfo?.GetValue(extractedReference) as IExtractedProperty;
        var valueProp = extractedProperty?.GetType().GetProperty("Confidence");
        valueProp?.SetValue(extractedProperty, confidence);

        return extractedReference;
    }

    private static ExtractedReference GetBlankExtractedReference()
    {
        var extractedReference = new ExtractedReference
        {
            Title = new Title()
            {
                Confidence = 95.0F,
                Value = "--TITLE--"
            },
            Abstract = new Abstract()
            {
                Confidence = 95.0F,
                Value = "--ABSTRACT--",
            },
            Authors = new Author[]
            {
                new Author()
                {
                    Confidence = 95.0F,
                    Value = "An Author",
                }
            },
            Affiliations = new Affiliation[]
            {
                new Affiliation()
                {
                    Confidence = 95.0F,
                    Value = "Affiliation"
                }
            },
            JournalTitle = new JournalTitle()
            {
                Confidence = 95.0F,
                Value = "--JOURNAL TITLE--"
            },
            Doi = new Doi()
            {
                Confidence = 95.0F,
                Value = "-- DOI --"
            },
            IssueNumber = new IssueNumber()
            {
                Confidence = 95.0F,
                Value = "--ISSUE NUMBER--"
            },
            Volume = new Volume()
            {
                Confidence = 95.0F,
                Value = "--VOLUME--"
            },
            Issn = new Issn()
            {
                Confidence = 95.0F,
                Value = "--ISSN--"
            },
            Year = new Year()
            {
                Confidence = 95.0F,
                Value = "--Year--"
            },
            Pages = new Pages()
            {
                Confidence = 95.0F,
                Value = "--PAGES--"
            },
            CountryOfOccurrence = new CountryOfOccurrence()
            {
                Confidence = 95.0F,
                Value = "--COUNTRY--"
            },
            Keywords = new Keywords()
            {
                Confidence = 95.0F,
                Value = "--KEYWORDS--"
            }
        };

        return extractedReference;
    }
}
