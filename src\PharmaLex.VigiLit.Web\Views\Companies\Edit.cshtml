@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.CompanyModel
@{
    ViewData["Title"] = "Companies";
    Layout = "CompanyLayout";
}

@section Buttons {
    <button asp-action="@(Model.Id == 0 ? "Create": "Edit" )" type="submit" class="button icon-button-save single-submit btn-default">Save</button>
    <a class="button secondary icon-button-cancel btn-default" href="/Companies">Cancel</a>
}

<section id="edit">
    <div class="gapped-2">
        <div class="flex gapped-3">
            <section class="text-left flex-grow-1">
                <input type="hidden" asp-for="Id" />
                <div class="form-group">
                    <label for="Name">Name</label>
                    <input asp-for="Name" type="text" required />
                    @Html.ValidationMessageFor(m => m.Name)
                </div>

                <div class="form-group">
                    <label for="ContactPersonName">Contact Person Name</label>
                    <input asp-for="ContactPersonName" type="text" maxlength="250" />
                </div>

                <div class="form-group">
                    <label for="ContactPersonEmail">Contact Person Email</label>
                    <input asp-for="ContactPersonEmail" type="text" maxlength="256" />
                    <span asp-validation-for="ContactPersonEmail"></span>
                </div>

                <div class="form-group">
                    <label for="IsActive">Active</label>
                    <input asp-for="IsActive" type="checkbox" />
                </div>
            </section>
        </div>
    </div>
</section>

<!-- Fix for console error issue -->
<script type="text/javascript">
    var pageConfig = {
        appElement: "#edit"
    }
</script>