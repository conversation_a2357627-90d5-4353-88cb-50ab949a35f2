﻿CREATE PROC dbo.spGetAiSuggestedClassification

/*---------------------------------------------------------------------------------------------------------------------
--
-- Summary:		Returns ai suggested classification for classification
--
-- Parameters:	@ClassificationId = classification ID
--				@DateRevised = ai suggestion must be on or after this date
--
-- Notes:		Used to display ai suggestion during pre-classification
--
-- Usage:		exec spGetAiSuggestedClassification 1, '2024-01-01'
--
---------------------------------------------------------------------------------------------------------------------*/

	@ClassificationId int, 
    @DateRevised datetime
AS

	SELECT TOP 1 
        [AiSuggestions].[Id],
        [AiSuggestions].[Status],
        CASE 
            WHEN [AiSuggestions].[Category] IS NULL
                THEN 0
            WHEN (SELECT [Id] FROM [ClassificationCategories] WHERE [Name] = [AiSuggestions].[Category]) IS NOT NULL
                THEN (SELECT [Id] FROM [ClassificationCategories] WHERE [Name] = [AiSuggestions].[Category]) 
            ELSE 0
        END As [CategoryId],
        [AiSuggestions].[Category],
        [AiSuggestions].[CategoryReason],
        [AiSuggestions].[DosageForm],
        [AiSuggestions].[DosageFormReason],
        [AiSuggestions].[CreatedDate],
        [AiSuggestions].[CreatedBy],
        [AiSuggestions].[LastUpdatedDate],
        [AiSuggestions].[LastUpdatedBy]
	FROM 
		[AiSuggestions] 
        INNER JOIN [References] ON [References].[PMID] = [AiSuggestions].[PMID] 
        INNER JOIN [Substances] ON [Substances].[Name] = [AiSuggestions].[Substance] 
        INNER JOIN [ReferenceClassifications] ON [ReferenceClassifications].[ReferenceId] = [References].[Id] AND [ReferenceClassifications].[SubstanceId] = [Substances].[Id]
	WHERE 
	    [ReferenceClassifications].[Id] = @ClassificationId 
        AND [AiSuggestions].[CreatedDate] >= @DateRevised
    ORDER BY Id DESC

GO