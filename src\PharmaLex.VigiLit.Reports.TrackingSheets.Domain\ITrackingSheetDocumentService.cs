﻿using PharmaLex.VigiLit.Domain.Models.Document.TrackingSheet;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public interface ITrackingSheetDocumentService
{
    Task Create(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, Stream stream, CancellationToken cancellationToken = default);
    Task<bool> Exists(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default);
    Task<Stream> OpenRead(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default);
    Task Delete(TrackingSheetDocumentDescriptor trackingSheetDocumentDescriptor, CancellationToken cancellationToken = default);
}