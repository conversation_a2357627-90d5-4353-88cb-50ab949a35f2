using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.Merz.Domain;

public class Repository : GenericRepository<MerzClientReportResult>, IRepository
{
    public Repository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<List<MerzClientReportResult>> GetStoredProcResults(int year, int month)
    {
        var startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Utc);
        var endDate = startDate.AddMonths(1);

        var results = await context.Set<MerzClientReportResult>()
            .FromSqlRaw("exec spMerzClassificationReport @CompanyName, @StartDate, @EndDate",
                new SqlParameter("@CompanyName", "Merz Therapeutics"),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate))
            .ToListAsync();

        return results;
    }
}
