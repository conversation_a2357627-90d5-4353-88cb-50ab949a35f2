﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Infrastructure.Unit.Tests.Utilities;

public class PubMedUrlRenderHelperTests
{
    private readonly IPubMedUrlRenderHelper _pubMedUrlRenderHelper;
    private readonly Mock<ILogger<PubMedUrlRenderHelper>> _mockLogger = new();

    public PubMedUrlRenderHelperTests()
    {
        var fakeFactory = new FakeLoggerFactory<PubMedUrlRenderHelper>(_mockLogger);
        _pubMedUrlRenderHelper = new PubMedUrlRenderHelper(fakeFactory);
    }

    [Fact]
    public void GetUrlLinkBasedOnSourceSystem_ForPubMedReturns_AbstractAndUrlConcatenated()
    {
        // Arrange
        var referenceModel = GetReferenceModel();

        // Act
        var abstractAndUrlConcatenated = _pubMedUrlRenderHelper.GetAbstractAndUrlConcatenated(referenceModel);

        // Assert
        Assert.Equal("Reference abstract <a href=\"https://pubmed.ncbi.nlm.nih.gov/12345\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ", abstractAndUrlConcatenated);
    }

    private static ReferenceModel GetReferenceModel()
    {
        return new ReferenceModel
        {
            Abstract = "Reference abstract",
            SourceId = "12345"
        };
    }
}