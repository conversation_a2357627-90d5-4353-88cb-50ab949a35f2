﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class CorrectResearcherClaims : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("exec('UPDATE [Claims] SET Name = ''PharmaLexResearcher'', DisplayName = ''PharmaLex Researcher'' WHERE Id = 7; " +
                                 "UPDATE [Claims] SET Name = ''PharmaLexClientResearcher'', DisplayName = ''PharmaLex Client Researcher'' WHERE Id = 8;')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}
