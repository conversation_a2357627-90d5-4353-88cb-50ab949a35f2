﻿using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class ImportHelper
{
    private readonly IImportRepository _importRepository;

    public ImportHelper(IImportRepository importRepository)
    {
        _importRepository = importRepository;
    }

    public async Task<Import> AddImport()
    {
        var import = new Import(ImportType.Scheduled, ImportTriggerType.Auto, DateTime.Now);
        _importRepository.Add(import);
        await _importRepository.SaveChangesAsync();
        return import;
    }
}