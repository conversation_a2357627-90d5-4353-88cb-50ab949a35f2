using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize]
[Route("[controller]")]
public class ReferencesController : BaseController
{
    private readonly IReferenceService _referenceService;
    private readonly IClassificationService _classificationService;
    private readonly IUserRepository<User> _userRepository;
    private readonly ICompanyService _companyService;
    private readonly ISplitReferenceService _splitReferenceService;
    private readonly IAccessControlService _accessControlService;

    public ReferencesController(IReferenceService referenceService, 
        IClassificationService classificationService, 
        ICompanyService companyService,
        IUserRepository<User> userRepository, 
        ISplitReferenceService splitReferenceService, 
        IUserSessionService userSessionService, 
        IAccessControlService accessControlService,
        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _referenceService = referenceService;
        _classificationService = classificationService;
        _userRepository = userRepository;
        _companyService = companyService;
        _splitReferenceService = splitReferenceService;
        _accessControlService = accessControlService;
    }

    [HttpGet("{referenceId}")]
    public async Task<IActionResult> ReferenceDetails(int referenceId)
    {
        try
        {
            var viewContext = new ViewReferencePermissionContext(CurrentUserId, referenceId);
            await _accessControlService.HasPermission(viewContext);

            var model = await _referenceService.GetReferenceDetails(referenceId, await _userRepository.GetForSecurity(CurrentUserId));
            return View(model);
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
    }

    [HttpGet("Classifications/{referenceClassificationId}")]
    public async Task<IActionResult> ReferenceHistory(int referenceClassificationId)
    {
        try
        {
            var viewContext = new ViewReferenceClassificationPermissionContext(CurrentUserId, referenceClassificationId);
            await _accessControlService.HasPermission(viewContext);

            var model = await _classificationService.GetReferenceHistoryDetailsInitialPageLoad(referenceClassificationId, await _userRepository.GetForSecurity(CurrentUserId));
            return View(model);
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
        catch (Exception)
        {
            ViewBag.error = "No reference history actions found for this substance.";
            return View();
        }
    }

    [HttpGet("Classifications/{referenceClassificationId}/Action/{actionId}")]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult<ReferenceHistoryDetailsPageModel>> GetReferenceHistoryByAction(int referenceClassificationId, int actionId, int previousActionId)
    {
        try
        {
            var viewContext = new ViewReferenceClassificationPermissionContext(CurrentUserId, referenceClassificationId);
            await _accessControlService.HasPermission(viewContext);

            var model = await _classificationService.GetReferenceHistoryDetailsByAction(referenceClassificationId, actionId, previousActionId, await _userRepository.GetForSecurity(CurrentUserId));
            return model;
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
    }

    [HttpPost("[action]")]
    [Authorize(Policy = Policies.InternalSupport)]
    public async Task<IActionResult> Reclassify([FromBody] ReferenceClassificationModel referenceClassification)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        await _classificationService.EditFromReferenceHistory(referenceClassification);

        return Ok();
    }

    [HttpGet("/References/Split/{referenceId}")]
    [Authorize(Policy = Policies.CaseFileOperator)]
    public async Task<IActionResult> ReferenceSplit(int referenceId)
    {
        var model = await _referenceService.GetSplitReferenceDetails(referenceId);
        return View(model);
    }

    [HttpGet("/References/Split/Companies/{substanceId}")]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Policies.CaseFileOperator)]
    public async Task<IEnumerable<CompanyModel>> GetSplitReferenceCompaniesForSubstance(int substanceId)
    {
        return await _companyService.GetActiveCompaniesWithActiveContractsForSubstance(substanceId);
    }

    [HttpPost("/References/Split/Save")]
    [Authorize(Policy = Policies.CaseFileOperator)]
    public async Task<IActionResult> SaveSplitReference([FromBody] ReferenceSplitModel model)
    {
        await _splitReferenceService.CreateClassification(model);
        return Ok();
    }
}