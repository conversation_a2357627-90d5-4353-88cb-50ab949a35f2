﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using PharmaLex.VigiLit.Reporting.Contracts.Models.Reports;

namespace PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;

public interface IReportService
{
    /// <summary>
    /// Gets the report.
    /// </summary>
    /// <param name="id">The identifier.</param>
    /// <returns>The report.</returns>
    Task<IReportEntity> GetReport(int id);

    /// <summary>
    /// Gets the reports page model.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <returns>
    /// The reports page model.
    /// </returns>
    Task<ReportsPageModel> GetReportsPageModel(IUserEntity user);
}
