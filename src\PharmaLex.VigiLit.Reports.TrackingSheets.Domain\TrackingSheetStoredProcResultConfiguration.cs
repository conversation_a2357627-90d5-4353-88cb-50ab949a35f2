using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

public class TrackingSheetStoredProcResultConfiguration : EntityBaseMap<TrackingSheetStoredProcResult>
{
    public override void Configure(EntityTypeBuilder<TrackingSheetStoredProcResult> builder)
    {
        builder.HasNoKey().ToView(null);
    }
}