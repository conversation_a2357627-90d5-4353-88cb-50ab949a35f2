﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class SplitClassifierId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MasterAssessorId",
                table: "ReferenceClassifications",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PreAssessorId",
                table: "ReferenceClassifications",
                type: "int",
                nullable: true);

            // Populate the new cols based on the old col.
            migrationBuilder.Sql("exec('UPDATE ReferenceClassifications SET PreAssessorId = ClassifierId')");
            migrationBuilder.Sql("exec('UPDATE ReferenceClassifications SET MasterAssessorId = ClassifierId')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MasterAssessorId",
                table: "ReferenceClassifications")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferenceClassificationsHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);

            migrationBuilder.DropColumn(
                name: "PreAssessorId",
                table: "ReferenceClassifications")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "ReferenceClassificationsHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null);
        }
    }
}
