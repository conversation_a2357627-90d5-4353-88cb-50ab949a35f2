<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" Sdk="Microsoft.Docker.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectVersion>2.1</ProjectVersion>
    <DockerTargetOS>Linux</DockerTargetOS>
    <DockerPublishLocally>False</DockerPublishLocally>
    <ProjectGuid>************************************</ProjectGuid>
    <DockerComposeProjectName>vigilit</DockerComposeProjectName>
	  <DockerComposeBaseFilePath>docker-compose-develop</DockerComposeBaseFilePath>
  </PropertyGroup>
  <ItemGroup>
    <None Include="docker-compose-develop.override.yml">
      <DependentUpon>docker-compose-develop.yml</DependentUpon>
    </None>
    <None Include="docker-compose-develop.yml" />
    <None Include=".dockerignore" />
  </ItemGroup>
</Project>