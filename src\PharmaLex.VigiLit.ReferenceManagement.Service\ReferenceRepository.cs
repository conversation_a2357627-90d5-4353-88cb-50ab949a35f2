using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class ReferenceRepository : TrackingGenericRepository<Reference>, IReferenceRepository, IImportingReferenceRepository
{
    private readonly IMapper _mapper;

    public ReferenceRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<ReferenceModel>> GetAllAsync()
    {
        var query = context.Set<Reference>()
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceModel>(query).ToListAsync();
    }

    public async Task<ReferenceDetailedModel> GetReferenceSnapshot(int id, DateTime timestamp)
    {
        var query = await context.Set<Reference>()
            .TemporalAll()
            .AsNoTracking()
            .Where(u => u.Id == id)
            .Select(s => new ReferenceDetailedTemporalModel
            {
                Id = s.Id,
                Abstract = s.Abstract,
                AffiliationTextFirstAuthor = s.AffiliationTextFirstAuthor,
                Authors = s.Authors,
                CountryOfOccurrence = s.CountryOfOccurrence,
                DateRevised = s.DateRevised,
                Doi = s.Doi,
                Language = s.Language,
                SourceId = s.SourceId,
                Title = s.Title,
                Keywords = s.Keywords,
                MeshHeadings = s.MeshHeadings,
                PeriodStart = EF.Property<DateTime>(s, "PeriodStart"),
                PeriodEnd = EF.Property<DateTime>(s, "PeriodEnd"),
                SourceSystem = (SourceSystem) s.SourceSystem
            })
            .ToListAsync();

        // Note: the temporal time periods denoted by PeriodStart/PeriodEnd for each record are mutually exclusive; they never overlap
        var referenceDetails = query.SingleOrDefault(t => timestamp >= t.PeriodStart && timestamp < t.PeriodEnd);

        if (referenceDetails == null)
        {
            // the timestamp in ReferenceHistoryAction is outside the range of all the PeriodStart-PeriodEnd ranges in the temporal table.
            // It can only be before the earliest PeriodStart since the last PeriodEnd is 31/12/9999 23:59:59
            referenceDetails = query.OrderBy(t => t.PeriodStart).FirstOrDefault();
        }

        return _mapper.Map<ReferenceDetailedModel>(referenceDetails);
    }

    public async Task<Reference> GetByIdForImport(int id)
    {
        // Optimised for import do not re-use.
        return await context.Set<Reference>()
            .Where(u => u.Id == id)
            .FirstAsync();
    }

    public async Task<ReferenceIdentifiers> GetReferenceIdentifiers(string sourceId)
    {
        var referenceIdentifiers = await context.Set<Reference>()
            .Where(r => r.SourceId == sourceId)
            .AsNoTracking()
            .Select(r => new ReferenceIdentifiers(r.Id, r.SourceId, r.DateRevised))
            .FirstOrDefaultAsync();

        return referenceIdentifiers!;
    }

    public async Task<IEnumerable<ReferenceSupportModel>> Search(string term, User user)
    {
        // only search if there's a term
        if (string.IsNullOrWhiteSpace(term))
        {
            return Enumerable.Empty<ReferenceSupportModel>();
        }

        // trim the input
        var termTrimmed = term.Trim();

        // try parse as int
        //_ = int.TryParse(termTrimmed, out int termInt); // What some awful code!

        // search
        var query = context.Set<Reference>()
            .AsNoTracking()
            .Where(r => r.SourceId == termTrimmed || r.Doi == termTrimmed);

        // security
        if (user.IsCompanyUser())
        {
            if (!user.HasActiveCompany())
            {
                return Enumerable.Empty<ReferenceSupportModel>();
            }

            query = query.Where(r => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == user.GetActiveCompanyId() && ci.ReferenceId == r.Id));
        }

        return await _mapper.ProjectTo<ReferenceSupportModel>(query).ToListAsync();
    }

    public async Task<Reference> GetReferenceById(int id)
    {
        return await context.Set<Reference>()
           .Where(r => r.Id == id)
           .FirstAsync();
    }

    public async Task<Reference> GetReferenceBySourceId(string sourceId)
    {
        Reference? reference = null;
        reference = await context.Set<Reference>()
             .Where(r => r.SourceId == sourceId)
             .FirstOrDefaultAsync();
        return reference!;
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }

    public async Task<ReferenceSendGridTemplateModel> GetForEmail(int referenceId)
    {
        ReferenceSendGridTemplateModel? result = null;
        var query = context.Set<Reference>()
            .Where(u => u.Id == referenceId)
            .AsNoTracking();

        result = await _mapper.ProjectTo<ReferenceSendGridTemplateModel>(query).FirstOrDefaultAsync();
        return result!;
    }

    public async Task<bool> DoesDoiExist(string doiId)
    {
        return await context.Set<Reference>()
            .AnyAsync(r => r.Doi == doiId);
    }
}