﻿using System.Xml.Serialization;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.ESearch;

[XmlRoot("eSearchResult")]
public class ESearchResult
{
    public ESearchResult()
    {
        WarningList = new WarningList();
        ErrorList = new ErrorList();
        IdList = new IdList();
        TranslationSet = new TranslationSet();
        TranslationStack = new TranslationStack();
    }
    [XmlElement("IdList", typeof(IdList))]
    public IdList IdList { get; set; }

    public int Count { get; set; }

    public string ERROR { get; set; }

    public int QueryKey { get; set; }

    public string QueryTranslation { get; set; }

    public int RetMax { get; set; }

    public int RetStart { get; set; }

    public TranslationSet TranslationSet { get; set; }

    public TranslationStack TranslationStack { get; set; }

    public string WebEnv { get; set; }

    //[XmlElement("Count", typeof(string))]
    //[XmlElement("ERROR", typeof(string))]
    ////[XmlElement("IdList", typeof(IdList))]
    //[XmlElement("QueryKey", typeof(string))]
    //[XmlElement("QueryTranslation", typeof(string))]
    //[XmlElement("RetMax", typeof(string))]
    //[XmlElement("RetStart", typeof(string))]
    //[XmlElement("TranslationSet", typeof(TranslationSet))]
    //[XmlElement("TranslationStack", typeof(TranslationStack))]
    //[XmlElement("WebEnv", typeof(string))]
    //[XmlChoiceIdentifierAttribute("ItemsElementName")]
    //public object[] Items
    //{
    //    get
    //    {
    //        return _items;
    //    }
    //    set
    //    {
    //        _items = value;
    //    }
    //}

    //[XmlElement("ItemsElementName")]
    //[XmlIgnore]
    //public ItemsChoiceType[] ItemsElementName
    //{
    //    get
    //    {
    //        return _itemsElementName;
    //    }
    //    set
    //    {
    //        _itemsElementName = value;
    //    }
    //}

    public ErrorList ErrorList { get; set; }

    public WarningList WarningList { get; set; }
}