
<!--
     File isoamsn.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     References to the VARIANT SELECTOR 1 character (&#x0FE00;)
     should match the uses listed in Unicode Technical Report 25.

     Entity names in this file are derived from files carrying the
     following notice:

     (C) International Organization for Standardization 1991
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.

-->

<!ENTITY gnap             "&#x02A8A;" ><!--/gnapprox N: greater, not approximate -->
<!ENTITY gnE              "&#x02269;" ><!--/gneqq N: greater, not dbl equals -->
<!ENTITY gne              "&#x02A88;" ><!--/gneq N: greater, not equals -->
<!ENTITY gnsim            "&#x022E7;" ><!--/gnsim N: greater, not similar -->
<!ENTITY gvnE             "&#x02269;&#x0FE00;" ><!--/gvertneqq N: gt, vert, not dbl eq -->
<!ENTITY lnap             "&#x02A89;" ><!--/lnapprox N: less, not approximate -->
<!ENTITY lnE              "&#x02268;" ><!--/lneqq N: less, not double equals -->
<!ENTITY lne              "&#x02A87;" ><!--/lneq N: less, not equals -->
<!ENTITY lnsim            "&#x022E6;" ><!--/lnsim N: less, not similar -->
<!ENTITY lvnE             "&#x02268;&#x0FE00;" ><!--/lvertneqq N: less, vert, not dbl eq -->
<!ENTITY nap              "&#x02249;" ><!--/napprox N: not approximate -->
<!ENTITY napE             "&#x02A70;&#x00338;" ><!--not approximately equal or equal to -->
<!ENTITY napid            "&#x0224B;&#x00338;" ><!--not approximately identical to -->
<!ENTITY ncong            "&#x02247;" ><!--/ncong N: not congruent with -->
<!ENTITY ncongdot         "&#x02A6D;&#x00338;" ><!--not congruent, dot -->
<!ENTITY nequiv           "&#x02262;" ><!--/nequiv N: not identical with -->
<!ENTITY ngE              "&#x02267;&#x00338;" ><!--/ngeqq N: not greater, dbl equals -->
<!ENTITY nge              "&#x02271;" ><!--/ngeq N: not greater-than-or-equal -->
<!ENTITY nges             "&#x02A7E;&#x00338;" ><!--/ngeqslant N: not gt-or-eq, slanted -->
<!ENTITY nGg              "&#x022D9;&#x00338;" ><!--not triple greater than -->
<!ENTITY ngsim            "&#x02275;" ><!--not greater, similar -->
<!ENTITY nGt              "&#x0226B;&#x020D2;" ><!--not, vert, much greater than -->
<!ENTITY ngt              "&#x0226F;" ><!--/ngtr N: not greater-than -->
<!ENTITY nGtv             "&#x0226B;&#x00338;" ><!--not much greater than, variant -->
<!ENTITY nlE              "&#x02266;&#x00338;" ><!--/nleqq N: not less, dbl equals -->
<!ENTITY nle              "&#x02270;" ><!--/nleq N: not less-than-or-equal -->
<!ENTITY nles             "&#x02A7D;&#x00338;" ><!--/nleqslant N: not less-or-eq, slant -->
<!ENTITY nLl              "&#x022D8;&#x00338;" ><!--not triple less than -->
<!ENTITY nlsim            "&#x02274;" ><!--not less, similar -->
<!ENTITY nLt              "&#x0226A;&#x020D2;" ><!--not, vert, much less than -->
<!ENTITY nlt              "&#x0226E;" ><!--/nless N: not less-than -->
<!ENTITY nltri            "&#x022EA;" ><!--/ntriangleleft N: not left triangle -->
<!ENTITY nltrie           "&#x022EC;" ><!--/ntrianglelefteq N: not l tri, eq -->
<!ENTITY nLtv             "&#x0226A;&#x00338;" ><!--not much less than, variant -->
<!ENTITY nmid             "&#x02224;" ><!--/nmid -->
<!ENTITY npar             "&#x02226;" ><!--/nparallel N: not parallel -->
<!ENTITY npr              "&#x02280;" ><!--/nprec N: not precedes -->
<!ENTITY nprcue           "&#x022E0;" ><!--not curly precedes, eq -->
<!ENTITY npre             "&#x02AAF;&#x00338;" ><!--/npreceq N: not precedes, equals -->
<!ENTITY nrtri            "&#x022EB;" ><!--/ntriangleright N: not rt triangle -->
<!ENTITY nrtrie           "&#x022ED;" ><!--/ntrianglerighteq N: not r tri, eq -->
<!ENTITY nsc              "&#x02281;" ><!--/nsucc N: not succeeds -->
<!ENTITY nsccue           "&#x022E1;" ><!--not succeeds, curly eq -->
<!ENTITY nsce             "&#x02AB0;&#x00338;" ><!--/nsucceq N: not succeeds, equals -->
<!ENTITY nsim             "&#x02241;" ><!--/nsim N: not similar -->
<!ENTITY nsime            "&#x02244;" ><!--/nsimeq N: not similar, equals -->
<!ENTITY nsmid            "&#x02224;" ><!--/nshortmid -->
<!ENTITY nspar            "&#x02226;" ><!--/nshortparallel N: not short par -->
<!ENTITY nsqsube          "&#x022E2;" ><!--not, square subset, equals -->
<!ENTITY nsqsupe          "&#x022E3;" ><!--not, square superset, equals -->
<!ENTITY nsub             "&#x02284;" ><!--not subset -->
<!ENTITY nsubE            "&#x02AC5;&#x00338;" ><!--/nsubseteqq N: not subset, dbl eq -->
<!ENTITY nsube            "&#x02288;" ><!--/nsubseteq N: not subset, equals -->
<!ENTITY nsup             "&#x02285;" ><!--not superset -->
<!ENTITY nsupE            "&#x02AC6;&#x00338;" ><!--/nsupseteqq N: not superset, dbl eq -->
<!ENTITY nsupe            "&#x02289;" ><!--/nsupseteq N: not superset, equals -->
<!ENTITY ntgl             "&#x02279;" ><!--not greater, less -->
<!ENTITY ntlg             "&#x02278;" ><!--not less, greater -->
<!ENTITY nvap             "&#x0224D;&#x020D2;" ><!--not, vert, approximate -->
<!ENTITY nVDash           "&#x022AF;" ><!--/nVDash N: not dbl vert, dbl dash -->
<!ENTITY nVdash           "&#x022AE;" ><!--/nVdash N: not dbl vertical, dash -->
<!ENTITY nvDash           "&#x022AD;" ><!--/nvDash N: not vertical, dbl dash -->
<!ENTITY nvdash           "&#x022AC;" ><!--/nvdash N: not vertical, dash -->
<!ENTITY nvge             "&#x02265;&#x020D2;" ><!--not, vert, greater-than-or-equal -->
<!ENTITY nvgt             "&#x0003E;&#x020D2;" ><!--not, vert, greater-than -->
<!ENTITY nvle             "&#x02264;&#x020D2;" ><!--not, vert, less-than-or-equal -->
<!ENTITY nvlt             "&#38;#x0003C;&#x020D2;" ><!--not, vert, less-than -->
<!ENTITY nvltrie          "&#x022B4;&#x020D2;" ><!--not, vert, left triangle, equals -->
<!ENTITY nvrtrie          "&#x022B5;&#x020D2;" ><!--not, vert, right triangle, equals -->
<!ENTITY nvsim            "&#x0223C;&#x020D2;" ><!--not, vert, similar -->
<!ENTITY parsim           "&#x02AF3;" ><!--parallel, similar -->
<!ENTITY prnap            "&#x02AB9;" ><!--/precnapprox N: precedes, not approx -->
<!ENTITY prnE             "&#x02AB5;" ><!--/precneqq N: precedes, not dbl eq -->
<!ENTITY prnsim           "&#x022E8;" ><!--/precnsim N: precedes, not similar -->
<!ENTITY rnmid            "&#x02AEE;" ><!--reverse /nmid -->
<!ENTITY scnap            "&#x02ABA;" ><!--/succnapprox N: succeeds, not approx -->
<!ENTITY scnE             "&#x02AB6;" ><!--/succneqq N: succeeds, not dbl eq -->
<!ENTITY scnsim           "&#x022E9;" ><!--/succnsim N: succeeds, not similar -->
<!ENTITY simne            "&#x02246;" ><!--similar, not equals -->
<!ENTITY solbar           "&#x0233F;" ><!--solidus, bar through -->
<!ENTITY subnE            "&#x02ACB;" ><!--/subsetneqq N: subset, not dbl eq -->
<!ENTITY subne            "&#x0228A;" ><!--/subsetneq N: subset, not equals -->
<!ENTITY supnE            "&#x02ACC;" ><!--/supsetneqq N: superset, not dbl eq -->
<!ENTITY supne            "&#x0228B;" ><!--/supsetneq N: superset, not equals -->
<!ENTITY vnsub            "&#x02282;&#x020D2;" ><!--/nsubset N: not subset, var -->
<!ENTITY vnsup            "&#x02283;&#x020D2;" ><!--/nsupset N: not superset, var -->
<!ENTITY vsubnE           "&#x02ACB;&#x0FE00;" ><!--/varsubsetneqq N: subset not dbl eq, var -->
<!ENTITY vsubne           "&#x0228A;&#x0FE00;" ><!--/varsubsetneq N: subset, not eq, var -->
<!ENTITY vsupnE           "&#x02ACC;&#x0FE00;" ><!--/varsupsetneqq N: super not dbl eq, var -->
<!ENTITY vsupne           "&#x0228B;&#x0FE00;" ><!--/varsupsetneq N: superset, not eq, var -->
