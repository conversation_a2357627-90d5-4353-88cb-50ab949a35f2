﻿using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

namespace PharmaLex.VigiLit.Test.DomainHelpers;

public class ImportContractHelper
{
    private readonly IImportContractRepository _importContractRepository;

    public ImportContractHelper(IImportContractRepository importContractRepository)
    {
        _importContractRepository = importContractRepository;
    }

    public async Task<ImportContract> AddImportContract(int importId, Contract contract, int substanceId, int projectId)
    {
        var importContract = new ImportContract(contract, DateTime.Now, null)
        {
            ImportId = importId,
        };

        _importContractRepository.Add(importContract);
        await _importContractRepository.SaveChangesAsync();
        return importContract;
    }
}