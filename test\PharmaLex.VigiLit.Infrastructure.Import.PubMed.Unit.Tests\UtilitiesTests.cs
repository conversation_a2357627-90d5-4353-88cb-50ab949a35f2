﻿namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests;

public class UtilitiesTests
{
    [Fact]
    public void RegexMultiReplace_ReplaceTags()
    {
        // Arrange
        var input = @"Sample Text 
                      <i>italic</i> and 
                      <b>bold</b> and 
                      <sub>subtext</sub> and 
                      <sup>supertext</sup> and 
                      <u>underline</u> the end.";

        List<(string pattern, string replacementText)> rules = new()
        {
            (pattern: @"<i>", replacementText: "##i##"),
            (pattern: @"</i>", replacementText: "##/i##"),
            (pattern: @"<b>", replacementText: "##b##"),
            (pattern: @"</b>", replacementText: "##/b##"),
            (pattern: @"<sub>", replacementText: "##sub##"),
            (pattern: @"</sub>", replacementText: "##/sub##"),
            (pattern: @"<sup>", replacementText: "##sup##"),
            (pattern: @"</sup>", replacementText: "##/sup##"),
            (pattern: @"<u>", replacementText: "##u##"),
            (pattern: @"</u>", replacementText: "##/u##")
        };

        // Act
        var output = Utilities.RegexMultiReplace(input, rules);

        // Assert
        var expectedOutput = @"Sample Text 
                      ##i##italic##/i## and 
                      ##b##bold##/b## and 
                      ##sub##subtext##/sub## and 
                      ##sup##supertext##/sup## and 
                      ##u##underline##/u## the end.";

        Assert.Equal(expectedOutput, output);
    }

    [Fact]
    public void RegexMultiReplace_UnReplaceTags()
    {
        // Arrange
        var input = @"Sample Text 
                      ##i##italic##/i## and 
                      ##b##bold##/b## and 
                      ##sub##subtext##/sub## and 
                      ##sup##supertext##/sup## and 
                      ##u##underline##/u## the end.";

        List<(string pattern, string replacementText)> rules = new()
        {
            (pattern: @"##i##", replacementText: "<i>"),
            (pattern: @"##/i##", replacementText: "</i>"),
            (pattern: @"##b##", replacementText: "<b>"),
            (pattern: @"##/b##", replacementText: "</b>"),
            (pattern: @"##sub##", replacementText: "<sub>"),
            (pattern: @"##/sub##", replacementText: "</sub>"),
            (pattern: @"##sup##", replacementText: "<sup>"),
            (pattern: @"##/sup##", replacementText: "</sup>"),
            (pattern: @"##u##", replacementText: "<u>"),
            (pattern: @"##/u##", replacementText: "</u>")
        };

        // Act
        var output = Utilities.RegexMultiReplace(input, rules);

        // Assert
        var expectedOutput = @"Sample Text 
                      <i>italic</i> and 
                      <b>bold</b> and 
                      <sub>subtext</sub> and 
                      <sup>supertext</sup> and 
                      <u>underline</u> the end.";

        Assert.Equal(expectedOutput, output);
    }
}
