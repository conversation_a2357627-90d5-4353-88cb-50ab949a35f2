﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddTemporaryMigrationTables_3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressUser",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressSubstance",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressReference",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressProject",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SH<PERSON>",
                table: "MIGRATION_ProgressInternalUser",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressContract",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressCompanies",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SHA",
                table: "MIGRATION_ProgressClassification",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MIGRATION_ProgressReferencePMIDs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PMID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SHA = table.Column<byte[]>(type: "varbinary(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlId = table.Column<long>(type: "bigint", nullable: false),
                    MySqlName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MySqlDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VigiLitId = table.Column<int>(type: "int", nullable: true),
                    Processed = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MIGRATION_ProgressReferencePMIDs", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MIGRATION_ProgressReferencePMIDs");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressUser");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressSubstance");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressReference");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressProject");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressInternalUser");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressContract");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressCompanies");

            migrationBuilder.DropColumn(
                name: "SHA",
                table: "MIGRATION_ProgressClassification");
        }
    }
}
