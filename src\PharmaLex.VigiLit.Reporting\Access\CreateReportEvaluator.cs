﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Reporting.Contracts.Security;

namespace PharmaLex.VigiLit.Reporting.Access;

internal class CreateReportEvaluator : PermissionEvaluator<CreateReportPermissionContext>
{
    private readonly IUserRepository _userRepository;
    
    public CreateReportEvaluator(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    private async Task<IUserEntity> GetSecurityUser(int userId)
    {
        return await _userRepository.GetForSecurity(userId);
    }

    public async Task<bool> CanUserCreateTrackingSheet(int userId, int requestCompanyId)
    {
        var user = await GetSecurityUser(userId);

        if (TryDetermineAccessByUserType(user, out bool accessResult))
        {
            return accessResult;
        }

        return user.GetActiveCompanyId() == requestCompanyId;
    }
    public override async Task<bool> HasPermissions(CreateReportPermissionContext context)
    {
        if (await CanUserCreateTrackingSheet(context.UserId, context.CompanyId))
        {
            return true;
        }

        throw new UnauthorizedAccessException();
    }

    /// <summary>
    /// Tries to evaluate access based on their user type.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <param name="accessResult">if returns <c>true</c> [access result].</param>
    /// <returns>
    /// Returns True if can evaluate what the access should be and returns that access result. 
    /// Returns False if further processing is necessary for the operation. 
    /// </returns>
    private static bool TryDetermineAccessByUserType(IUserEntity user, out bool accessResult)
    {
        accessResult = false;

        if (user == null)
        {
            accessResult = false;
            return true;
        }

        if (!user.IsCompanyUser())
        {
            accessResult = true;
            return true;
        }

        if (!user.HasActiveCompany())
        {
            accessResult = false;
            return true;
        }

        return false;
    }
}
