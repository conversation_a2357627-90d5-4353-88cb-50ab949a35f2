﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndex20230602a : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_SubstanceId",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_SubstanceId_Id_ReferenceId_LastUpdatedDate",
                table: "ReferenceClassifications",
                columns: new[] { "SubstanceId", "Id", "ReferenceId", "LastUpdatedDate" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_SubstanceId_Id_ReferenceId_LastUpdatedDate",
                table: "ReferenceClassifications");

            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_SubstanceId",
                table: "ReferenceClassifications",
                column: "SubstanceId");
        }
    }
}
